#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1237472 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:184), pid=39124, tid=40320
#
# JRE version: OpenJDK Runtime Environment (20.0+36) (build 20+36-2344)
# Java VM: OpenJDK 64-Bit Server VM (20+36-2344, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -Dvaadin.copilot.pluginDotFilePath=C:\Users\<USER>\git\scrapingmercari\.idea\.copilot-plugin -javaagent:C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\lib\idea_rt.jar=61472 -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 com.mrcresearch.screen.main.Application

Host: AMD Ryzen 5 8600G w/ Radeon 760M Graphics      , 12 cores, 31G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4484)
Time: Sat Jul 19 13:43:40 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4484) elapsed time: 42.424510 seconds (0d 0h 0m 42s)

---------------  T H R E A D  ---------------

Current thread (0x000002c4d71bad00):  JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=40320, stack(0x000000e63af70000,0x000000e63b070000)]


Current CompileTask:
C2:  42424 11138   !   4       sun.awt.PostEventQueue::flush (168 bytes)

Stack: [0x000000e63af70000,0x000000e63b070000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6bb95a]
V  [jvm.dll+0x8483aa]
V  [jvm.dll+0x849fa5]
V  [jvm.dll+0x84a6a3]
V  [jvm.dll+0x280b0f]
V  [jvm.dll+0xc2b11]
V  [jvm.dll+0xc2e35]
V  [jvm.dll+0x3bb17c]
V  [jvm.dll+0x3857f5]
V  [jvm.dll+0x384c6a]
V  [jvm.dll+0x24b259]
V  [jvm.dll+0x24a651]
V  [jvm.dll+0x1ce491]
V  [jvm.dll+0x259e1b]
V  [jvm.dll+0x2584f0]
V  [jvm.dll+0x3f2895]
V  [jvm.dll+0x7f4906]
V  [jvm.dll+0x6ba5cb]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002c4cce3afe0, length=49, elements={
0x000002c4cc410880, 0x000002c4cc4110c0, 0x000002c4cc411cf0, 0x000002c4cc412720,
0x000002c4cc41a020, 0x000002c4cc41bea0, 0x000002c4cc421db0, 0x000002c4cc426810,
0x000002c4c587ccd0, 0x000002c4cca8bf90, 0x000002c4cc8254a0, 0x000002c4ccd61c90,
0x000002c4ccb688e0, 0x000002c4ccffd1d0, 0x000002c4cd491870, 0x000002c4cd528830,
0x000002c4cd609c40, 0x000002c4cd60cc50, 0x000002c4cd60d1a0, 0x000002c4cd60ec30,
0x000002c4cde32f00, 0x000002c4cde319c0, 0x000002c4cde34ee0, 0x000002c4cde32460,
0x000002c4cde36970, 0x000002c4d7a6f430, 0x000002c4d7a70970, 0x000002c4d7a6eee0,
0x000002c4d7a6cf00, 0x000002c4d7a70ec0, 0x000002c4d7a71410, 0x000002c4d7a6d9a0,
0x000002c4d7a73940, 0x000002c4d7a73e90, 0x000002c4cde2f9e0, 0x000002c4cde35430,
0x000002c4d843daa0, 0x000002c4d843cab0, 0x000002c4d843dff0, 0x000002c4d843e540,
0x000002c4d843bac0, 0x000002c4d843d000, 0x000002c4d843c560, 0x000002c4d843d550,
0x000002c4d843b570, 0x000002c4d843ea90, 0x000002c4dcc32190, 0x000002c4dcc2fc60,
0x000002c4d71bad00
}

Java Threads: ( => current thread )
  0x000002c4cc410880 JavaThread "Reference Handler" daemon [_thread_blocked, id=24184, stack(0x000000e637300000,0x000000e637400000)]
  0x000002c4cc4110c0 JavaThread "Finalizer" daemon [_thread_blocked, id=36632, stack(0x000000e637400000,0x000000e637500000)]
  0x000002c4cc411cf0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=38732, stack(0x000000e637500000,0x000000e637600000)]
  0x000002c4cc412720 JavaThread "Attach Listener" daemon [_thread_blocked, id=6120, stack(0x000000e637600000,0x000000e637700000)]
  0x000002c4cc41a020 JavaThread "Service Thread" daemon [_thread_blocked, id=6532, stack(0x000000e637700000,0x000000e637800000)]
  0x000002c4cc41bea0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=39188, stack(0x000000e637800000,0x000000e637900000)]
  0x000002c4cc421db0 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=39192, stack(0x000000e637900000,0x000000e637a00000)]
  0x000002c4cc426810 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=17080, stack(0x000000e637a00000,0x000000e637b00000)]
  0x000002c4c587ccd0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=38844, stack(0x000000e637b00000,0x000000e637c00000)]
  0x000002c4cca8bf90 JavaThread "Monitor Ctrl-Break" daemon [_thread_in_native, id=27884, stack(0x000000e637d00000,0x000000e637e00000)]
  0x000002c4cc8254a0 JavaThread "Notification Thread" daemon [_thread_blocked, id=30764, stack(0x000000e637e00000,0x000000e637f00000)]
  0x000002c4ccd61c90 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=39248, stack(0x000000e638100000,0x000000e638200000)]
  0x000002c4ccb688e0 JavaThread "RMI TCP Connection(1)-192.168.56.1" daemon [_thread_in_native, id=39268, stack(0x000000e638200000,0x000000e638300000)]
  0x000002c4ccffd1d0 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=16956, stack(0x000000e638300000,0x000000e638400000)]
  0x000002c4cd491870 JavaThread "JMX server connection timeout 26" daemon [_thread_blocked, id=5036, stack(0x000000e638400000,0x000000e638500000)]
  0x000002c4cd528830 JavaThread "Java2D Disposer" daemon [_thread_blocked, id=39284, stack(0x000000e638500000,0x000000e638600000)]
  0x000002c4cd609c40 JavaThread "AWT-Shutdown" [_thread_blocked, id=39280, stack(0x000000e638600000,0x000000e638700000)]
  0x000002c4cd60cc50 JavaThread "AWT-Windows" daemon [_thread_in_native, id=39276, stack(0x000000e638700000,0x000000e638800000)]
  0x000002c4cd60d1a0 JavaThread "AWT-EventQueue-0" [_thread_in_native, id=39308, stack(0x000000e638000000,0x000000e638100000)]
  0x000002c4cd60ec30 JavaThread "DestroyJavaVM" [_thread_blocked, id=3372, stack(0x000000e636c00000,0x000000e636d00000)]
  0x000002c4cde32f00 JavaThread "TimerQueue" daemon [_thread_blocked, id=39560, stack(0x000000e639770000,0x000000e639870000)]
  0x000002c4cde319c0 JavaThread "SwingWorker-pool-1-thread-1" daemon [_thread_blocked, id=21668, stack(0x000000e639a70000,0x000000e639b70000)]
  0x000002c4cde34ee0 JavaThread "SearchQueueProcessor" daemon [_thread_blocked, id=39696, stack(0x000000e639b70000,0x000000e639c70000)]
  0x000002c4cde32460 JavaThread "SwingWorker-pool-1-thread-2" daemon [_thread_blocked, id=30728, stack(0x000000e637c00000,0x000000e637d00000)]
  0x000002c4cde36970 JavaThread "SwingWorker-pool-1-thread-3" daemon [_thread_blocked, id=11604, stack(0x000000e63a070000,0x000000e63a170000)]
  0x000002c4d7a6f430 JavaThread "SwingWorker-pool-1-thread-4" daemon [_thread_blocked, id=7192, stack(0x000000e639e70000,0x000000e639f70000)]
  0x000002c4d7a70970 JavaThread "SwingWorker-pool-1-thread-5" daemon [_thread_blocked, id=6372, stack(0x000000e639570000,0x000000e639670000)]
  0x000002c4d7a6eee0 JavaThread "SwingWorker-pool-1-thread-6" daemon [_thread_blocked, id=20284, stack(0x000000e639f70000,0x000000e63a070000)]
  0x000002c4d7a6cf00 JavaThread "SwingWorker-pool-1-thread-7" daemon [_thread_blocked, id=37740, stack(0x000000e63a270000,0x000000e63a370000)]
  0x000002c4d7a70ec0 JavaThread "SwingWorker-pool-1-thread-8" daemon [_thread_blocked, id=34020, stack(0x000000e63a170000,0x000000e63a270000)]
  0x000002c4d7a71410 JavaThread "SwingWorker-pool-1-thread-9" daemon [_thread_blocked, id=11832, stack(0x000000e63a370000,0x000000e63a470000)]
  0x000002c4d7a6d9a0 JavaThread "SwingWorker-pool-1-thread-10" daemon [_thread_blocked, id=6656, stack(0x000000e63a470000,0x000000e63a570000)]
  0x000002c4d7a73940 JavaThread "LittleProxy-0-ClientToProxyAcceptor-0" daemon [_thread_in_native, id=38016, stack(0x000000e63a670000,0x000000e63a770000)]
  0x000002c4d7a73e90 JavaThread "Exec Default Executor" daemon [_thread_in_native, id=37356, stack(0x000000e639100000,0x000000e639200000)]
  0x000002c4cde2f9e0 JavaThread "Exec Stream Pumper" daemon [_thread_in_native, id=15444, stack(0x000000e639970000,0x000000e639a70000)]
  0x000002c4cde35430 JavaThread "Exec Stream Pumper" daemon [_thread_in_native, id=38156, stack(0x000000e63a570000,0x000000e63a670000)]
  0x000002c4d843daa0 JavaThread "ForkJoinPool.commonPool-worker-1" daemon [_thread_blocked, id=39092, stack(0x000000e63a970000,0x000000e63aa70000)]
  0x000002c4d843cab0 JavaThread "ForkJoinPool.commonPool-worker-2" daemon [_thread_blocked, id=652, stack(0x000000e63aa70000,0x000000e63ab70000)]
  0x000002c4d843dff0 JavaThread "OkHttp ConnectionPool" daemon [_thread_blocked, id=28212, stack(0x000000e63ab70000,0x000000e63ac70000)]
  0x000002c4d843e540 JavaThread "Okio Watchdog" daemon [_thread_blocked, id=29488, stack(0x000000e63ac70000,0x000000e63ad70000)]
  0x000002c4d843bac0 JavaThread "LittleProxy-0-ClientToProxyWorker-0" daemon [_thread_in_native, id=2804, stack(0x000000e63a870000,0x000000e63a970000)]
  0x000002c4d843d000 JavaThread "LittleProxy-0-ProxyToServerWorker-0" daemon [_thread_in_native, id=8664, stack(0x000000e63ad70000,0x000000e63ae70000)]
  0x000002c4d843c560 JavaThread "LittleProxy-0-ClientToProxyWorker-1" daemon [_thread_in_native, id=23980, stack(0x000000e63b070000,0x000000e63b170000)]
  0x000002c4d843d550 JavaThread "LittleProxy-0-ProxyToServerWorker-1" daemon [_thread_in_native, id=21764, stack(0x000000e63b170000,0x000000e63b270000)]
  0x000002c4d843b570 JavaThread "LittleProxy-0-ClientToProxyWorker-2" daemon [_thread_in_native, id=15836, stack(0x000000e63b270000,0x000000e63b370000)]
  0x000002c4d843ea90 JavaThread "LittleProxy-0-ProxyToServerWorker-2" daemon [_thread_in_native, id=20880, stack(0x000000e63b370000,0x000000e63b470000)]
  0x000002c4dcc32190 JavaThread "LittleProxy-0-ClientToProxyWorker-3" daemon [_thread_in_native, id=39836, stack(0x000000e63b470000,0x000000e63b570000)]
  0x000002c4dcc2fc60 JavaThread "LittleProxy-0-ProxyToServerWorker-3" daemon [_thread_in_native, id=35088, stack(0x000000e63ae70000,0x000000e63af70000)]
=>0x000002c4d71bad00 JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=40320, stack(0x000000e63af70000,0x000000e63b070000)]

Other Threads:
  0x000002c4c5890de0 VMThread "VM Thread" [stack: 0x000000e637200000,0x000000e637300000] [id=17520]
  0x000002c4a357b970 WatcherThread "VM Periodic Task Thread" [stack: 0x000000e637f00000,0x000000e638000000] [id=4204]
  0x000002c4a3626fe0 WorkerThread "GC Thread#0" [stack: 0x000000e636d00000,0x000000e636e00000] [id=35428]
  0x000002c4cd7c2410 WorkerThread "GC Thread#1" [stack: 0x000000e638800000,0x000000e638900000] [id=39292]
  0x000002c4cd7c34f0 WorkerThread "GC Thread#2" [stack: 0x000000e638900000,0x000000e638a00000] [id=38052]
  0x000002c4cd7c2c80 WorkerThread "GC Thread#3" [stack: 0x000000e638a00000,0x000000e638b00000] [id=36364]
  0x000002c4cd7c48a0 WorkerThread "GC Thread#4" [stack: 0x000000e638b00000,0x000000e638c00000] [id=39296]
  0x000002c4cd7c2f50 WorkerThread "GC Thread#5" [stack: 0x000000e638c00000,0x000000e638d00000] [id=6956]
  0x000002c4cd7c3d60 WorkerThread "GC Thread#6" [stack: 0x000000e638d00000,0x000000e638e00000] [id=37440]
  0x000002c4cd7c37c0 WorkerThread "GC Thread#7" [stack: 0x000000e638e00000,0x000000e638f00000] [id=39300]
  0x000002c4cd7c4030 WorkerThread "GC Thread#8" [stack: 0x000000e638f00000,0x000000e639000000] [id=35424]
  0x000002c4cd7c4b70 WorkerThread "GC Thread#9" [stack: 0x000000e639000000,0x000000e639100000] [id=37256]
  0x000002c4c56ac910 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000e636e00000,0x000000e636f00000] [id=14676]
  0x000002c4a362c960 WorkerThread "G1 Conc#0" [stack: 0x000000e636f00000,0x000000e637000000] [id=22452]
  0x000002c4cd7c18d0 WorkerThread "G1 Conc#1" [stack: 0x000000e639670000,0x000000e639770000] [id=39544]
  0x000002c4cd7c45d0 WorkerThread "G1 Conc#2" [stack: 0x000000e639870000,0x000000e639970000] [id=39500]
  0x000002c4c574f0f0 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000e637000000,0x000000e637100000] [id=33424]
  0x000002c4c574fb90 ConcurrentGCThread "G1 Service" [stack: 0x000000e637100000,0x000000e637200000] [id=6984]

Threads with active compile tasks:
C2 CompilerThread0    48519 11165   !   4       java.awt.EventQueue::getNextEvent (84 bytes)
C2 CompilerThread1    48519 11138   !   4       sun.awt.PostEventQueue::flush (168 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x000000060e000000, size: 7968 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000800000000-0x0000000800c60000-0x0000000800c60000), size 12976128, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 0.
Compressed class space mapped at: 0x0000000801000000-0x0000000841000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 12 total, 12 available
 Memory: 31867M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 500M
 Heap Max Capacity: 7968M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 294912K, used 137274K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 6 survivors (24576K)
 Metaspace       used 55512K, committed 56128K, reserved 1114112K
  class space    used 6207K, committed 6464K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x000000060e000000, 0x000000060e400000, 0x000000060e400000|100%| O|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|   1|0x000000060e400000, 0x000000060e800000, 0x000000060e800000|100%| O|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|   2|0x000000060e800000, 0x000000060ec00000, 0x000000060ec00000|100%| O|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|   3|0x000000060ec00000, 0x000000060f000000, 0x000000060f000000|100%| O|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|   4|0x000000060f000000, 0x000000060f400000, 0x000000060f400000|100%| O|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|   5|0x000000060f400000, 0x000000060f800000, 0x000000060f800000|100%| O|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Untracked 
|   6|0x000000060f800000, 0x000000060fc00000, 0x000000060fc00000|100%| O|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Untracked 
|   7|0x000000060fc00000, 0x0000000610000000, 0x0000000610000000|100%| O|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|   8|0x0000000610000000, 0x0000000610400000, 0x0000000610400000|100%| O|  |TAMS 0x0000000610000000| PB 0x0000000610000000| Untracked 
|   9|0x0000000610400000, 0x0000000610800000, 0x0000000610800000|100%|HS|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Complete 
|  10|0x0000000610800000, 0x0000000610c00000, 0x0000000610c00000|100%|HC|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Complete 
|  11|0x0000000610c00000, 0x0000000611000000, 0x0000000611000000|100%|HC|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Complete 
|  12|0x0000000611000000, 0x0000000611400000, 0x0000000611400000|100%|HC|  |TAMS 0x0000000611000000| PB 0x0000000611000000| Complete 
|  13|0x0000000611400000, 0x0000000611800000, 0x0000000611800000|100%| O|  |TAMS 0x0000000611400000| PB 0x0000000611400000| Untracked 
|  14|0x0000000611800000, 0x0000000611c00000, 0x0000000611c00000|100%| O|  |TAMS 0x0000000611800000| PB 0x0000000611800000| Untracked 
|  15|0x0000000611c00000, 0x0000000612000000, 0x0000000612000000|100%| O|  |TAMS 0x0000000611c00000| PB 0x0000000611c00000| Untracked 
|  16|0x0000000612000000, 0x0000000612400000, 0x0000000612400000|100%| O|  |TAMS 0x0000000612000000| PB 0x0000000612000000| Untracked 
|  17|0x0000000612400000, 0x0000000612800000, 0x0000000612800000|100%| O|  |TAMS 0x0000000612400000| PB 0x0000000612400000| Untracked 
|  18|0x0000000612800000, 0x0000000612c00000, 0x0000000612c00000|100%| O|  |TAMS 0x0000000612800000| PB 0x0000000612800000| Untracked 
|  19|0x0000000612c00000, 0x0000000613000000, 0x0000000613000000|100%| O|  |TAMS 0x0000000612c00000| PB 0x0000000612c00000| Untracked 
|  20|0x0000000613000000, 0x000000061323f600, 0x0000000613400000| 56%| O|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Untracked 
|  21|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000| PB 0x0000000613400000| Untracked 
|  22|0x0000000613800000, 0x0000000613c00000, 0x0000000613c00000|100%| O|  |TAMS 0x0000000613800000| PB 0x0000000613800000| Untracked 
|  23|0x0000000613c00000, 0x0000000614000000, 0x0000000614000000|100%| O|  |TAMS 0x0000000613c00000| PB 0x0000000613c00000| Untracked 
|  24|0x0000000614000000, 0x0000000614400000, 0x0000000614400000|100%| O|  |TAMS 0x0000000614000000| PB 0x0000000614000000| Untracked 
|  25|0x0000000614400000, 0x00000006147cf5a0, 0x0000000614800000| 95%| S|CS|TAMS 0x0000000614400000| PB 0x0000000614400000| Complete 
|  26|0x0000000614800000, 0x0000000614c00000, 0x0000000614c00000|100%| S|CS|TAMS 0x0000000614800000| PB 0x0000000614800000| Complete 
|  27|0x0000000614c00000, 0x0000000615000000, 0x0000000615000000|100%| S|CS|TAMS 0x0000000614c00000| PB 0x0000000614c00000| Complete 
|  28|0x0000000615000000, 0x0000000615400000, 0x0000000615400000|100%| S|CS|TAMS 0x0000000615000000| PB 0x0000000615000000| Complete 
|  29|0x0000000615400000, 0x0000000615800000, 0x0000000615800000|100%| S|CS|TAMS 0x0000000615400000| PB 0x0000000615400000| Complete 
|  30|0x0000000615800000, 0x0000000615c00000, 0x0000000615c00000|100%| S|CS|TAMS 0x0000000615800000| PB 0x0000000615800000| Complete 
|  31|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000| PB 0x0000000615c00000| Untracked 
|  32|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  33|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Untracked 
|  34|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Untracked 
|  35|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Untracked 
|  36|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Untracked 
|  37|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Untracked 
|  38|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Untracked 
|  39|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Untracked 
|  40|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Untracked 
|  41|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  42|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  43|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
|  44|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
|  45|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
|  46|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
|  47|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Untracked 
|  48|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Untracked 
|  49|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Untracked 
|  50|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Untracked 
|  51|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Untracked 
|  52|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Untracked 
|  53|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Untracked 
|  54|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Untracked 
|  55|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Untracked 
|  56|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Untracked 
|  57|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
|  58|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
|  59|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
|  60|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
|  61|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Untracked 
|  62|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Untracked 
|  63|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Untracked 
|  64|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Untracked 
|  65|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Untracked 
|  66|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
|  67|0x000000061ec00000, 0x000000061ece1a78, 0x000000061f000000| 22%| E|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Complete 
|  68|0x000000061f000000, 0x000000061f400000, 0x000000061f400000|100%| E|CS|TAMS 0x000000061f000000| PB 0x000000061f000000| Complete 
|  69|0x000000061f400000, 0x000000061f800000, 0x000000061f800000|100%| E|CS|TAMS 0x000000061f400000| PB 0x000000061f400000| Complete 
|  70|0x000000061f800000, 0x000000061fc00000, 0x000000061fc00000|100%| E|CS|TAMS 0x000000061f800000| PB 0x000000061f800000| Complete 
| 124|0x000000062d000000, 0x000000062d400000, 0x000000062d400000|100%| E|CS|TAMS 0x000000062d000000| PB 0x000000062d000000| Complete 

Card table byte_map: [0x000002c4bbb00000,0x000002c4bca90000] _byte_map_base: 0x000002c4b8a90000

Marking Bits: (CMBitMap*) 0x000002c4a3627610
 Bits: [0x000002c4bda20000, 0x000002c4c56a0000)

Polling page: 0x000002c4a1450000

Metaspace:

Usage:
  Non-class:     48.15 MB used.
      Class:      6.06 MB used.
       Both:     54.21 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      48.50 MB ( 76%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       6.31 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      54.81 MB (  5%) committed. 

Chunk freelists:
   Non-Class:  2.61 MB
       Class:  1.70 MB
        Both:  4.31 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 58.88 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 736.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 877.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 1952.
num_chunk_merges: 6.
num_chunk_splits: 1304.
num_chunks_enlarged: 869.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=8768Kb max_used=8768Kb free=111231Kb
 bounds [0x000002c4b30f0000, 0x000002c4b3990000, 0x000002c4ba620000]
CodeHeap 'profiled nmethods': size=120000Kb used=23222Kb max_used=23222Kb free=96777Kb
 bounds [0x000002c4ab620000, 0x000002c4accd0000, 0x000002c4b2b50000]
CodeHeap 'non-nmethods': size=5760Kb used=2322Kb max_used=2376Kb free=3437Kb
 bounds [0x000002c4b2b50000, 0x000002c4b2dc0000, 0x000002c4b30f0000]
 total_blobs=11774 nmethods=10780 adapters=903
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 41.269 Thread 0x000002c4cc421db0 nmethod 11142 0x000002c4b397a310 code [0x000002c4b397a540, 0x000002c4b397b708]
Event: 41.283 Thread 0x000002c4cc421db0 11146   !   4       org.openqa.selenium.json.Input::init (71 bytes)
Event: 41.345 Thread 0x000002c4cc421db0 nmethod 11146 0x000002c4b397cf90 code [0x000002c4b397d1c0, 0x000002c4b397e298]
Event: 41.345 Thread 0x000002c4cc421db0 11163       4       java.util.Arrays::copyOf (10 bytes)
Event: 41.360 Thread 0x000002c4cc421db0 nmethod 11163 0x000002c4b397fb10 code [0x000002c4b397fc80, 0x000002c4b397ff48]
Event: 41.360 Thread 0x000002c4cc421db0 11136       4       java.awt.event.InvocationEvent::<init> (13 bytes)
Event: 41.360 Thread 0x000002c4cc421db0 nmethod 11136 0x000002c4b3980090 code [0x000002c4b3980200, 0x000002c4b39802c0]
Event: 41.433 Thread 0x000002c4cc421db0 11165   !   4       java.awt.EventQueue::getNextEvent (84 bytes)
Event: 41.472 Thread 0x000002c4cc426810 11167       3       sun.java2d.SunGraphics2D::getFontMetrics (29 bytes)
Event: 41.472 Thread 0x000002c4cc426810 nmethod 11167 0x000002c4accc4b90 code [0x000002c4accc4d40, 0x000002c4accc4f90]
Event: 41.842 Thread 0x000002c4cc426810 11168       3       java.awt.Component::getNativeContainer (29 bytes)
Event: 41.842 Thread 0x000002c4cc426810 nmethod 11168 0x000002c4accc5090 code [0x000002c4accc5240, 0x000002c4accc56c0]
Event: 41.842 Thread 0x000002c4cc426810 11169       3       com.formdev.flatlaf.util.HSLColor::fromRGB (231 bytes)
Event: 41.842 Thread 0x000002c4cc426810 nmethod 11169 0x000002c4accc5810 code [0x000002c4accc5aa0, 0x000002c4accc61f0]
Event: 41.842 Thread 0x000002c4cc426810 11172       3       com.formdev.flatlaf.util.HSLColor::HueToRGB (75 bytes)
Event: 41.842 Thread 0x000002c4cc426810 nmethod 11172 0x000002c4accc6410 code [0x000002c4accc6640, 0x000002c4accc6a10]
Event: 41.842 Thread 0x000002c4cc426810 11170       3       java.awt.Color::getRGBColorComponents (85 bytes)
Event: 41.843 Thread 0x000002c4cc426810 nmethod 11170 0x000002c4accc6a90 code [0x000002c4accc6ca0, 0x000002c4accc74f0]
Event: 41.843 Thread 0x000002c4cc426810 11171       3       com.formdev.flatlaf.util.HSLColor::toRGB (223 bytes)
Event: 41.843 Thread 0x000002c4cc426810 nmethod 11171 0x000002c4accc7790 code [0x000002c4accc7ac0, 0x000002c4accc83d8]

GC Heap History (20 events):
Event: 22.802 GC heap before
{Heap before GC invocations=27 (full 0):
 garbage-first heap   total 294912K, used 247895K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 42 young (172032K), 4 survivors (16384K)
 Metaspace       used 39241K, committed 39808K, reserved 1114112K
  class space    used 4093K, committed 4352K, reserved 1048576K
}
Event: 22.803 GC heap after
{Heap after GC invocations=28 (full 0):
 garbage-first heap   total 294912K, used 88642K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 39241K, committed 39808K, reserved 1114112K
  class space    used 4093K, committed 4352K, reserved 1048576K
}
Event: 22.928 GC heap before
{Heap before GC invocations=28 (full 0):
 garbage-first heap   total 294912K, used 256578K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 43 young (176128K), 2 survivors (8192K)
 Metaspace       used 39255K, committed 39808K, reserved 1114112K
  class space    used 4093K, committed 4352K, reserved 1048576K
}
Event: 22.930 GC heap after
{Heap after GC invocations=29 (full 0):
 garbage-first heap   total 294912K, used 92296K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 39255K, committed 39808K, reserved 1114112K
  class space    used 4093K, committed 4352K, reserved 1048576K
}
Event: 23.024 GC heap before
{Heap before GC invocations=29 (full 0):
 garbage-first heap   total 294912K, used 256136K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 43 young (176128K), 3 survivors (12288K)
 Metaspace       used 39257K, committed 39808K, reserved 1114112K
  class space    used 4093K, committed 4352K, reserved 1048576K
}
Event: 23.026 GC heap after
{Heap after GC invocations=30 (full 0):
 garbage-first heap   total 294912K, used 95577K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 4 survivors (16384K)
 Metaspace       used 39257K, committed 39808K, reserved 1114112K
  class space    used 4093K, committed 4352K, reserved 1048576K
}
Event: 23.574 GC heap before
{Heap before GC invocations=30 (full 0):
 garbage-first heap   total 294912K, used 247129K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 42 young (172032K), 4 survivors (16384K)
 Metaspace       used 39337K, committed 39872K, reserved 1114112K
  class space    used 4093K, committed 4352K, reserved 1048576K
}
Event: 23.575 GC heap after
{Heap after GC invocations=31 (full 0):
 garbage-first heap   total 294912K, used 92327K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 39337K, committed 39872K, reserved 1114112K
  class space    used 4093K, committed 4352K, reserved 1048576K
}
Event: 24.027 GC heap before
{Heap before GC invocations=31 (full 0):
 garbage-first heap   total 294912K, used 256167K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 43 young (176128K), 3 survivors (12288K)
 Metaspace       used 39354K, committed 39872K, reserved 1114112K
  class space    used 4093K, committed 4352K, reserved 1048576K
}
Event: 24.028 GC heap after
{Heap after GC invocations=32 (full 0):
 garbage-first heap   total 294912K, used 90795K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 39354K, committed 39872K, reserved 1114112K
  class space    used 4093K, committed 4352K, reserved 1048576K
}
Event: 24.274 GC heap before
{Heap before GC invocations=32 (full 0):
 garbage-first heap   total 294912K, used 258731K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 43 young (176128K), 2 survivors (8192K)
 Metaspace       used 39354K, committed 39872K, reserved 1114112K
  class space    used 4093K, committed 4352K, reserved 1048576K
}
Event: 24.276 GC heap after
{Heap after GC invocations=33 (full 0):
 garbage-first heap   total 294912K, used 96628K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 4 survivors (16384K)
 Metaspace       used 39354K, committed 39872K, reserved 1114112K
  class space    used 4093K, committed 4352K, reserved 1048576K
}
Event: 28.933 GC heap before
{Heap before GC invocations=33 (full 0):
 garbage-first heap   total 294912K, used 248180K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 42 young (172032K), 4 survivors (16384K)
 Metaspace       used 39971K, committed 40512K, reserved 1114112K
  class space    used 4198K, committed 4416K, reserved 1048576K
}
Event: 28.936 GC heap after
{Heap after GC invocations=34 (full 0):
 garbage-first heap   total 294912K, used 97665K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 4 survivors (16384K)
 Metaspace       used 39971K, committed 40512K, reserved 1114112K
  class space    used 4198K, committed 4416K, reserved 1048576K
}
Event: 30.608 GC heap before
{Heap before GC invocations=34 (full 0):
 garbage-first heap   total 294912K, used 253313K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 42 young (172032K), 4 survivors (16384K)
 Metaspace       used 42125K, committed 42688K, reserved 1114112K
  class space    used 4492K, committed 4736K, reserved 1048576K
}
Event: 30.612 GC heap after
{Heap after GC invocations=35 (full 0):
 garbage-first heap   total 294912K, used 101173K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 42125K, committed 42688K, reserved 1114112K
  class space    used 4492K, committed 4736K, reserved 1048576K
}
Event: 30.846 GC heap before
{Heap before GC invocations=35 (full 0):
 garbage-first heap   total 294912K, used 260917K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 41 young (167936K), 2 survivors (8192K)
 Metaspace       used 44461K, committed 44928K, reserved 1114112K
  class space    used 4830K, committed 5056K, reserved 1048576K
}
Event: 30.849 GC heap after
{Heap after GC invocations=36 (full 0):
 garbage-first heap   total 294912K, used 103739K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 44461K, committed 44928K, reserved 1114112K
  class space    used 4830K, committed 5056K, reserved 1048576K
}
Event: 34.981 GC heap before
{Heap before GC invocations=36 (full 0):
 garbage-first heap   total 294912K, used 259387K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 41 young (167936K), 2 survivors (8192K)
 Metaspace       used 55219K, committed 55808K, reserved 1114112K
  class space    used 6177K, committed 6464K, reserved 1048576K
}
Event: 34.985 GC heap after
{Heap after GC invocations=37 (full 0):
 garbage-first heap   total 294912K, used 120890K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 6 survivors (24576K)
 Metaspace       used 55219K, committed 55808K, reserved 1114112K
  class space    used 6177K, committed 6464K, reserved 1048576K
}

Dll operation events (20 events):
Event: 0.017 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\java.dll
Event: 0.026 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\jsvml.dll
Event: 0.125 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\zip.dll
Event: 0.132 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\instrument.dll
Event: 0.138 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\net.dll
Event: 0.141 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\nio.dll
Event: 0.144 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\zip.dll
Event: 0.213 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\jimage.dll
Event: 0.237 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\extnet.dll
Event: 0.250 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\awt.dll
Event: 0.267 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\management.dll
Event: 0.291 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\management_ext.dll
Event: 0.749 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\sqlite-********-7010c4f7-c3c2-4262-aa44-e9844154f057-sqlitejdbc.dll
Event: 1.067 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\freetype.dll
Event: 1.069 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\fontmanager.dll
Event: 1.517 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\flatlaf.temp\flatlaf-windows-x86_64-122566269054000.dll
Event: 5.224 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\javajpeg.dll
Event: 6.493 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\webp-imageio-unknown-08e192c4-3e77-4a6a-bc09-be5c18050e43-webp-imageio.dll
Event: 7.188 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\sunmscapi.dll
Event: 30.424 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\verify.dll

Deoptimization events (20 events):
Event: 41.040 Thread 0x000002c4cde34ee0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002c4b38dbfdc relative=0x000000000000065c
Event: 41.040 Thread 0x000002c4cde34ee0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002c4b38dbfdc method=org.openqa.selenium.json.Input.init()V @ 18 c2
Event: 41.040 Thread 0x000002c4cde34ee0 DEOPT PACKING pc=0x000002c4b38dbfdc sp=0x000000e639c6e320
Event: 41.040 Thread 0x000002c4cde34ee0 DEOPT UNPACKING pc=0x000002c4b2b6d2a2 sp=0x000000e639c6e308 mode 2
Event: 41.040 Thread 0x000002c4cde34ee0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002c4b38dbfdc relative=0x000000000000065c
Event: 41.040 Thread 0x000002c4cde34ee0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002c4b38dbfdc method=org.openqa.selenium.json.Input.init()V @ 18 c2
Event: 41.040 Thread 0x000002c4cde34ee0 DEOPT PACKING pc=0x000002c4b38dbfdc sp=0x000000e639c6de70
Event: 41.040 Thread 0x000002c4cde34ee0 DEOPT UNPACKING pc=0x000002c4b2b6d2a2 sp=0x000000e639c6de58 mode 2
Event: 41.040 Thread 0x000002c4cde34ee0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002c4b38e0760 relative=0x0000000000000660
Event: 41.040 Thread 0x000002c4cde34ee0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002c4b38e0760 method=org.openqa.selenium.json.Input.init()V @ 18 c2
Event: 41.040 Thread 0x000002c4cde34ee0 DEOPT PACKING pc=0x000002c4b38e0760 sp=0x000000e639c6d8f0
Event: 41.040 Thread 0x000002c4cde34ee0 DEOPT UNPACKING pc=0x000002c4b2b6d2a2 sp=0x000000e639c6d880 mode 2
Event: 41.040 Thread 0x000002c4cde34ee0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002c4b38e0760 relative=0x0000000000000660
Event: 41.040 Thread 0x000002c4cde34ee0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002c4b38e0760 method=org.openqa.selenium.json.Input.init()V @ 18 c2
Event: 41.040 Thread 0x000002c4cde34ee0 DEOPT PACKING pc=0x000002c4b38e0760 sp=0x000000e639c6d8f0
Event: 41.040 Thread 0x000002c4cde34ee0 DEOPT UNPACKING pc=0x000002c4b2b6d2a2 sp=0x000000e639c6d880 mode 2
Event: 41.040 Thread 0x000002c4cde34ee0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002c4b38dbfdc relative=0x000000000000065c
Event: 41.041 Thread 0x000002c4cde34ee0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002c4b38dbfdc method=org.openqa.selenium.json.Input.init()V @ 18 c2
Event: 41.041 Thread 0x000002c4cde34ee0 DEOPT PACKING pc=0x000002c4b38dbfdc sp=0x000000e639c6d890
Event: 41.041 Thread 0x000002c4cde34ee0 DEOPT UNPACKING pc=0x000002c4b2b6d2a2 sp=0x000000e639c6d880 mode 2

Classes loaded (20 events):
Event: 34.299 Loading class sun/security/ssl/ServerHello$T12ServerHelloProducer$KeyExchangeProperties
Event: 34.299 Loading class sun/security/ssl/ServerHello$T12ServerHelloProducer$KeyExchangeProperties done
Event: 34.299 Loading class sun/security/ssl/StatusResponseManager
Event: 34.299 Loading class sun/security/ssl/StatusResponseManager done
Event: 34.307 Loading class sun/security/ssl/SSLCipher$T12GcmReadCipherGenerator$GcmReadCipher
Event: 34.307 Loading class sun/security/ssl/SSLCipher$T12GcmReadCipherGenerator$GcmReadCipher done
Event: 34.308 Loading class sun/security/ssl/SessionTicketExtension$KeyState
Event: 34.308 Loading class sun/security/ssl/SessionTicketExtension$KeyState done
Event: 34.308 Loading class sun/security/ssl/SessionTicketExtension$StatelessKey
Event: 34.308 Loading class sun/security/ssl/SessionTicketExtension$StatelessKey done
Event: 34.308 Loading class com/sun/crypto/provider/AESKeyGenerator
Event: 34.308 Loading class com/sun/crypto/provider/AESKeyGenerator done
Event: 34.309 Loading class sun/security/ssl/SSLCipher$T12GcmWriteCipherGenerator$GcmWriteCipher
Event: 34.309 Loading class sun/security/ssl/SSLCipher$T12GcmWriteCipherGenerator$GcmWriteCipher done
Event: 34.311 Loading class java/util/regex/Pattern$1
Event: 34.312 Loading class java/util/regex/Pattern$1 done
Event: 34.620 Loading class java/util/concurrent/atomic/Striped64$Cell
Event: 34.621 Loading class java/util/concurrent/atomic/Striped64$Cell done
Event: 41.052 Loading class java/math/MathContext
Event: 41.053 Loading class java/math/MathContext done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 32.697 Thread 0x000002c4cde34ee0 Exception <a 'java/net/ConnectException'{0x0000000619633640}: Connection refused: no further information> (0x0000000619633640) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 538]
Event: 32.698 Thread 0x000002c4cde34ee0 Exception <a 'java/net/ConnectException'{0x0000000619633ea8}: Connection refused: no further information> (0x0000000619633ea8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 538]
Event: 32.699 Thread 0x000002c4cde34ee0 Exception <a 'java/net/ConnectException'{0x0000000619634710}: Connection refused: no further information> (0x0000000619634710) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 538]
Event: 32.713 Thread 0x000002c4cde34ee0 Exception <a 'java/net/ConnectException'{0x0000000619634f78}: Connection refused: no further information> (0x0000000619634f78) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 538]
Event: 32.714 Thread 0x000002c4cde34ee0 Exception <a 'java/net/ConnectException'{0x000000061978c778}: Connection refused: no further information> (0x000000061978c778) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 538]
Event: 32.714 Thread 0x000002c4cde34ee0 Exception <a 'java/net/ConnectException'{0x000000061978cfe0}: Connection refused: no further information> (0x000000061978cfe0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 538]
Event: 32.728 Thread 0x000002c4cde34ee0 Exception <a 'java/net/ConnectException'{0x000000061978d848}: Connection refused: no further information> (0x000000061978d848) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 538]
Event: 32.729 Thread 0x000002c4cde34ee0 Exception <a 'java/net/ConnectException'{0x000000061978e0b0}: Connection refused: no further information> (0x000000061978e0b0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 538]
Event: 32.730 Thread 0x000002c4cde34ee0 Exception <a 'java/net/ConnectException'{0x000000061978e918}: Connection refused: no further information> (0x000000061978e918) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 538]
Event: 32.742 Thread 0x000002c4cde34ee0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006197ed9c0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000006197ed9c0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 32.742 Thread 0x000002c4cde34ee0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000006197f3038}: Found class java.lang.Object, but interface was expected> (0x00000006197f3038) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 32.743 Thread 0x000002c4cde34ee0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006197fb6f0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006197fb6f0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 32.750 Thread 0x000002c4cde34ee0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061904cea8}: 'double java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x000000061904cea8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 32.751 Thread 0x000002c4cde34ee0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006190530d8}: 'float java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000006190530d8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 32.852 Thread 0x000002c4cde34ee0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000618eae230}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000618eae230) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 34.099 Thread 0x000002c4d843bac0 Implicit null exception at 0x000002c4b38fe0ab to 0x000002c4b38fe2e8
Event: 34.222 Thread 0x000002c4d843bac0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000617a22948}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000617a22948) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 34.222 Thread 0x000002c4d843bac0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000617a274b0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000617a274b0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 34.223 Thread 0x000002c4d843bac0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000617a2b778}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x0000000617a2b778) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 41.069 Thread 0x000002c4cde34ee0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061ec2f3f8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000061ec2f3f8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

VM Operations (20 events):
Event: 32.797 Executing VM operation: HandshakeAllThreads
Event: 32.797 Executing VM operation: HandshakeAllThreads done
Event: 32.803 Executing VM operation: HandshakeAllThreads
Event: 32.803 Executing VM operation: HandshakeAllThreads done
Event: 33.811 Executing VM operation: Cleanup
Event: 33.811 Executing VM operation: Cleanup done
Event: 34.024 Executing VM operation: HandshakeAllThreads
Event: 34.024 Executing VM operation: HandshakeAllThreads done
Event: 34.265 Executing VM operation: HandshakeAllThreads
Event: 34.265 Executing VM operation: HandshakeAllThreads done
Event: 34.317 Executing VM operation: HandshakeAllThreads
Event: 34.318 Executing VM operation: HandshakeAllThreads done
Event: 34.327 Executing VM operation: HandshakeAllThreads
Event: 34.327 Executing VM operation: HandshakeAllThreads done
Event: 34.981 Executing VM operation: G1CollectForAllocation
Event: 34.985 Executing VM operation: G1CollectForAllocation done
Event: 35.997 Executing VM operation: Cleanup
Event: 35.998 Executing VM operation: Cleanup done
Event: 42.051 Executing VM operation: Cleanup
Event: 42.051 Executing VM operation: Cleanup done

Events (20 events):
Event: 32.879 Thread 0x000002c4d843dff0 Thread added: 0x000002c4d843dff0
Event: 32.883 Thread 0x000002c4d843e540 Thread added: 0x000002c4d843e540
Event: 33.186 Thread 0x000002c4d71aab20 Thread exited: 0x000002c4d71aab20
Event: 33.881 Thread 0x000002c4d843bac0 Thread added: 0x000002c4d843bac0
Event: 33.968 Thread 0x000002c4d843d000 Thread added: 0x000002c4d843d000
Event: 34.019 Thread 0x000002c4dc2d4c10 Thread added: 0x000002c4dc2d4c10
Event: 34.019 Thread 0x000002c4de9d5030 Thread added: 0x000002c4de9d5030
Event: 34.317 Thread 0x000002c4d843c560 Thread added: 0x000002c4d843c560
Event: 34.330 Thread 0x000002c4d843d550 Thread added: 0x000002c4d843d550
Event: 34.419 Thread 0x000002c4d843b570 Thread added: 0x000002c4d843b570
Event: 34.428 Thread 0x000002c4d843ea90 Thread added: 0x000002c4d843ea90
Event: 34.435 Thread 0x000002c4de9d5030 Thread exited: 0x000002c4de9d5030
Event: 34.465 Thread 0x000002c4de9d5030 Thread added: 0x000002c4de9d5030
Event: 34.980 Thread 0x000002c4dcc32190 Thread added: 0x000002c4dcc32190
Event: 34.981 Thread 0x000002c4de9d5030 Thread exited: 0x000002c4de9d5030
Event: 34.981 Thread 0x000002c4dc2d4c10 Thread exited: 0x000002c4dc2d4c10
Event: 35.009 Thread 0x000002c4dcc2fc60 Thread added: 0x000002c4dcc2fc60
Event: 35.108 Thread 0x000002c4d71bce60 Thread added: 0x000002c4d71bce60
Event: 35.544 Thread 0x000002c4d71bce60 Thread exited: 0x000002c4d71bce60
Event: 41.068 Thread 0x000002c4d71bad00 Thread added: 0x000002c4d71bad00


Dynamic libraries:
0x00007ff7db080000 - 0x00007ff7db08e000 	C:\Users\<USER>\.jdks\openjdk-20\bin\java.exe
0x00007ffd1d480000 - 0x00007ffd1d6e8000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffd1c8e0000 - 0x00007ffd1c9a9000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffd1acb0000 - 0x00007ffd1b09d000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffd1a7f0000 - 0x00007ffd1a93b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffd150f0000 - 0x00007ffd1510b000 	C:\Users\<USER>\.jdks\openjdk-20\bin\VCRUNTIME140.dll
0x00007ffd03620000 - 0x00007ffd03637000 	C:\Users\<USER>\.jdks\openjdk-20\bin\jli.dll
0x00007ffd1c9b0000 - 0x00007ffd1cb7c000 	C:\WINDOWS\System32\USER32.dll
0x00007ffd1aab0000 - 0x00007ffd1aad7000 	C:\WINDOWS\System32\win32u.dll
0x00007ffd04ca0000 - 0x00007ffd04f3a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3\COMCTL32.dll
0x00007ffd1d240000 - 0x00007ffd1d26b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffd1d270000 - 0x00007ffd1d319000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffd1ab70000 - 0x00007ffd1aca7000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffd1a940000 - 0x00007ffd1a9e3000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffd1c340000 - 0x00007ffd1c36f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffd03610000 - 0x00007ffd0361c000 	C:\Users\<USER>\.jdks\openjdk-20\bin\vcruntime140_1.dll
0x00007ffcea420000 - 0x00007ffcea4ae000 	C:\Users\<USER>\.jdks\openjdk-20\bin\msvcp140.dll
0x00007ffbd7140000 - 0x00007ffbd7dd7000 	C:\Users\<USER>\.jdks\openjdk-20\bin\server\jvm.dll
0x00007ffd1c400000 - 0x00007ffd1c4b4000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffd1cff0000 - 0x00007ffd1d096000 	C:\WINDOWS\System32\sechost.dll
0x00007ffd1bd20000 - 0x00007ffd1be38000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffd0b220000 - 0x00007ffd0b255000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffd13320000 - 0x00007ffd1332b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffcf62a0000 - 0x00007ffcf62aa000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffd1c380000 - 0x00007ffd1c3f4000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffd19350000 - 0x00007ffd1936b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffcfd3e0000 - 0x00007ffcfd3ea000 	C:\Users\<USER>\.jdks\openjdk-20\bin\jimage.dll
0x00007ffd09690000 - 0x00007ffd098d1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffd1b220000 - 0x00007ffd1b5a6000 	C:\WINDOWS\System32\combase.dll
0x00007ffd1d0a0000 - 0x00007ffd1d180000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffcfeb40000 - 0x00007ffcfeb83000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffd1a750000 - 0x00007ffd1a7e9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffd02a40000 - 0x00007ffd02a4e000 	C:\Users\<USER>\.jdks\openjdk-20\bin\instrument.dll
0x00007ffcfc660000 - 0x00007ffcfc686000 	C:\Users\<USER>\.jdks\openjdk-20\bin\java.dll
0x00007ffcb3880000 - 0x00007ffcb3957000 	C:\Users\<USER>\.jdks\openjdk-20\bin\jsvml.dll
0x00007ffd1b5d0000 - 0x00007ffd1bd1a000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffd1b0a0000 - 0x00007ffd1b214000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffd181f0000 - 0x00007ffd18a4b000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffd1ce60000 - 0x00007ffd1cf55000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffd1be40000 - 0x00007ffd1beaa000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffd1a4e0000 - 0x00007ffd1a50f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffcf5460000 - 0x00007ffcf5478000 	C:\Users\<USER>\.jdks\openjdk-20\bin\zip.dll
0x00007ffcf54f0000 - 0x00007ffcf5503000 	C:\Users\<USER>\.jdks\openjdk-20\bin\net.dll
0x00007ffd13090000 - 0x00007ffd131ae000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffd19900000 - 0x00007ffd1996a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffcf5480000 - 0x00007ffcf5496000 	C:\Users\<USER>\.jdks\openjdk-20\bin\nio.dll
0x00007ffcf54e0000 - 0x00007ffcf54e7000 	C:\Users\<USER>\.jdks\openjdk-20\bin\extnet.dll
0x00007ffcac730000 - 0x00007ffcac8c0000 	C:\Users\<USER>\.jdks\openjdk-20\bin\awt.dll
0x00007ffd175e0000 - 0x00007ffd1767e000 	C:\WINDOWS\SYSTEM32\apphelp.dll
0x00007ffd18de0000 - 0x00007ffd18f06000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffd18d50000 - 0x00007ffd18d83000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffd1d1b0000 - 0x00007ffd1d1ba000 	C:\WINDOWS\System32\NSI.dll
0x00007ffd0a440000 - 0x00007ffd0a44b000 	C:\Windows\System32\rasadhlp.dll
0x00007ffd12ce0000 - 0x00007ffd12d66000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffd02a30000 - 0x00007ffd02a3a000 	C:\Users\<USER>\.jdks\openjdk-20\bin\management.dll
0x00007ffcf4e70000 - 0x00007ffcf4e7b000 	C:\Users\<USER>\.jdks\openjdk-20\bin\management_ext.dll
0x00007ffd1b5b0000 - 0x00007ffd1b5b8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffd19bb0000 - 0x00007ffd19bcb000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffd192b0000 - 0x00007ffd192eb000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffd199a0000 - 0x00007ffd199cb000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffd1a4b0000 - 0x00007ffd1a4d6000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffd19bd0000 - 0x00007ffd19bdc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffd132f0000 - 0x00007ffd1330f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffd12fd0000 - 0x00007ffd12ff5000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffcf4e40000 - 0x00007ffcf4e58000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffce5430000 - 0x00007ffce5442000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffce0f50000 - 0x00007ffce0f80000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007ffd13e30000 - 0x00007ffd13e50000 	C:\WINDOWS\system32\wshbth.dll
0x00007ffcf4690000 - 0x00007ffcf477c000 	C:\Users\<USER>\AppData\Local\Temp\sqlite-********-7010c4f7-c3c2-4262-aa44-e9844154f057-sqlitejdbc.dll
0x00007ffd029d0000 - 0x00007ffd02a1e000 	C:\WINDOWS\SYSTEM32\pdh.dll
0x00007ffd17700000 - 0x00007ffd177af000 	C:\WINDOWS\system32\uxtheme.dll
0x00007ffd1c570000 - 0x00007ffd1c6d0000 	C:\WINDOWS\System32\MSCTF.dll
0x00007ffd1c730000 - 0x00007ffd1c8ce000 	C:\WINDOWS\System32\ole32.dll
0x00007ffd17b40000 - 0x00007ffd17b76000 	C:\WINDOWS\system32\DWMAPI.DLL
0x00007ffcddc00000 - 0x00007ffcddc86000 	C:\Users\<USER>\.jdks\openjdk-20\bin\freetype.dll
0x00007ffcb2440000 - 0x00007ffcb2506000 	C:\Users\<USER>\.jdks\openjdk-20\bin\fontmanager.dll
0x00007ffc76560000 - 0x00007ffc76678000 	C:\WINDOWS\system32\opengl32.dll
0x00007ffc76510000 - 0x00007ffc7653d000 	C:\WINDOWS\SYSTEM32\GLU32.dll
0x00007ffd17900000 - 0x00007ffd1794d000 	C:\WINDOWS\SYSTEM32\dxcore.dll
0x00007ffce9360000 - 0x00007ffce951a000 	C:\WINDOWS\system32\d3d9.dll
0x00007ffd13220000 - 0x00007ffd132e4000 	C:\WINDOWS\System32\DriverStore\FileRepository\nv_dispi.inf_amd64_ea7f458f0e49497d\nvldumdx.dll
0x00007ffd19c20000 - 0x00007ffd19c33000 	C:\WINDOWS\SYSTEM32\msasn1.dll
0x00007ffd13000000 - 0x00007ffd1303b000 	C:\WINDOWS\SYSTEM32\cryptnet.dll
0x00007ffd1a5d0000 - 0x00007ffd1a747000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffd19c90000 - 0x00007ffd19cfb000 	C:\WINDOWS\SYSTEM32\wldp.dll
0x00007ffd12e50000 - 0x00007ffd12fcc000 	C:\WINDOWS\SYSTEM32\drvstore.dll
0x00007ffd1a180000 - 0x00007ffd1a1ad000 	C:\WINDOWS\SYSTEM32\devobj.dll
0x00007ffd1a1b0000 - 0x00007ffd1a207000 	C:\WINDOWS\SYSTEM32\cfgmgr32.dll
0x00007ffd1aae0000 - 0x00007ffd1ab64000 	C:\WINDOWS\System32\wintrust.dll
0x00007ffd1cfc0000 - 0x00007ffd1cfe0000 	C:\WINDOWS\System32\imagehlp.dll
0x00007ffd0fbd0000 - 0x00007ffd128e0000 	C:\WINDOWS\System32\DriverStore\FileRepository\nv_dispi.inf_amd64_ea7f458f0e49497d\nvgpucomp64.dll
0x00007ffc97e10000 - 0x00007ffc99b9c000 	C:\WINDOWS\System32\DriverStore\FileRepository\nv_dispi.inf_amd64_ea7f458f0e49497d\nvd3dumx.dll
0x00007ffd17970000 - 0x00007ffd179d4000 	C:\WINDOWS\SYSTEM32\directxdatabasehelper.dll
0x00007ffceacb0000 - 0x00007ffceafc4000 	C:\WINDOWS\system32\nvspcap64.dll
0x00007ffd194a0000 - 0x00007ffd194d6000 	C:\WINDOWS\SYSTEM32\ntmarta.dll
0x00007ffd19970000 - 0x00007ffd19997000 	C:\WINDOWS\SYSTEM32\gpapi.dll
0x00007ffcea8a0000 - 0x00007ffcea9f5000 	C:\WINDOWS\System32\DriverStore\FileRepository\nv_dispi.inf_amd64_ea7f458f0e49497d\nvppex.dll
0x00007ffcf4660000 - 0x00007ffcf4666000 	C:\Users\<USER>\AppData\Local\Temp\flatlaf.temp\flatlaf-windows-x86_64-122566269054000.dll
0x00007ffd1c4c0000 - 0x00007ffd1c568000 	C:\WINDOWS\System32\clbcatq.dll
0x00007ffd15fe0000 - 0x00007ffd1621b000 	C:\WINDOWS\SYSTEM32\WindowsCodecs.dll
0x00007ffcebd70000 - 0x00007ffcebdca000 	C:\WINDOWS\system32\dataexchange.dll
0x00007ffd0f6e0000 - 0x00007ffd0f918000 	C:\WINDOWS\system32\twinapi.appcore.dll
0x00007ffcf4650000 - 0x00007ffcf4657000 	C:\Users\<USER>\.jdks\openjdk-20\bin\jawt.dll
0x00007ffd01940000 - 0x00007ffd01a90000 	C:\WINDOWS\SYSTEM32\textinputframework.dll
0x00007ffd04b60000 - 0x00007ffd04bdd000 	C:\WINDOWS\system32\Oleacc.dll
0x00007ffceb0f0000 - 0x00007ffceb14e000 	C:\WINDOWS\system32\ApplicationTargetedFeatureDatabase.dll
0x00007ffd17170000 - 0x00007ffd17295000 	C:\WINDOWS\SYSTEM32\CoreMessaging.dll
0x00007ffd136a0000 - 0x00007ffd13983000 	C:\WINDOWS\SYSTEM32\CoreUIComponents.dll
0x00007ffcc5250000 - 0x00007ffcc56f9000 	C:\Program Files (x86)\Google\Google Japanese Input\GoogleIMEJaTIP64.dll
0x00007ffcf4620000 - 0x00007ffcf464e000 	C:\Users\<USER>\.jdks\openjdk-20\bin\javajpeg.dll
0x00007ffcb13d0000 - 0x00007ffcb149c000 	C:\Users\<USER>\AppData\Local\Temp\webp-imageio-unknown-08e192c4-3e77-4a6a-bc09-be5c18050e43-webp-imageio.dll
0x00007ffcf5320000 - 0x00007ffcf532e000 	C:\Users\<USER>\.jdks\openjdk-20\bin\sunmscapi.dll
0x00007ffd19dd0000 - 0x00007ffd19e00000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffd19d80000 - 0x00007ffd19dbf000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffcf4e80000 - 0x00007ffcf4e90000 	C:\Users\<USER>\.jdks\openjdk-20\bin\verify.dll
0x00007ffcf4ca0000 - 0x00007ffcf4ca8000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\.jdks\openjdk-20\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3;C:\Users\<USER>\.jdks\openjdk-20\bin\server;C:\Users\<USER>\AppData\Local\Temp;C:\WINDOWS\System32\DriverStore\FileRepository\nv_dispi.inf_amd64_ea7f458f0e49497d;C:\Users\<USER>\AppData\Local\Temp\flatlaf.temp;C:\Program Files (x86)\Google\Google Japanese Input

VM Arguments:
jvm_args: -Dvaadin.copilot.pluginDotFilePath=C:\Users\<USER>\git\scrapingmercari\.idea\.copilot-plugin -javaagent:C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\lib\idea_rt.jar=61472 -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 
java_command: com.mrcresearch.screen.main.Application
java_class_path (initial): C:\Users\<USER>\git\scrapingmercari\target\classes;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-java\3.141.59\selenium-java-3.141.59.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-api\3.141.59\selenium-api-3.141.59.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-chrome-driver\3.141.59\selenium-chrome-driver-3.141.59.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-edge-driver\3.141.59\selenium-edge-driver-3.141.59.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-firefox-driver\3.141.59\selenium-firefox-driver-3.141.59.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-ie-driver\3.141.59\selenium-ie-driver-3.141.59.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-opera-driver\3.141.59\selenium-opera-driver-3.141.59.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-remote-driver\3.141.59\selenium-remote-driver-3.141.59.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-safari-driver\3.141.59\selenium-safari-driver-3.141.59.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-support\3.141.59\selenium-support-3.141.59.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.8.15\byte-buddy-1.8.15.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-exec\1.3\commons-exec-1.3.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\25.0-jre\guava-25.0-jre.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\1.3.9\jsr305-1.3.9.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-compat-qual\2.0.0\checker-compat-qual-2.0.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.1.3\error_prone_annotations-2.1.3.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.1\j2objc-annotations-1.1.jar;C:\Users\<USER>\.m2\repository\org\codehaus\mojo\animal-sniffer-annotations\1.14\animal-sniffer-annotations-1.14.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\3.11.0\okhttp-3.11.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio\1.14.0\okio-1.14.0.jar;C:\Users\<USER>\.m2\repository\io\github\bonigarcia\webdrivermanager\6.1.0\webdrivermanager-6.1.0.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.17\slf4j-api-2.0.17.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.13.0\gson-2.13.0.jar;C:\Users\<USER>\.m2\repository\com\github\docker-java\docker-java\3.5.0\docker-java-3.5.0.jar;C:\Users\<USER>\.m2\repository\com\github\docker-java\docker-java-core\3.5.0\docker-java-core-3.5.0.jar;C:\Users\<USER>\.m2\repository\com\github\docker-java\docker-java-api\3.5.0\docker-java-api-3.5.0.jar;C:\Users\<USER>\.m2\repository\com\github\docker-java\docker-java-transport-httpclient5\3.5.0\docker-java-transport-httpclient5-3.5.0.jar;C:\Users\<USER>\.m2\repository\com\github\docker-java\docker-java-transport\3.5.0\docker-java-transport-3.5.0.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\5.17.0\jna-5.17.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.17.0\commons-lang3-3.17.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.27.1\commons-compress-1.27.1.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.17.1\commons-codec-1.17.1.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna-platform\5.13.0\jna-platform-5.13.0.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.14.0\commons-io-2.14.0.jar;C:\Users\<USER>\.m2\repository\net\lightbody\bmp\browsermob-core\2.1.5\browsermob-core-2.1.5.jar;C:\Users\<USER>\.m2\repository\net\lightbody\bmp\littleproxy\1.1.0-beta-bmp-17\littleproxy-1.1.0-beta-bmp-17.jar;C:\Users\<USER>\.m2\repository\dnsjava\dnsjava\2.1.8\dnsjava-2.1.8.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jcl-over-slf4j\1.7.25\jcl-over-slf4j-1.7.25.jar;C:\Users\<USER>\.m2\repository\com\jcraft\jzlib\1.1.3\jzlib-1.1.3.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-all\4.0.51.Final\netty-all-4.0.51.Final.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk15on\1.58\bcprov-jdk15on-1.58.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcpkix-jdk15on\1.58\bcpkix-jdk15on-1.58.jar;C:\Users\<USER>\.m2\repository\net\lightbody\bmp\mitm\2.1.5\mitm-2.1.5.jar;C:\Users\<USER>\.m2\repository\com\aayushatharva\brotli4j\brotli4j\1.15.0\brotli4j-1.15.0.jar;C:\Users\<USER>\.m2\repository\com\aayushatharva\brotli4j\service\1.15.0\service-1.15.0.jar;C:\Users\<USER>\.m2\repository\com\aayushatharva\brotli4j\native-windows-x86_64\1.15.0\native-windows-x86_64-1.15.0.jar;C:\Users\<USER>\.m2\repository\org\brotli\dec\0.1.2\dec-0.1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\client5\httpclient5\5.4.2\httpclient5-5.4.2.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\core5\httpcore5\5.3.3\httpcore5-5.3.3.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\core5\httpcore5-h2\5.3.3\httpcore5-h2-5.3.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.17.2\jackson-databind-2.17.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.17.2\jackson-core-2.17.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.17.2\jackson-annotations-2.17.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-csv\2.17.2\jackson-dataformat-csv-2.17.2.jar;C:\Users\<USER>\.m2\repository\com\miglayout\miglayout-swing\5.0\miglayout-swing-5.0.jar;C:\Users\<USER>\.m2\repository\com\miglayout\miglayout-core\5.0\miglayout-core-5.0.jar;C:\Users\<USER>\.m2\repository\com\formdev\flatlaf\3.6\flatlaf-3.6.jar;C:\Users\<USER>\.m2\repository\com\formdev\flatlaf-extras\3.6\flatlaf-extras-3.6.jar;C:\Users\<USER>\.m2\repository\com\github\weisj\jsvg\1.4.0\jsvg-1.4.0.jar;C:\Users\<USER>\.m2\repository\org\kordamp\ikonli\ikonli-swing\12.4.0\ikonli-swing-12.4.0.jar;C:\Users\<USER>\.m2\repository\org\kordamp\ikonli\ikonli-core\12.4.0\ikonli-core-12.4.0.jar;C:\Users\<USER>\.m2\repository\org\kordamp\ikonli\ikonli-antdesignicons-pack\12.4.0\ikonli-antdesignicons-pack-12.4.0.jar;C:\Users\<USER>\.m2\repository\org\kordamp\ikonli\ikonli-fontawesome5-pack\12.4.0\ikonli-fontawesome5-pack-12.4.0.jar;C:\Users\<USER>\.m2\repository\org\xerial\sqlite-jdbc\********\sqlite-jdbc-********.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.30\lombok-1.18.30.jar;C:\Users\<USER>\.m2\repository\io\github\darkxanter\webp-imageio\0.3.3\webp-imageio-0.3.3.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 524288000                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8355053568                                {product} {ergonomic}
   size_t MaxNewSize                               = 5012193280                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8355053568                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Users\<USER>\.jdks\jbrsdk_jcef-21.0.7
PATH=C:\Program Files (x86)\Razer Chroma SDK\bin;C:\Program Files\Razer Chroma SDK\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\VMware\VMware Player\bin\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\dotnet\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\platform-tools;C:\TDM-GCC-64\bin;C:\Program Files\Java\jdk-18.0.2\bin;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;%JAVA_OHME%\bin;C:\Program Files (x86)\Paragon Software\APFS for Windows\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\Program Files (x86)\Razer\ChromaBroadcast\bin;C:\Program Files\Razer\ChromaBroadcast\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Program Files\JetBrains\IntelliJ IDEA 2024.2.1\bin;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Program Files\JetBrains\IntelliJ IDEA 2024.2.1\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=chooi
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 117 Stepping 2, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4484)
OS uptime: 0 days 3:25 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (12 cores per cpu, 2 threads per core) family 25 model 117 stepping 2 microcode 0xa705205, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ss, avx512_ifma

Memory: 4k page, system-wide physical 31867M (1579M free)
TotalPageFile size 57457M (AvailPageFile size 181M)
current process WorkingSet (physical memory assigned to process): 542M, peak: 575M
current process commit charge ("private bytes"): 620M, peak: 688M

vm_info: OpenJDK 64-Bit Server VM (20+36-2344) for windows-amd64 JRE (20+36-2344), built on 2023-02-10T19:30:15Z by "mach5one" with MS VC++ 17.1 (VS2022)

END.
