#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 180355072 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3528), pid=14680, tid=34824
#
# JRE version: OpenJDK Runtime Environment (20.0+36) (build 20+36-2344)
# Java VM: OpenJDK 64-Bit Server VM (20+36-2344, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -Dvaadin.copilot.pluginDotFilePath=C:\Users\<USER>\git\scrapingmercari\.idea\.copilot-plugin -javaagent:C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\lib\idea_rt.jar=55112 -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 com.mrcresearch.screen.main.Application

Host: AMD Ryzen 5 8600G w/ Radeon 760M Graphics      , 12 cores, 31G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4768)
Time: Mon Sep  1 12:43:59 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4768) elapsed time: 560.563691 seconds (0d 0h 9m 20s)

---------------  T H R E A D  ---------------

Current thread (0x0000013b5be9faf0):  VMThread "VM Thread" [stack: 0x0000003574b00000,0x0000003574c00000] [id=34824]

Stack: [0x0000003574b00000,0x0000003574c00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6bb95a]
V  [jvm.dll+0x8483aa]
V  [jvm.dll+0x849fa5]
V  [jvm.dll+0x84a6a3]
V  [jvm.dll+0x280b0f]
V  [jvm.dll+0x6b86e9]
V  [jvm.dll+0x6ad2da]
V  [jvm.dll+0x35adf5]
V  [jvm.dll+0x363016]
V  [jvm.dll+0x3b3b3e]
V  [jvm.dll+0x3b3dc5]
V  [jvm.dll+0x32b50a]
V  [jvm.dll+0x32e126]
V  [jvm.dll+0x3381ee]
V  [jvm.dll+0x371a5a]
V  [jvm.dll+0x84ffee]
V  [jvm.dll+0x851101]
V  [jvm.dll+0x851609]
V  [jvm.dll+0x851883]
V  [jvm.dll+0x7f4906]
V  [jvm.dll+0x6ba5cb]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]

VM_Operation (0x00000035747ff940): G1PauseRemark, mode: safepoint, requested by thread 0x0000013b37150560


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000013b618b5cf0, length=60, elements={
0x0000013b5bec7eb0, 0x0000013b5bec89f0, 0x0000013b5becdf20, 0x0000013b5beceb50,
0x0000013b5becfe50, 0x0000013b5bed2f50, 0x0000013b5bede9d0, 0x0000013b5bedfaf0,
0x0000013b5bfde9d0, 0x0000013b606f84a0, 0x0000013b606f89f0, 0x0000013b612f7430,
0x0000013b6152e8c0, 0x0000013b615062d0, 0x0000013b614cf520, 0x0000013b615fd0a0,
0x0000013b615fe5e0, 0x0000013b615feb30, 0x0000013b615fd5f0, 0x0000013b616fbe80,
0x0000013b616fee50, 0x0000013b617008e0, 0x0000013b61700e30, 0x0000013b616ffe40,
0x0000013b61700390, 0x0000013b61701380, 0x0000013b616ff8f0, 0x0000013b60b6eec0,
0x0000013b60b70400, 0x0000013b60b70950, 0x0000013b60b70ea0, 0x0000013b60b713f0,
0x0000013b60b6ded0, 0x0000013b60b72930, 0x0000013b60b71e90, 0x0000013b60b723e0,
0x0000013b60b72e80, 0x0000013b60b733d0, 0x0000013b60b73e70, 0x0000013b60b74910,
0x0000013b60b73920, 0x0000013b60b743c0, 0x0000013b60b74e60, 0x0000013b60b753b0,
0x0000013b60b75900, 0x0000013b616fc920, 0x0000013b61702370, 0x0000013b61702e10,
0x0000013b61703360, 0x0000013b61701e20, 0x0000013b6f90a700, 0x0000013b6f90c6e0,
0x0000013b6f90ac50, 0x0000013b6f90b1a0, 0x0000013b6f90b6f0, 0x0000013b6f909710,
0x0000013b6f90f160, 0x0000013b6f910150, 0x0000013b6f8c5c40, 0x0000013b6f8c88c0
}

Java Threads: ( => current thread )
  0x0000013b5bec7eb0 JavaThread "Reference Handler" daemon [_thread_blocked, id=33888, stack(0x0000003574c00000,0x0000003574d00000)]
  0x0000013b5bec89f0 JavaThread "Finalizer" daemon [_thread_blocked, id=20752, stack(0x0000003574d00000,0x0000003574e00000)]
  0x0000013b5becdf20 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=33640, stack(0x0000003574e00000,0x0000003574f00000)]
  0x0000013b5beceb50 JavaThread "Attach Listener" daemon [_thread_blocked, id=27044, stack(0x0000003574f00000,0x0000003575000000)]
  0x0000013b5becfe50 JavaThread "Service Thread" daemon [_thread_blocked, id=34848, stack(0x0000003575000000,0x0000003575100000)]
  0x0000013b5bed2f50 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=26864, stack(0x0000003575100000,0x0000003575200000)]
  0x0000013b5bede9d0 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=21816, stack(0x0000003575200000,0x0000003575300000)]
  0x0000013b5bedfaf0 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=18836, stack(0x0000003575300000,0x0000003575400000)]
  0x0000013b5bfde9d0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=27152, stack(0x0000003575400000,0x0000003575500000)]
  0x0000013b606f84a0 JavaThread "Monitor Ctrl-Break" daemon [_thread_in_native, id=34876, stack(0x0000003575700000,0x0000003575800000)]
  0x0000013b606f89f0 JavaThread "Notification Thread" daemon [_thread_blocked, id=30832, stack(0x0000003575800000,0x0000003575900000)]
  0x0000013b612f7430 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=35512, stack(0x0000003576300000,0x0000003576400000)]
  0x0000013b6152e8c0 JavaThread "RMI TCP Connection(1)-192.168.0.143" daemon [_thread_blocked, id=22708, stack(0x0000003576400000,0x0000003576500000)]
  0x0000013b615062d0 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=1532, stack(0x0000003576500000,0x0000003576600000)]
  0x0000013b614cf520 JavaThread "JMX server connection timeout 35" daemon [_thread_blocked, id=28620, stack(0x0000003576600000,0x0000003576700000)]
  0x0000013b615fd0a0 JavaThread "MercariResearchDB-Pool housekeeper" daemon [_thread_blocked, id=34968, stack(0x0000003576700000,0x0000003576800000)]
  0x0000013b615fe5e0 JavaThread "Java2D Disposer" daemon [_thread_blocked, id=34324, stack(0x0000003575500000,0x0000003575600000)]
  0x0000013b615feb30 JavaThread "AWT-Shutdown" [_thread_blocked, id=35032, stack(0x0000003575600000,0x0000003575700000)]
  0x0000013b615fd5f0 JavaThread "AWT-Windows" daemon [_thread_in_native, id=34372, stack(0x0000003576900000,0x0000003576a00000)]
  0x0000013b616fbe80 JavaThread "AWT-EventQueue-0" [_thread_blocked, id=30368, stack(0x0000003576c00000,0x0000003576d00000)]
  0x0000013b616fee50 JavaThread "DestroyJavaVM" [_thread_blocked, id=22672, stack(0x0000003574500000,0x0000003574600000)]
  0x0000013b617008e0 JavaThread "TimerQueue" daemon [_thread_blocked, id=30152, stack(0x0000003577000000,0x0000003577100000)]
  0x0000013b61700e30 JavaThread "SwingWorker-pool-1-thread-1" daemon [_thread_blocked, id=32840, stack(0x0000003577400000,0x0000003577500000)]
  0x0000013b616ffe40 JavaThread "SwingWorker-pool-1-thread-2" daemon [_thread_blocked, id=36532, stack(0x0000003577300000,0x0000003577400000)]
  0x0000013b61700390 JavaThread "SearchQueueProcessor" daemon [_thread_blocked, id=28424, stack(0x0000003577500000,0x0000003577600000)]
  0x0000013b61701380 JavaThread "SwingWorker-pool-1-thread-3" daemon [_thread_blocked, id=29436, stack(0x0000003576f00000,0x0000003577000000)]
  0x0000013b616ff8f0 JavaThread "SwingWorker-pool-1-thread-4" daemon [_thread_blocked, id=31908, stack(0x0000003577c00000,0x0000003577d00000)]
  0x0000013b60b6eec0 JavaThread "SwingWorker-pool-1-thread-5" daemon [_thread_blocked, id=29156, stack(0x0000003577a00000,0x0000003577b00000)]
  0x0000013b60b70400 JavaThread "SwingWorker-pool-1-thread-6" daemon [_thread_blocked, id=33064, stack(0x0000003576800000,0x0000003576900000)]
  0x0000013b60b70950 JavaThread "Thread-2" daemon [_thread_blocked, id=28312, stack(0x0000003574200000,0x0000003574300000)]
  0x0000013b60b70ea0 JavaThread "SwingWorker-pool-1-thread-7" daemon [_thread_blocked, id=35656, stack(0x0000003576af0000,0x0000003576bf0000)]
  0x0000013b60b713f0 JavaThread "SwingWorker-pool-1-thread-8" daemon [_thread_blocked, id=13064, stack(0x0000003576d00000,0x0000003576e00000)]
  0x0000013b60b6ded0 JavaThread "LittleProxy-0-ClientToProxyAcceptor-0" daemon [_thread_blocked, id=31404, stack(0x0000003574300000,0x0000003574400000)]
  0x0000013b60b72930 JavaThread "LittleProxy-0-ClientToProxyWorker-0" daemon [_thread_blocked, id=34016, stack(0x0000003577900000,0x0000003577a00000)]
  0x0000013b60b71e90 JavaThread "LittleProxy-0-ProxyToServerWorker-0" daemon [_thread_blocked, id=36664, stack(0x0000003577b00000,0x0000003577c00000)]
  0x0000013b60b723e0 JavaThread "LittleProxy-0-ClientToProxyWorker-1" daemon [_thread_in_native, id=36236, stack(0x0000003577e00000,0x0000003577f00000)]
  0x0000013b60b72e80 JavaThread "LittleProxy-0-ProxyToServerWorker-1" daemon [_thread_blocked, id=32348, stack(0x0000003577f00000,0x0000003578000000)]
  0x0000013b60b733d0 JavaThread "ForkJoinPool.commonPool-worker-2" daemon [_thread_blocked, id=25720, stack(0x0000003577800000,0x0000003577900000)]
  0x0000013b60b73e70 JavaThread "HttpClient-1-SelectorManager" daemon [_thread_in_native, id=36324, stack(0x0000003578000000,0x0000003578100000)]
  0x0000013b60b74910 JavaThread "External Process Output Forwarder - C:\Users\<USER>\.cache\selenium\geckodriver\win64\0.36.0\geckodriver.exe" daemon [_thread_in_native, id=29024, stack(0x0000003578100000,0x0000003578200000)]
  0x0000013b60b73920 JavaThread "Driver Service Executor" daemon [_thread_blocked, id=36708, stack(0x0000003578200000,0x0000003578300000)]
  0x0000013b60b743c0 JavaThread "Driver Service Executor" daemon [_thread_blocked, id=2740, stack(0x0000003578300000,0x0000003578400000)]
  0x0000013b60b74e60 JavaThread "JdkHttpClient-0-0" daemon [_thread_blocked, id=14080, stack(0x0000003578400000,0x0000003578500000)]
  0x0000013b60b753b0 JavaThread "JdkHttpClient-0-1" daemon [_thread_blocked, id=36308, stack(0x0000003578500000,0x0000003578600000)]
  0x0000013b60b75900 JavaThread "JdkHttpClient-0-2" daemon [_thread_blocked, id=35472, stack(0x0000003578600000,0x0000003578700000)]
  0x0000013b616fc920 JavaThread "LittleProxy-0-ClientToProxyWorker-2" daemon [_thread_in_native, id=36776, stack(0x0000003578700000,0x0000003578800000)]
  0x0000013b61702370 JavaThread "LittleProxy-0-ProxyToServerWorker-2" daemon [_thread_blocked, id=26952, stack(0x0000003577600000,0x0000003577700000)]
  0x0000013b61702e10 JavaThread "LittleProxy-0-ClientToProxyWorker-3" daemon [_thread_in_native, id=28348, stack(0x0000003578900000,0x0000003578a00000)]
  0x0000013b61703360 JavaThread "LittleProxy-0-ClientToProxyWorker-4" daemon [_thread_blocked, id=35600, stack(0x0000003578a00000,0x0000003578b00000)]
  0x0000013b61701e20 JavaThread "LittleProxy-0-ProxyToServerWorker-3" daemon [_thread_blocked, id=27860, stack(0x0000003578b00000,0x0000003578c00000)]
  0x0000013b6f90a700 JavaThread "LittleProxy-0-ProxyToServerWorker-4" daemon [_thread_blocked, id=34556, stack(0x0000003578c00000,0x0000003578d00000)]
  0x0000013b6f90c6e0 JavaThread "LittleProxy-0-ClientToProxyWorker-5" daemon [_thread_blocked, id=32984, stack(0x0000003578d00000,0x0000003578e00000)]
  0x0000013b6f90ac50 JavaThread "LittleProxy-0-ProxyToServerWorker-5" daemon [_thread_blocked, id=36764, stack(0x0000003578800000,0x0000003578900000)]
  0x0000013b6f90b1a0 JavaThread "SwingWorker-pool-1-thread-9" daemon [_thread_blocked, id=11172, stack(0x0000003577700000,0x0000003577800000)]
  0x0000013b6f90b6f0 JavaThread "LittleProxy-0-ClientToProxyWorker-6" daemon [_thread_blocked, id=19700, stack(0x0000003578f00000,0x0000003579000000)]
  0x0000013b6f909710 JavaThread "LittleProxy-0-ProxyToServerWorker-6" daemon [_thread_blocked, id=28012, stack(0x0000003579000000,0x0000003579100000)]
  0x0000013b6f90f160 JavaThread "LittleProxy-0-ClientToProxyWorker-7" daemon [_thread_blocked, id=12012, stack(0x0000003579200000,0x0000003579300000)]
  0x0000013b6f910150 JavaThread "LittleProxy-0-ProxyToServerWorker-7" daemon [_thread_in_native, id=16008, stack(0x0000003579100000,0x0000003579200000)]
  0x0000013b6f8c5c40 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=28960, stack(0x0000003579300000,0x0000003579400000)]
  0x0000013b6f8c88c0 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=36700, stack(0x0000003579400000,0x0000003579500000)]

Other Threads:
=>0x0000013b5be9faf0 VMThread "VM Thread" [stack: 0x0000003574b00000,0x0000003574c00000] [id=34824]
  0x0000013b606f8f40 WatcherThread "VM Periodic Task Thread" [stack: 0x0000003575900000,0x0000003575a00000] [id=30792]
  0x0000013b5922f4b0 WorkerThread "GC Thread#0" [stack: 0x0000003574600000,0x0000003574700000] [id=36052]
  0x0000013b61337430 WorkerThread "GC Thread#1" [stack: 0x0000003575a00000,0x0000003575b00000] [id=24636]
  0x0000013b613368f0 WorkerThread "GC Thread#2" [stack: 0x0000003575b00000,0x0000003575c00000] [id=31604]
  0x0000013b61337f70 WorkerThread "GC Thread#3" [stack: 0x0000003575c00000,0x0000003575d00000] [id=35804]
  0x0000013b61337ca0 WorkerThread "GC Thread#4" [stack: 0x0000003575d00000,0x0000003575e00000] [id=35224]
  0x0000013b613379d0 WorkerThread "GC Thread#5" [stack: 0x0000003575e00000,0x0000003575f00000] [id=35628]
  0x0000013b61336350 WorkerThread "GC Thread#6" [stack: 0x0000003575f00000,0x0000003576000000] [id=35536]
  0x0000013b61336bc0 WorkerThread "GC Thread#7" [stack: 0x0000003576000000,0x0000003576100000] [id=36644]
  0x0000013b61336e90 WorkerThread "GC Thread#8" [stack: 0x0000003576100000,0x0000003576200000] [id=36648]
  0x0000013b61337160 WorkerThread "GC Thread#9" [stack: 0x0000003576200000,0x0000003576300000] [id=10796]
  0x0000013b37150560 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000003574700000,0x0000003574800000] [id=18136]
  0x0000013b37152550 WorkerThread "G1 Conc#0" [stack: 0x0000003574800000,0x0000003574900000] [id=32452]
  0x0000013b616035e0 WorkerThread "G1 Conc#1" [stack: 0x0000003577100000,0x0000003577200000] [id=29964]
  0x0000013b61602230 WorkerThread "G1 Conc#2" [stack: 0x0000003577200000,0x0000003577300000] [id=31188]
  0x0000013b3717e5e0 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000003574900000,0x0000003574a00000] [id=32472]
  0x0000013b592be060 ConcurrentGCThread "G1 Service" [stack: 0x0000003574a00000,0x0000003574b00000] [id=30424]

Threads with active compile tasks:
C2 CompilerThread1   560585 13568       4       sun.security.util.ObjectIdentifier::toString (285 bytes)
C2 CompilerThread2   560585 13631 %     4       sun.security.util.DomainName$Rules$RuleSet::match @ 12 (143 bytes)

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x0000013b370cd540] Threads_lock - owner thread: 0x0000013b5be9faf0
[0x0000013b370cc6a0] Heap_lock - owner thread: 0x0000013b37150560

Heap address: 0x000000060e000000, size: 7968 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000800000000-0x0000000800c60000-0x0000000800c60000), size 12976128, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 0.
Compressed class space mapped at: 0x0000000801000000-0x0000000841000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 12 total, 12 available
 Memory: 31867M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 500M
 Heap Max Capacity: 7968M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 548864K, used 427214K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 2 survivors (8192K)
 Metaspace       used 58718K, committed 59392K, reserved 1114112K
  class space    used 6603K, committed 6912K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x000000060e000000, 0x000000060e400000, 0x000000060e400000|100%| O|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|   1|0x000000060e400000, 0x000000060e7c0800, 0x000000060e800000| 93%| O|  |TAMS 0x000000060e7c0800| PB 0x000000060e7c0800| Untracked 
|   2|0x000000060e800000, 0x000000060ec00000, 0x000000060ec00000|100%| O|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|   3|0x000000060ec00000, 0x000000060f000000, 0x000000060f000000|100%| O|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|   4|0x000000060f000000, 0x000000060f400000, 0x000000060f400000|100%| O|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Untracked 
|   5|0x000000060f400000, 0x000000060f800000, 0x000000060f800000|100%| O|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Updating 
|   6|0x000000060f800000, 0x000000060fc00000, 0x000000060fc00000|100%| O|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|   7|0x000000060fc00000, 0x0000000610000000, 0x0000000610000000|100%| O|  |TAMS 0x000000060ff75e00| PB 0x000000060ff75e00| Untracked 
|   8|0x0000000610000000, 0x0000000610400000, 0x0000000610400000|100%| O|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Updating 
|   9|0x0000000610400000, 0x0000000610800000, 0x0000000610800000|100%| O|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Updating 
|  10|0x0000000610800000, 0x0000000610c00000, 0x0000000610c00000|100%|HS|  |TAMS 0x0000000610c00000| PB 0x0000000610800000| Complete 
|  11|0x0000000610c00000, 0x0000000611000000, 0x0000000611000000|100%|HC|  |TAMS 0x0000000611000000| PB 0x0000000610c00000| Complete 
|  12|0x0000000611000000, 0x0000000611400000, 0x0000000611400000|100%|HC|  |TAMS 0x0000000611400000| PB 0x0000000611000000| Complete 
|  13|0x0000000611400000, 0x0000000611800000, 0x0000000611800000|100%|HC|  |TAMS 0x0000000611800000| PB 0x0000000611400000| Complete 
|  14|0x0000000611800000, 0x0000000611c00000, 0x0000000611c00000|100%|HS|  |TAMS 0x0000000611c00000| PB 0x0000000611800000| Complete 
|  15|0x0000000611c00000, 0x0000000612000000, 0x0000000612000000|100%|HC|  |TAMS 0x0000000612000000| PB 0x0000000611c00000| Complete 
|  16|0x0000000612000000, 0x0000000612400000, 0x0000000612400000|100%|HC|  |TAMS 0x0000000612400000| PB 0x0000000612000000| Complete 
|  17|0x0000000612400000, 0x0000000612800000, 0x0000000612800000|100%|HC|  |TAMS 0x0000000612800000| PB 0x0000000612400000| Complete 
|  18|0x0000000612800000, 0x0000000612c00000, 0x0000000612c00000|100%|HC|  |TAMS 0x0000000612c00000| PB 0x0000000612800000| Complete 
|  19|0x0000000612c00000, 0x0000000613000000, 0x0000000613000000|100%| O|  |TAMS 0x0000000612c00000| PB 0x0000000612c00000| Untracked 
|  20|0x0000000613000000, 0x0000000613400000, 0x0000000613400000|100%| O|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Untracked 
|  21|0x0000000613400000, 0x0000000613800000, 0x0000000613800000|100%|HS|  |TAMS 0x0000000613800000| PB 0x0000000613400000| Complete 
|  22|0x0000000613800000, 0x0000000613c00000, 0x0000000613c00000|100%|HS|  |TAMS 0x0000000613c00000| PB 0x0000000613800000| Complete 
|  23|0x0000000613c00000, 0x0000000614000000, 0x0000000614000000|100%|HC|  |TAMS 0x0000000614000000| PB 0x0000000613c00000| Complete 
|  24|0x0000000614000000, 0x0000000614400000, 0x0000000614400000|100%|HC|  |TAMS 0x0000000614400000| PB 0x0000000614000000| Complete 
|  25|0x0000000614400000, 0x0000000614800000, 0x0000000614800000|100%|HC|  |TAMS 0x0000000614800000| PB 0x0000000614400000| Complete 
|  26|0x0000000614800000, 0x0000000614c00000, 0x0000000614c00000|100%|HC|  |TAMS 0x0000000614c00000| PB 0x0000000614800000| Complete 
|  27|0x0000000614c00000, 0x0000000615000000, 0x0000000615000000|100%|HS|  |TAMS 0x0000000615000000| PB 0x0000000614c00000| Complete 
|  28|0x0000000615000000, 0x0000000615400000, 0x0000000615400000|100%|HC|  |TAMS 0x0000000615400000| PB 0x0000000615000000| Complete 
|  29|0x0000000615400000, 0x0000000615800000, 0x0000000615800000|100%|HC|  |TAMS 0x0000000615800000| PB 0x0000000615400000| Complete 
|  30|0x0000000615800000, 0x0000000615c00000, 0x0000000615c00000|100%|HC|  |TAMS 0x0000000615c00000| PB 0x0000000615800000| Complete 
|  31|0x0000000615c00000, 0x0000000616000000, 0x0000000616000000|100%|HC|  |TAMS 0x0000000616000000| PB 0x0000000615c00000| Complete 
|  32|0x0000000616000000, 0x0000000616400000, 0x0000000616400000|100%| O|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  33|0x0000000616400000, 0x0000000616800000, 0x0000000616800000|100%|HS|  |TAMS 0x0000000616800000| PB 0x0000000616400000| Complete 
|  34|0x0000000616800000, 0x0000000616c00000, 0x0000000616c00000|100%|HC|  |TAMS 0x0000000616c00000| PB 0x0000000616800000| Complete 
|  35|0x0000000616c00000, 0x0000000617000000, 0x0000000617000000|100%|HC|  |TAMS 0x0000000617000000| PB 0x0000000616c00000| Complete 
|  36|0x0000000617000000, 0x0000000617400000, 0x0000000617400000|100%|HC|  |TAMS 0x0000000617400000| PB 0x0000000617000000| Complete 
|  37|0x0000000617400000, 0x0000000617800000, 0x0000000617800000|100%|HC|  |TAMS 0x0000000617800000| PB 0x0000000617400000| Complete 
|  38|0x0000000617800000, 0x0000000617c00000, 0x0000000617c00000|100%|HS|  |TAMS 0x0000000617c00000| PB 0x0000000617800000| Complete 
|  39|0x0000000617c00000, 0x0000000618000000, 0x0000000618000000|100%|HC|  |TAMS 0x0000000618000000| PB 0x0000000617c00000| Complete 
|  40|0x0000000618000000, 0x0000000618400000, 0x0000000618400000|100%|HC|  |TAMS 0x0000000618400000| PB 0x0000000618000000| Complete 
|  41|0x0000000618400000, 0x0000000618800000, 0x0000000618800000|100%|HC|  |TAMS 0x0000000618800000| PB 0x0000000618400000| Complete 
|  42|0x0000000618800000, 0x0000000618c00000, 0x0000000618c00000|100%|HC|  |TAMS 0x0000000618c00000| PB 0x0000000618800000| Complete 
|  43|0x0000000618c00000, 0x0000000619000000, 0x0000000619000000|100%|HS|  |TAMS 0x0000000619000000| PB 0x0000000618c00000| Complete 
|  44|0x0000000619000000, 0x0000000619400000, 0x0000000619400000|100%|HC|  |TAMS 0x0000000619400000| PB 0x0000000619000000| Complete 
|  45|0x0000000619400000, 0x00000006196efa00, 0x0000000619800000| 73%| O|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Updating 
|  46|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
|  47|0x0000000619c00000, 0x000000061a000000, 0x000000061a000000|100%|HS|  |TAMS 0x000000061a000000| PB 0x0000000619c00000| Complete 
|  48|0x000000061a000000, 0x000000061a400000, 0x000000061a400000|100%|HC|  |TAMS 0x000000061a400000| PB 0x000000061a000000| Complete 
|  49|0x000000061a400000, 0x000000061a800000, 0x000000061a800000|100%|HC|  |TAMS 0x000000061a800000| PB 0x000000061a400000| Complete 
|  50|0x000000061a800000, 0x000000061ac00000, 0x000000061ac00000|100%|HC|  |TAMS 0x000000061ac00000| PB 0x000000061a800000| Complete 
|  51|0x000000061ac00000, 0x000000061b000000, 0x000000061b000000|100%|HC|  |TAMS 0x000000061b000000| PB 0x000000061ac00000| Complete 
|  52|0x000000061b000000, 0x000000061b400000, 0x000000061b400000|100%|HS|  |TAMS 0x000000061b400000| PB 0x000000061b000000| Complete 
|  53|0x000000061b400000, 0x000000061b800000, 0x000000061b800000|100%|HC|  |TAMS 0x000000061b800000| PB 0x000000061b400000| Complete 
|  54|0x000000061b800000, 0x000000061bc00000, 0x000000061bc00000|100%|HC|  |TAMS 0x000000061bc00000| PB 0x000000061b800000| Complete 
|  55|0x000000061bc00000, 0x000000061c000000, 0x000000061c000000|100%|HC|  |TAMS 0x000000061c000000| PB 0x000000061bc00000| Complete 
|  56|0x000000061c000000, 0x000000061c400000, 0x000000061c400000|100%|HC|  |TAMS 0x000000061c400000| PB 0x000000061c000000| Complete 
|  57|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
|  58|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
|  59|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
|  60|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
|  61|0x000000061d400000, 0x000000061d800000, 0x000000061d800000|100%|HS|  |TAMS 0x000000061d800000| PB 0x000000061d400000| Complete 
|  62|0x000000061d800000, 0x000000061dc00000, 0x000000061dc00000|100%|HC|  |TAMS 0x000000061dc00000| PB 0x000000061d800000| Complete 
|  63|0x000000061dc00000, 0x000000061e000000, 0x000000061e000000|100%|HC|  |TAMS 0x000000061e000000| PB 0x000000061dc00000| Complete 
|  64|0x000000061e000000, 0x000000061e400000, 0x000000061e400000|100%|HC|  |TAMS 0x000000061e400000| PB 0x000000061e000000| Complete 
|  65|0x000000061e400000, 0x000000061e800000, 0x000000061e800000|100%|HC|  |TAMS 0x000000061e800000| PB 0x000000061e400000| Complete 
|  66|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
|  67|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Untracked 
|  68|0x000000061f000000, 0x000000061f400000, 0x000000061f400000|100%|HS|  |TAMS 0x000000061f400000| PB 0x000000061f000000| Complete 
|  69|0x000000061f400000, 0x000000061f800000, 0x000000061f800000|100%|HC|  |TAMS 0x000000061f800000| PB 0x000000061f400000| Complete 
|  70|0x000000061f800000, 0x000000061fc00000, 0x000000061fc00000|100%|HC|  |TAMS 0x000000061fc00000| PB 0x000000061f800000| Complete 
|  71|0x000000061fc00000, 0x0000000620000000, 0x0000000620000000|100%|HC|  |TAMS 0x0000000620000000| PB 0x000000061fc00000| Complete 
|  72|0x0000000620000000, 0x0000000620400000, 0x0000000620400000|100%|HC|  |TAMS 0x0000000620400000| PB 0x0000000620000000| Complete 
|  73|0x0000000620400000, 0x0000000620800000, 0x0000000620800000|100%|HS|  |TAMS 0x0000000620800000| PB 0x0000000620400000| Complete 
|  74|0x0000000620800000, 0x0000000620c00000, 0x0000000620c00000|100%|HC|  |TAMS 0x0000000620c00000| PB 0x0000000620800000| Complete 
|  75|0x0000000620c00000, 0x0000000621000000, 0x0000000621000000|100%|HC|  |TAMS 0x0000000621000000| PB 0x0000000620c00000| Complete 
|  76|0x0000000621000000, 0x0000000621400000, 0x0000000621400000|100%|HC|  |TAMS 0x0000000621400000| PB 0x0000000621000000| Complete 
|  77|0x0000000621400000, 0x0000000621800000, 0x0000000621800000|100%|HC|  |TAMS 0x0000000621800000| PB 0x0000000621400000| Complete 
|  78|0x0000000621800000, 0x0000000621c00000, 0x0000000621c00000|100%|HS|  |TAMS 0x0000000621c00000| PB 0x0000000621800000| Complete 
|  79|0x0000000621c00000, 0x0000000622000000, 0x0000000622000000|100%|HC|  |TAMS 0x0000000622000000| PB 0x0000000621c00000| Complete 
|  80|0x0000000622000000, 0x0000000622400000, 0x0000000622400000|100%|HC|  |TAMS 0x0000000622400000| PB 0x0000000622000000| Complete 
|  81|0x0000000622400000, 0x0000000622800000, 0x0000000622800000|100%|HC|  |TAMS 0x0000000622800000| PB 0x0000000622400000| Complete 
|  82|0x0000000622800000, 0x0000000622c00000, 0x0000000622c00000|100%|HC|  |TAMS 0x0000000622c00000| PB 0x0000000622800000| Complete 
|  83|0x0000000622c00000, 0x0000000623000000, 0x0000000623000000|100%|HS|  |TAMS 0x0000000623000000| PB 0x0000000622c00000| Complete 
|  84|0x0000000623000000, 0x0000000623400000, 0x0000000623400000|100%|HC|  |TAMS 0x0000000623400000| PB 0x0000000623000000| Complete 
|  85|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%|HC|  |TAMS 0x0000000623800000| PB 0x0000000623400000| Complete 
|  86|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%|HC|  |TAMS 0x0000000623c00000| PB 0x0000000623800000| Complete 
|  87|0x0000000623c00000, 0x0000000624000000, 0x0000000624000000|100%|HC|  |TAMS 0x0000000624000000| PB 0x0000000623c00000| Complete 
|  88|0x0000000624000000, 0x0000000624400000, 0x0000000624400000|100%|HS|  |TAMS 0x0000000624400000| PB 0x0000000624000000| Complete 
|  89|0x0000000624400000, 0x0000000624800000, 0x0000000624800000|100%|HC|  |TAMS 0x0000000624800000| PB 0x0000000624400000| Complete 
|  90|0x0000000624800000, 0x0000000624c00000, 0x0000000624c00000|100%|HC|  |TAMS 0x0000000624c00000| PB 0x0000000624800000| Complete 
|  91|0x0000000624c00000, 0x0000000625000000, 0x0000000625000000|100%|HC|  |TAMS 0x0000000625000000| PB 0x0000000624c00000| Complete 
|  92|0x0000000625000000, 0x0000000625400000, 0x0000000625400000|100%|HC|  |TAMS 0x0000000625400000| PB 0x0000000625000000| Complete 
|  93|0x0000000625400000, 0x0000000625800000, 0x0000000625800000|100%|HS|  |TAMS 0x0000000625800000| PB 0x0000000625400000| Complete 
|  94|0x0000000625800000, 0x0000000625c00000, 0x0000000625c00000|100%|HC|  |TAMS 0x0000000625c00000| PB 0x0000000625800000| Complete 
|  95|0x0000000625c00000, 0x0000000626000000, 0x0000000626000000|100%|HC|  |TAMS 0x0000000626000000| PB 0x0000000625c00000| Complete 
|  96|0x0000000626000000, 0x0000000626400000, 0x0000000626400000|100%|HC|  |TAMS 0x0000000626400000| PB 0x0000000626000000| Complete 
|  97|0x0000000626400000, 0x0000000626800000, 0x0000000626800000|100%|HC|  |TAMS 0x0000000626800000| PB 0x0000000626400000| Complete 
|  98|0x0000000626800000, 0x0000000626a839d8, 0x0000000626c00000| 62%| S|CS|TAMS 0x0000000626800000| PB 0x0000000626800000| Complete 
|  99|0x0000000626c00000, 0x0000000627000000, 0x0000000627000000|100%| S|CS|TAMS 0x0000000626c00000| PB 0x0000000626c00000| Complete 
| 100|0x0000000627000000, 0x0000000627000000, 0x0000000627400000|  0%| F|  |TAMS 0x0000000627000000| PB 0x0000000627000000| Untracked 
| 101|0x0000000627400000, 0x0000000627400000, 0x0000000627800000|  0%| F|  |TAMS 0x0000000627400000| PB 0x0000000627400000| Untracked 
| 102|0x0000000627800000, 0x0000000627800000, 0x0000000627c00000|  0%| F|  |TAMS 0x0000000627800000| PB 0x0000000627800000| Untracked 
| 103|0x0000000627c00000, 0x0000000627c00000, 0x0000000628000000|  0%| F|  |TAMS 0x0000000627c00000| PB 0x0000000627c00000| Untracked 
| 104|0x0000000628000000, 0x0000000628000000, 0x0000000628400000|  0%| F|  |TAMS 0x0000000628000000| PB 0x0000000628000000| Untracked 
| 105|0x0000000628400000, 0x0000000628400000, 0x0000000628800000|  0%| F|  |TAMS 0x0000000628400000| PB 0x0000000628400000| Untracked 
| 106|0x0000000628800000, 0x0000000628800000, 0x0000000628c00000|  0%| F|  |TAMS 0x0000000628800000| PB 0x0000000628800000| Untracked 
| 107|0x0000000628c00000, 0x0000000628c00000, 0x0000000629000000|  0%| F|  |TAMS 0x0000000628c00000| PB 0x0000000628c00000| Untracked 
| 108|0x0000000629000000, 0x0000000629000000, 0x0000000629400000|  0%| F|  |TAMS 0x0000000629000000| PB 0x0000000629000000| Untracked 
| 109|0x0000000629400000, 0x0000000629400000, 0x0000000629800000|  0%| F|  |TAMS 0x0000000629400000| PB 0x0000000629400000| Untracked 
| 110|0x0000000629800000, 0x0000000629800000, 0x0000000629c00000|  0%| F|  |TAMS 0x0000000629800000| PB 0x0000000629800000| Untracked 
| 111|0x0000000629c00000, 0x0000000629c00000, 0x000000062a000000|  0%| F|  |TAMS 0x0000000629c00000| PB 0x0000000629c00000| Untracked 
| 112|0x000000062a000000, 0x000000062a000000, 0x000000062a400000|  0%| F|  |TAMS 0x000000062a000000| PB 0x000000062a000000| Untracked 
| 113|0x000000062a400000, 0x000000062a400000, 0x000000062a800000|  0%| F|  |TAMS 0x000000062a400000| PB 0x000000062a400000| Untracked 
| 114|0x000000062a800000, 0x000000062a800000, 0x000000062ac00000|  0%| F|  |TAMS 0x000000062a800000| PB 0x000000062a800000| Untracked 
| 115|0x000000062ac00000, 0x000000062ac00000, 0x000000062b000000|  0%| F|  |TAMS 0x000000062ac00000| PB 0x000000062ac00000| Untracked 
| 116|0x000000062b000000, 0x000000062b400000, 0x000000062b400000|100%|HS|  |TAMS 0x000000062b400000| PB 0x000000062b000000| Complete 
| 117|0x000000062b400000, 0x000000062b800000, 0x000000062b800000|100%|HC|  |TAMS 0x000000062b800000| PB 0x000000062b400000| Complete 
| 118|0x000000062b800000, 0x000000062bc00000, 0x000000062bc00000|100%|HC|  |TAMS 0x000000062bc00000| PB 0x000000062b800000| Complete 
| 119|0x000000062bc00000, 0x000000062c000000, 0x000000062c000000|100%|HC|  |TAMS 0x000000062c000000| PB 0x000000062bc00000| Complete 
| 120|0x000000062c000000, 0x000000062c400000, 0x000000062c400000|100%|HC|  |TAMS 0x000000062c400000| PB 0x000000062c000000| Complete 
| 121|0x000000062c400000, 0x000000062c400000, 0x000000062c800000|  0%| F|  |TAMS 0x000000062c400000| PB 0x000000062c400000| Untracked 
| 122|0x000000062c800000, 0x000000062c800000, 0x000000062cc00000|  0%| F|  |TAMS 0x000000062c800000| PB 0x000000062c800000| Untracked 
| 123|0x000000062cc00000, 0x000000062cc00000, 0x000000062d000000|  0%| F|  |TAMS 0x000000062cc00000| PB 0x000000062cc00000| Untracked 
| 124|0x000000062d000000, 0x000000062d000000, 0x000000062d400000|  0%| F|  |TAMS 0x000000062d000000| PB 0x000000062d000000| Untracked 
| 125|0x000000062d400000, 0x000000062d800000, 0x000000062d800000|100%|HS|  |TAMS 0x000000062d800000| PB 0x000000062d400000| Complete 
| 126|0x000000062d800000, 0x000000062dc00000, 0x000000062dc00000|100%|HC|  |TAMS 0x000000062dc00000| PB 0x000000062d800000| Complete 
| 127|0x000000062dc00000, 0x000000062e000000, 0x000000062e000000|100%|HC|  |TAMS 0x000000062e000000| PB 0x000000062dc00000| Complete 
| 128|0x000000062e000000, 0x000000062e400000, 0x000000062e400000|100%|HC|  |TAMS 0x000000062e400000| PB 0x000000062e000000| Complete 
| 129|0x000000062e400000, 0x000000062e800000, 0x000000062e800000|100%|HC|  |TAMS 0x000000062e800000| PB 0x000000062e400000| Complete 
| 130|0x000000062e800000, 0x000000062e800000, 0x000000062ec00000|  0%| F|  |TAMS 0x000000062e800000| PB 0x000000062e800000| Untracked 
| 131|0x000000062ec00000, 0x000000062ec47870, 0x000000062f000000|  6%| E|  |TAMS 0x000000062ec00000| PB 0x000000062ec00000| Complete 
|1990|0x00000007ff800000, 0x00000007ffc00000, 0x00000007ffc00000|100%| E|CS|TAMS 0x00000007ff800000| PB 0x00000007ff800000| Complete 
|1991|0x00000007ffc00000, 0x0000000800000000, 0x0000000800000000|100%| O|  |TAMS 0x0000000800000000| PB 0x0000000800000000| Updating 

Card table byte_map: [0x0000013b4f650000,0x0000013b505e0000] _byte_map_base: 0x0000013b4c5e0000

Marking Bits: (CMBitMap*) 0x0000013b3713f810
 Bits: [0x0000013b51570000, 0x0000013b591f0000)

Polling page: 0x0000013b34fc0000

Metaspace:

Usage:
  Non-class:     50.89 MB used.
      Class:      6.45 MB used.
       Both:     57.34 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      51.25 MB ( 80%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       6.75 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      58.00 MB (  5%) committed. 

Chunk freelists:
   Non-Class:  96.00 KB
       Class:  1.12 MB
        Both:  1.22 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 94.62 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 942.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 928.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 2347.
num_chunk_merges: 0.
num_chunk_splits: 1484.
num_chunks_enlarged: 929.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=7116Kb max_used=7116Kb free=112883Kb
 bounds [0x0000013b46c40000, 0x0000013b47340000, 0x0000013b4e170000]
CodeHeap 'profiled nmethods': size=120000Kb used=17065Kb max_used=19853Kb free=102934Kb
 bounds [0x0000013b3f170000, 0x0000013b404e0000, 0x0000013b466a0000]
CodeHeap 'non-nmethods': size=5760Kb used=2393Kb max_used=2473Kb free=3366Kb
 bounds [0x0000013b466a0000, 0x0000013b46920000, 0x0000013b46c40000]
 total_blobs=9346 nmethods=8305 adapters=948
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 558.499 Thread 0x0000013b5bedfaf0 nmethod 13628 0x0000013b402cf390 code [0x0000013b402cf5a0, 0x0000013b402cfb00]
Event: 558.499 Thread 0x0000013b5bedfaf0 13630       3       java.util.LinkedList$DescendingIterator::next (8 bytes)
Event: 558.499 Thread 0x0000013b5bedfaf0 nmethod 13630 0x0000013b402cef90 code [0x0000013b402cf120, 0x0000013b402cf278]
Event: 558.500 Thread 0x0000013b5bedfaf0 13632       3       java.security.Signature::update (91 bytes)
Event: 558.500 Thread 0x0000013b5bedfaf0 nmethod 13632 0x0000013b402ce390 code [0x0000013b402ce5a0, 0x0000013b402cece0]
Event: 558.500 Thread 0x0000013b5bedfaf0 13633       1       sun.security.ec.XECOperations::getParameters (5 bytes)
Event: 558.500 Thread 0x0000013b5bedfaf0 nmethod 13633 0x0000013b4732fd90 code [0x0000013b4732ff00, 0x0000013b4732ffd0]
Event: 558.501 Thread 0x0000013b5bedfaf0 13634       1       sun.security.ec.XECParameters::getName (5 bytes)
Event: 558.501 Thread 0x0000013b5bedfaf0 nmethod 13634 0x0000013b47330090 code [0x0000013b47330200, 0x0000013b473302d0]
Event: 558.502 Thread 0x0000013b6f8c88c0 nmethod 13605 0x0000013b47330390 code [0x0000013b47330580, 0x0000013b47331318]
Event: 558.503 Thread 0x0000013b6f8c88c0 13631 %     4       sun.security.util.DomainName$Rules$RuleSet::match @ 12 (143 bytes)
Event: 558.504 Thread 0x0000013b5bede9d0 nmethod 13596 0x0000013b47331990 code [0x0000013b47331b40, 0x0000013b47332268]
Event: 558.504 Thread 0x0000013b5bede9d0 13629       4       sun.security.ssl.SSLCipher$T12GcmWriteCipherGenerator$GcmWriteCipher::calculateFragmentSize (14 bytes)
Event: 558.505 Thread 0x0000013b5bede9d0 nmethod 13629 0x0000013b47332590 code [0x0000013b47332700, 0x0000013b47332798]
Event: 558.505 Thread 0x0000013b5bede9d0 13607       4       org.littleshoot.proxy.HttpFiltersAdapter::proxyToServerRequestSent (1 bytes)
Event: 558.505 Thread 0x0000013b5bede9d0 nmethod 13607 0x0000013b47332890 code [0x0000013b47332a00, 0x0000013b47332a88]
Event: 558.505 Thread 0x0000013b5bede9d0 13606       4       org.littleshoot.proxy.HttpFiltersAdapter::proxyToServerRequestSending (1 bytes)
Event: 558.506 Thread 0x0000013b5bede9d0 nmethod 13606 0x0000013b47332b90 code [0x0000013b47332d00, 0x0000013b47332d88]
Event: 558.506 Thread 0x0000013b5bede9d0 13620       4       java.lang.String::scale (7 bytes)
Event: 558.506 Thread 0x0000013b5bede9d0 nmethod 13620 0x0000013b47332e90 code [0x0000013b47333000, 0x0000013b47333090]

GC Heap History (20 events):
Event: 520.588 GC heap before
{Heap before GC invocations=16 (full 0):
 garbage-first heap   total 204800K, used 161959K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 30 young (122880K), 2 survivors (8192K)
 Metaspace       used 44722K, committed 45376K, reserved 1114112K
  class space    used 4872K, committed 5184K, reserved 1048576K
}
Event: 520.592 GC heap after
{Heap after GC invocations=17 (full 0):
 garbage-first heap   total 204800K, used 53858K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 44722K, committed 45376K, reserved 1114112K
  class space    used 4872K, committed 5184K, reserved 1048576K
}
Event: 522.512 GC heap before
{Heap before GC invocations=17 (full 0):
 garbage-first heap   total 204800K, used 156258K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 20 young (81920K), 3 survivors (12288K)
 Metaspace       used 53298K, committed 53888K, reserved 1114112K
  class space    used 5994K, committed 6272K, reserved 1048576K
}
Event: 522.520 GC heap after
{Heap after GC invocations=18 (full 0):
 garbage-first heap   total 204800K, used 100072K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 53298K, committed 53888K, reserved 1114112K
  class space    used 5994K, committed 6272K, reserved 1048576K
}
Event: 522.688 GC heap before
{Heap before GC invocations=19 (full 0):
 garbage-first heap   total 221184K, used 145128K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 10 young (40960K), 3 survivors (12288K)
 Metaspace       used 53592K, committed 54208K, reserved 1114112K
  class space    used 6034K, committed 6336K, reserved 1048576K
}
Event: 522.693 GC heap after
{Heap after GC invocations=20 (full 0):
 garbage-first heap   total 221184K, used 120538K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 53592K, committed 54208K, reserved 1114112K
  class space    used 6034K, committed 6336K, reserved 1048576K
}
Event: 525.012 GC heap before
{Heap before GC invocations=21 (full 0):
 garbage-first heap   total 274432K, used 267994K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 18 young (73728K), 2 survivors (8192K)
 Metaspace       used 56612K, committed 57280K, reserved 1114112K
  class space    used 6481K, committed 6784K, reserved 1048576K
}
Event: 525.018 GC heap after
{Heap after GC invocations=22 (full 0):
 garbage-first heap   total 282624K, used 205187K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 56612K, committed 57280K, reserved 1114112K
  class space    used 6481K, committed 6784K, reserved 1048576K
}
Event: 526.776 GC heap before
{Heap before GC invocations=22 (full 0):
 garbage-first heap   total 323584K, used 307587K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 13 young (53248K), 2 survivors (8192K)
 Metaspace       used 57364K, committed 58048K, reserved 1114112K
  class space    used 6568K, committed 6848K, reserved 1048576K
}
Event: 526.781 GC heap after
{Heap after GC invocations=23 (full 0):
 garbage-first heap   total 331776K, used 268842K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 57364K, committed 58048K, reserved 1114112K
  class space    used 6568K, committed 6848K, reserved 1048576K
}
Event: 526.830 GC heap before
{Heap before GC invocations=23 (full 0):
 garbage-first heap   total 331776K, used 277034K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 5 young (20480K), 2 survivors (8192K)
 Metaspace       used 57431K, committed 58048K, reserved 1114112K
  class space    used 6568K, committed 6848K, reserved 1048576K
}
Event: 526.833 GC heap after
{Heap after GC invocations=24 (full 0):
 garbage-first heap   total 331776K, used 269750K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 57431K, committed 58048K, reserved 1114112K
  class space    used 6568K, committed 6848K, reserved 1048576K
}
Event: 526.870 GC heap before
{Heap before GC invocations=25 (full 0):
 garbage-first heap   total 471040K, used 282038K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 5 young (20480K), 1 survivors (4096K)
 Metaspace       used 57471K, committed 58112K, reserved 1114112K
  class space    used 6568K, committed 6848K, reserved 1048576K
}
Event: 526.873 GC heap after
{Heap after GC invocations=26 (full 0):
 garbage-first heap   total 471040K, used 278361K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 57471K, committed 58112K, reserved 1114112K
  class space    used 6568K, committed 6848K, reserved 1048576K
}
Event: 530.861 GC heap before
{Heap before GC invocations=27 (full 0):
 garbage-first heap   total 487424K, used 446297K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 39 young (159744K), 2 survivors (8192K)
 Metaspace       used 57837K, committed 58496K, reserved 1114112K
  class space    used 6575K, committed 6848K, reserved 1048576K
}
Event: 530.864 GC heap after
{Heap after GC invocations=28 (full 0):
 garbage-first heap   total 487424K, used 293347K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 57837K, committed 58496K, reserved 1114112K
  class space    used 6575K, committed 6848K, reserved 1048576K
}
Event: 556.999 GC heap before
{Heap before GC invocations=28 (full 0):
 garbage-first heap   total 528384K, used 518627K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 29 young (118784K), 3 survivors (12288K)
 Metaspace       used 58476K, committed 59136K, reserved 1114112K
  class space    used 6597K, committed 6848K, reserved 1048576K
}
Event: 557.004 GC heap after
{Heap after GC invocations=29 (full 0):
 garbage-first heap   total 548864K, used 420133K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 5 young (20480K), 5 survivors (20480K)
 Metaspace       used 58476K, committed 59136K, reserved 1114112K
  class space    used 6597K, committed 6848K, reserved 1048576K
}
Event: 558.477 GC heap before
{Heap before GC invocations=29 (full 0):
 garbage-first heap   total 548864K, used 489765K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 23 young (94208K), 5 survivors (20480K)
 Metaspace       used 58712K, committed 59392K, reserved 1114112K
  class space    used 6603K, committed 6912K, reserved 1048576K
}
Event: 558.495 GC heap after
{Heap after GC invocations=30 (full 0):
 garbage-first heap   total 548864K, used 423118K [0x000000060e000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 58712K, committed 59392K, reserved 1114112K
  class space    used 6603K, committed 6912K, reserved 1048576K
}

Dll operation events (19 events):
Event: 0.010 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\java.dll
Event: 0.017 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\jsvml.dll
Event: 0.100 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\zip.dll
Event: 0.104 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\instrument.dll
Event: 0.110 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\net.dll
Event: 0.112 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\nio.dll
Event: 0.114 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\zip.dll
Event: 0.224 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\jimage.dll
Event: 0.277 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\extnet.dll
Event: 0.287 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\management.dll
Event: 0.301 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\management_ext.dll
Event: 0.325 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\awt.dll
Event: 0.417 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\verify.dll
Event: 0.828 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\sqlite-********-e8282477-bfd1-4d8c-a18c-391e18269526-sqlitejdbc.dll
Event: 1.141 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\freetype.dll
Event: 1.145 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\fontmanager.dll
Event: 1.963 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\flatlaf.temp\flatlaf-windows-x86_64-17060927481000.dll
Event: 6.823 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\webp-imageio-unknown-c701de3f-5128-48e9-b2f1-fb49bd7f501e-webp-imageio.dll
Event: 7.163 Loaded shared library C:\Users\<USER>\.jdks\openjdk-20\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 558.101 Thread 0x0000013b6f909710 DEOPT PACKING pc=0x0000013b3fb4c44d sp=0x00000035790fdc50
Event: 558.101 Thread 0x0000013b6f909710 DEOPT UNPACKING pc=0x0000013b4670e462 sp=0x00000035790fd168 mode 0
Event: 558.101 Thread 0x0000013b6f90a700 DEOPT PACKING pc=0x0000013b3fb4c44d sp=0x0000003578cfdd90
Event: 558.101 Thread 0x0000013b6f90a700 DEOPT UNPACKING pc=0x0000013b4670e462 sp=0x0000003578cfd2a8 mode 0
Event: 558.101 Thread 0x0000013b61702370 DEOPT PACKING pc=0x0000013b3fb4c44d sp=0x00000035776fd810
Event: 558.101 Thread 0x0000013b61702370 DEOPT UNPACKING pc=0x0000013b4670e462 sp=0x00000035776fcd28 mode 0
Event: 558.106 Thread 0x0000013b61701e20 DEOPT PACKING pc=0x0000013b3fb4c44d sp=0x0000003578bfde90
Event: 558.106 Thread 0x0000013b61701e20 DEOPT UNPACKING pc=0x0000013b4670e462 sp=0x0000003578bfd3a8 mode 0
Event: 558.338 Thread 0x0000013b60b70950 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000013b46c45db4 relative=0x0000000000000194
Event: 558.338 Thread 0x0000013b60b70950 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000013b46c45db4 method=org.openqa.selenium.json.Input.fill()Z @ 85 c2
Event: 558.338 Thread 0x0000013b60b70950 DEOPT PACKING pc=0x0000013b46c45db4 sp=0x00000035742fe790
Event: 558.338 Thread 0x0000013b60b70950 DEOPT UNPACKING pc=0x0000013b466bd2a2 sp=0x00000035742fe730 mode 2
Event: 558.431 Thread 0x0000013b60b71e90 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000013b46dd9084 relative=0x0000000000000084
Event: 558.431 Thread 0x0000013b60b71e90 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000013b46dd9084 method=java.util.Collections$3.hasMoreElements()Z @ 4 c2
Event: 558.431 Thread 0x0000013b60b71e90 DEOPT PACKING pc=0x0000013b46dd9084 sp=0x0000003577bfd950
Event: 558.431 Thread 0x0000013b60b71e90 DEOPT UNPACKING pc=0x0000013b466bd2a2 sp=0x0000003577bfd8e0 mode 2
Event: 558.431 Thread 0x0000013b60b71e90 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000013b46f13f24 relative=0x0000000000000224
Event: 558.431 Thread 0x0000013b60b71e90 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000013b46f13f24 method=java.util.Collections$3.nextElement()Ljava/lang/Object; @ 4 c2
Event: 558.431 Thread 0x0000013b60b71e90 DEOPT PACKING pc=0x0000013b46f13f24 sp=0x0000003577bfd920
Event: 558.431 Thread 0x0000013b60b71e90 DEOPT UNPACKING pc=0x0000013b466bd2a2 sp=0x0000003577bfd8e0 mode 2

Classes loaded (20 events):
Event: 556.056 Loading class sun/security/util/math/IntegerModuloP$MultiplicativeInverser$Secp256R1
Event: 556.056 Loading class sun/security/util/math/IntegerModuloP$MultiplicativeInverser$Secp256R1 done
Event: 556.338 Loading class java/nio/StringCharBuffer
Event: 556.338 Loading class java/nio/StringCharBuffer done
Event: 556.356 Loading class java/util/zip/CheckedInputStream
Event: 556.357 Loading class java/util/zip/CheckedInputStream done
Event: 556.358 Loading class java/io/SequenceInputStream
Event: 556.359 Loading class java/io/SequenceInputStream done
Event: 556.359 Loading class java/util/zip/GZIPInputStream$1
Event: 556.359 Loading class java/util/zip/GZIPInputStream$1 done
Event: 557.479 Loading class sun/security/ssl/Alert$AlertMessage
Event: 557.479 Loading class sun/security/ssl/Alert$AlertMessage done
Event: 557.480 Loading class sun/net/ConnectionResetException
Event: 557.480 Loading class sun/net/ConnectionResetException done
Event: 557.481 Loading class java/lang/StackTraceElement$HashedModules
Event: 557.481 Loading class java/lang/StackTraceElement$HashedModules done
Event: 557.482 Loading class java/nio/channels/DatagramChannel
Event: 557.482 Loading class java/nio/channels/MulticastChannel
Event: 557.482 Loading class java/nio/channels/MulticastChannel done
Event: 557.482 Loading class java/nio/channels/DatagramChannel done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 556.582 Thread 0x0000013b60b72e80 Implicit null exception at 0x0000013b3f4f779f to 0x0000013b3f4f836c
Event: 556.582 Thread 0x0000013b6f90a700 Implicit null exception at 0x0000013b3f4f779f to 0x0000013b3f4f836c
Event: 556.582 Thread 0x0000013b60b72e80 Exception <a 'java/lang/NullPointerException'{0x00000006277416f8}> (0x00000006277416f8) 
thrown [s\open\src\hotspot\share\runtime\sharedRuntime.cpp, line 709]
Event: 556.582 Thread 0x0000013b6f90a700 Exception <a 'java/lang/NullPointerException'{0x0000000627093d30}> (0x0000000627093d30) 
thrown [s\open\src\hotspot\share\runtime\sharedRuntime.cpp, line 709]
Event: 557.230 Thread 0x0000013b6f90ac50 Implicit null exception at 0x0000013b3f4f779f to 0x0000013b3f4f836c
Event: 557.230 Thread 0x0000013b6f90ac50 Exception <a 'java/lang/NullPointerException'{0x000000062ae934f8}> (0x000000062ae934f8) 
thrown [s\open\src\hotspot\share\runtime\sharedRuntime.cpp, line 709]
Event: 557.480 Thread 0x0000013b6f90c6e0 Exception <a 'java/io/IOException'{0x000000062a20bd90}> (0x000000062a20bd90) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 519]
Event: 557.480 Thread 0x0000013b61702e10 Exception <a 'java/io/IOException'{0x00000007ff836170}> (0x00000007ff836170) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 519]
Event: 557.480 Thread 0x0000013b6f90b6f0 Exception <a 'java/io/IOException'{0x00000006293ab590}> (0x00000006293ab590) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 519]
Event: 557.480 Thread 0x0000013b616fc920 Exception <a 'java/io/IOException'{0x00000007ff9e3e48}> (0x00000007ff9e3e48) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 519]
Event: 557.482 Thread 0x0000013b6f90f160 Exception <a 'java/io/IOException'{0x000000062d28efd0}> (0x000000062d28efd0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 519]
Event: 557.483 Thread 0x0000013b616fc920 Exception <a 'java/io/IOException'{0x00000007ff9e56e8}> (0x00000007ff9e56e8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 519]
Event: 557.494 Thread 0x0000013b61703360 Exception <a 'java/io/IOException'{0x000000062d20efc0}> (0x000000062d20efc0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 519]
Event: 557.506 Thread 0x0000013b60b723e0 Exception <a 'java/io/IOException'{0x000000062d01fdc0}> (0x000000062d01fdc0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 519]
Event: 557.509 Thread 0x0000013b60b72930 Exception <a 'java/io/IOException'{0x000000062d1aefc0}> (0x000000062d1aefc0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 519]
Event: 557.515 Thread 0x0000013b61702e10 Exception <a 'java/io/IOException'{0x00000007ff83cb50}> (0x00000007ff83cb50) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 519]
Event: 557.739 Thread 0x0000013b6f90c6e0 Exception <a 'java/io/IOException'{0x000000062a24c6e0}> (0x000000062a24c6e0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 519]
Event: 558.429 Thread 0x0000013b61703360 Exception <a 'java/io/IOException'{0x000000062783a720}> (0x000000062783a720) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 519]
Event: 558.462 Thread 0x0000013b6f910150 Implicit null exception at 0x0000013b3f4f779f to 0x0000013b3f4f836c
Event: 558.462 Thread 0x0000013b6f910150 Exception <a 'java/lang/NullPointerException'{0x000000062701a6b0}> (0x000000062701a6b0) 
thrown [s\open\src\hotspot\share\runtime\sharedRuntime.cpp, line 709]

VM Operations (20 events):
Event: 532.877 Executing VM operation: Cleanup done
Event: 534.886 Executing VM operation: Cleanup
Event: 534.886 Executing VM operation: Cleanup done
Event: 541.937 Executing VM operation: Cleanup
Event: 541.937 Executing VM operation: Cleanup done
Event: 544.957 Executing VM operation: Cleanup
Event: 544.957 Executing VM operation: Cleanup done
Event: 555.045 Executing VM operation: Cleanup
Event: 555.045 Executing VM operation: Cleanup done
Event: 556.054 Executing VM operation: Cleanup
Event: 556.055 Executing VM operation: Cleanup done
Event: 556.720 Executing VM operation: ICBufferFull
Event: 556.721 Executing VM operation: ICBufferFull done
Event: 556.999 Executing VM operation: G1CollectForAllocation
Event: 557.004 Executing VM operation: G1CollectForAllocation done
Event: 557.733 Executing VM operation: ICBufferFull
Event: 557.733 Executing VM operation: ICBufferFull done
Event: 558.477 Executing VM operation: G1CollectForAllocation
Event: 558.495 Executing VM operation: G1CollectForAllocation done
Event: 558.513 Executing VM operation: G1PauseRemark

Events (20 events):
Event: 534.810 Thread 0x0000013b6f90b6f0 Thread exited: 0x0000013b6f90b6f0
Event: 534.978 Thread 0x0000013b6f8c2a30 Thread exited: 0x0000013b6f8c2a30
Event: 534.978 Thread 0x0000013b6f8c3ae0 Thread exited: 0x0000013b6f8c3ae0
Event: 555.924 Thread 0x0000013b6f90b6f0 Thread added: 0x0000013b6f90b6f0
Event: 555.949 Thread 0x0000013b6f909710 Thread added: 0x0000013b6f909710
Event: 556.010 Thread 0x0000013b6f8c3ae0 Thread added: 0x0000013b6f8c3ae0
Event: 556.015 Thread 0x0000013b6f8c5c40 Thread added: 0x0000013b6f8c5c40
Event: 556.336 Thread 0x0000013b6f8c5c40 Thread exited: 0x0000013b6f8c5c40
Event: 556.493 Thread 0x0000013b6f90f160 Thread added: 0x0000013b6f90f160
Event: 556.495 Thread 0x0000013b6f8c3ae0 Thread exited: 0x0000013b6f8c3ae0
Event: 556.509 Thread 0x0000013b6f910150 Thread added: 0x0000013b6f910150
Event: 556.558 Thread 0x0000013b6f8c5c40 Thread added: 0x0000013b6f8c5c40
Event: 558.468 Thread 0x0000013b6f8c88c0 Thread added: 0x0000013b6f8c88c0
Event: 558.545 Thread 0x0000013b5be9faf0 flushing nmethod 0x0000013b40455890
Event: 558.545 Thread 0x0000013b5be9faf0 flushing nmethod 0x0000013b3fd83c10
Event: 558.545 Thread 0x0000013b5be9faf0 flushing nmethod 0x0000013b3fc4a090
Event: 558.545 Thread 0x0000013b5be9faf0 flushing nmethod 0x0000013b3f650e90
Event: 558.545 Thread 0x0000013b5be9faf0 flushing nmethod 0x0000013b3f395f90
Event: 558.545 Thread 0x0000013b5be9faf0 flushing nmethod 0x0000013b3f3d2590
Event: 558.545 Thread 0x0000013b5be9faf0 flushing nmethod 0x0000013b3f266110


Dynamic libraries:
0x00007ff72eb80000 - 0x00007ff72eb8e000 	C:\Users\<USER>\.jdks\openjdk-20\bin\java.exe
0x00007ffa54b00000 - 0x00007ffa54d67000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffa52f90000 - 0x00007ffa53059000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffa51e10000 - 0x00007ffa52200000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffa51c30000 - 0x00007ffa51d7b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffa39a00000 - 0x00007ffa39a1b000 	C:\Users\<USER>\.jdks\openjdk-20\bin\VCRUNTIME140.dll
0x00007ffa38cc0000 - 0x00007ffa38cd7000 	C:\Users\<USER>\.jdks\openjdk-20\bin\jli.dll
0x00007ffa54450000 - 0x00007ffa54615000 	C:\WINDOWS\System32\USER32.dll
0x00007ffa324d0000 - 0x00007ffa3276a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4768_none_3e0c112ce331287c\COMCTL32.dll
0x00007ffa525b0000 - 0x00007ffa525d7000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa538d0000 - 0x00007ffa53979000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffa52ee0000 - 0x00007ffa52f0b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffa525e0000 - 0x00007ffa52718000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffa52720000 - 0x00007ffa527c3000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffa52db0000 - 0x00007ffa52ddf000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffa38cb0000 - 0x00007ffa38cbc000 	C:\Users\<USER>\.jdks\openjdk-20\bin\vcruntime140_1.dll
0x00007ff9a1240000 - 0x00007ff9a12ce000 	C:\Users\<USER>\.jdks\openjdk-20\bin\msvcp140.dll
0x00007ff910f00000 - 0x00007ff911b97000 	C:\Users\<USER>\.jdks\openjdk-20\bin\server\jvm.dll
0x00007ffa54240000 - 0x00007ffa542f4000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffa53a40000 - 0x00007ffa53ae6000 	C:\WINDOWS\System32\sechost.dll
0x00007ffa54640000 - 0x00007ffa54758000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffa27fc0000 - 0x00007ffa27fca000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffa45e80000 - 0x00007ffa45eb5000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffa4afe0000 - 0x00007ffa4afeb000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffa52f10000 - 0x00007ffa52f84000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffa509b0000 - 0x00007ffa509cb000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffa2a1f0000 - 0x00007ffa2a1fa000 	C:\Users\<USER>\.jdks\openjdk-20\bin\jimage.dll
0x00007ffa44210000 - 0x00007ffa44451000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffa52a00000 - 0x00007ffa52d85000 	C:\WINDOWS\System32\combase.dll
0x00007ffa52df0000 - 0x00007ffa52ed0000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffa31350000 - 0x00007ffa31393000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffa52210000 - 0x00007ffa522a9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffa2a1e0000 - 0x00007ffa2a1ee000 	C:\Users\<USER>\.jdks\openjdk-20\bin\instrument.dll
0x00007ff94e2f0000 - 0x00007ff94e316000 	C:\Users\<USER>\.jdks\openjdk-20\bin\java.dll
0x00007ff94d930000 - 0x00007ff94da07000 	C:\Users\<USER>\.jdks\openjdk-20\bin\jsvml.dll
0x00007ffa53af0000 - 0x00007ffa5423d000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffa52430000 - 0x00007ffa525a3000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffa4f800000 - 0x00007ffa50060000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffa549c0000 - 0x00007ffa54ab5000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffa54380000 - 0x00007ffa543ea000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffa51b50000 - 0x00007ffa51b79000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff94d910000 - 0x00007ff94d928000 	C:\Users\<USER>\.jdks\openjdk-20\bin\zip.dll
0x00007ff9a1220000 - 0x00007ff9a1233000 	C:\Users\<USER>\.jdks\openjdk-20\bin\net.dll
0x00007ffa4ab70000 - 0x00007ffa4ac8e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffa50f40000 - 0x00007ffa50fab000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff94e2d0000 - 0x00007ff94e2e6000 	C:\Users\<USER>\.jdks\openjdk-20\bin\nio.dll
0x00007ffa50400000 - 0x00007ffa50526000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffa50370000 - 0x00007ffa503a3000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffa52da0000 - 0x00007ffa52daa000 	C:\WINDOWS\System32\NSI.dll
0x00007ffa450c0000 - 0x00007ffa450cb000 	C:\Windows\System32\rasadhlp.dll
0x00007ff94d900000 - 0x00007ff94d907000 	C:\Users\<USER>\.jdks\openjdk-20\bin\extnet.dll
0x00007ffa4a2c0000 - 0x00007ffa4a346000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ff94d8f0000 - 0x00007ff94d8fa000 	C:\Users\<USER>\.jdks\openjdk-20\bin\management.dll
0x00007ff94d8e0000 - 0x00007ff94d8eb000 	C:\Users\<USER>\.jdks\openjdk-20\bin\management_ext.dll
0x00007ffa52d90000 - 0x00007ffa52d98000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff94d750000 - 0x00007ff94d8e0000 	C:\Users\<USER>\.jdks\openjdk-20\bin\awt.dll
0x00007ffa4ec40000 - 0x00007ffa4ecde000 	C:\WINDOWS\SYSTEM32\apphelp.dll
0x00007ff94d740000 - 0x00007ff94d750000 	C:\Users\<USER>\.jdks\openjdk-20\bin\verify.dll
0x00007ffa51200000 - 0x00007ffa5121b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffa508f0000 - 0x00007ffa5092b000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffa50fe0000 - 0x00007ffa5100b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffa51b20000 - 0x00007ffa51b46000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffa51220000 - 0x00007ffa5122c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffa4aff0000 - 0x00007ffa4b00f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffa4a870000 - 0x00007ffa4a895000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffa33290000 - 0x00007ffa332a8000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffa33270000 - 0x00007ffa33282000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffa33230000 - 0x00007ffa33260000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007ffa4a630000 - 0x00007ffa4a650000 	C:\WINDOWS\system32\wshbth.dll
0x00007ffa2a250000 - 0x00007ffa2a29e000 	C:\WINDOWS\SYSTEM32\pdh.dll
0x00007ff94d640000 - 0x00007ff94d72c000 	C:\Users\<USER>\AppData\Local\Temp\sqlite-********-e8282477-bfd1-4d8c-a18c-391e18269526-sqlitejdbc.dll
0x00007ffa4ed70000 - 0x00007ffa4ee1f000 	C:\WINDOWS\system32\uxtheme.dll
0x00007ffa52890000 - 0x00007ffa529f1000 	C:\WINDOWS\System32\MSCTF.dll
0x00007ffa53720000 - 0x00007ffa538c0000 	C:\WINDOWS\System32\ole32.dll
0x00007ffa4f140000 - 0x00007ffa4f175000 	C:\WINDOWS\system32\DWMAPI.DLL
0x00007ff94d570000 - 0x00007ff94d5f6000 	C:\Users\<USER>\.jdks\openjdk-20\bin\freetype.dll
0x00007ff94d4a0000 - 0x00007ff94d566000 	C:\Users\<USER>\.jdks\openjdk-20\bin\fontmanager.dll
0x00007ff94d620000 - 0x00007ff94d632000 	C:\WINDOWS\System32\perfproc.dll
0x00007ff9f3f00000 - 0x00007ff9f4018000 	C:\WINDOWS\system32\opengl32.dll
0x00007ffa20d70000 - 0x00007ffa20d9d000 	C:\WINDOWS\SYSTEM32\GLU32.dll
0x00007ffa4ef60000 - 0x00007ffa4efad000 	C:\WINDOWS\SYSTEM32\dxcore.dll
0x00007ffa114b0000 - 0x00007ffa1166b000 	C:\WINDOWS\system32\d3d9.dll
0x00007ffa4a8a0000 - 0x00007ffa4a964000 	C:\WINDOWS\System32\DriverStore\FileRepository\nv_dispi.inf_amd64_fe5f369669db2f36\nvldumdx.dll
0x00007ffa51270000 - 0x00007ffa51283000 	C:\WINDOWS\SYSTEM32\msasn1.dll
0x00007ffa4a5f0000 - 0x00007ffa4a62b000 	C:\WINDOWS\SYSTEM32\cryptnet.dll
0x00007ffa522b0000 - 0x00007ffa52427000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffa51350000 - 0x00007ffa513bb000 	C:\WINDOWS\SYSTEM32\wldp.dll
0x00007ffa4a470000 - 0x00007ffa4a5ec000 	C:\WINDOWS\SYSTEM32\drvstore.dll
0x00007ffa517f0000 - 0x00007ffa5181d000 	C:\WINDOWS\SYSTEM32\devobj.dll
0x00007ffa51820000 - 0x00007ffa51877000 	C:\WINDOWS\SYSTEM32\cfgmgr32.dll
0x00007ffa51d80000 - 0x00007ffa51e04000 	C:\WINDOWS\System32\wintrust.dll
0x00007ffa53980000 - 0x00007ffa539a0000 	C:\WINDOWS\System32\imagehlp.dll
0x00007ffa46520000 - 0x00007ffa4a2b2000 	C:\WINDOWS\System32\DriverStore\FileRepository\nv_dispi.inf_amd64_fe5f369669db2f36\nvgpucomp64.dll
0x00007ffa45ec0000 - 0x00007ffa45f9e000 	C:\WINDOWS\System32\DriverStore\FileRepository\nv_dispi.inf_amd64_fe5f369669db2f36\NvMemMapStoragex.dll
0x00007ffa518f0000 - 0x00007ffa5194e000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffa518d0000 - 0x00007ffa518e4000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff9bb7a0000 - 0x00007ff9be2a5000 	C:\WINDOWS\System32\DriverStore\FileRepository\nv_dispi.inf_amd64_fe5f369669db2f36\nvd3dumx.dll
0x00007ffa4bde0000 - 0x00007ffa4be4a000 	C:\WINDOWS\SYSTEM32\directxdatabasehelper.dll
0x00007ffa07150000 - 0x00007ffa07297000 	C:\WINDOWS\system32\nvspcap64.dll
0x00007ffa50ae0000 - 0x00007ffa50b16000 	C:\WINDOWS\SYSTEM32\ntmarta.dll
0x00007ffa50fb0000 - 0x00007ffa50fd7000 	C:\WINDOWS\SYSTEM32\gpapi.dll
0x00007ffa10080000 - 0x00007ffa10237000 	C:\WINDOWS\System32\DriverStore\FileRepository\nv_dispi.inf_amd64_fe5f369669db2f36\nvppex.dll
0x00007ff94d610000 - 0x00007ff94d616000 	C:\Users\<USER>\AppData\Local\Temp\flatlaf.temp\flatlaf-windows-x86_64-17060927481000.dll
0x00007ffa530e0000 - 0x00007ffa53188000 	C:\WINDOWS\System32\clbcatq.dll
0x00007ffa4d430000 - 0x00007ffa4d66f000 	C:\WINDOWS\SYSTEM32\WindowsCodecs.dll
0x00007ffa25200000 - 0x00007ffa2525a000 	C:\WINDOWS\system32\dataexchange.dll
0x00007ffa462e0000 - 0x00007ffa46518000 	C:\WINDOWS\system32\twinapi.appcore.dll
0x00007ff94d600000 - 0x00007ff94d607000 	C:\Users\<USER>\.jdks\openjdk-20\bin\jawt.dll
0x00007ffa34360000 - 0x00007ffa344b1000 	C:\WINDOWS\SYSTEM32\textinputframework.dll
0x00007ffa32770000 - 0x00007ffa327ed000 	C:\WINDOWS\system32\Oleacc.dll
0x00007ffa342f0000 - 0x00007ffa3434e000 	C:\WINDOWS\system32\ApplicationTargetedFeatureDatabase.dll
0x00007ffa4e830000 - 0x00007ffa4e955000 	C:\WINDOWS\SYSTEM32\CoreMessaging.dll
0x00007ffa4b3a0000 - 0x00007ffa4b683000 	C:\WINDOWS\SYSTEM32\CoreUIComponents.dll
0x00007ff9d1100000 - 0x00007ff9d15a9000 	C:\Program Files (x86)\Google\Google Japanese Input\GoogleIMEJaTIP64.dll
0x00007ff94d3d0000 - 0x00007ff94d49c000 	C:\Users\<USER>\AppData\Local\Temp\webp-imageio-unknown-c701de3f-5128-48e9-b2f1-fb49bd7f501e-webp-imageio.dll
0x00007ff94e270000 - 0x00007ff94e27e000 	C:\Users\<USER>\.jdks\openjdk-20\bin\sunmscapi.dll
0x00007ffa51490000 - 0x00007ffa514c0000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffa51440000 - 0x00007ffa5147f000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffa0c840000 - 0x00007ffa0c848000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\.jdks\openjdk-20\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4768_none_3e0c112ce331287c;C:\Users\<USER>\.jdks\openjdk-20\bin\server;C:\Users\<USER>\AppData\Local\Temp;C:\WINDOWS\System32\DriverStore\FileRepository\nv_dispi.inf_amd64_fe5f369669db2f36;C:\Users\<USER>\AppData\Local\Temp\flatlaf.temp;C:\Program Files (x86)\Google\Google Japanese Input

VM Arguments:
jvm_args: -Dvaadin.copilot.pluginDotFilePath=C:\Users\<USER>\git\scrapingmercari\.idea\.copilot-plugin -javaagent:C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\lib\idea_rt.jar=55112 -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 
java_command: com.mrcresearch.screen.main.Application
java_class_path (initial): C:\Users\<USER>\git\scrapingmercari\target\classes;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-java\4.23.0\selenium-java-4.23.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-api\4.23.0\selenium-api-4.23.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-chrome-driver\4.23.0\selenium-chrome-driver-4.23.0.jar;C:\Users\<USER>\.m2\repository\com\google\auto\service\auto-service-annotations\1.1.1\auto-service-annotations-1.1.1.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-chromium-driver\4.23.0\selenium-chromium-driver-4.23.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-json\4.23.0\selenium-json-4.23.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-manager\4.23.0\selenium-manager-4.23.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-devtools-v125\4.23.0\selenium-devtools-v125-4.23.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-devtools-v126\4.23.0\selenium-devtools-v126-4.23.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-devtools-v127\4.23.0\selenium-devtools-v127-4.23.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-devtools-v85\4.23.0\selenium-devtools-v85-4.23.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-edge-driver\4.23.0\selenium-edge-driver-4.23.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-firefox-driver\4.23.0\selenium-firefox-driver-4.23.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-http\4.23.0\selenium-http-4.23.0.jar;C:\Users\<USER>\.m2\repository\dev\failsafe\failsafe\3.3.2\failsafe-3.3.2.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-ie-driver\4.23.0\selenium-ie-driver-4.23.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-remote-driver\4.23.0\selenium-remote-driver-4.23.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\semconv\opentelemetry-semconv\1.25.0-alpha\opentelemetry-semconv-1.25.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-api\1.40.0\opentelemetry-api-1.40.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-context\1.40.0\opentelemetry-context-1.40.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-exporter-logging\1.40.0\opentelemetry-exporter-logging-1.40.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-common\1.40.0\opentelemetry-sdk-common-1.40.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-extension-autoconfigure-spi\1.40.0\opentelemetry-sdk-extension-autoconfigure-spi-1.40.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-extension-autoconfigure\1.40.0\opentelemetry-sdk-extension-autoconfigure-1.40.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-api-incubator\1.40.0-alpha\opentelemetry-api-incubator-1.40.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-trace\1.40.0\opentelemetry-sdk-trace-1.40.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk\1.40.0\opentelemetry-sdk-1.40.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-metrics\1.40.0\opentelemetry-sdk-metrics-1.40.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-logs\1.40.0\opentelemetry-sdk-logs-1.40.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.18\byte-buddy-1.14.18.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-os\4.23.0\selenium-os-4.23.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-exec\1.4.0\commons-exec-1.4.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-safari-driver\4.23.0\selenium-safari-driver-4.23.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-support\4.23.0\selenium-support-4.23.0.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.14.0\commons-io-2.14.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.18.0\commons-lang3-3.18.0.jar;C:\Users\<USER>\.m2\repository\com\browserup\browserup-proxy-core\2.1.2\browserup-proxy-core-2.1.2.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.50.Final\netty-codec-4.1.50.Final.jar;C:\Users\<USER>\.m2\repository\xyz\rogfam\littleproxy\2.0.0-beta-5\littleproxy-2.0.0-beta-5.jar;C:\Users\<USER>\.m2\repository\com\barchart\udt\barchart-udt-bundle\2.3.0\barchart-udt-bundle-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\browserup\browserup-proxy-mitm\2.1.2\browserup-proxy-mitm-2.1.2.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\28.2-jre\guava-28.2-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\2.10.0\checker-qual-2.10.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.3.4\error_prone_annotations-2.3.4.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\com\jcraft\jzlib\1.1.3\jzlib-1.1.3.jar;C:\Users\<USER>\.m2\repository\dnsjava\dnsjava\3.1.0\dnsjava-3.1.0.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-all\4.1.50.Final\netty-all-4.1.50.Final.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcpkix-jdk15on\1.64\bcpkix-jdk15on-1.64.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk15on\1.64\bcprov-jdk15on-1.64.jar;C:\Users\<USER>\.m2\repository\org\javassist\javassist\3.27.0-GA\javassist-3.27.0-GA.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jcl-over-slf4j\1.7.30\jcl-over-slf4j-1.7.30.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.108.Final\netty-codec-http-4.1.108.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.108.Final\netty-common-4.1.108.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.108.Final\netty-buffer-4.1.108.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.108.Final\netty-transport-4.1.108.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.108.Final\netty-resolver-4.1.108.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.108.Final\netty-handler-4.1.108.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.108.Final\netty-transport-native-unix-common-4.1.108.Final.jar;C:\Users\<USER>\.m2\repository\com\aayushatharva\brotli4j\brotli4j\1.15.0\brotli4j-1.15.0.jar;C:\Users\<USER>\.m2\repository\com\aayushatharva\brotli4j\service\1.15.0\service-1.15.0.jar;C:\Users\<USER>\.m2\repository\com\aayushatharva\brotli4j\native-windows-x86_64\1.15.0\native-windows-x86_64-1.15.0.jar;C:\Users\<USER>\.m2\repository\org\brotli\dec\0.1.2\dec-0.1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\client5\httpclient5\5.4.2\httpclient5-5.4.2.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\core5\httpcore5\5.3.3\httpcore5-5.3.3.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\core5\httpcore5-h2\5.3.3\httpcore5-h2-5.3.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.17.2\jackson-databind-2.17.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.17.2\jackson-core-2.17.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.17.2\jackson-annotations-2.17.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-csv\2.17.2\jackson-dataformat-csv-2.17.2.jar;C:\Users\<USER>\.m2\repository\com\miglayout\miglayout-swing\5.0\miglayout-swing-5.0.jar;C:\Users\<USER>\.m2\repository\com\miglayout\miglayout-core\5.0\miglayout-core-5.0.jar;C:\Users\<USER>\.m2\repository\com\formdev\flatlaf\3.6\flatlaf-3.6.jar;C:\Users\<USER>\.m2\repository\com\formdev\flatlaf-extras\3.6\flatlaf-extras-3.6.jar;C:\Users\<USER>\.m2\repository\com\github\weisj\jsvg\1.4.0\jsvg-1.4.0.jar;C:\Users\<USER>\.m2\repository\org\kordamp\ikonli\ikonli-swing\12.4.0\ikonli-swing-12.4.0.jar;C:\Users\<USER>\.m2\repository\org\kordamp\ikonli\ikonli-core\12.4.0\ikonli-core-12.4.0.jar;C:\Users\<USER>\.m2\repository\org\kordamp\ikonli\ikonli-antdesignicons-pack\12.4.0\ikonli-antdesignicons-pack-12.4.0.jar;C:\Users\<USER>\.m2\repository\org\kordamp\ikonli\ikonli-fontawesome5-pack\12.4.0\ikonli-fontawesome5-pack-12.4.0.jar;C:\Users\<USER>\.m2\repository\org\xerial\sqlite-jdbc\********\sqlite-jdbc-********.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.30\lombok-1.18.30.jar;C:\Users\<USER>\.m2\repository\io\github\darkxanter\webp-imageio\0.3.3\webp-imageio-0.3.3.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.32\slf4j-api-1.7.32.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.6\logback-classic-1.2.6.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.6\logback-core-1.2.6.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 524288000                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8355053568                                {product} {ergonomic}
   size_t MaxNewSize                               = 5012193280                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8355053568                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Users\<USER>\.jdks\jbrsdk_jcef-21.0.7
PATH=C:\Program Files (x86)\Razer Chroma SDK\bin;C:\Program Files\Razer Chroma SDK\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\VMware\VMware Player\bin\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\dotnet\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\platform-tools;C:\TDM-GCC-64\bin;C:\Program Files\Java\jdk-18.0.2\bin;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;%JAVA_OHME%\bin;C:\Program Files (x86)\Paragon Software\APFS for Windows\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\Program Files (x86)\Razer\ChromaBroadcast\bin;C:\Program Files\Razer\ChromaBroadcast\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Program Files\JetBrains\IntelliJ IDEA 2024.2.1\bin;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Program Files\JetBrains\IntelliJ IDEA 2024.2.1\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=chooi
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 117 Stepping 2, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4768)
OS uptime: 0 days 0:37 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (12 cores per cpu, 2 threads per core) family 25 model 117 stepping 2 microcode 0xa705205, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ss, avx512_ifma

Memory: 4k page, system-wide physical 31867M (3104M free)
TotalPageFile size 49623M (AvailPageFile size 12M)
current process WorkingSet (physical memory assigned to process): 1019M, peak: 1021M
current process commit charge ("private bytes"): 1184M, peak: 1356M

vm_info: OpenJDK 64-Bit Server VM (20+36-2344) for windows-amd64 JRE (20+36-2344), built on 2023-02-10T19:30:15Z by "mach5one" with MS VC++ 17.1 (VS2022)

END.
