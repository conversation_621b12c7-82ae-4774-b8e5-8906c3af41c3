# scrapingmercari

## デプロイ方法について

すべて、Intellijの上で操作する前提で書いていく

### 共通で必要なこと

maven buildで下記コマンドを実行する

```shell
mvn clean install
```

そうすると、依存関係を含んだjarが吐き出されるので、それを使ってdeployしていく

#### Intellijの端末を開いて、一旦場所を移動して、バージョンの設定をする

```shell
cd BuildApp
version="3.3.6"
```

#### jdepsで必要なものを確認する

``` shell
$$deps=jdeps --print-module-deps --ignore-missing-deps ..\target\MrcSoldoutResearchTool-$version.jar
```

```shell
echo $deps
```

#### jdepsで必要なものの一覧がずれているか確認する

ローカルになければコイツラをいれる必要あり（JREをバンドルしたい） java.base,java.desktop,java.logging

> Windowsの場合
> ```shell
> rm jre/windows/
> ```
> ```shell
>  module_path="C:/Users/<USER>/.jdks/openjdk-20.0.2"
>  out_path="jre/windows"
> ```
> Macの場合
> ```shell
> rm -rf jre/mac
> ```
> ```shell
>  $module_path=""
>  $out_path="jre/mac"
> ```

最後はこいつが必要

```shell
jlink --module-path $module_path --add-modules $deps --output $out_path
```

### 共通でデプロイに必要な変数の設定

```shell
 appname="MrcResearch"
 mainjar="MrcSoldoutResearchTool.jar"
 mainclass="com.mrcrsch.main.Main"
```

### Windowsでのデプロイ

```shell
 icon_path="icons/icon.ico"
```

```shell
cp ../target/MrcSoldoutResearchTool-$version-jar-with-dependencies.jar ./input/$mainjar
```

```shell
jpackage=C:/Users/<USER>/.jdks/openjdk-22.0.2/bin/jpackage.exe
$$jpackage --type exe --win-menu --win-dir-chooser --win-shortcut --win-per-user-install --win-shortcut-prompt --icon $icon_path --name $appname --win-menu-group $appname --app-version $version --input input\ --main-jar $mainjar --main-class $mainclass --dest output/
```

### Macでのデプロイ

```shell
version="2.4.2"
icon_path="icons/icon.icns"
```

```shell
jpackage --type dmg --icon $icon_path --name $appname --app-version $version --input input/ --main-jar $mainjar --main-class $mainclass --dest output/
```

