# Netty脆弱性への対応計画

## 1. 概要

本プロジェクトで利用している`io.netty`ライブラリに関する脆弱性の報告を受け、その対応計画を策定します。

プロジェクトの依存関係を調査した結果、Nettyは以下の2つの方法で利用されています。

1.  **直接的な依存**: `pom.xml`で`io.netty:netty-codec-http`が直接依存関係として定義されており、コード内で`HttpResponseStatus`クラスが利用されています。
2.  **間接的な依存**: `com.browserup:browserup-proxy-core`ライブラリが内部でNettyに依存しています。これは推測であり、`mvn dependency:tree`で確認する必要があります。

## 2. 対応計画

以下の2段階で対応を進めます。

### ステップ1: 直接的な依存の除去（短期的な対策）

これは影響範囲が限定的で、すぐに対応可能な修正です。

- **作業内容**:
    1. `src/main/java/com/mrcresearch/service/auth/service/Auth.java`を修正:
        - `import io.netty.handler.codec.http.HttpResponseStatus;`を削除します。
        - `HttpResponseStatus.OK.code()` を `java.net.HttpURLConnection.HTTP_OK` に置換します。
    2. `src/main/java/com/mrcresearch/service/common/Version.java`を修正:
        - `import io.netty.handler.codec.http.HttpResponseStatus;`を削除します。
        - `HttpResponseStatus.OK.code()` を `java.net.HttpURLConnection.HTTP_OK` に置換します。
    3. `pom.xml`を修正:
        - `io.netty:netty-codec-http`の`<dependency>`ブロックを完全に削除します。

- **期待される効果**:
    - プロジェクトから直接的なNettyへの依存がなくなり、コードが簡素化されます。
    - 脆弱性を持つ可能性のあるライブラリへの直接参照がなくなります。

### ステップ2: 間接的な依存への対応（中期的な対策）

`browserup-proxy-core`に起因する脆弱性に対応します。

- **作業内容**:
    1. **依存関係の更新**:
        - `browserup-proxy-core`の新しいバージョンがリリースされているか確認します。
        - 新しいバージョンで、脆弱性のないバージョンのNettyが利用されている場合、`pom.xml`のバージョンを更新します。
    2. **代替ライブラリの検討**:
        - `browserup-proxy-core`の更新が難しい場合、または更新しても脆弱性が解消されない場合は、代替となるプロキシライブラリを調査・検討します。（例：`LittleProxy`, `mitmproxy-java`など）
        - 代替ライブラリへの移行は、関連するコードの広範囲な修正が必要になる可能性があります。

- **期待される効果**:
    - プロジェクトの依存関係ツリーから脆弱性のあるNettyが完全に排除され、セキュリティリスクが解消されます。

## 3. 検証

- 各ステップの完了後、`mvn clean package`コマンドでプロジェクトが正常にビルドできることを確認します。
- アプリケーションを起動し、ログイン機能やスクレイピング機能など、プロキシ機能に関連する一連の動作が正常に行えることをテストします。
- `mvn dependency:tree` を実行し、Nettyへの依存が想定通り解消されていることを確認します。
