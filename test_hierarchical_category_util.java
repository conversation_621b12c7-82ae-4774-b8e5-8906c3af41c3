import com.mrcresearch.util.database.CategoryUtil;

public class test_hierarchical_category_util {
    public static void main(String[] args) {
        System.out.println("[DEBUG_LOG] Testing CategoryUtil.getCategoryIdByName method with hierarchical categories");
        
        // Test case 1: null input
        String result1 = CategoryUtil.getCategoryIdByName(null);
        System.out.println("[DEBUG_LOG] Test 1 - null input: " + result1);
        
        // Test case 2: empty string
        String result2 = CategoryUtil.getCategoryIdByName("");
        System.out.println("[DEBUG_LOG] Test 2 - empty string: " + result2);
        
        // Test case 3: hierarchical category (example from comment)
        String result3 = CategoryUtil.getCategoryIdByName("カテゴリー1>カテゴリー2>カテゴリー3,カテゴリー4");
        System.out.println("[DEBUG_LOG] Test 3 - hierarchical with comma: " + result3);
        
        // Test case 4: simple category name
        String result4 = CategoryUtil.getCategoryIdByName("レディース");
        System.out.println("[DEBUG_LOG] Test 4 - simple category: " + result4);
        
        // Test case 5: comma separated categories
        String result5 = CategoryUtil.getCategoryIdByName("レディース,メンズ");
        System.out.println("[DEBUG_LOG] Test 5 - comma separated: " + result5);
        
        // Test case 6: hierarchical category without comma
        String result6 = CategoryUtil.getCategoryIdByName("レディース>トップス>Tシャツ/カットソー(半袖/袖なし)");
        System.out.println("[DEBUG_LOG] Test 6 - hierarchical single: " + result6);
        
        // Test case 7: two level hierarchy
        String result7 = CategoryUtil.getCategoryIdByName("レディース>トップス");
        System.out.println("[DEBUG_LOG] Test 7 - two level hierarchy: " + result7);
        
        System.out.println("[DEBUG_LOG] All tests completed");
    }
}