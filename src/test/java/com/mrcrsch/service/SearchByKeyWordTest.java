package com.mrcrsch.service;


import com.mrcresearch.service.analyze.model.KeywordSearchModel;

public class SearchByKeyWordTest {
    public void testOne() {
        KeywordSearchModel searchModel = new KeywordSearchModel();
        searchModel.setMaxMore(5);
        searchModel.setBaseUrl("https://jp.mercari.com/search?keyword=%E3%81%99%E3%81%B9%E3%81%A6%E3%81%AE%E5%95%86%E5%93%81%E3%81%AF%E3%81%93%E3%81%A1%E3%82%89&status=sold_out%7Ctrading&item_condition_id=1&sort=created_time&order=desc");

        //when(ApplicationsProperty.getProperty(ApplicationsProperty.EXTENSION)).thenReturn("2");
    }
}
