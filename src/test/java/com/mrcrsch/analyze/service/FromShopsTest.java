package com.mrcrsch.analyze.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mrcresearch.service.analyze.model.GetItemModel;
import com.mrcresearch.service.analyze.model.GroupItemModel;
import com.mrcresearch.service.analyze.service.FromShops;
import com.mrcresearch.service.models.json.shops.graphhql.Edge;
import com.mrcresearch.service.models.json.shops.graphhql.GraphHql;
import com.mrcresearch.service.models.json.shops.graphhql.Node;
import com.mrcresearch.service.models.json.shops.graphhql.Product;
import com.mrcresearch.service.tools.DriverTool;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.firefox.FirefoxDriver;
import org.openqa.selenium.firefox.FirefoxOptions;

import java.io.IOException;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.fail;


public class FromShopsTest {
    FromShops fromShops = new FromShops();
    DriverTool driverTool = new DriverTool();

    @Test
    public void testOne() throws Exception {
        driverTool.startDriver(false);
        fromShops.execute(driverTool, "MTqBJ56H7sUtSFBVbxzWV");
        driverTool.stopDriver();
    }

    @Test
    public void testTwo() throws Exception {
        driverTool.startDriver(false);
        fromShops.execute(driverTool, "QmCzFFQGLRfe3ZMtmVTgFL");
        driverTool.stopDriver();
    }

    @Test
    public void testThree() {
        FirefoxOptions options = new FirefoxOptions();
        options.setCapability("webSocketUrl", true);
        options.addArguments("-devtools");

        WebDriver driver = new FirefoxDriver(options);

        // TODO BiDiで、contentのsize以外の取得について決まってから再実験する
//        try (Network network = new Network(driver)) {
//            network.onResponseCompleted(nw -> {
//                System.out.println("Content : " + nw.getResponseData().getContent().toString());
//            });
//            driver.get("https://support.blueconic.com/en/articles/247592-generating-a-har-file-for-troubleshooting");
//
//
//            driver.get("https://www.tele.soumu.go.jp/j/adm/freq/search/index.htm");
//        } finally {
//            driver.quit();
//        }
    }

    @Test
    public void testGraphHqlJson() {
        String filePath = "/shops_graphhql.json";
        List<GetItemModel> items = getGraphHql();
        items.forEach(System.out::println);
    }

    @Test
    public void testFirstItem() {
        String filePath = "/shops_first_item.json";
        List<GetItemModel> items = getFirstItem();
        items.forEach(System.out::println);
    }

    @Test
    public void testFirstItem2() {
        String filePath = "/firstitem_sunoumoon.json";
        List<GetItemModel> items = getFirstItem();
        items.forEach(System.out::println);
    }

    @Test
    public void testProductJson() {
        String filePath = "/MTqBJ56H7sUtSFBVbxzWV.json";
        List<Product> items = getProducts();
        items.forEach(System.out::println);
    }

    @Test
    public void testMergeItems() {
        // レビューの情報
        List<GetItemModel> items = new ArrayList<>();
        items.addAll(getFirstItem());
        items.addAll(getGraphHql());

        System.out.println("items");
        items.forEach(System.out::println);

        // 商品情報
        List<Product> products = getProducts();

        System.out.println("products");
        products.forEach(System.out::println);

        // マージしていく
        List<GroupItemModel> groupItemList = new ArrayList<>();
        LocalDateTime timeNow = LocalDateTime.now();

        HashMap<String, GroupItemModel> groupMap = new HashMap<>();

        for (GetItemModel item : items) {
            if (groupMap.containsKey(item.getId())) {
                // すでに持っている場合は、内容を足していくだけ
                GroupItemModel groupItem = groupMap.get(item.getId());
                groupItem.addDisplay();
                groupItem.countUpdated(timeNow, item.getUpdated());

            } else {
                Product product = products.stream()
                        .filter(p -> p.getId().equals(item.getId()))
                        .findFirst().orElse(new Product());

                GroupItemModel addItem = new GroupItemModel(item, timeNow);
                addItem.setName(product.getName());
                addItem.setPrice(product.getPrice());
                addItem.setImageUrl(item.getThumbnailUrl());

                if (addItem.getImageUrl() == null) {
                    addItem.setImageUrl(getImageUrl(StringUtils.mid(product.getAssetsCountOne().get(0).toString(), 26, 22)));
                }

                groupMap.put(item.getId(), addItem);
            }
        }
        groupMap.values().forEach(System.out::println);
    }

    private List<GetItemModel> getFirstItem() {
        ObjectMapper objectMapper = new ObjectMapper();
        String filePath = "/shops_first_item.json";
        List<GetItemModel> items = new ArrayList<>();
        try {
            URL resource = getClass().getResource(filePath);
            JsonNode fileNode = objectMapper.readTree(resource);
            JsonNode nodes = fileNode.path("pageProps").path("__APOLLO_STATE__");
            for (JsonNode entry : nodes) {
                if ("ProductReview".equals(entry.elements().next().asText())) {
                    Node review = objectMapper.readValue(entry.toString(), Node.class);
                    items.add(new GetItemModel(review, "aaa"));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return items;
    }

    private List<GetItemModel> getGraphHql() {
        ObjectMapper objectMapper = new ObjectMapper();
        String filePath = "/shops_graphhql.json";
        List<GetItemModel> items = new ArrayList<>();
        try {
            URL resource = getClass().getResource(filePath);
            GraphHql graphHql = objectMapper.readValue(resource, GraphHql.class);

            for (Edge edge : graphHql.getData().getProductReviews().getEdges()) {
                items.add(new GetItemModel(edge.getNode(), "aaa"));
            }
        } catch (IOException e) {
            e.printStackTrace();
            fail("Failed to read " + filePath);
        }
        return items;
    }

    private List<Product> getProducts() {
        List<Product> products = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();
        String productPath = "/MTqBJ56H7sUtSFBVbxzWV.json";
        try {
            URL resource = getClass().getResource(productPath);
            JsonNode fileNode = objectMapper.readTree(resource);
            JsonNode nodes = fileNode.path("pageProps").path("__APOLLO_STATE__");
            for (JsonNode entry : nodes) {
                if ("Product".equals(entry.elements().next().asText())) {
                    Product product = objectMapper.readValue(entry.toString(), Product.class);
                    products.add(product);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return products;
    }

    @Test
    public void testImageUrl() {
        String assetname = "LkPQuJMDuy6TBYLu8uVeHG";
        String url = getImageUrl(assetname);
        System.out.println(url);
    }

    private String getImageUrl(String assetName) {
        ObjectMapper objectMapper = new ObjectMapper();
        String productPath = "/MTqBJ56H7sUtSFBVbxzWV.json";
        try {
            URL resource = getClass().getResource(productPath);
            JsonNode fileNode = objectMapper.readTree(resource);
            JsonNode nodes = fileNode.path("pageProps").path("__APOLLO_STATE__");
            for (JsonNode entry : nodes) {
                if ("ProductAsset".equals(entry.elements().next().asText()) &&
                        assetName.equals(entry.path("id").asText())) {
                    // 商品の情報を取得する
                    return entry.path("imageUrl({\"options\":{\"presets\":[\"Small\"]}})").asText();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}