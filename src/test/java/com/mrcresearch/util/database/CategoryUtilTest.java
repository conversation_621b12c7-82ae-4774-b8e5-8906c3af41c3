package com.mrcresearch.util.database;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for CategoryUtil
 * Tests all public methods with various input scenarios including edge cases
 */
@DisplayName("CategoryUtil Tests")
public class CategoryUtilTest {

    @Nested
    @DisplayName("getCategoryNameById Tests")
    class GetCategoryNameByIdTests {

        @Test
        @DisplayName("Should return 'カテゴリなし' for null input")
        void shouldReturnNoCategoryForNullInput() {
            String result = CategoryUtil.getCategoryNameById(null);
            assertEquals("カテゴリなし", result);
        }

        @Test
        @DisplayName("Should return 'カテゴリなし' for empty string")
        void shouldReturnNoCategoryForEmptyString() {
            String result = CategoryUtil.getCategoryNameById("");
            assertEquals("カテゴリなし", result);
        }

        @Test
        @DisplayName("Should return 'カテゴリなし' for whitespace only")
        void shouldReturnNoCategoryForWhitespaceOnly() {
            String result = CategoryUtil.getCategoryNameById("   ");
            assertEquals("カテゴリなし", result);
        }

        @Test
        @DisplayName("Should return 'カテゴリなし' for categoryId '0'")
        void shouldReturnNoCategoryForZero() {
            String result = CategoryUtil.getCategoryNameById("0");
            assertEquals("カテゴリなし", result);
        }

        @Test
        @DisplayName("Should return category name for valid categoryId")
        void shouldReturnCategoryNameForValidId() {
            // This test depends on actual CSV data, so we test the behavior
            String result = CategoryUtil.getCategoryNameById("1");
            assertNotNull(result);
            // Should either return actual category name or fallback
            assertTrue(result.equals("カテゴリなし") || !result.isEmpty());
        }

        @Test
        @DisplayName("Should handle categoryId with leading/trailing spaces")
        void shouldHandleIdWithSpaces() {
            String result = CategoryUtil.getCategoryNameById("  1  ");
            assertNotNull(result);
            assertTrue(result.equals("カテゴリなし") || !result.isEmpty());
        }

        @Test
        @DisplayName("Should return 'カテゴリなし' for non-existent categoryId")
        void shouldReturnNoCategoryForNonExistentId() {
            String result = CategoryUtil.getCategoryNameById("99999");
            assertEquals("カテゴリなし", result);
        }
    }

    @Nested
    @DisplayName("convertToDisplayCategoryName Tests")
    class ConvertToDisplayCategoryNameTests {

        @Test
        @DisplayName("Should return empty string for null input")
        void shouldReturnEmptyStringForNull() {
            String result = CategoryUtil.convertToDisplayCategoryName(null);
            assertEquals("", result);
        }

        @Test
        @DisplayName("Should return empty string for empty input")
        void shouldReturnEmptyStringForEmpty() {
            String result = CategoryUtil.convertToDisplayCategoryName("");
            assertEquals("", result);
        }

        @Test
        @DisplayName("Should return empty string for whitespace only")
        void shouldReturnEmptyStringForWhitespace() {
            String result = CategoryUtil.convertToDisplayCategoryName("   ");
            assertEquals("", result);
        }

        @Test
        @DisplayName("Should return same string if already category name")
        void shouldReturnSameIfAlreadyCategoryName() {
            String categoryName = "レディース";
            String result = CategoryUtil.convertToDisplayCategoryName(categoryName);
            assertNotNull(result);
            // Result should be the same or converted version
        }

        @Test
        @DisplayName("Should convert category ID to name")
        void shouldConvertIdToName() {
            String result = CategoryUtil.convertToDisplayCategoryName("1");
            assertNotNull(result);
            // Should return converted name or original if conversion fails
        }

        @Test
        @DisplayName("Should handle comma-separated category IDs")
        void shouldHandleCommaSeparatedIds() {
            String result = CategoryUtil.convertToDisplayCategoryName("1,2,3");
            assertNotNull(result);
            assertFalse(result.isEmpty());
        }
    }

    @Nested
    @DisplayName("getCategoryIdByName Tests")
    class GetCategoryIdByNameTests {

        @Test
        @DisplayName("Should return '-1' for null input")
        void shouldReturnMinusOneForNull() {
            String result = CategoryUtil.getCategoryIdByName(null);
            assertEquals("-1", result);
        }

        @Test
        @DisplayName("Should return '-1' for empty string")
        void shouldReturnMinusOneForEmpty() {
            String result = CategoryUtil.getCategoryIdByName("");
            assertEquals("-1", result);
        }

        @Test
        @DisplayName("Should return '-1' for whitespace only")
        void shouldReturnMinusOneForWhitespace() {
            String result = CategoryUtil.getCategoryIdByName("   ");
            assertEquals("-1", result);
        }

        @Test
        @DisplayName("Should handle simple category name")
        void shouldHandleSimpleCategoryName() {
            String result = CategoryUtil.getCategoryIdByName("レディース");
            assertNotNull(result);
            // Should return ID or -1 if not found
            assertTrue(result.equals("-1") || result.matches("\\d+"));
        }

        @Test
        @DisplayName("Should handle comma-separated category names")
        void shouldHandleCommaSeparatedNames() {
            String result = CategoryUtil.getCategoryIdByName("レディース,メンズ");
            assertNotNull(result);
            // Should return comma-separated IDs or -1
            assertTrue(result.equals("-1") || result.matches("[\\d,]+"));
        }

        @Test
        @DisplayName("Should handle hierarchical category with '>' separator")
        void shouldHandleHierarchicalCategory() {
            String result = CategoryUtil.getCategoryIdByName("レディース>トップス");
            assertNotNull(result);
            // Should return ID of the last category in hierarchy or -1
            assertTrue(result.equals("-1") || result.matches("\\d+"));
        }

        @Test
        @DisplayName("Should handle hierarchical category with comma at the end")
        void shouldHandleHierarchicalWithComma() {
            String result = CategoryUtil.getCategoryIdByName("レディース>トップス>Tシャツ,カットソー");
            assertNotNull(result);
            // Should return comma-separated IDs or -1
            assertTrue(result.equals("-1") || result.matches("[\\d,]+"));
        }

        @Test
        @DisplayName("Should handle complex hierarchical structure from comment example")
        void shouldHandleComplexHierarchy() {
            // Test case from the comment: カテゴリー1>カテゴリー2>カテゴリー3,カテゴリー4
            String result = CategoryUtil.getCategoryIdByName("カテゴリー1>カテゴリー2>カテゴリー3,カテゴリー4");
            assertNotNull(result);
            assertTrue(result.equals("-1") || result.matches("[\\d,]+"));
        }

        @Test
        @DisplayName("Should handle single category in hierarchy")
        void shouldHandleSingleCategoryInHierarchy() {
            String result = CategoryUtil.getCategoryIdByName("レディース>トップス>Tシャツ");
            assertNotNull(result);
            assertTrue(result.equals("-1") || result.matches("\\d+"));
        }

        @Test
        @DisplayName("Should handle category names with spaces")
        void shouldHandleCategoryNamesWithSpaces() {
            String result = CategoryUtil.getCategoryIdByName(" レディース > トップス ");
            assertNotNull(result);
            assertTrue(result.equals("-1") || result.matches("\\d+"));
        }

        @Test
        @DisplayName("Should handle mixed spaces in comma-separated names")
        void shouldHandleMixedSpacesInCommaSeparated() {
            String result = CategoryUtil.getCategoryIdByName(" レディース , メンズ ");
            assertNotNull(result);
            assertTrue(result.equals("-1") || result.matches("[\\d,]+"));
        }

        @Test
        @DisplayName("Should return '-1' for non-existent category")
        void shouldReturnMinusOneForNonExistentCategory() {
            String result = CategoryUtil.getCategoryIdByName("存在しないカテゴリー");
            assertEquals("-1", result);
        }

        @Test
        @DisplayName("Should return '-1' for partial hierarchy with non-existent parent")
        void shouldReturnMinusOneForNonExistentParent() {
            String result = CategoryUtil.getCategoryIdByName("存在しない親>子カテゴリー");
            assertEquals("-1", result);
        }

        @Test
        @DisplayName("Should handle empty parts in hierarchy")
        void shouldHandleEmptyPartsInHierarchy() {
            String result = CategoryUtil.getCategoryIdByName("レディース>>トップス");
            assertNotNull(result);
            // Should handle empty parts gracefully
        }

        @Test
        @DisplayName("Should handle empty parts in comma separation")
        void shouldHandleEmptyPartsInCommaSeparation() {
            String result = CategoryUtil.getCategoryIdByName("レディース,,メンズ");
            assertNotNull(result);
            // Should handle empty parts gracefully
        }
    }

    @Nested
    @DisplayName("Constructor Tests")
    class ConstructorTests {

        @Test
        @DisplayName("Should throw UnsupportedOperationException when trying to instantiate")
        void shouldThrowExceptionWhenInstantiating() {
            assertThrows(UnsupportedOperationException.class, () -> {
                // Using reflection to test private constructor
                java.lang.reflect.Constructor<CategoryUtil> constructor = 
                    CategoryUtil.class.getDeclaredConstructor();
                constructor.setAccessible(true);
                constructor.newInstance();
            });
        }
    }

    @Nested
    @DisplayName("Integration Tests")
    class IntegrationTests {

        @Test
        @DisplayName("Should work with round-trip conversion: ID to Name to ID")
        void shouldWorkWithRoundTripConversion() {
            // Test round-trip conversion if possible
            String originalId = "1";
            String categoryName = CategoryUtil.getCategoryNameById(originalId);
            
            if (!categoryName.equals("カテゴリなし")) {
                String convertedId = CategoryUtil.getCategoryIdByName(categoryName);
                // Note: Due to hierarchical structure, exact match might not always be possible
                assertNotNull(convertedId);
                assertNotEquals("-1", convertedId);
            }
        }

        @Test
        @DisplayName("Should handle real category data consistently")
        void shouldHandleRealCategoryDataConsistently() {
            // Test with some common category names that might exist
            String[] testCategories = {"レディース", "メンズ", "キッズ", "本・音楽・ゲーム"};
            
            for (String category : testCategories) {
                String id = CategoryUtil.getCategoryIdByName(category);
                assertNotNull(id);
                
                if (!id.equals("-1")) {
                    String name = CategoryUtil.getCategoryNameById(id);
                    assertNotNull(name);
                    assertNotEquals("カテゴリなし", name);
                }
            }
        }
    }

    @Nested
    @DisplayName("Error Handling Tests")
    class ErrorHandlingTests {

        @Test
        @DisplayName("Should handle Categories initialization failure gracefully")
        void shouldHandleCategoriesInitFailureGracefully() {
            // These methods should not throw exceptions even if Categories fails to initialize
            assertDoesNotThrow(() -> CategoryUtil.getCategoryNameById("1"));
            assertDoesNotThrow(() -> CategoryUtil.convertToDisplayCategoryName("test"));
            assertDoesNotThrow(() -> CategoryUtil.getCategoryIdByName("test"));
        }

        @Test
        @DisplayName("Should handle reflection errors gracefully in getCategoryIdByName")
        void shouldHandleReflectionErrorsGracefully() {
            // Test with various inputs that might cause reflection issues
            assertDoesNotThrow(() -> CategoryUtil.getCategoryIdByName("テスト"));
            assertDoesNotThrow(() -> CategoryUtil.getCategoryIdByName("test>category"));
            assertDoesNotThrow(() -> CategoryUtil.getCategoryIdByName("test,category"));
        }
    }
}