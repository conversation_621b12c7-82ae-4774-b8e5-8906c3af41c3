package NewDriver;

import org.junit.jupiter.api.Test;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.firefox.FirefoxDriver;
import org.openqa.selenium.firefox.FirefoxOptions;

import java.io.File;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

public class NewDriverTest {
    private static final String SCROLL_BOTTOM_HEAD = "window.scrollTo(0, document.body.scrollHeight / 10 * ";
    // private static WebDriver driver;
    private static final String SCROLL_BOTTOM_END = ");";
    private static final String STOP_LAZY_LOAD = "document.querySelectorAll('img').forEach(elem => elem.removeAttribute('loading')) ";
    static File harExportFile;

    public static void main(String[] args) throws InterruptedException {
        FirefoxOptions options = new FirefoxOptions();

        harExportFile = null;
        try {
            harExportFile = File.createTempFile("mrcresearch_", "");
            harExportFile.deleteOnExit();
            System.out.println("Created har export tmpFile: " + harExportFile.getAbsolutePath());
            System.out.println(harExportFile.getParent());
            System.out.println(harExportFile.getName());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        if (harExportFile.exists()) {
//            profile.setPreference("devtools.netmonitor.har.enableAutoExportToFile", true);
//            profile.setPreference("devtools.netmonitor.har.defaultLogDir", String.valueOf(harExportFile.getParent()));
//            profile.setPreference("devtools.netmonitor.har.defaultFileName", "network-log-tmpFile-%Y-%m-%d-%H-%M-%S");
//            profile.compressHar(true);
//            profile.defaultFileName(harExportFile.getName());
//            profile.setPreference("devtools.netmonitor.har.defaultLogDir", harExportFile.getParent());
//            profile.setPreference("devtools.netmonitor.har.enableAutoExportToFile", true); // trueならページごとにHARが出力される
//            profile.setPreference("devtools.netmonitor.har.forceExport", true);
//            profile.setPreference("devtools.netmonitor.har.includeResponseBodies", true);
//            profile.setPreference("devtools.netmonitor.har.jsonp", false);
//            profile.setPreference("devtools.netmonitor.har.jsonpCallback", false);
//            profile.setPreference("devtools.netmonitor.har.pageLoadedTimeout", "2500");
//            profile.setPreference("extensions.netmonitor.har.enableAutomation", true);
//            profile.setPreference("extensions.netmonitor.har.contentAPIToken", "test");
//            profile.setPreference("extensions.netmonitor.har.autoConnect", true);
//            profile.setPreference("devtools.toolbox.selectedTool", "netmonitor"); // 「ネットワーク」タブを選択する

            options.addArguments("-devtools");
            options.addArguments("--headless");
//            options.enableBiDi();
        } else {
            // TODO エラー処理を追加する
            System.out.println("Failed to create har export tmpFile.");
        }

        WebDriver driver = new FirefoxDriver(options);

        driver.get("https://jp.mercari.com/search?search_condition_id=1cx0zHHN0HTIeMxxpY2QdMRxpdHkdMBxrHea1t-WkluijveOAgOOCpOODs-ODneODvOODiA");
        ((JavascriptExecutor) driver).executeScript(STOP_LAZY_LOAD);
        Thread.sleep(5000);
        try {
            for (int i = 1; i <= 10; i++) {
                TimeUnit.MILLISECONDS.sleep(200);
                ((JavascriptExecutor) driver).executeScript(SCROLL_BOTTOM_HEAD + i + SCROLL_BOTTOM_END);
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        Thread.sleep(3300);

        driver.get("https://jp.mercari.com/search?page_token=v1%3A1&search_condition_id=1cx0zHHN0HTIeMxxpY2QdMRxpdHkdMBxrHea1t-WkluijveOAgOOCpOODs-ODneODvOODiA");
        ((JavascriptExecutor) driver).executeScript(STOP_LAZY_LOAD);
        Thread.sleep(3300);
        try {
            for (int i = 1; i <= 10; i++) {
                TimeUnit.MILLISECONDS.sleep(200);
                ((JavascriptExecutor) driver).executeScript(SCROLL_BOTTOM_HEAD + i + SCROLL_BOTTOM_END);
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        Thread.sleep(3300);

        // harExportFile.getParent()のファイルを一覧で表示
        File directory = new File(harExportFile.getParent());
        if (directory.exists() && directory.isDirectory()) {
            File[] files = directory.listFiles();
            for (File file : files) {
                if (file.getName().startsWith(harExportFile.getName())) {
                    file.delete();
                }
            }
        }

        driver.quit();
    }

    @Test
    public void testBiDiMonitoring() {
        FirefoxOptions options = new FirefoxOptions();
        options.setCapability("webSocketUrl", true);
        options.addArguments("-devtools");

//        FirefoxCustomProfile profile = new FirefoxCustomProfile();
//        profile.setPreference("devtools.toolbox.selectedTool", "netmonitor"); // 「ネットワーク」タブを選択する
//        profile.setPreference("devtools.toolbox.footer.height", 0); // 高さをゼロにする
//        profile.setPreference("devtools.netmonitor.saveRequestAndResponseBodies", true); // リクエスト内容を取得する
//        options.setProfile(profile);
//
//        WebDriver driver = new FirefoxDriver(options);

//        try (Network network = new Network(driver)) {
//            network.onResponseCompleted(nw -> {
//                System.out.println("Content : " + nw.getResponseData().getContent());
//            });
//            driver.get("https://www.selenium.dev/selenium/web/bidi/logEntryAdded.html");
//
//
////            driver.get("https://www.tele.soumu.go.jp/j/adm/freq/search/index.htm");
//        } finally {
//            driver.quit();
//        }
    }
}
