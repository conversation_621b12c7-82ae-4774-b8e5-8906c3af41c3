package Categories;

import com.browserup.harreader.model.HarEntry;
import com.browserup.harreader.model.HttpMethod;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mrcresearch.screen.component.category.CategoryModel;
import com.mrcresearch.service.tools.DecodeUtil;
import com.mrcresearch.service.tools.DriverTool;
import org.apache.commons.lang3.StringUtils;
import org.openqa.selenium.By;
import org.openqa.selenium.OutputType;

import java.io.BufferedWriter;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * カテゴリの変化の調査用
 */
public class Category {
    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    DecodeUtil decodeUtil = new DecodeUtil();
    DriverTool dt = new DriverTool();

    public static void main(String[] args) {
//        try {
//            new Category().getCategoryScreenShot();
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//        try {
//            System.out.println(sdf.parse("2024/07/29"));
//        } catch (ParseException e) {
//            throw new RuntimeException(e);
//        }
        try {
            new Category().getCategoryJSON();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private static void showFields() {
        String image = "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";
        System.out.println(Base64.getDecoder().decode(image).toString());
    }

    private void getCategoryScreenShot() throws IOException {
        String baseURL = "https://jp.mercari.com/search?category_id=";
        String basePath = "C:\\Users\\<USER>\\OneDrive\\Pictures\\スクリーンショット\\selenium\\";
        List<String> searchIds = Files.readAllLines(Paths.get("C:\\Users\\<USER>\\git\\scrapingmercari\\src\\test\\resources\\searchIds.txt"), StandardCharsets.UTF_8);
        dt.startDriver(false);

        for (String id : searchIds) {
            Path savePath = Paths.get(basePath + id + ".png");
            dt.openAndWait(baseURL + id);
            Files.write(savePath, dt.driver.findElement(By.xpath("/html/body/div[1]/div[1]/div[2]/main/div[1]/div[1]/aside/div/div/div/ul/li[2]")).getScreenshotAs(OutputType.BYTES));
        }

        dt.stopDriver();
    }

    private void getCategoryJSON() throws IOException {
        String apiUri = "https://api.mercari.jp/master/v2/datasets/item_categories";
        String accessUrl = "https://jp.mercari.com/categories";

        dt.startDriver(false);
        // HARを取得する
        dt.proxy.newHar();

        // カテゴリ一覧にアクセスする
        dt.openAndWait(accessUrl);

        List<HarEntry> entries = dt.getHarEntries();

        List<CategoryModel> categories = new ArrayList<>();

        // 必要な分を取得する
        for (HarEntry entry : entries) {
            // リクエストの内容で対象のAPIからのリクエスト && GETメソッドで受信している
            if (entry.getRequest().getUrl().contains(apiUri)
                    && HttpMethod.GET.equals(entry.getRequest().getMethod())) {
                // そのままでは解析できないため、いったんStringに変えてあげる
                ObjectMapper mapper = new ObjectMapper();
                String nodeTxt = decodeUtil.decodeNodeTxt(entry.getResponse().getContent().getText());
                JsonNode items = mapper.readTree(nodeTxt);

                // 1つ1つアイテムを入れていく
                for (JsonNode item : items.path("itemCategories")) {
                    System.out.println(item.path("id").asText());
                    categories.add(new CategoryModel(
                            item.path("id").asText(),
                            item.path("name").asText(),
                            StringUtils.defaultIfEmpty(item.path("level").asText(), "0"),
                            StringUtils.defaultIfEmpty(item.path("parentCategoryId").asText(), "0"),
                            item.path("hasChild").asBoolean(false)
                    ));
                }
            }
        }
        dt.stopDriver();

        // 結果をエクスポート
        OutputStreamWriter oswr;
        BufferedWriter bfw;
        try (FileOutputStream fost = new FileOutputStream("category.csv", false)) {
            // Excelで読み込むための初期化(BOM)
            fost.write(0xef);
            fost.write(0xbb);
            fost.write(0xbf);

            oswr = new OutputStreamWriter(fost, UTF_8);
            bfw = new BufferedWriter(oswr);

            // ヘッダを出力する
            bfw.write("id,name,level,parentId,hasChild\n");

            // 解析結果を出力する
            for (CategoryModel category : categories) {
                bfw.write(category.getId() + ","); // カテゴリID
                bfw.write(category.getName() + ","); // カテゴリ名
                bfw.write(category.getLevel() + ","); // レベル
                bfw.write(category.getParentId() + ","); // 親カテゴリID
                bfw.write(category.isHasChild() + "\n"); // チェックボックスなのか
            }
            // ストリームをフラッシュ
            bfw.flush();
            // 出力ストリームをフラッシュ
            oswr.flush();
            fost.flush();

            // ストリームを閉じる
            bfw.close();
            oswr.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
