package com.mrcresearch.screen.login;

import com.formdev.flatlaf.FlatClientProperties;
import com.mrcresearch.service.auth.model.AuthStatus;
import com.mrcresearch.service.ui.CredentialsService;
import com.mrcresearch.util.TextFieldContextMenuUtil;
import com.mrcresearch.security.InputValidator;
import com.mrcresearch.security.LoginAttemptService;
import com.mrcresearch.security.AuditLogger;
import com.mrcresearch.util.ui.UIStyles;
import lombok.Getter;
import lombok.Setter;
import net.miginfocom.swing.MigLayout;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.awt.event.KeyEvent;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;

/**
 * ログイン画面のパネルコンポーネント。
 * メールアドレスとパスワードの入力、認証処理の実行、
 * および認証結果に応じた画面遷移コールバックを提供します。
 */
public class Login extends JPanel {
    private static final Logger logger = LoggerFactory.getLogger(Login.class);
    private final CredentialsService credentialsService = new CredentialsService();
    private JTextField txtUserMail;
    private JPasswordField txtPassword;
    /**
     * -- GETTER --
     * ログインボタンのインスタンスを取得します。
     *
     * @return ログインボタン
     */
    @Getter
    private JButton btnLogin;
    /**
     * -- GETTER --
     * 直近の認証結果を取得します。
     *
     * @return 認証結果（未実行の場合はnull）
     */
    @Getter
    private AuthStatus lastAuthResult; // 直近の認証結果を保持
    /**
     * -- SETTER --
     * 認証成功時に実行するコールバックを設定します。
     *
     * @param onLoginSuccess 認証成功時に実行する処理
     */
    @Setter
    private Runnable onLoginSuccess; // 認証成功時のコールバック

    public Login() {
        init();
        loadSavedCredentials(); // Load saved credentials from database
        setVisible(true);
    }

    /**
     * データベースから保存済みの認証情報を読み込み、UIへ反映します。
     */
    private void loadSavedCredentials() {
        // 必要であればデータベースを初期化
        credentialsService.init();

        // ログイン情報をデータベースから読み込む
        String[] loginInfo = credentialsService.loadLoginInfo();
        if (loginInfo != null) {
            // 読み込んだ情報をUIへ反映
            String email = loginInfo[0];
            String passwordHash = loginInfo[1];
            txtUserMail.setText(email);
            // 検証用にパスワードハッシュをクライアントプロパティへ保持
            txtPassword.putClientProperty("passwordHash", passwordHash);
        }
    }

    private void init() {
        setLayout(new MigLayout("fill,insets 20", "[center]", "[center]"));
        txtUserMail = new JTextField();
        txtPassword = new JPasswordField();
        JPanel panel = new JPanel(new MigLayout("wrap,fillx,insets 35 45 30 45", "fill,250:350"));
        panel.putClientProperty(FlatClientProperties.STYLE, "arc:20;");

        // ロゴ
        JPanel lbLogo = createLogoLabel();
        if (lbLogo != null) {
            panel.add(lbLogo, "align center, gapbottom 10");
        }

        // タイトル
        JLabel lbTitle = new JLabel("メルクリサーチへようこそ！");
        UIStyles.applyTitleStyle(lbTitle);
        UIStyles.setAccessibleName(lbTitle, "ログインタイトル", "メルクリサーチのログイン画面タイトル");
        panel.add(lbTitle, "align center");

        // メールアドレス
        panel.add(txtUserMail, "grow");
        txtUserMail.putClientProperty(FlatClientProperties.PLACEHOLDER_TEXT, "メールアドレス");

        // メールアドレス欄にコンテキストメニューを有効化
        TextFieldContextMenuUtil.enableTextFieldContextMenu(txtUserMail);

        // パスワード
        panel.add(txtPassword, "grow");
        txtPassword.putClientProperty(FlatClientProperties.PLACEHOLDER_TEXT, "パスワード");
        txtPassword.putClientProperty(FlatClientProperties.STYLE, "showRevealButton:true");
        txtPassword.setText(credentialsService.getPassword());

        // パスワード欄にコンテキストメニューを有効化
        TextFieldContextMenuUtil.enableTextFieldContextMenu(txtPassword);

        // ログインボタン
        btnLogin = new JButton(new ImageIcon("/icons/refresh-age-option.png"));
        btnLogin.setText("ログイン");
        UIStyles.applyPrimaryButton(btnLogin);
        btnLogin.setMnemonic(KeyEvent.VK_L);
        UIStyles.setAccessibleName(btnLogin, "ログインボタン", "入力した認証情報でログインを実行します");

        panel.add(btnLogin, "grow");

        // アクセシビリティ: 入力欄にアクセシブル名
        UIStyles.setAccessibleName(txtUserMail, "メールアドレス入力", "ログイン用メールアドレスを入力");
        UIStyles.setAccessibleName(txtPassword, "パスワード入力", "ログイン用パスワードを入力");

        // Enterキーでログインを実行
        panel.getInputMap(JComponent.WHEN_ANCESTOR_OF_FOCUSED_COMPONENT)
                .put(KeyStroke.getKeyStroke(KeyEvent.VK_ENTER, 0), "LOGIN");
        panel.getActionMap().put("LOGIN", new AbstractAction() {
            @Override
            public void actionPerformed(java.awt.event.ActionEvent e) {
                btnLogin.doClick();
            }
        });

        // ログインボタンにアクションリスナーを追加
        btnLogin.addActionListener(e -> {
            String email = txtUserMail.getText() != null ? txtUserMail.getText().trim() : "";
            String password = new String(txtPassword.getPassword());

            // セキュリティ強化された入力チェック
            if (email.isEmpty() || password.isEmpty()) {
                JOptionPane.showMessageDialog(this, "メールアドレスとパスワードを入力してください", "入力エラー", JOptionPane.ERROR_MESSAGE);
                if (email.isEmpty()) txtUserMail.requestFocusInWindow();
                else txtPassword.requestFocusInWindow();
                return;
            }

            // InputValidatorによる包括的な検証
            if (!InputValidator.isValidEmail(email)) {
                JOptionPane.showMessageDialog(this, "メールアドレスの形式が正しくありません", "入力エラー", JOptionPane.ERROR_MESSAGE);
                txtUserMail.requestFocusInWindow();
                return;
            }

            // 入力の安全性チェック
            if (!InputValidator.isValidInput(email, 255, false)) {
                JOptionPane.showMessageDialog(this, "入力に無効な文字が含まれています", "セキュリティエラー", JOptionPane.ERROR_MESSAGE);
                return;
            }

            // ログイン試行回数制限チェック
            LoginAttemptService loginAttemptService = LoginAttemptService.getInstance();
            if (loginAttemptService.isBlocked(email)) {
                long remainingMinutes = loginAttemptService.getRemainingLockoutMinutes(email);
                JOptionPane.showMessageDialog(this,
                        String.format("アカウントが一時的にロックされています。%d分後に再試行してください。", remainingMinutes),
                        "アクセス制限", JOptionPane.WARNING_MESSAGE);

                // 監査ログにブロック試行を記録
                AuditLogger.getInstance().logUnauthorizedAccess(email, "localhost", "login",
                        "ブロックされたアカウントでのログイン試行");
                return;
            }

            // 認証サービスを用いて認証を実施
            performAuthentication(email, password);
        });

        panel.add(createSignupLabel(), "grow");

        add(panel);
    }

    /**
     * ロゴラベルを作成する
     *
     * @return ロゴを表示するJLabel、読み込みに失敗した場合はnull
     */
    private JPanel createLogoLabel() {
        // Create a panel to hold both the icon and text
        JPanel logoPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 5, 0));
        logoPanel.setOpaque(false);

        // Load the icon
        ImageIcon icon = new ImageIcon(Objects.requireNonNull(getClass().getResource("/img/icon.png")));
        // Scale the icon to an appropriate size
        Image img = icon.getImage().getScaledInstance(40, 40, Image.SCALE_SMOOTH);
        ImageIcon scaledIcon = new ImageIcon(img);

        // Create icon label
        JLabel iconLabel = new JLabel(scaledIcon);

        // Create text label
        JLabel textLabel = new JLabel("MrcResearch");
        textLabel.putClientProperty(FlatClientProperties.STYLE, "font:bold +20");

        // Add both to the panel
        logoPanel.add(iconLabel);
        logoPanel.add(textLabel);

        return logoPanel;
    }

    /**
     * ユーザー登録ページへのリンクラベルを作成します。
     *
     * @return 登録リンクを含むコンポーネント
     */
    private Component createSignupLabel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER, 0, 0));
        panel.putClientProperty(FlatClientProperties.STYLE, "background:null");
        JButton btnRegister = new JButton("<html><a href=\"\">登録する（ブラウザが開きます）</a></html>");
        btnRegister.setContentAreaFilled(false);
        btnRegister.setCursor(new Cursor(Cursor.HAND_CURSOR));
        btnRegister.addActionListener(e -> {
            try {
                Desktop.getDesktop().browse(new URI("https://mcrsldrschtl.com/register"));
            } catch (IOException | URISyntaxException ex) {
                throw new RuntimeException(ex);
            }
        });

        panel.add(btnRegister);
        return panel;
    }

    /**
     * 認証サービスを用いてログイン認証を実行します。
     *
     * @param email    ユーザーのメールアドレス
     * @param password ユーザーのパスワード
     */
    private void performAuthentication(String email, String password) {
        // ローディング状態を表示
        btnLogin.setEnabled(false);
        btnLogin.setText("認証中...");

        // UIスレッドをブロックしないようバックグラウンドで認証を実行
        SwingWorker<AuthStatus, Void> authWorker = new SwingWorker<AuthStatus, Void>() {
            @Override
            protected AuthStatus doInBackground() throws Exception {
                com.mrcresearch.service.auth.service.Auth authService =
                        new com.mrcresearch.service.auth.service.Auth();
                return authService.getLoginResult(email, password);
            }

            @Override
            protected void done() {
                try {
                    AuthStatus authResult = get();
                    lastAuthResult = authResult;

                    logger.debug("認証が完了しました");
                    logger.debug("認証ステータス: {}", (authResult != null ? authResult.getStatus() : "null"));
                    logger.debug("有効期限: {}", (authResult != null ? authResult.getExpireDate() : "null"));

                    // Reset button state
                    btnLogin.setEnabled(true);
                    btnLogin.setText("ログイン");

                    LoginAttemptService loginAttemptService = LoginAttemptService.getInstance();
                    AuditLogger auditLogger = AuditLogger.getInstance();

                    if (authResult != null && AuthStatus.ACTIVE.equals(authResult.getStatus())) {
                        // Authentication successful
                        logger.debug("認証成功、メイン画面へ進みます");

                        // ログイン成功を記録
                        loginAttemptService.loginSucceeded(email);
                        auditLogger.logLoginSuccess(email, "localhost", System.getProperty("user.agent", "Desktop App"));

                        // Save credentials to database for future use
                        credentialsService.saveLoginInfo(email, password);

                        // Immediately trigger screen transition
                        if (onLoginSuccess != null) {
                            SwingUtilities.invokeLater(onLoginSuccess);
                        }

                    } else if (authResult != null && AuthStatus.INACTIVE.equals(authResult.getStatus())) {
                        // Account inactive
                        logger.debug("アカウントが無効です");

                        // ログイン失敗を記録（アカウント無効）
                        loginAttemptService.loginFailed(email);
                        auditLogger.logLoginFailure(email, "localhost", "アカウント無効");

                        // ロックアウト状態をチェックして通知
                        if (loginAttemptService.isBlocked(email)) {
                            long lockoutDuration = loginAttemptService.getRemainingLockoutMinutes(email);
                            auditLogger.logAccountLockout(email, "localhost", lockoutDuration);
                        }

                        JOptionPane.showMessageDialog(Login.this,
                                "アカウントが無効です。サブスクリプションを確認してください。",
                                "認証エラー",
                                JOptionPane.WARNING_MESSAGE);
                    } else {
                        // Authentication failed
                        logger.debug("認証に失敗しました");

                        // ログイン失敗を記録
                        loginAttemptService.loginFailed(email);
                        auditLogger.logLoginFailure(email, "localhost", "認証情報不正");

                        // ロックアウト状態をチェックして通知
                        if (loginAttemptService.isBlocked(email)) {
                            long lockoutDuration = loginAttemptService.getRemainingLockoutMinutes(email);
                            auditLogger.logAccountLockout(email, "localhost", lockoutDuration);
                        }

                        JOptionPane.showMessageDialog(Login.this,
                                "メールアドレスまたはパスワードが正しくありません",
                                "認証エラー",
                                JOptionPane.ERROR_MESSAGE);
                    }

                } catch (Exception e) {
                    // Reset button state
                    btnLogin.setEnabled(true);
                    btnLogin.setText("ログイン");

                    // システムエラーを監査ログに記録
                    AuditLogger.getInstance().logSystemError("AUTHENTICATION_ERROR", e.getMessage(), email);

                    // Handle authentication error
                    JOptionPane.showMessageDialog(Login.this,
                            "認証中にエラーが発生しました: " + e.getMessage(),
                            "エラー",
                            JOptionPane.ERROR_MESSAGE);
                    logger.error("Authentication error", e);
                }
            }
        };

        authWorker.execute();
    }

}
