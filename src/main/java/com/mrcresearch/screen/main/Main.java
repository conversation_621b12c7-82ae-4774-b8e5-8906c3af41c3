package com.mrcresearch.screen.main;

import com.mrcresearch.screen.component.Header;
import com.mrcresearch.screen.component.Menu;
import com.mrcresearch.screen.component.SalesAggregationPanel;
import com.mrcresearch.screen.favorite.FavoriteSellerScreen;
import com.mrcresearch.screen.keyword.Keyword;
import com.mrcresearch.screen.model.MenuModel;
import com.mrcresearch.screen.seller.Seller;
import com.mrcresearch.screen.settings.Settings;
import com.mrcresearch.util.ThemeIconUtil;
import lombok.Getter;
import net.miginfocom.swing.MigLayout;
import org.kordamp.ikonli.antdesignicons.AntDesignIconsOutlined;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;

public class Main extends JPanel {

    private static final Logger logger = LoggerFactory.getLogger(Main.class);
    private MigLayout migLayout;
    @Getter
    private Menu menu;
    private Header header;
    private JPanel contentPanel;
    /**
     * -- GETTER --
     * Get the keyword screen instance
     *
     * @return The keyword screen instance
     */
    @Getter
    private Keyword keywordScreen;
    private Settings settingsScreen;
    private Seller sellerScreen;
    private FavoriteSellerScreen favoriteSellerScreen;
    private com.mrcresearch.screen.cleanup.ProductCleanup productCleanupScreen;
    /**
     * -- GETTER --
     * Get the sales aggregation panel instance
     *
     * @return The sales aggregation panel instance
     */
    @Getter
    private SalesAggregationPanel salesAggregationPanel;
    private boolean menuVisible = true;

    public Main() {
        logger.info("メインパネルを初期化しています");
        initComponents();
        init();

    }

    private void initComponents() {
        keywordScreen = new Keyword();
        settingsScreen = new Settings();
        sellerScreen = new Seller();
        favoriteSellerScreen = new FavoriteSellerScreen();
        salesAggregationPanel = new SalesAggregationPanel();
        productCleanupScreen = new com.mrcresearch.screen.cleanup.ProductCleanup();

        // Set main screen reference in settings for cross-component updates
        settingsScreen.setMainScreen(this);

        // Set main screen reference in favorite seller screen for navigation
        favoriteSellerScreen.setMainScreen(this);

        // Listen for events from child components
        keywordScreen.addPropertyChangeListener("showProductResearch", this::handleShowProductResearch);
        sellerScreen.addPropertyChangeListener("showSellerResearch", this::handleShowSellerResearchFromSellerScreen);
    }

    private void init() {
        migLayout = new MigLayout("fill", "0[300::300]0[grow, fill]0", "0[50px, fill, top]0[grow, fill]0");
        setLayout(migLayout);

        // メニュー
        menu = new Menu();
        add(menu, "w 300!, spany 2");

        // ヘッダー
        header = new Header();
        add(header, "h 50!, grow, wrap");

        // コンテンツパネル
        contentPanel = new JPanel(new CardLayout());
        contentPanel.add(keywordScreen, "keyword");
        contentPanel.add(sellerScreen, "seller");
        contentPanel.add(favoriteSellerScreen, "favorite");
        contentPanel.add(salesAggregationPanel, "results");
        contentPanel.add(settingsScreen, "settings");
        contentPanel.add(productCleanupScreen, "cleanup");
        contentPanel.setVisible(true);
        add(contentPanel, "grow");

        // メニューのイベント設定
        setupMenuEvents();

        // Set up menu toggle button
        header.getMenuToggleButton().setIcon(ThemeIconUtil.ofHeaderButton(AntDesignIconsOutlined.MENU_FOLD, 20));
        header.setMenuToggleListener(e -> toggleMenuVisibility());

        // Set initial header title
        header.setTitleText("キーワード検索");

        // デフォルトでキーワード検索画面を表示
        showScreen("keyword");

        setVisible(true);
    }

    /**
     * Toggle the visibility of the menu panel
     */
    private void toggleMenuVisibility() {
        menuVisible = !menuVisible;

        // Remove all components
        removeAll();

        if (menuVisible) {
            // Reset layout to include menu
            migLayout = new MigLayout("fill", "0[300::300]0[grow, fill]0", "0[50px, fill, top]0[grow, fill]0");
            setLayout(migLayout);

            // Add menu back with original constraints
            add(menu, "w 300!, spany 2");
            add(header, "h 50!, grow, wrap");
            add(contentPanel, "grow");

            // Update icon to show "hide" option
            header.getMenuToggleButton().setIcon(ThemeIconUtil.ofHeaderButton(AntDesignIconsOutlined.MENU_FOLD, 20));
        } else {
            // Use a single column layout when menu is hidden
            migLayout = new MigLayout("fill", "0[grow, fill]0", "0[50px, fill, top]0[grow, fill]0");
            setLayout(migLayout);

            // Add components without menu
            add(header, "h 50!, grow, wrap");
            add(contentPanel, "grow");

            // Update icon to show "show" option
            header.getMenuToggleButton().setIcon(ThemeIconUtil.ofHeaderButton(AntDesignIconsOutlined.MENU_UNFOLD, 20));
        }

        // Refresh the layout
        revalidate();
        repaint();
    }

    private void setupMenuEvents() {
        // Set initial selected state for the default screen (keyword search)
        menu.getMenus().get(0).setSelected(true);

        for (MenuModel menuItem : menu.getMenus()) {
            menuItem.getButton().addActionListener(e -> {
                // Reset all menu items to unselected state
                for (MenuModel m : menu.getMenus()) {
                    m.setSelected(false);
                }

                // Set the clicked menu item to selected state
                menuItem.setSelected(true);

                // メニューインデックスに基づいて画面を切り替え
                switch (menuItem.getMenuIndex()) {
                    case 0: // キーワード検索
                        showScreen("keyword");
                        break;
                    case 1: // セラー検索
                        showScreen("seller");
                        break;
                    case 2: // お気に入りのセラー
                        showScreen("favorite");
                        break;
                    case 3: // リサーチ結果
                        showScreen("results");
                        break;
                    case 4: // 設定
                        showScreen("settings");
                        break;
                    case 5: // 商品データ削除
                        showScreen("cleanup");
                        break;
                    // 他のメニュー項目のケースをここに追加
                    default:
                        logger.info("メニューインデックス: {} に対応する画面が定義されていません", menuItem.getMenuIndex());
                        break;
                }
            });
        }
    }

    private void showScreen(String screenName) {
        // Hide all screens first
        keywordScreen.setVisible(false);
        sellerScreen.setVisible(false);
        favoriteSellerScreen.setVisible(false);
        salesAggregationPanel.setVisible(false);
        settingsScreen.setVisible(false);
        productCleanupScreen.setVisible(false);

        // Show the selected screen and update header title
        if ("keyword".equals(screenName)) {
            keywordScreen.setVisible(true);
            header.setTitleText("キーワード検索");
        } else if ("seller".equals(screenName)) {
            sellerScreen.setVisible(true);
            header.setTitleText("セラー検索");
        } else if ("favorite".equals(screenName)) {
            favoriteSellerScreen.setVisible(true);
            favoriteSellerScreen.refreshData(); // 最新データを取得
            header.setTitleText("お気に入りのセラー");
        } else if ("results".equals(screenName)) {
            salesAggregationPanel.setVisible(true);
            salesAggregationPanel.loadAggregationData(); // 最新データを取得
            header.setTitleText("リサーチ結果");
        } else if ("settings".equals(screenName)) {
            settingsScreen.setVisible(true);
            header.setTitleText("設定");
        } else if ("cleanup".equals(screenName)) {
            productCleanupScreen.setVisible(true);
            header.setTitleText("商品データ削除");
        }

        // Use CardLayout as well
        CardLayout cardLayout = (CardLayout) contentPanel.getLayout();
        cardLayout.show(contentPanel, screenName);

        // Force revalidate and repaint to ensure the screen is displayed
        contentPanel.revalidate();
        contentPanel.repaint();
    }

    /**
     * Set the expiration date to be displayed in the menu
     *
     * @param expirationDate The expiration date in yyyy/MM/dd format
     */
    public void setExpirationDate(String expirationDate) {
        logger.debug("Main.setExpirationDate が呼び出されました: {}", expirationDate);

        if (menu != null) {
            logger.debug("メニューがnullではないため、menu.setExpirationDate を呼び出します");
            menu.setExpirationDate(expirationDate);
        } else {
            logger.error("ERROR: Menu is null in Main.setExpirationDate!");
        }
    }

    /**
     * Show version notification in the menu
     */
    public void showVersionNotification() {
        logger.debug("Main.showVersionNotification が呼び出されました");

        if (menu != null) {
            logger.debug("メニューがnullではないため、menu.showVersionNotification を呼び出します");
            menu.showVersionNotification();
        } else {
            logger.error("ERROR: Menu is null in Main.showVersionNotification!");
        }
    }

    /**
     * Hide version notification in the menu
     */
    public void hideVersionNotification() {
        logger.debug("Main.hideVersionNotification が呼び出されました");

        if (menu != null) {
            logger.debug("メニューがnullではないため、menu.hideVersionNotification を呼び出します");
            menu.hideVersionNotification();
        } else {
            logger.error("ERROR: Menu is null in Main.hideVersionNotification!");
        }
    }

    /**
     * Get the keyword screen instance
     *
     * @return The keyword screen instance
     */
    public Keyword getKeywordScreen() {
        return keywordScreen;
    }

    /**
     * Get the sales aggregation panel instance
     *
     * @return The sales aggregation panel instance
     */
    public SalesAggregationPanel getSalesAggregationPanel() {
        return salesAggregationPanel;
    }

    /**
     * Show seller research result for a specific seller ID in sales aggregation screen
     */
    public void showSellerResearchResult(String sellerId) {
        // Update menu selection state to show "リサーチ結果" as selected
        for (MenuModel menuItem : menu.getMenus()) {
            menuItem.setSelected(menuItem.getMenuIndex() == 3); // リサーチ結果 is index 3
        }

        // Switch to sales aggregation screen (results screen)
        showScreen("results");

        // Filter by the specific seller ID
        salesAggregationPanel.filterBySellerId(sellerId);
    }

    /**
     * Show product research result for a specific keyword search ID in sales aggregation screen
     */
    private void handleShowProductResearch(java.beans.PropertyChangeEvent evt) {
        if (evt.getNewValue() instanceof Object[] data && data.length >= 2) {
            int keywordSearchId = (int) data[0];
            String keyword = (String) data[1];

            // Update menu selection state to show "リサーチ結果" as selected
            for (MenuModel menuItem : menu.getMenus()) {
                menuItem.setSelected(menuItem.getMenuIndex() == 3); // リサーチ結果 is index 3
            }

            // Switch to sales aggregation screen (results screen)
            showScreen("results");

            // Filter by the specific keyword search ID
            salesAggregationPanel.filterByKeywordSearchId(keywordSearchId);

            // Update header title
            header.setTitleText("リサーチ結果: " + keyword);
        }
    }

    private void handleShowSellerResearchFromSellerScreen(java.beans.PropertyChangeEvent evt) {
        if (evt.getNewValue() instanceof String sellerId) {
            showSellerResearchResult(sellerId);
        }
    }

    /**
     * Apply theme change to all components in Main panel
     * This method updates all child components with the new theme
     */
    public void applyThemeChange() {
        try {
            logger.debug("メインパネルのテーマ更新を開始します");

            // Update all child components first
            SwingUtilities.updateComponentTreeUI(this);

            // Small delay to ensure theme is applied
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            // Update menu toggle button icon with new theme colors
            if (header != null && header.getMenuToggleButton() != null) {
                if (menuVisible) {
                    header.getMenuToggleButton().setIcon(ThemeIconUtil.ofHeaderButton(
                            AntDesignIconsOutlined.MENU_FOLD, 20));
                } else {
                    header.getMenuToggleButton().setIcon(ThemeIconUtil.ofHeaderButton(
                            AntDesignIconsOutlined.MENU_UNFOLD, 20));
                }
            }

            // Update all screen components and their icons
            if (keywordScreen != null) {
                SwingUtilities.updateComponentTreeUI(keywordScreen);
                updateKeywordScreenIcons();
                keywordScreen.revalidate();
                keywordScreen.repaint();
            }

            if (settingsScreen != null) {
                SwingUtilities.updateComponentTreeUI(settingsScreen);
                updateSettingsScreenIcons();
                settingsScreen.revalidate();
                settingsScreen.repaint();
            }

            if (sellerScreen != null) {
                SwingUtilities.updateComponentTreeUI(sellerScreen);
                updateSellerScreenIcons();
                sellerScreen.revalidate();
                sellerScreen.repaint();
            }

            if (favoriteSellerScreen != null) {
                SwingUtilities.updateComponentTreeUI(favoriteSellerScreen);
                favoriteSellerScreen.revalidate();
                favoriteSellerScreen.repaint();
            }

            if (salesAggregationPanel != null) {
                SwingUtilities.updateComponentTreeUI(salesAggregationPanel);
                salesAggregationPanel.revalidate();
                salesAggregationPanel.repaint();
                // Apply theme change to update seller ID colors
                salesAggregationPanel.applyThemeChange();
            }

            if (productCleanupScreen != null) {
                SwingUtilities.updateComponentTreeUI(productCleanupScreen);
                productCleanupScreen.revalidate();
                productCleanupScreen.repaint();
            }

            // Update menu component
            if (menu != null) {
                SwingUtilities.updateComponentTreeUI(menu);
                menu.applyThemeChange();
                menu.revalidate();
                menu.repaint();
            }

            // Update content panel
            if (contentPanel != null) {
                SwingUtilities.updateComponentTreeUI(contentPanel);
                contentPanel.revalidate();
                contentPanel.repaint();
            }

            // Force complete repaint of the entire Main panel
            revalidate();
            repaint();

            // Additional force update for all buttons
            forceUpdateAllButtons();

            logger.debug("メインパネルのテーマ更新が完了しました");

        } catch (Exception e) {
            logger.error("Error updating Main panel theme: {}", e.getMessage(), e);
        }
    }


    /**
     * Update keyword screen icons with new theme colors
     */
    private void updateKeywordScreenIcons() {
        try {
            if (keywordScreen != null) {
                keywordScreen.updateIconsForTheme();
                updateComponentIcons(keywordScreen);
            }
        } catch (Exception e) {
            logger.error("Error updating keyword screen icons: {}", e.getMessage());
        }
    }

    /**
     * Update settings screen icons with new theme colors
     */
    private void updateSettingsScreenIcons() {
        try {
            if (settingsScreen != null) {
                settingsScreen.updateIconsForTheme();
                updateComponentIcons(settingsScreen);
            }
        } catch (Exception e) {
            logger.error("Error updating settings screen icons: {}", e.getMessage());
        }
    }

    /**
     * Update seller screen icons with new theme colors
     */
    private void updateSellerScreenIcons() {
        try {
            if (sellerScreen != null) {
                updateComponentIcons(sellerScreen);
            }
        } catch (Exception e) {
            logger.error("Error updating seller screen icons: {}", e.getMessage());
        }
    }

    /**
     * Recursively update all FontIcon components and button styles in a container
     */
    private void updateComponentIcons(Container container) {
        try {
            Component[] components = container.getComponents();
            for (Component component : components) {
                if (component instanceof JButton button) {

                    // Skip menu buttons - they are handled by Menu.applyThemeChange()
                    if (isMenuButton(button)) {
                        logger.debug("メニューボタンをスキップします: {}", button.getText());
                        continue;
                    }

                    // Update button background color based on theme
                    updateButtonStyle(button);

                    // Update icon color if it's a FontIcon
                    Icon icon = button.getIcon();
                    if (icon instanceof org.kordamp.ikonli.swing.FontIcon fontIcon) {

                        Color oldColor = fontIcon.getIconColor();

                        // Skip status icons (green, red, yellow/orange) as they should maintain their specific colors
                        if (isStatusIcon(oldColor)) {
                            logger.debug("ステータスアイコンをスキップします (色: {})", oldColor);
                            continue;
                        }

                        // Get the appropriate button icon color for the current theme
                        Color newColor = ThemeIconUtil.getButtonIconColor();

                        // Update the icon color based on current theme
                        fontIcon.setIconColor(newColor);

                        // Debug output
                        logger.debug("ボタンアイコンの色が {} から {} に変更されました", oldColor, newColor);
                    }

                    // Force button to repaint
                    button.revalidate();
                    button.repaint();

                } else if (component instanceof Container) {
                    // Recursively update child containers
                    updateComponentIcons((Container) component);
                }
            }
        } catch (Exception e) {
            logger.error("Error updating component icons: {}", e.getMessage(), e);
        }
    }

    /**
     * Check if a color represents a status icon that should not be changed
     */
    private boolean isStatusIcon(Color color) {
        if (color == null) return false;

        // Check for common status colors (green, red, yellow/orange)
        return color.equals(Color.GREEN) ||
                color.equals(new Color(0, 128, 0)) ||
                color.equals(Color.RED) ||
                color.equals(new Color(204, 204, 0)) ||
                color.equals(Color.YELLOW) ||
                color.equals(Color.ORANGE);
    }

    /**
     * Check if a button is a menu button that should be handled by Menu.applyThemeChange()
     *
     * @param button The button to check
     * @return true if it's a menu button, false otherwise
     */
    private boolean isMenuButton(JButton button) {
        if (button == null) return false;

        // Check if the button is part of the menu by checking its text content
        String buttonText = button.getText();
        if (buttonText != null) {
            return buttonText.equals("キーワード検索") ||
                    buttonText.equals("セラー検索") ||
                    buttonText.equals("お気に入りのセラー") ||
                    buttonText.equals("リサーチ結果") ||
                    buttonText.equals("設定") ||
                    buttonText.equals("商品データ削除");
        }

        return false;
    }

    /**
     * Update button style based on current theme
     */
    private void updateButtonStyle(JButton button) {
        try {
            // Check if this is a special button (like header button) that should keep its custom colors
            Color currentBg = button.getBackground();

            // Skip buttons with specific custom colors (like header buttons)
            if (currentBg != null && (
                    currentBg.equals(new Color(59, 89, 152)) || // Header button color
                            currentBg.getRed() == 59 && currentBg.getGreen() == 89 && currentBg.getBlue() == 152
            )) {
                // Keep custom colored buttons as they are
                return;
            }

            // For regular buttons, reset background to use theme defaults
            button.setBackground(null);

            // Force the button to use the current theme's button style
            SwingUtilities.updateComponentTreeUI(button);

            logger.debug("テーマに合わせてボタンのスタイルを更新しました");

        } catch (Exception e) {
            logger.error("Error updating button style: {}", e.getMessage());
        }
    }

    /**
     * Force update all buttons in the entire Main panel
     */
    private void forceUpdateAllButtons() {
        try {
            logger.debug("すべてのボタンを強制的に更新します");

            // Update all buttons recursively
            forceUpdateButtonsInContainer(this);

        } catch (Exception e) {
            logger.error("Error force updating all buttons: {}", e.getMessage());
        }
    }

    /**
     * Recursively force update all buttons in a container
     */
    private void forceUpdateButtonsInContainer(Container container) {
        try {
            Component[] components = container.getComponents();
            for (Component component : components) {
                if (component instanceof JButton button) {

                    // Skip header buttons with custom colors
                    Color currentBg = button.getBackground();
                    if (currentBg != null && currentBg.equals(new Color(59, 89, 152))) {
                        continue;
                    }

                    // Reset button properties to use theme defaults
                    button.setBackground(null);
                    button.setOpaque(true);

                    // Force UI update
                    SwingUtilities.updateComponentTreeUI(button);
                    button.revalidate();
                    button.repaint();

                } else if (component instanceof Container) {
                    // Recursively update child containers
                    forceUpdateButtonsInContainer((Container) component);
                }
            }
        } catch (Exception e) {
            logger.error("Error force updating buttons in container: {}", e.getMessage());
        }
    }

}