package com.mrcresearch.screen.keyword;

import com.formdev.flatlaf.FlatClientProperties;
import com.mrcresearch.model.ProgressItem;
import com.mrcresearch.screen.component.ColorCheckBox;
import com.mrcresearch.screen.component.KeywordSearchResultPanel;
import com.mrcresearch.screen.component.category.Categories;
import com.mrcresearch.screen.component.category.CategoryModel;
import com.mrcresearch.screen.model.ColorModel;
import com.mrcresearch.screen.model.DetailedSearchModel;
import com.mrcresearch.screen.model.ItemConditionModel;
import com.mrcresearch.service.analyze.service.SearchByKeyWord;
import com.mrcresearch.service.common.FontSettings;
import com.mrcresearch.util.SearchQueueManager;
import com.mrcresearch.util.TextFieldContextMenuUtil;
import com.mrcresearch.util.ThemeIconUtil;
import com.mrcresearch.util.database.DatabaseUtil;
import com.mrcresearch.util.database.SettingsRepository;
import net.miginfocom.swing.MigLayout;
import org.kordamp.ikonli.antdesignicons.AntDesignIconsOutlined;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import javax.swing.Timer;
import javax.swing.text.AbstractDocument;
import javax.swing.text.AttributeSet;
import javax.swing.text.BadLocationException;
import javax.swing.text.DocumentFilter;
import java.awt.*;
import java.beans.PropertyChangeEvent;
import java.util.*;
import java.util.List;
import java.util.Queue;


/**
 * Keyword search panel for finding products based on various criteria.
 * This class encapsulates the UI and logic for the keyword search screen.
 */
public class Keyword extends JPanel {

    private static final Logger logger = LoggerFactory.getLogger(Keyword.class);

    // --- Constants ---
    public static final String CATEGORY_ALL = "すべて";
    private static final int CATEGORY_LEVEL_0 = 0;
    private static final int CATEGORY_LEVEL_1 = 1;
    private static final int CATEGORY_LEVEL_2 = 2;
    private static final int CATEGORY_LEVEL_3 = 3;
    private static final int CATEGORY_LEVEL_4 = 4;
    private static final int CATEGORY_LEVEL_SELECT = 5;


    // --- UI Components ---
    private JPanel mainPanel;

    // Keyword Search
    private JTextField txtKeyword;
    private JTextField txtPages;
    private JButton btnSearch;
    private JButton btnClearAll;

    // Category Search
    private JComboBox<String> categoryCombo1;
    private JComboBox<String> categoryCombo2;
    private JComboBox<String> categoryCombo3;
    private JComboBox<String> categoryCombo4;
    private JComboBox<String> categoryCombo5;
    private JList<String> multiSelectList;
    private JButton btnClearCategories;

    // Price Range
    private JTextField txtPriceMin;
    private JTextField txtPriceMax;

    // Search Options
    private JComboBox<String> soldStatusCombo;
    private JCheckBox chkSortByNew;
    private JCheckBox chkIgnoreShops;

    // Detailed Search
    private JPanel detailedSearchPanel;
    private JCheckBox[] itemConditionCheckboxes;
    private JTextField txtExcludeKeyword;
    private ColorCheckBox[] colorCheckboxes;

    // Search Results
    private KeywordSearchResultPanel searchResultPanel;

    // --- Data Models & Services ---
    private static final Categories categories = new Categories();
    private static final HashMap<Integer, List<CategoryModel>> categoryMap = new HashMap<>();
    private Object[] selectedItems = null;

    /**
     * Constructor for the Keyword panel.
     */
    public Keyword() {
        init();
        loadDefaultSettings();
        setVisible(true);
    }

    // --- Initialization ---

    /**
     * Initializes the entire keyword search panel by orchestrating component creation,
     * layout, and event listener setup.
     */
    private void init() {
        setLayout(new MigLayout("fill,insets 20", "[grow]", "[grow]"));
        DatabaseUtil.initDatabase();

        createComponents();
        layoutComponents();
        setupEventListeners();
        setupInputValidation();

        // Setup the page adjustment buttons after the components are laid out
        setupPageAdjustmentButtons();

        // Add property change listener to handle restore button click from search results
        searchResultPanel.addPropertyChangeListener("restoreSearch", this::handleRestoreSearch);
        searchResultPanel.addPropertyChangeListener("showProductResearch", this::handleShowProductResearch);

        // Wrap main panel in a scroll pane
        JScrollPane scrollablePanel = new JScrollPane(mainPanel);
        scrollablePanel.setBorder(BorderFactory.createEmptyBorder());
        scrollablePanel.getVerticalScrollBar().setUnitIncrement(16);

        // Create and add the tabbed pane
        JTabbedPane tabbedPane = createTabbedPane(scrollablePanel);
        add(tabbedPane, "grow");
    }

    /**
     * Creates and initializes all UI components.
     */
    private void createComponents() {
        // Main container panel with rounded corners
        mainPanel = new JPanel(new MigLayout("fillx,insets 35 45 30 45", "[fill,250:280][fill,250:280]"));
        mainPanel.putClientProperty(FlatClientProperties.STYLE, "arc:20;");

        // Keyword input components
        txtKeyword = new JTextField();
        txtPages = new JTextField("10"); // Default value, overridden by loadDefaultSettings()
        btnSearch = new JButton(ThemeIconUtil.ofButton(AntDesignIconsOutlined.SEARCH, 16));
        btnClearAll = new JButton(ThemeIconUtil.ofButton(AntDesignIconsOutlined.CLEAR, 16));

        // Price range components
        txtPriceMin = new JTextField();
        txtPriceMax = new JTextField();

        // Status and options components
        soldStatusCombo = new JComboBox<>(new String[]{"すべて", "販売済み", "販売中"});
        chkSortByNew = new JCheckBox("新着順");
        chkIgnoreShops = new JCheckBox("SHOPSを無視");

        // Category components
        initCategories(); // Initializes category combos and list
        btnClearCategories = new JButton(ThemeIconUtil.ofButton(AntDesignIconsOutlined.CLEAR, 16));
        btnClearCategories.setText("クリア");

        // Detailed search panel and its components
        createDetailedSearchPanel();

        // Search results panel
        searchResultPanel = new KeywordSearchResultPanel();

        // Set default states for options
        soldStatusCombo.setSelectedItem("販売済み");
        chkSortByNew.setSelected(true);
        chkIgnoreShops.setSelected(true);
    }

    /**
     * Lays out all UI components on the main panel using MigLayout.
     */
    private void layoutComponents() {
        // Section 1: Keyword
        addLabelAndComponent(mainPanel, "キーワード:", txtKeyword, "span 2, grow, wrap");
        txtKeyword.putClientProperty(FlatClientProperties.PLACEHOLDER_TEXT, "検索キーワードを入力");
        txtKeyword.setFont(FontSettings.getTextFont());

        // Section 2: Categories
        layoutCategoryPanel();

        // Section 3: Price Range and Sold Status
        layoutPriceAndStatusPanel();

        // Section 4: Options and Pages
        layoutOptionsAndPagesPanel();

        // Section 5: Detailed Search Panel
        mainPanel.add(detailedSearchPanel, "span 2, grow, wrap");

        // Section 6: Search and Clear Buttons
        layoutButtonPanel();
    }

    /**
     * Sets up all event listeners for the UI components.
     */
    private void setupEventListeners() {
        // Enable context menus for all relevant text fields
        TextFieldContextMenuUtil.enableTextFieldContextMenu(txtKeyword);
        TextFieldContextMenuUtil.enableTextFieldContextMenu(txtPages);
        TextFieldContextMenuUtil.enableTextFieldContextMenu(txtPriceMin);
        TextFieldContextMenuUtil.enableTextFieldContextMenu(txtPriceMax);
        TextFieldContextMenuUtil.enableTextFieldContextMenu(txtExcludeKeyword);


        // Add focus listeners to validate price fields when focus is lost
        txtPriceMin.addFocusListener(new java.awt.event.FocusAdapter() {
            @Override
            public void focusLost(java.awt.event.FocusEvent evt) {
                validatePriceField(txtPriceMin, 300, 9999999);
            }
        });
        txtPriceMax.addFocusListener(new java.awt.event.FocusAdapter() {
            @Override
            public void focusLost(java.awt.event.FocusEvent evt) {
                validatePriceField(txtPriceMax, 300, 9999999);
            }
        });

        // Main search button action
        btnSearch.addActionListener(e -> performSearch());

        // Clear All button action
        btnClearAll.addActionListener(e -> {
            int result = JOptionPane.showConfirmDialog(this, "入力内容を全てクリアしますか？", "全クリア確認", JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);
            if (result == JOptionPane.YES_OPTION) {
                clearAllInputFields();
            }
        });

        // Set up listener for the 'Clear Categories' button
        initClearButton();
    }

    /**
     * Sets up input validation filters for text fields like price and pages.
     */
    private void setupInputValidation() {
        // Price Min/Max fields only accept digits
        ((AbstractDocument) txtPriceMin.getDocument()).setDocumentFilter(new NumericDocumentFilter());
        ((AbstractDocument) txtPriceMax.getDocument()).setDocumentFilter(new NumericDocumentFilter());

        // Pages field validation happens on focus lost instead of during input
        // This allows for easier entry of two-digit numbers
        txtPages.addFocusListener(new java.awt.event.FocusAdapter() {
            @Override
            public void focusLost(java.awt.event.FocusEvent evt) {
                validatePageNumber();
            }
        });
    }

    /**
     * Validates the page number input and corrects it if necessary.
     *
     * @return true if the page number is valid, false otherwise
     */
    private boolean validatePageNumber() {
        try {
            String text = txtPages.getText().trim();
            if (text.isEmpty()) {
                txtPages.setText("10"); // Default value
                return true;
            }

            int value = Integer.parseInt(text);
            if (value < 2) {
                txtPages.setText("2");
                return false;
            } else if (value > 99) {
                txtPages.setText("99");
                return false;
            }
            return true;
        } catch (NumberFormatException e) {
            txtPages.setText("10"); // Default value on error
            return false;
        }
    }

    // --- Layout Helper Methods ---

    /**
     * Lays out the category selection components.
     */
    private void layoutCategoryPanel() {
        JLabel categoryLabel = new JLabel("カテゴリー:");
        categoryLabel.setFont(FontSettings.getLabelFont());
        mainPanel.add(categoryLabel, "span 2, gaptop 10, wrap");

        // Left panel for dropdowns
        JPanel categoryLeftPanel = new JPanel(new MigLayout("wrap, fillx, insets 0", "[fill]"));
        categoryCombo1.setFont(FontSettings.getLabelFont());
        categoryLeftPanel.add(categoryCombo1, "grow");
        categoryCombo2.setFont(FontSettings.getLabelFont());
        categoryLeftPanel.add(categoryCombo2, "grow");
        categoryCombo3.setFont(FontSettings.getLabelFont());
        categoryLeftPanel.add(categoryCombo3, "grow");
        categoryCombo4.setFont(FontSettings.getLabelFont());
        categoryLeftPanel.add(categoryCombo4, "grow");
        mainPanel.add(categoryLeftPanel, "grow");

        // Right panel for multi-select list and clear button
        JPanel categoryRightPanel = new JPanel(new MigLayout("fillx, insets 0, wrap", "[fill]"));
        multiSelectList.setFont(FontSettings.getLabelFont());
        JScrollPane scrollPane = new JScrollPane(multiSelectList);
        categoryRightPanel.add(scrollPane, "grow, h 175px");

        btnClearCategories.setFont(FontSettings.getLabelFont());
        categoryRightPanel.add(btnClearCategories, "align right");
        mainPanel.add(categoryRightPanel, "grow, wrap");
    }

    /**
     * Lays out the price range and sales status components.
     */
    private void layoutPriceAndStatusPanel() {
        JPanel pricePanel = new JPanel(new MigLayout("insets 0", "[grow][grow][][grow]"));
        JLabel priceRangeLabel = new JLabel("価格範囲 (300-9999999):");
        priceRangeLabel.setFont(FontSettings.getLabelFont());
        pricePanel.add(priceRangeLabel, "gaptop 10");

        txtPriceMin.setFont(FontSettings.getLabelFont());
        txtPriceMin.putClientProperty(FlatClientProperties.PLACEHOLDER_TEXT, "最小価格");
        pricePanel.add(txtPriceMin, "grow");

        JLabel priceRangeSeparator = new JLabel(" 〜 ");
        priceRangeSeparator.setFont(FontSettings.getLabelFont());
        pricePanel.add(priceRangeSeparator, "align center");

        txtPriceMax.setFont(FontSettings.getLabelFont());
        txtPriceMax.putClientProperty(FlatClientProperties.PLACEHOLDER_TEXT, "最大価格");
        pricePanel.add(txtPriceMax, "grow");

        JLabel soldStatusLabel = new JLabel("販売状態:");
        soldStatusLabel.setFont(FontSettings.getLabelFont());
        pricePanel.add(soldStatusLabel, "gapleft 20");

        soldStatusCombo.setFont(FontSettings.getLabelFont());
        pricePanel.add(soldStatusCombo, "grow");

        mainPanel.add(pricePanel, "span 2, grow, wrap");
    }

    /**
     * Lays out the search options (checkboxes) and page number input.
     */
    private void layoutOptionsAndPagesPanel() {
        JPanel optionsPanel = new JPanel(new MigLayout("insets 0", "[][grow][][grow]"));

        chkSortByNew.setFont(FontSettings.getLabelFont());
        optionsPanel.add(chkSortByNew, "gaptop 10");

        chkIgnoreShops.setFont(FontSettings.getLabelFont());
        optionsPanel.add(chkIgnoreShops, "gaptop 10");

        JLabel pagesLabel = new JLabel("ページ数 (2-99):");
        pagesLabel.setFont(FontSettings.getLabelFont());
        optionsPanel.add(pagesLabel, "gaptop 10, gapleft 20");

        JPanel pageInputPanel = createPageInputPanel();
        optionsPanel.add(pageInputPanel, "grow, gaptop 10");

        mainPanel.add(optionsPanel, "span 2, grow, wrap");
    }

    /**
     * Lays out the final action buttons (Search, Clear All).
     */
    private void layoutButtonPanel() {
        btnSearch.setText("検索");
        btnClearAll.setText("全クリア");
        btnSearch.setFont(FontSettings.getLabelFont());
        btnClearAll.setFont(FontSettings.getLabelFont());

        JPanel buttonPanel = new JPanel(new MigLayout("insets 0", "[grow][grow]"));
        buttonPanel.add(btnClearAll, "grow");
        buttonPanel.add(btnSearch, "grow, gapleft 10");
        mainPanel.add(buttonPanel, "span 2, gaptop 10, grow, wrap");
    }

    /**
     * Creates the panel with page number input and +/- buttons.
     *
     * @return The fully constructed page input panel.
     */
    private JPanel createPageInputPanel() {
        JPanel pageInputPanel = new JPanel(new MigLayout("insets 0", "[][][grow][][]"));
        txtPages.putClientProperty(FlatClientProperties.PLACEHOLDER_TEXT, "2-99");
        txtPages.setFont(FontSettings.getLabelFont());

        JButton btnMinus10 = new JButton("-10");
        JButton btnMinus5 = new JButton("-5");
        JButton btnPlus5 = new JButton("+5");
        JButton btnPlus10 = new JButton("+10");

        btnMinus10.setFont(FontSettings.getSmallFont());
        btnMinus5.setFont(FontSettings.getSmallFont());
        btnPlus5.setFont(FontSettings.getSmallFont());
        btnPlus10.setFont(FontSettings.getSmallFont());

        pageInputPanel.add(btnMinus10);
        pageInputPanel.add(btnMinus5);
        pageInputPanel.add(txtPages, "grow");
        pageInputPanel.add(btnPlus5);
        pageInputPanel.add(btnPlus10);

        // Action listeners are set up in setupPageAdjustmentButtons
        return pageInputPanel;
    }

    /**
     * Creates the main tabbed pane for search input and results.
     *
     * @param inputPanel The panel containing all search input fields.
     * @return The configured JTabbedPane.
     */
    private JTabbedPane createTabbedPane(Component inputPanel) {
        JTabbedPane tabbedPane = new JTabbedPane();
        tabbedPane.putClientProperty(FlatClientProperties.TABBED_PANE_TAB_HEIGHT, 40);
        tabbedPane.putClientProperty(FlatClientProperties.STYLE, "font:+2");

        JPanel inputTab = new JPanel(new MigLayout("fill", "[grow]", "[grow]"));
        inputTab.add(inputPanel, "grow");

        JPanel resultsTab = new JPanel(new MigLayout("fill", "[grow]", "[grow]"));
        resultsTab.add(searchResultPanel, "grow");

        tabbedPane.addTab("キーワード検索", inputTab);
        tabbedPane.addTab("履歴", resultsTab);

        // Add change listener to reload data when the results tab is selected
        tabbedPane.addChangeListener(e -> {
            if (tabbedPane.getSelectedIndex() == 1) { // Results tab
                SwingUtilities.invokeLater(() -> searchResultPanel.loadData());
            }
        });

        return tabbedPane;
    }

    /**
     * Helper to add a labeled component to a panel.
     *
     * @param panel       The parent panel.
     * @param labelText   The text for the label.
     * @param component   The component to add.
     * @param constraints The MigLayout constraints for the component.
     */
    private void addLabelAndComponent(JPanel panel, String labelText, JComponent component, String constraints) {
        JLabel label = new JLabel(labelText);
        label.setFont(FontSettings.getLabelFont());
        panel.add(label, "span 2, grow, wrap");
        panel.add(component, constraints);
    }

    // --- Event Listener Setup ---

    /**
     * Sets up action listeners for the page number adjustment buttons (+/-, +5/-5, etc.).
     */
    private void setupPageAdjustmentButtons() {
        // Find the buttons in the page input panel
        JPanel pageInputPanel = (JPanel) txtPages.getParent();
        if (pageInputPanel == null) {
            return; // Safety check
        }

        // Get all components in the panel
        Component[] components = pageInputPanel.getComponents();
        JButton btnMinus10 = null;
        JButton btnMinus5 = null;
        JButton btnPlus5 = null;
        JButton btnPlus10 = null;

        // Find the buttons by their text
        for (Component component : components) {
            if (component instanceof JButton) {
                JButton button = (JButton) component;
                switch (button.getText()) {
                    case "-10":
                        btnMinus10 = button;
                        break;
                    case "-5":
                        btnMinus5 = button;
                        break;
                    case "+5":
                        btnPlus5 = button;
                        break;
                    case "+10":
                        btnPlus10 = button;
                        break;
                }
            }
        }

        // Add action listeners to the buttons
        if (btnMinus10 != null) {
            btnMinus10.addActionListener(e -> adjustPageNumber(-10));
        }

        if (btnMinus5 != null) {
            btnMinus5.addActionListener(e -> adjustPageNumber(-5));
        }

        if (btnPlus5 != null) {
            btnPlus5.addActionListener(e -> adjustPageNumber(5));
        }

        if (btnPlus10 != null) {
            btnPlus10.addActionListener(e -> adjustPageNumber(10));
        }
    }

    /**
     * Adjusts the page number by the specified amount, ensuring it stays within valid range.
     *
     * @param amount The amount to adjust by (can be positive or negative)
     */
    private void adjustPageNumber(int amount) {
        try {
            int currentPages = Integer.parseInt(txtPages.getText().trim());
            int newPages = currentPages + amount;

            // Ensure the value stays within valid range
            newPages = Math.max(2, Math.min(99, newPages));

            txtPages.setText(String.valueOf(newPages));
        } catch (NumberFormatException e) {
            // If current value is invalid, set to default
            txtPages.setText("10");
        }
    }


    // --- Search Logic ---

    /**
     * Gathers all search parameters, validates them, and initiates the search task.
     */
    private void performSearch() {
        String keyword = getKeyword();
        if (keyword == null || keyword.trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "キーワードを入力してください", "入力エラー", JOptionPane.WARNING_MESSAGE);
            return;
        }

        SearchQueueManager.SearchTask keywordSearchTask = createSearchTask(keyword);
        SearchQueueManager.getInstance().addTask(keywordSearchTask);

        // Fallback timer to re-enable search button if something goes wrong
        Timer fallbackTimer = new Timer(60000, evt -> {
            if (!btnSearch.isEnabled()) {
                btnSearch.setEnabled(true);
                btnClearAll.setEnabled(true);
                btnSearch.setText("検索");
                logger.debug("フォールバックタイマーにより検索ボタンがリセットされました。");
            }
        });
        fallbackTimer.setRepeats(false);
        fallbackTimer.start();
    }

    /**
     * Creates a new search task with all the current parameters from the UI.
     *
     * @param keyword The search keyword.
     * @return A configured SearchTask instance.
     */
    private SearchQueueManager.SearchTask createSearchTask(String keyword) {
        return new SearchQueueManager.SearchTask() {
            private final String searchKeyword = keyword;
            private final String searchConditions = buildSearchConditions(searchKeyword);

            @Override
            public void execute() throws Exception {
                String categoriesStr = createCategoriesStr();
                String categoryIdStr = createCategoryIdStr();
                String researchStatus = "リサーチ中"; // "Researching"

                int minPrice = getPriceMin();
                int maxPrice = getPriceMax();
                int pages = getPages();
                boolean sortByNew = isSortByNew();
                boolean ignoreShops = isIgnoreShops();
                String salesStatus = getSoldStatus();

                DetailedSearchModel detailedSearch = getDetailedSearchModel();
                String itemConditionIds = detailedSearch.getItemConditionIdsAsString();
                String excludeKeyword = detailedSearch.getExcludeKeyword();
                String colorIds = detailedSearch.getColorIdsAsString();

                final int[] recordId = new int[1];
                SwingUtilities.invokeAndWait(() -> {
                    if (searchResultPanel != null) {
                        searchResultPanel.showLoadingIndicator();
                        recordId[0] = searchResultPanel.addKeywordSearchResultWithDetailedOptions(
                                searchKeyword, categoriesStr, researchStatus, minPrice, maxPrice, pages,
                                sortByNew, ignoreShops, salesStatus, itemConditionIds, excludeKeyword, colorIds,
                                categoryIdStr);
                    }

                    JTabbedPane tabbedPane = findTabbedPane();
                    if (tabbedPane != null) {
                        tabbedPane.setSelectedIndex(1); // Switch to results tab
                    }
                    clearAllInputFields(); // Auto-clear fields after search starts
                });

                if (recordId[0] > 0) {
                    String progressId = "keyword_" + recordId[0] + "_" + System.currentTimeMillis();
                    ProgressItem progressItem = new ProgressItem(progressId, "キーワード検索", searchKeyword, 1, 1);
                    progressItem.setStatus(ProgressItem.Status.PROCESSING);
                    SearchQueueManager.getInstance().addProgressItem(progressItem);
                    startAsyncStatusUpdateWithProgressAndCompletion(recordId[0], progressItem);
                }
            }

            @Override
            public String getTaskName() {
                return "キーワード検索: " + searchKeyword;
            }

            @Override
            public String getTaskType() {
                return "KEYWORD";
            }

            @Override
            public int getEstimatedTimeSeconds() {
                return 50;
            }

            @Override
            public int getPriority() {
                return 100;
            }

            @Override
            public String getDetailedDescription() {
                return searchConditions;
            }

            @Override
            public boolean hasAsyncOperations() {
                return true;
            }

            @Override
            public void onTaskCompleted() {
                logger.info("キーワード検索タスクが完了しました: " + searchKeyword);
            }
        };
    }

    /**
     * Starts the background thread to perform the search and update the UI upon completion.
     *
     * @param recordId     The database record ID for this search.
     * @param progressItem The progress tracking item for the queue.
     */
    private void startAsyncStatusUpdateWithProgressAndCompletion(int recordId, ProgressItem progressItem) {
        Thread statusUpdateThread = new Thread(() -> {
            // Execute the actual search
            new SearchByKeyWord().execute(recordId);

            // Refresh the UI on the Event Dispatch Thread
            SwingUtilities.invokeLater(() -> {
                if (searchResultPanel != null) {
                    searchResultPanel.loadData();
                }
            });

            // Notify the queue manager that this task is fully complete
            SearchQueueManager.getInstance().notifyTaskCompleted();
        });
        statusUpdateThread.setDaemon(true);
        statusUpdateThread.start();
    }


    // --- Category Management ---

    /**
     * Initializes the category components, including combo boxes and the multi-select list.
     */
    private void initCategories() {
        categories.init();

        // Initialize the map that holds category data for each level
        categoryMap.put(CATEGORY_LEVEL_0, categories.getCategoryFromParentId("0"));
        for (int i = 1; i <= 5; i++) {
            categoryMap.put(i, null);
        }

        // Create combo boxes
        categoryCombo1 = new JComboBox<>();
        categoryCombo2 = new JComboBox<>();
        categoryCombo3 = new JComboBox<>();
        categoryCombo4 = new JComboBox<>();
        categoryCombo5 = new JComboBox<>();

        // Populate the first combo box with root categories
        if (categories.isInitialized()) {
            categoryCombo1.setModel(categories.getCategoryComboBoxModel(categoryMap.get(CATEGORY_LEVEL_0)));
        } else {
            categoryCombo1.setModel(categories.getEmptyComboBoxModel());
        }

        // Hide other combo boxes initially
        categoryCombo2.setModel(categories.getEmptyComboBoxModel());
        categoryCombo2.setVisible(false);
        categoryCombo3.setModel(categories.getEmptyComboBoxModel());
        categoryCombo3.setVisible(false);
        categoryCombo4.setModel(categories.getEmptyComboBoxModel());
        categoryCombo4.setVisible(false);
        categoryCombo5.setModel(categories.getEmptyComboBoxModel());
        categoryCombo5.setVisible(false);

        // Create the multi-select list
        multiSelectList = new JList<>(new DefaultListModel<>());
        multiSelectList.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);

        // Set up listeners for category changes
        setUpCategoryListeners();
    }

    /**
     * Sets up the action listeners for all category combo boxes.
     */
    private void setUpCategoryListeners() {
        categoryCombo1.addActionListener(e -> selectCategoryCombo(CATEGORY_LEVEL_0, categoryCombo1, categoryCombo2));
        categoryCombo2.addActionListener(e -> selectCategoryCombo(CATEGORY_LEVEL_1, categoryCombo2, categoryCombo3));
        categoryCombo3.addActionListener(e -> selectCategoryCombo(CATEGORY_LEVEL_2, categoryCombo3, categoryCombo4));
        categoryCombo4.addActionListener(e -> selectCategoryCombo(CATEGORY_LEVEL_3, categoryCombo4, categoryCombo5));
        categoryCombo5.addActionListener(e -> {
            resetCategories(CATEGORY_LEVEL_4);
            if (!categoryCombo5.getSelectedItem().toString().equals(CATEGORY_ALL)) {
                List<CategoryModel> nextCategories = categories.getNextCategoryList(
                        categoryMap.get(CATEGORY_LEVEL_4), categoryCombo5.getSelectedItem().toString());
                categoryMap.put(CATEGORY_LEVEL_SELECT, nextCategories);
                multiSelectList.setModel(categories.getCategoryListModel(nextCategories));
            }
        });
    }

    /**
     * Handles the selection change of a category combo box, updating the next level's content.
     *
     * @param categoryNum  The level of the combo box being updated.
     * @param currentCombo The combo box that triggered the event.
     * @param nextCombo    The next combo box to populate.
     */
    private void selectCategoryCombo(int categoryNum, JComboBox<String> currentCombo, JComboBox<String> nextCombo) {
        if (currentCombo.getSelectedItem() == null) return;

        String selectedCategory = currentCombo.getSelectedItem().toString();
        if (!categories.isValidCategoryName(selectedCategory)) return;

        resetCategories(categoryNum); // Reset all subsequent category levels

        setCursor(Cursor.getPredefinedCursor(Cursor.WAIT_CURSOR));
        try {
            if (!selectedCategory.equals(CATEGORY_ALL)) {
                if (!categories.childHasCheckBox(categoryMap.get(categoryNum), selectedCategory)) {
                    // If children are also categories, populate the next combo box
                    List<CategoryModel> nextCategories = categories.getNextCategoryList(categoryMap.get(categoryNum), selectedCategory);
                    if (nextCategories != null && !nextCategories.isEmpty()) {
                        categoryMap.put(categoryNum + 1, nextCategories);
                        nextCombo.setModel(categories.getNextCategoryComboBoxModel(nextCategories));
                        nextCombo.setVisible(true);
                        nextCombo.repaint();
                    } else {
                        nextCombo.setVisible(false);
                    }
                } else {
                    // If children are selectable items, populate the multi-select list
                    List<CategoryModel> checkboxCategories = categories.getNextCategoryList(categoryMap.get(categoryNum), selectedCategory);
                    if (checkboxCategories != null) {
                        categoryMap.put(CATEGORY_LEVEL_SELECT, checkboxCategories); // Treat as final level before selection
                        multiSelectList.setModel(categories.getCategoryListModel(checkboxCategories));
                        multiSelectList.repaint();
                    }
                }
            }
        } finally {
            setCursor(Cursor.getDefaultCursor());
        }
    }

    /**
     * Resets category selections from a specified level downwards.
     * This method uses a fall-through switch, so calling with level 0 will reset everything.
     *
     * @param level The category level to start resetting from (0-4).
     */
    public void resetCategories(int level) {
        switch (level) {
            case CATEGORY_LEVEL_0:
                categoryMap.put(CATEGORY_LEVEL_1, null);
                categoryCombo2.setModel(categories.getEmptyComboBoxModel());
                categoryCombo2.setVisible(false);
            case CATEGORY_LEVEL_1:
                categoryMap.put(CATEGORY_LEVEL_2, null);
                categoryCombo3.setModel(categories.getEmptyComboBoxModel());
                categoryCombo3.setVisible(false);
            case CATEGORY_LEVEL_2:
                categoryMap.put(CATEGORY_LEVEL_3, null);
                categoryCombo4.setModel(categories.getEmptyComboBoxModel());
                categoryCombo4.setVisible(false);
            case CATEGORY_LEVEL_3:
                categoryMap.put(CATEGORY_LEVEL_4, null);
                categoryCombo5.setModel(categories.getEmptyComboBoxModel());
                categoryCombo5.setVisible(false);
            case CATEGORY_LEVEL_4:
                categoryMap.put(CATEGORY_LEVEL_SELECT, null);
                multiSelectList.setModel(new DefaultListModel<>());
                multiSelectList.clearSelection();
                break;
            default:
                // Do nothing
        }

        // Repaint affected components
        SwingUtilities.invokeLater(() -> {
            switch (level) {
                case CATEGORY_LEVEL_0:
                    categoryCombo2.repaint();
                case CATEGORY_LEVEL_1:
                    categoryCombo3.repaint();
                case CATEGORY_LEVEL_2:
                    categoryCombo4.repaint();
                case CATEGORY_LEVEL_3:
                    categoryCombo5.repaint();
                case CATEGORY_LEVEL_4:
                    multiSelectList.repaint();
                default:
                    break;
            }
        });
    }

    // --- Field Clearing and Resetting ---

    /**
     * Clears all input fields on the panel and resets them to their default state.
     */
    private void clearAllInputFields() {
        setCursor(Cursor.getPredefinedCursor(Cursor.WAIT_CURSOR));
        try {
            txtKeyword.setText("");
            txtPriceMin.setText("");
            txtPriceMax.setText("");
            txtPages.setText(getDefaultPageValue());
            soldStatusCombo.setSelectedItem("販売済み");
            chkSortByNew.setSelected(true);
            chkIgnoreShops.setSelected(true);
            clearAllCategories();
            clearDetailedSearchFields();

            // Repaint all components for immediate visual feedback
            SwingUtilities.invokeLater(this::repaintAll);
        } catch (Exception e) {
            logger.error("Error clearing input fields: " + e.getMessage(), e);
        } finally {
            setCursor(Cursor.getDefaultCursor());
        }
    }

    /**
     * Clears all category selections and resets the category UI.
     */
    private void clearAllCategories() {
        setCursor(Cursor.getPredefinedCursor(Cursor.WAIT_CURSOR));
        try {
            categoryCombo1.setSelectedIndex(0); // This will trigger the reset cascade
        } finally {
            setCursor(Cursor.getDefaultCursor());
        }
    }

    /**
     * Adds the listener for the 'Clear Categories' button.
     */
    private void initClearButton() {
        btnClearCategories.addActionListener(e -> {
            int result = JOptionPane.showConfirmDialog(this, "すべてのカテゴリ選択をクリアしますか？", "カテゴリクリア確認", JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);
            if (result == JOptionPane.YES_OPTION) {
                clearAllCategories();
            }
        });
    }

    /**
     * Restores the category selection based on a category display name string.
     *
     * @param categoryPathStr A string like "Cat1 > Cat2, Item1, Item2".
     */
    private void restoreCategoriesByName(String categoryPathStr) {
        if (categoryPathStr == null || categoryPathStr.trim().isEmpty()) {
            clearAllCategories();
            return;
        }

        // 1. Parse the input string
        String[] multiSelectParts = categoryPathStr.split(", ");
        String[] pathParts = multiSelectParts[0].split(" > ");

        Queue<String> pathQueue = new java.util.LinkedList<>(Arrays.asList(pathParts));
        List<String> multiSelectNames = new java.util.ArrayList<>();
        if (multiSelectParts.length > 1) {
            for (int i = 1; i < multiSelectParts.length; i++) {
                multiSelectNames.add(multiSelectParts[i]);
            }
        }

        // 2. Start the selection process on the EDT
        SwingUtilities.invokeLater(() -> {
            setCursor(Cursor.getPredefinedCursor(Cursor.WAIT_CURSOR));

            // First, clear all existing selections.
            clearAllCategories();

            // 3. Use a Timer to sequentially select items from the queue.
            Timer selectionTimer = new Timer(250, null);
            selectionTimer.addActionListener(e -> {
                if (pathQueue.isEmpty()) {
                    // When the path is fully selected, handle the multi-select list
                    if (!multiSelectNames.isEmpty()) {
                        DefaultListModel<String> listModel = (DefaultListModel<String>) multiSelectList.getModel();
                        List<Integer> indicesToSelect = new ArrayList<>();
                        for (int i = 0; i < listModel.getSize(); i++) {
                            if (multiSelectNames.contains(listModel.getElementAt(i))) {
                                indicesToSelect.add(i);
                            }
                        }
                        if (!indicesToSelect.isEmpty()) {
                            int[] selectedIndices = indicesToSelect.stream().mapToInt(Integer::intValue).toArray();
                            multiSelectList.setSelectedIndices(selectedIndices);
                        }
                    }

                    // Stop the timer and restore cursor
                    selectionTimer.stop();
                    setCursor(Cursor.getDefaultCursor());
                    return;
                }

                // Find the next visible combo box and select the item
                String categoryToSelect = pathQueue.poll();
                JComboBox<String>[] combos = new JComboBox[]{categoryCombo1, categoryCombo2, categoryCombo3, categoryCombo4, categoryCombo5};
                for (JComboBox<String> combo : combos) {
                    if (combo.isVisible()) {
                        // Check if the item exists in the current combo box
                        boolean itemExists = false;
                        for (int i = 0; i < combo.getItemCount(); i++) {
                            if (categoryToSelect.equals(combo.getItemAt(i))) {
                                itemExists = true;
                                break;
                            }
                        }
                        // If it exists, select it. The action listener will populate the next combo.
                        if (itemExists) {
                            combo.setSelectedItem(categoryToSelect);
                            break; // Move to the next timer tick
                        }
                    }
                }
            });

            // A short delay before starting the timer to allow the initial clear to process.
            Timer startTimer = new Timer(100, e -> selectionTimer.start());
            startTimer.setRepeats(false);
            startTimer.start();
        });
    }

    /**
     * Handles the 'restoreSearch' event fired from the results panel.
     *
     * @param evt The property change event containing the search parameters.
     */
    private void handleRestoreSearch(PropertyChangeEvent evt) {
        Object[] searchParams = (Object[]) evt.getNewValue();
        if (searchParams == null) return;

        if (searchParams.length >= 8) {
            txtKeyword.setText((String) searchParams[0]);
            txtPriceMin.setText(searchParams[2].equals(0) ? "" : String.valueOf(searchParams[2]));
            txtPriceMax.setText(searchParams[3].equals(0) ? "" : String.valueOf(searchParams[3]));
            txtPages.setText(searchParams[4].equals(0) ? getDefaultPageValue() : String.valueOf(searchParams[4]));
            chkSortByNew.setSelected((Boolean) searchParams[5]);
            chkIgnoreShops.setSelected((Boolean) searchParams[6]);
            soldStatusCombo.setSelectedItem((String) searchParams[7]);

            if (searchParams.length >= 11) {
                restoreDetailedSearchParameters((String) searchParams[8], (String) searchParams[9], (String) searchParams[10]);
            } else {
                clearDetailedSearchFields();
            }

            // Restore categories if category path string is provided
            if (searchParams.length >= 12 && searchParams[11] != null) {
                restoreCategoriesByName((String) searchParams[11]);
            } else {
                clearAllCategories();
            }

            JTabbedPane tabbedPane = findTabbedPane();
            if (tabbedPane != null) {
                tabbedPane.setSelectedIndex(0);
            }
        }
    }

    private void handleShowProductResearch(java.beans.PropertyChangeEvent evt) {
        // Re-fire the event for the parent component (Main) to catch
        firePropertyChange("showProductResearch", null, evt.getNewValue());
    }

    /**
     * Repaints all major input components.
     */
    private void repaintAll() {
        txtKeyword.repaint();
        txtPriceMin.repaint();
        txtPriceMax.repaint();
        txtPages.repaint();
        soldStatusCombo.repaint();
        chkSortByNew.repaint();
        chkIgnoreShops.repaint();
        if (txtExcludeKeyword != null) txtExcludeKeyword.repaint();
        mainPanel.revalidate();
        mainPanel.repaint();
    }

    // --- Getters for Search Parameters ---

    public String getKeyword() {
        return txtKeyword.getText();
    }

    public int getPages() {
        try {
            return Integer.parseInt(txtPages.getText());
        } catch (NumberFormatException e) {
            return 2; // Default value
        }
    }

    public String getSelectedCategory1() {
        return categoryCombo1.getSelectedItem() != null ? categoryCombo1.getSelectedItem().toString() : CATEGORY_ALL;
    }

    public String getSelectedCategory2() {
        return categoryCombo2.getSelectedItem() != null ? categoryCombo2.getSelectedItem().toString() : CATEGORY_ALL;
    }

    public String getSelectedCategory3() {
        return categoryCombo3.getSelectedItem() != null ? categoryCombo3.getSelectedItem().toString() : CATEGORY_ALL;
    }

    public String getSelectedCategory4() {
        return categoryCombo4.getSelectedItem() != null ? categoryCombo4.getSelectedItem().toString() : CATEGORY_ALL;
    }

    public Object[] getSelectedMultiSelectItems() {
        return multiSelectList.getSelectedValuesList().toArray();
    }

    public int getPriceMin() {
        try {
            return Integer.parseInt(txtPriceMin.getText());
        } catch (NumberFormatException e) {
            return 300; // Default min
        }
    }

    public int getPriceMax() {
        try {
            return Integer.parseInt(txtPriceMax.getText());
        } catch (NumberFormatException e) {
            return 9999999; // Default max
        }
    }

    public String getSoldStatus() {
        return soldStatusCombo.getSelectedItem().toString();
    }

    public boolean isSortByNew() {
        return chkSortByNew.isSelected();
    }

    public boolean isIgnoreShops() {
        return chkIgnoreShops.isSelected();
    }


    // --- Detailed Search ---

    /**
     * Creates the panel for detailed search options like item condition, exclude keyword, and color.
     */
    private void createDetailedSearchPanel() {
        detailedSearchPanel = new JPanel(new MigLayout("fillx,insets 10", "[grow]", "[]"));
        detailedSearchPanel.putClientProperty(FlatClientProperties.STYLE, "arc:10;background:$Panel.background");

        // Item Condition
        addDetailedSearchSection(detailedSearchPanel, "商品の状態:", createItemConditionPanel());

        // Exclude Keyword
        txtExcludeKeyword = new JTextField();
        txtExcludeKeyword.setFont(FontSettings.getLabelFont());
        txtExcludeKeyword.putClientProperty(FlatClientProperties.PLACEHOLDER_TEXT, "除外したいキーワードを入力");
        addDetailedSearchSection(detailedSearchPanel, "除外キーワード:", txtExcludeKeyword);

        // Color
        addDetailedSearchSection(detailedSearchPanel, "色:", createColorPanel());
    }

    /**
     * Helper to add a section with a bold label to the detailed search panel.
     *
     * @param parent    The parent panel.
     * @param title     The title of the section.
     * @param component The component for that section.
     */
    private void addDetailedSearchSection(JPanel parent, String title, JComponent component) {
        JLabel label = new JLabel(title);
        label.setFont(label.getFont().deriveFont(Font.BOLD, FontSettings.getLabelFont().getSize2D()));
        parent.add(label, "gaptop 10, wrap");
        parent.add(component, "grow, wrap");
    }

    /**
     * Creates the panel with checkboxes for item conditions.
     *
     * @return The item condition panel.
     */
    private JPanel createItemConditionPanel() {
        ItemConditionModel[] conditions = ItemConditionModel.getAllConditions();
        itemConditionCheckboxes = new JCheckBox[conditions.length];
        JPanel conditionPanel = new JPanel(new MigLayout("insets 0", "[][][][]"));

        for (int i = 0; i < conditions.length; i++) {
            itemConditionCheckboxes[i] = new JCheckBox(conditions[i].getName());
            itemConditionCheckboxes[i].setFont(FontSettings.getLabelFont());
            if (conditions[i].isDefault()) {
                itemConditionCheckboxes[i].setSelected(true);
            }
            conditionPanel.add(itemConditionCheckboxes[i], "");
        }
        return conditionPanel;
    }

    /**
     * Creates the panel with checkboxes for colors.
     *
     * @return The color selection panel.
     */
    private JPanel createColorPanel() {
        ColorModel[] colors = ColorModel.getAllColors();
        colorCheckboxes = new ColorCheckBox[colors.length];
        JPanel colorPanel = new JPanel(new MigLayout("insets 0", "[][][][]"));

        for (int i = 0; i < colors.length; i++) {
            colorCheckboxes[i] = new ColorCheckBox(colors[i]);
            colorPanel.add(colorCheckboxes[i], i % 6 == 5 ? "wrap" : "");
        }
        return colorPanel;
    }

    /**
     * Gathers all selected detailed search options into a model object.
     *
     * @return A DetailedSearchModel populated with the current UI state.
     */
    private DetailedSearchModel getDetailedSearchModel() {
        DetailedSearchModel model = new DetailedSearchModel();
        List<ItemConditionModel> selectedConditions = new ArrayList<>();
        ItemConditionModel[] allConditions = ItemConditionModel.getAllConditions();
        for (int i = 0; i < itemConditionCheckboxes.length; i++) {
            if (itemConditionCheckboxes[i].isSelected()) {
                selectedConditions.add(allConditions[i]);
            }
        }
        model.setSelectedItemConditions(selectedConditions);

        model.setExcludeKeyword(txtExcludeKeyword.getText().trim());

        List<ColorModel> selectedColors = new ArrayList<>();
        for (ColorCheckBox checkbox : colorCheckboxes) {
            if (checkbox.isSelected()) {
                selectedColors.add(checkbox.getColor());
            }
        }
        model.setSelectedColors(selectedColors);
        return model;
    }

    /**
     * Clears all selections in the detailed search panel.
     */
    private void clearDetailedSearchFields() {
        if (txtExcludeKeyword != null) {
            txtExcludeKeyword.setText("");
        }
        if (itemConditionCheckboxes != null) {
            ItemConditionModel[] conditions = ItemConditionModel.getAllConditions();
            for (int i = 0; i < itemConditionCheckboxes.length; i++) {
                itemConditionCheckboxes[i].setSelected(conditions[i].isDefault());
            }
        }
        if (colorCheckboxes != null) {
            for (ColorCheckBox checkbox : colorCheckboxes) {
                checkbox.setSelected(false);
            }
        }
    }

    /**
     * Restores the state of the detailed search panel from saved data.
     *
     * @param itemConditionIds Comma-separated string of condition IDs.
     * @param excludeKeyword   The exclude keyword.
     * @param colorIds         Comma-separated string of color IDs.
     */
    private void restoreDetailedSearchParameters(String itemConditionIds, String excludeKeyword, String colorIds) {
        txtExcludeKeyword.setText(excludeKeyword != null ? excludeKeyword : "");

        // Restore item conditions
        for (JCheckBox checkbox : itemConditionCheckboxes) checkbox.setSelected(false);
        if (itemConditionIds != null && !itemConditionIds.trim().isEmpty()) {
            String[] ids = itemConditionIds.split(",");
            ItemConditionModel[] allConditions = ItemConditionModel.getAllConditions();
            for (String idStr : ids) {
                try {
                    int id = Integer.parseInt(idStr.trim());
                    for (int i = 0; i < allConditions.length; i++) {
                        if (allConditions[i].getId() == id) {
                            itemConditionCheckboxes[i].setSelected(true);
                            break;
                        }
                    }
                } catch (NumberFormatException e) { /* ignore */ }
            }
        } else { // Set default if no saved data
            ItemConditionModel[] conditions = ItemConditionModel.getAllConditions();
            for (int i = 0; i < itemConditionCheckboxes.length; i++) {
                itemConditionCheckboxes[i].setSelected(conditions[i].isDefault());
            }
        }

        // Restore colors
        for (ColorCheckBox checkbox : colorCheckboxes) checkbox.setSelected(false);
        if (colorIds != null && !colorIds.trim().isEmpty()) {
            String[] ids = colorIds.split(",");
            ColorModel[] allColors = ColorModel.getAllColors();
            for (String idStr : ids) {
                try {
                    int id = Integer.parseInt(idStr.trim());
                    for (int i = 0; i < allColors.length; i++) {
                        if (allColors[i].getId() == id) {
                            colorCheckboxes[i].setSelected(true);
                            break;
                        }
                    }
                } catch (NumberFormatException e) { /* ignore */ }
            }
        }
    }


    // --- Utility and Helper Methods ---

    /**
     * Finds the parent JTabbedPane.
     *
     * @return The JTabbedPane, or null if not found.
     */
    private JTabbedPane findTabbedPane() {
        // The JTabbedPane is a direct child of this Keyword panel.
        for (Component comp : this.getComponents()) {
            if (comp instanceof JTabbedPane) {
                return (JTabbedPane) comp;
            }
        }
        return null; // Should not happen if layout is correct
    }

    /**
     * Builds a string representation of the current search conditions for display.
     *
     * @param searchKeyword The keyword being searched.
     * @return A formatted string of search conditions.
     */
    private String buildSearchConditions(String searchKeyword) {
        StringBuilder conditions = new StringBuilder(searchKeyword);
        String cat1 = getSelectedCategory1();
        if (!CATEGORY_ALL.equals(cat1)) {
            conditions.append(" [").append(cat1);
            String cat2 = getSelectedCategory2();
            if (!CATEGORY_ALL.equals(cat2)) {
                conditions.append(" > ").append(cat2);
            }
            conditions.append("]");
        }
        int minPrice = getPriceMin();
        int maxPrice = getPriceMax();
        if (minPrice != 300 || maxPrice != 9999999) {
            conditions.append(" [").append(minPrice).append("～").append(maxPrice).append("円]");
        }
        return conditions.toString();
    }

    /**
     * Creates a display string for the selected categories.
     *
     * @return A formatted string like "Cat1 > Cat2, Item1, Item2".
     */
    private String createCategoriesStr() {
        selectedItems = getSelectedMultiSelectItems();
        StringBuilder sb = new StringBuilder();
        String[] combos = {getSelectedCategory1(), getSelectedCategory2(), getSelectedCategory3(), getSelectedCategory4()};
        boolean first = true;
        for (String combo : combos) {
            if (!combo.equals(CATEGORY_ALL)) {
                if (!first) sb.append(" > ");
                sb.append(combo);
                first = false;
            }
        }

        if (selectedItems.length > 0) {
            if (sb.length() > 0) sb.append(", ");
            for (int i = 0; i < selectedItems.length; i++) {
                sb.append(selectedItems[i].toString());
                if (i < selectedItems.length - 1) {
                    sb.append(", ");
                }
            }
        }
        return sb.toString();
    }

    private String createCategoryIdStr() {
        List<String> selectedCategoryIds = new ArrayList<>();

        // 1. Multi-select list has priority
        if (multiSelectList.getSelectedValuesList() != null && !multiSelectList.getSelectedValuesList().isEmpty()) {
            List<CategoryModel> sourceList = categoryMap.get(CATEGORY_LEVEL_SELECT);
            if (sourceList != null) {
                for (String categoryName : multiSelectList.getSelectedValuesList()) {
                    CategoryModel model = sourceList.stream()
                            .filter(c -> c.getName().equals(categoryName))
                            .findFirst()
                            .orElse(null);
                    if (model != null) {
                        selectedCategoryIds.add(model.getId());
                    }
                }
            }
            return String.join(",", selectedCategoryIds);
        }

        // 2. Check combo boxes from deepest to shallowest
        JComboBox<String>[] combos = new JComboBox[]{categoryCombo5, categoryCombo4, categoryCombo3, categoryCombo2, categoryCombo1};
        for (int i = 0; i < combos.length; i++) {
            if (combos[i].isVisible() && combos[i].getSelectedItem() != null && !CATEGORY_ALL.equals(combos[i].getSelectedItem().toString())) {
                String selectedName = combos[i].getSelectedItem().toString();
                int mapKey = CATEGORY_LEVEL_4 - i;
                List<CategoryModel> sourceList = categoryMap.get(mapKey);
                if (sourceList != null) {
                    CategoryModel model = sourceList.stream()
                            .filter(c -> c.getName().equals(selectedName))
                            .findFirst()
                            .orElse(null);

                    if (model != null) {
                        selectedCategoryIds.add(model.getId());
                        return String.join(",", selectedCategoryIds);
                    }
                }
            }
        }

        return ""; // No specific category selected
    }

    /**
     * Validates a price field to ensure its value is within a given range.
     *
     * @param field The JTextField to validate.
     * @param min   The minimum allowed value.
     * @param max   The maximum allowed value.
     */
    private void validatePriceField(JTextField field, int min, int max) {
        String text = field.getText().trim();
        if (text.isEmpty()) return;

        try {
            int value = Integer.parseInt(text);
            if (value < min) field.setText(String.valueOf(min));
            else if (value > max) field.setText(String.valueOf(max));
        } catch (NumberFormatException e) {
            field.setText(""); // Clear if not a valid number
        }
    }

    /**
     * Loads default settings from the database, specifically the default number of pages.
     */
    private void loadDefaultSettings() {
        try {
            Object[] settings = DatabaseUtil.loadSettings();
            if (settings != null && settings.length > 4) {
                int defaultKeywordPages = SettingsRepository.getDefaultKeywordPages();
                if (defaultKeywordPages >= 2 && defaultKeywordPages <= 99) {
                    txtPages.setText(String.valueOf(defaultKeywordPages));
                } else {
                    txtPages.setText("10");
                }
            } else {
                txtPages.setText("10");
            }
        } catch (Exception e) {
            txtPages.setText("10");
            logger.error("Error loading default settings: " + e.getMessage(), e);
        }
    }

    /**
     * Gets the default page value from settings, with a fallback.
     *
     * @return The default page value as a string.
     */
    private String getDefaultPageValue() {
        try {
            Object[] settings = DatabaseUtil.loadSettings();
            if (settings != null && settings.length > 4) {
                return String.valueOf((int) settings[4]);
            }
        } catch (Exception e) {
            logger.error("Error getting default page value: " + e.getMessage());
        }
        return "10"; // Fallback
    }

    /**
     * A simple DocumentFilter that only allows numeric input.
     */
    private static class NumericDocumentFilter extends DocumentFilter {
        @Override
        public void replace(FilterBypass fb, int offset, int length, String text, AttributeSet attrs) throws BadLocationException {
            if (text.matches("[0-9]*")) {
                super.replace(fb, offset, length, text, attrs);
            } else {
                UIManager.getLookAndFeel().provideErrorFeedback(null);
            }
        }
    }

    /**
     * Public method to allow external components to refresh the settings.
     */
    public void refreshDefaultSettings() {
        loadDefaultSettings();
    }

    /**
     * Updates all icons in the panel to match the current theme.
     */
    public void updateIconsForTheme() {
        try {
            if (btnSearch != null) btnSearch.setIcon(ThemeIconUtil.ofButton(AntDesignIconsOutlined.SEARCH, 16));
            if (btnClearAll != null) btnClearAll.setIcon(ThemeIconUtil.ofButton(AntDesignIconsOutlined.CLEAR, 16));
            if (btnClearCategories != null)
                btnClearCategories.setIcon(ThemeIconUtil.ofButton(AntDesignIconsOutlined.CLEAR, 16));
        } catch (Exception e) {
            logger.error("Error updating keyword screen icons for theme: " + e.getMessage());
        }
    }
}