package com.mrcresearch.screen.cleanup;

import com.formdev.flatlaf.FlatClientProperties;
import com.mrcresearch.service.ProductCleanupService;
import com.mrcresearch.service.common.FontSettings;
import com.mrcresearch.util.TextFieldContextMenuUtil;
import com.mrcresearch.util.ThemeIconUtil;
import net.miginfocom.swing.MigLayout;
import org.kordamp.ikonli.antdesignicons.AntDesignIconsOutlined;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.text.NumberFormat;

/**
 * 商品データ削除画面
 * 売り切れから指定日数経過した商品とその関連データを削除する機能を提供
 */
public class ProductCleanup extends JPanel {

    // UI Components
    private JTextField txtDaysOld;
    private JButton btnPreview;
    private JButton btnExecute;
    private JButton btnExecuteImagesOnly;
    private JLabel lblPreviewResult;
    private JProgressBar progressBar;
    private JTextArea txtResult;
    private JScrollPane scrollResult;
    private JRadioButton radioAllData;
    private JRadioButton radioImagesOnly;
    private ButtonGroup cleanupModeGroup;

    // Status
    private boolean isProcessing = false;
    private int previewCount = 0;

    public ProductCleanup() {
        init();
    }

    private void init() {
        setLayout(new MigLayout("fill,insets 20", "[grow]", "[]"));

        // Main panel
        JPanel panel = new JPanel(new MigLayout("wrap 1,fillx,insets 35 45 30 45", "[fill,400:600]"));
        panel.putClientProperty(FlatClientProperties.STYLE, "arc:20;");

        // Title
        JLabel titleLabel = new JLabel("商品データ削除");
        titleLabel.setFont(titleLabel.getFont().deriveFont(Font.BOLD, 24f));
        panel.add(titleLabel, "gaptop 10, gapbottom 10");

        // Description
        JLabel descLabel = new JLabel("売り切れから指定日数経過した商品データを削除します。");
        descLabel.setFont(descLabel.getFont().deriveFont(16f));
        panel.add(descLabel, "wrap");

        JLabel descLabel2 = new JLabel("削除対象：商品データ、関連検索データ、サムネイル画像");
        descLabel2.setFont(descLabel2.getFont().deriveFont(16f));
        panel.add(descLabel2, "wrap");

        JLabel descLabel3 = new JLabel("※削除後は元に戻せません。");
        descLabel3.setFont(descLabel3.getFont().deriveFont(16f));
        panel.add(descLabel3, "wrap, gapbottom 10");

        // 削除モード選択
        JPanel modePanel = new JPanel(new MigLayout("insets 0", "[][]"));
        JLabel modeLabel = new JLabel("削除モード:");
        modeLabel.setFont(modeLabel.getFont().deriveFont(16f));
        modePanel.add(modeLabel, "");

        cleanupModeGroup = new ButtonGroup();

        radioAllData = new JRadioButton("全データ削除");
        radioAllData.setFont(radioAllData.getFont().deriveFont(16f));
        radioAllData.setSelected(true);
        cleanupModeGroup.add(radioAllData);
        modePanel.add(radioAllData, "");

        radioImagesOnly = new JRadioButton("画像のみ削除");
        radioImagesOnly.setFont(radioImagesOnly.getFont().deriveFont(16f));
        cleanupModeGroup.add(radioImagesOnly);
        modePanel.add(radioImagesOnly, "");

        panel.add(modePanel, "wrap, gapbottom 20");

        // Days input section
        JPanel daysPanel = new JPanel(new MigLayout("insets 0", "[][][]"));

        JLabel daysLabel = new JLabel("削除対象日数:");
        daysLabel.setFont(daysLabel.getFont().deriveFont(16f));
        daysPanel.add(daysLabel, "");

        txtDaysOld = new JTextField("30");
        txtDaysOld.setFont(txtDaysOld.getFont().deriveFont(16f));
        TextFieldContextMenuUtil.enableTextFieldContextMenu(txtDaysOld);
        daysPanel.add(txtDaysOld, "w 100!");

        JLabel daysUnitLabel = new JLabel("日前");
        daysUnitLabel.setFont(daysUnitLabel.getFont().deriveFont(16f));
        daysPanel.add(daysUnitLabel, "");

        panel.add(daysPanel, "gapbottom 20");

        // Preview section
        JPanel previewPanel = new JPanel(new MigLayout("insets 0", "[][]"));

        btnPreview = new JButton("削除対象を確認", ThemeIconUtil.ofButton(AntDesignIconsOutlined.SEARCH, 16));
        btnPreview.setFont(btnPreview.getFont().deriveFont(16f));
        btnPreview.addActionListener(this::previewCleanup);
        previewPanel.add(btnPreview, "");

        lblPreviewResult = new JLabel("");
        lblPreviewResult.setFont(lblPreviewResult.getFont().deriveFont(16f));
        previewPanel.add(lblPreviewResult, "gapleft 20");

        panel.add(previewPanel, "gapbottom 20");

        // Execute section
        JPanel executePanel = new JPanel(new MigLayout("insets 0", "[][]"));

        btnExecute = new JButton("削除実行", ThemeIconUtil.ofButton(AntDesignIconsOutlined.DELETE, 16));
        btnExecute.setFont(btnExecute.getFont().deriveFont(16f));
        btnExecute.setEnabled(false);
        btnExecute.addActionListener(this::executeCleanup);
        executePanel.add(btnExecute, "");

        btnExecuteImagesOnly = new JButton("画像のみ削除", ThemeIconUtil.ofButton(AntDesignIconsOutlined.PICTURE, 16));
        btnExecuteImagesOnly.setFont(btnExecuteImagesOnly.getFont().deriveFont(16f));
        btnExecuteImagesOnly.setEnabled(false);
        btnExecuteImagesOnly.addActionListener(this::executeImagesOnlyCleanup);
        executePanel.add(btnExecuteImagesOnly, "gapleft 10");

        panel.add(executePanel, "gapbottom 20");

        // Progress bar
        progressBar = new JProgressBar();
        progressBar.setStringPainted(true);
        progressBar.setVisible(false);
        panel.add(progressBar, "growx, gapbottom 20");

        // Result area
        JLabel resultLabel = new JLabel("実行結果:");
        resultLabel.setFont(resultLabel.getFont().deriveFont(16f));
        panel.add(resultLabel, "gapbottom 5");

        txtResult = new JTextArea(10, 50);
        txtResult.setEditable(false);
        txtResult.setFont(FontSettings.getMonospacedFont14());
        scrollResult = new JScrollPane(txtResult);
        scrollResult.setBorder(BorderFactory.createLoweredBevelBorder());
        panel.add(scrollResult, "grow, h 400!");

        // Wrap panel in scroll pane
        JScrollPane scrollablePanel = new JScrollPane(panel);
        scrollablePanel.setBorder(BorderFactory.createEmptyBorder());
        scrollablePanel.getVerticalScrollBar().setUnitIncrement(16);
        add(scrollablePanel, "grow");
    }

    /**
     * プレビュー処理 - 削除対象の件数を確認
     */
    private void previewCleanup(ActionEvent e) {
        if (isProcessing) {
            return;
        }

        int daysOld = getDaysOld();
        if (daysOld <= 0) {
            JOptionPane.showMessageDialog(this,
                    "有効な日数を入力してください（1以上の数値）",
                    "入力エラー",
                    JOptionPane.ERROR_MESSAGE);
            return;
        }

        SwingWorker<Integer, Void> worker = new SwingWorker<Integer, Void>() {
            @Override
            protected Integer doInBackground() throws Exception {
                setProcessing(true);
                return ProductCleanupService.getOldItemsCount(daysOld);
            }

            @Override
            protected void done() {
                try {
                    previewCount = get();
                    NumberFormat formatter = NumberFormat.getNumberInstance();
                    lblPreviewResult.setText("削除対象: " + formatter.format(previewCount) + " 件");

                    if (previewCount > 0) {
                        lblPreviewResult.setForeground(new Color(204, 102, 0)); // Orange
                        btnExecute.setEnabled(true);
                        btnExecuteImagesOnly.setEnabled(true);
                    } else {
                        lblPreviewResult.setForeground(new Color(0, 128, 0)); // Green
                        btnExecute.setEnabled(false);
                        btnExecuteImagesOnly.setEnabled(false);
                    }

                    txtResult.append("プレビュー完了: " + formatter.format(previewCount) + " 件の商品が削除対象です\n");
                    txtResult.setCaretPosition(txtResult.getDocument().getLength());

                } catch (Exception ex) {
                    lblPreviewResult.setText("エラーが発生しました");
                    lblPreviewResult.setForeground(Color.RED);
                    txtResult.append("プレビューエラー: " + ex.getMessage() + "\n");
                    txtResult.setCaretPosition(txtResult.getDocument().getLength());
                    ex.printStackTrace();
                } finally {
                    setProcessing(false);
                }
            }
        };

        worker.execute();
    }

    /**
     * 削除実行処理
     */
    private void executeCleanup(ActionEvent e) {
        if (isProcessing || previewCount <= 0) {
            return;
        }

        // 確認ダイアログ
        NumberFormat formatter = NumberFormat.getNumberInstance();

        // 削除モードに応じたメッセージを作成
        String modeMessage = radioImagesOnly.isSelected()
                ? "画像のみを削除"
                : "商品データと画像を削除";

        int result = JOptionPane.showConfirmDialog(this,
                "本当に " + formatter.format(previewCount) + " 件の" + modeMessage + "しますか？\n" +
                        "この操作は取り消すことができません。",
                "削除確認",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.WARNING_MESSAGE);

        if (result != JOptionPane.YES_OPTION) {
            return;
        }

        int daysOld = getDaysOld();

        SwingWorker<ProductCleanupService.CleanupResult, String> worker =
                new SwingWorker<ProductCleanupService.CleanupResult, String>() {

                    @Override
                    protected ProductCleanupService.CleanupResult doInBackground() throws Exception {
                        setProcessing(true);
                        progressBar.setVisible(true);
                        progressBar.setIndeterminate(true);
                        progressBar.setString("削除処理中...");

                        // 選択された削除モードを取得
                        ProductCleanupService.CleanupMode mode = radioImagesOnly.isSelected()
                                ? ProductCleanupService.CleanupMode.IMAGES_ONLY
                                : ProductCleanupService.CleanupMode.ALL_DATA;

                        return ProductCleanupService.executeCleanup(daysOld, mode, this::publish);
                    }

                    @Override
                    protected void process(java.util.List<String> chunks) {
                        for (String message : chunks) {
                            txtResult.append(message + "\n");
                            txtResult.setCaretPosition(txtResult.getDocument().getLength());
                        }
                    }

                    @Override
                    protected void done() {
                        try {
                            ProductCleanupService.CleanupResult cleanupResult = get();

                            txtResult.append("\n=== 削除完了 ===\n");

                            if (radioImagesOnly.isSelected()) {
                                txtResult.append("削除モード: 画像のみ\n");
                                txtResult.append("削除された画像: " + formatter.format(cleanupResult.getDeletedImages()) + " 件\n");
                            } else {
                                txtResult.append("削除モード: 全データ\n");
                                txtResult.append("削除された商品: " + formatter.format(cleanupResult.getDeletedItems()) + " 件\n");
                                txtResult.append("削除された画像: " + formatter.format(cleanupResult.getDeletedImages()) + " 件\n");
                            }

                            txtResult.append("エラー件数: " + formatter.format(cleanupResult.getErrorCount()) + " 件\n");
                            txtResult.setCaretPosition(txtResult.getDocument().getLength());

                            // Reset UI
                            lblPreviewResult.setText("");
                            btnExecute.setEnabled(false);
                            previewCount = 0;

                            // 削除モードに応じたメッセージを作成
                            String completionMessage = "削除処理が完了しました。\n";

                            if (radioImagesOnly.isSelected()) {
                                completionMessage += "削除された画像: " + formatter.format(cleanupResult.getDeletedImages()) + " 件";
                            } else {
                                completionMessage += "削除された商品: " + formatter.format(cleanupResult.getDeletedItems()) + " 件\n" +
                                        "削除された画像: " + formatter.format(cleanupResult.getDeletedImages()) + " 件";
                            }

                            JOptionPane.showMessageDialog(ProductCleanup.this,
                                    completionMessage,
                                    "削除完了",
                                    JOptionPane.INFORMATION_MESSAGE);

                        } catch (Exception ex) {
                            txtResult.append("\n削除処理エラー: " + ex.getMessage() + "\n");
                            txtResult.setCaretPosition(txtResult.getDocument().getLength());

                            JOptionPane.showMessageDialog(ProductCleanup.this,
                                    "削除処理中にエラーが発生しました: " + ex.getMessage(),
                                    "エラー",
                                    JOptionPane.ERROR_MESSAGE);
                            ex.printStackTrace();
                        } finally {
                            progressBar.setVisible(false);
                            setProcessing(false);
                        }
                    }
                };

        worker.execute();
    }

    /**
     * 日数入力値を取得
     */
    private int getDaysOld() {
        try {
            return Integer.parseInt(txtDaysOld.getText().trim());
        } catch (NumberFormatException e) {
            return -1;
        }
    }

    /**
     * 処理状態を設定
     */
    private void setProcessing(boolean processing) {
        this.isProcessing = processing;
        btnPreview.setEnabled(!processing);
        btnExecute.setEnabled(!processing && previewCount > 0);
        btnExecuteImagesOnly.setEnabled(!processing && previewCount > 0);
        txtDaysOld.setEnabled(!processing);
        radioAllData.setEnabled(!processing);
        radioImagesOnly.setEnabled(!processing);
    }

    /**
     * 画像のみ削除実行処理
     */
    private void executeImagesOnlyCleanup(ActionEvent e) {
        if (isProcessing || previewCount <= 0) {
            return;
        }

        // 確認ダイアログ
        NumberFormat formatter = NumberFormat.getNumberInstance();

        int result = JOptionPane.showConfirmDialog(this,
                "本当に " + formatter.format(previewCount) + " 件の画像のみを削除しますか？\n" +
                        "この操作は取り消すことができません。",
                "削除確認",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.WARNING_MESSAGE);

        if (result != JOptionPane.YES_OPTION) {
            return;
        }

        int daysOld = getDaysOld();

        SwingWorker<ProductCleanupService.CleanupResult, String> worker =
                new SwingWorker<ProductCleanupService.CleanupResult, String>() {

                    @Override
                    protected ProductCleanupService.CleanupResult doInBackground() throws Exception {
                        setProcessing(true);
                        progressBar.setVisible(true);
                        progressBar.setIndeterminate(true);
                        progressBar.setString("画像削除処理中...");

                        // 画像のみ削除モードを強制的に使用
                        return ProductCleanupService.executeCleanup(daysOld, ProductCleanupService.CleanupMode.IMAGES_ONLY, this::publish);
                    }

                    @Override
                    protected void process(java.util.List<String> chunks) {
                        for (String message : chunks) {
                            txtResult.append(message + "\n");
                            txtResult.setCaretPosition(txtResult.getDocument().getLength());
                        }
                    }

                    @Override
                    protected void done() {
                        try {
                            ProductCleanupService.CleanupResult cleanupResult = get();

                            txtResult.append("\n=== 画像削除完了 ===\n");
                            txtResult.append("削除モード: 画像のみ\n");
                            txtResult.append("削除された画像: " + formatter.format(cleanupResult.getDeletedImages()) + " 件\n");
                            txtResult.append("エラー件数: " + formatter.format(cleanupResult.getErrorCount()) + " 件\n");
                            txtResult.setCaretPosition(txtResult.getDocument().getLength());

                            // Reset UI
                            lblPreviewResult.setText("");
                            btnExecute.setEnabled(false);
                            btnExecuteImagesOnly.setEnabled(false);
                            previewCount = 0;

                            // 完了メッセージ
                            String completionMessage = "画像削除処理が完了しました。\n" +
                                    "削除された画像: " + formatter.format(cleanupResult.getDeletedImages()) + " 件";

                            JOptionPane.showMessageDialog(ProductCleanup.this,
                                    completionMessage,
                                    "画像削除完了",
                                    JOptionPane.INFORMATION_MESSAGE);

                        } catch (Exception ex) {
                            txtResult.append("\n画像削除処理エラー: " + ex.getMessage() + "\n");
                            txtResult.setCaretPosition(txtResult.getDocument().getLength());

                            JOptionPane.showMessageDialog(ProductCleanup.this,
                                    "画像削除処理中にエラーが発生しました: " + ex.getMessage(),
                                    "エラー",
                                    JOptionPane.ERROR_MESSAGE);
                            ex.printStackTrace();
                        } finally {
                            progressBar.setVisible(false);
                            setProcessing(false);
                        }
                    }
                };

        worker.execute();
    }
}
