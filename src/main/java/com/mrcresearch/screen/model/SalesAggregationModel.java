package com.mrcresearch.screen.model;

import java.util.Map;

/**
 * 売り切れ・取引中アイテムの集計結果を格納するモデルクラス
 */
public class SalesAggregationModel {
    private String sellerId;
    private String itemId; // 商品ID（画像表示用）
    private String itemName;
    private String categoryName;
    private String thumbnailPath; // サムネイル画像パス
    private int price;
    private int totalSalesCount; // 過去7日間の総合件数（売り切れ・取引中）
    private Map<String, Integer> dailySalesCount; // 日別件数 (日付 -> 件数)

    // セラー情報の追加フィールド
    private String sellerName; // seller_searchesテーブルから取得
    private String lastUpdatedAt; // seller_searchesテーブルのlast_updated_atカラム
    private String researchStatus; // sellersテーブルのresearch_statusカラム

    /**
     * デフォルトコンストラクタ
     */
    public SalesAggregationModel() {
    }

    /**
     * 全フィールドを指定するコンストラクタ
     */
    public SalesAggregationModel(String sellerId, String itemId, String itemName, String categoryName,
                                 String thumbnailPath, int price, int totalSalesCount, Map<String, Integer> dailySalesCount) {
        this.sellerId = sellerId;
        this.itemId = itemId;
        this.itemName = itemName;
        this.categoryName = categoryName;
        this.thumbnailPath = thumbnailPath;
        this.price = price;
        this.totalSalesCount = totalSalesCount;
        this.dailySalesCount = dailySalesCount;
    }

    // Getters and Setters
    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getThumbnailPath() {
        return thumbnailPath;
    }

    public void setThumbnailPath(String thumbnailPath) {
        this.thumbnailPath = thumbnailPath;
    }

    public int getPrice() {
        return price;
    }

    public void setPrice(int price) {
        this.price = price;
    }

    public int getTotalSalesCount() {
        return totalSalesCount;
    }

    public void setTotalSalesCount(int totalSalesCount) {
        this.totalSalesCount = totalSalesCount;
    }

    public Map<String, Integer> getDailySalesCount() {
        return dailySalesCount;
    }

    public void setDailySalesCount(Map<String, Integer> dailySalesCount) {
        this.dailySalesCount = dailySalesCount;
    }

    public String getSellerName() {
        return sellerName;
    }

    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
    }

    public String getLastUpdatedAt() {
        return lastUpdatedAt;
    }

    public void setLastUpdatedAt(String lastUpdatedAt) {
        this.lastUpdatedAt = lastUpdatedAt;
    }

    public String getResearchStatus() {
        return researchStatus;
    }

    public void setResearchStatus(String researchStatus) {
        this.researchStatus = researchStatus;
    }
}
