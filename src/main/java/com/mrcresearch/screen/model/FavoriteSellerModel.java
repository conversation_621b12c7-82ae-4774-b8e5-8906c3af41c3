package com.mrcresearch.screen.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Model class for favorite sellers
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FavoriteSellerModel {
    private int id;
    private String sellerName;
    private String sellerId;
    private Date lastUpdated;
    private String researchStatus;
    private String tags;
    private String groupName;

    // Constructor without ID for backward compatibility
    public FavoriteSellerModel(String sellerName, String sellerId, Date lastUpdated,
                               String researchStatus, String tags, String groupName) {
        this.sellerName = sellerName;
        this.sellerId = sellerId;
        this.lastUpdated = lastUpdated;
        this.researchStatus = researchStatus;
        this.tags = tags;
        this.groupName = groupName;
    }
}