package com.mrcresearch.screen.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Model class for group/tag information with seller counts and last updated timestamps
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroupTagInfoModel {
    private String name;           // Group or tag name
    private int sellerCount;       // Number of sellers in this group/tag
    private Date lastUpdated;      // Most recent research date from favorite_groups/favorite_tags table
    private Date researchStartedAt; // Research start date from favorite_groups/favorite_tags table
    private boolean hasResearch;   // Whether any research has been conducted

    // Constructor for backward compatibility
    public GroupTagInfoModel(String name, int sellerCount, Date lastUpdated, boolean hasResearch) {
        this.name = name;
        this.sellerCount = sellerCount;
        this.lastUpdated = lastUpdated;
        this.hasResearch = hasResearch;
        this.researchStartedAt = null;
    }

    /**
     * Check if research is currently in progress.
     * Research is in progress if research_started_at is greater than last_updated.
     *
     * @return true if research is in progress, false otherwise
     */
    public boolean isResearchInProgress() {
        if (researchStartedAt == null) {
            return false;
        }

        if (lastUpdated == null) {
            return true; // Research started but never completed
        }

        return researchStartedAt.after(lastUpdated);
    }
}
