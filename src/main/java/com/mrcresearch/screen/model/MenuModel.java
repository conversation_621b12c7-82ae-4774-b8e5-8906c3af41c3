package com.mrcresearch.screen.model;

import lombok.Getter;
import lombok.Setter;
import org.kordamp.ikonli.Ikon;
import org.kordamp.ikonli.swing.FontIcon;

import javax.swing.*;
import java.awt.*;

@Getter
@Setter
public class MenuModel {
    private Icon icon;
    private Ikon originalIcon; // Store the original Ikon for theme updates
    private String menuName;
    private Color color;
    private int menuIndex;
    private JButton button;
    private Color defaultBackground = new Color(50, 50, 50); // Dark gray for dark theme
    private Color selectedBackground = new Color(59, 89, 152); // Slightly lighter gray with blue tint for dark theme

    public MenuModel(Ikon icon, String menuName, int menuIndex) {
        this.originalIcon = icon;
        // Use fixed color for menu icons that doesn't change with theme
        this.color = new Color(200, 200, 200); // Fixed light gray color
        this.icon = FontIcon.of(icon, 20, this.color); // Create icon with fixed color
        this.menuName = menuName;
        this.menuIndex = menuIndex;
        initButton();
    }

    public void initButton() {
        button = new JButton(menuName, icon);
        float fontSize = 20;
        button.setFont(button.getFont().deriveFont(fontSize));
        button.setBackground(defaultBackground);
        button.setForeground(color);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        button.setBorderPainted(false);
        button.setFocusPainted(false);
        button.setContentAreaFilled(true);
        button.setOpaque(true);
        button.setVisible(true);
    }

    /**
     * Set the button as selected or unselected
     *
     * @param selected true if the button should be selected, false otherwise
     */
    public void setSelected(boolean selected) {
        if (selected) {
            button.setBackground(selectedBackground);
        } else {
            button.setBackground(defaultBackground);
        }
    }
}
