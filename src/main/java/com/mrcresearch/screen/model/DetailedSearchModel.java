package com.mrcresearch.screen.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * Model class for detailed search parameters.
 * Encapsulates all detailed search conditions for keyword search.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DetailedSearchModel {
    private static final Logger logger = LoggerFactory.getLogger(DetailedSearchModel.class);
    private List<ItemConditionModel> selectedItemConditions;
    private String excludeKeyword;
    private List<ColorModel> selectedColors;

    /**
     * Convert selected item conditions to comma-separated string of IDs
     *
     * @return Comma-separated string of item condition IDs, or null if "すべて" is selected
     */
    public String getItemConditionIdsAsString() {
        if (selectedItemConditions == null || selectedItemConditions.isEmpty()) {
            logger.debug("DEBUG: 商品の状態が選択されていません");
            return null;
        }

        // Check if "すべて" (ID=0) is selected
        boolean hasAll = selectedItemConditions.stream().anyMatch(condition -> condition.getId() == 0);
        if (hasAll) {
            logger.debug("DEBUG: 商品の状態に「すべて」が選択されているため、nullを返します");
            return null; // "すべて" means no filter
        }

        String result = selectedItemConditions.stream()
                .map(condition -> String.valueOf(condition.getId()))
                .reduce((a, b) -> a + "," + b)
                .orElse(null);

        logger.debug("DEBUG: 商品の状態ID (文字列): {}", result);
        return result;
    }

    /**
     * Convert selected colors to comma-separated string of IDs
     *
     * @return Comma-separated string of color IDs, or null if "すべて" is selected
     */
    public String getColorIdsAsString() {
        if (selectedColors == null || selectedColors.isEmpty()) {
            logger.debug("DEBUG: 色が選択されていません");
            return null;
        }

        // Check if "すべて" (ID=0) is selected
        boolean hasAll = selectedColors.stream().anyMatch(color -> color.getId() == 0);
        if (hasAll) {
            logger.debug("DEBUG: 色に「すべて」が選択されているため、nullを返します");
            return null; // "すべて" means no filter
        }

        String result = selectedColors.stream()
                .map(color -> String.valueOf(color.getId()))
                .reduce((a, b) -> a + "," + b)
                .orElse(null);

        logger.debug("DEBUG: 色ID (文字列): {}", result);
        return result;
    }

    /**
     * Check if any detailed search parameters are set
     *
     * @return true if any detailed search parameters are configured
     */
    public boolean hasDetailedSearchParameters() {
        return (getItemConditionIdsAsString() != null) ||
                (excludeKeyword != null && !excludeKeyword.trim().isEmpty()) ||
                (getColorIdsAsString() != null);
    }

    @Override
    public String toString() {
        return "DetailedSearchModel{"
                + "selectedItemConditions=" + selectedItemConditions +
                ", excludeKeyword='" + excludeKeyword + "'" +
                ", selectedColors=" + selectedColors +
                '}';
    }
}