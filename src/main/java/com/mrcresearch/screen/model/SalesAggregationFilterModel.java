package com.mrcresearch.screen.model;

import lombok.Data;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 売り切れ・取引中集計画面のフィルタ条件を格納するモデルクラス
 */
@Data
public class SalesAggregationFilterModel {

    /**
     * 商品名フィルタ（全角・半角スペース区切りでの複数キーワード検索）
     */
    private String nameFilter;

    /**
     * セラーIDフィルタ
     */
    private String sellerIdFilter;

    /**
     * キーワード検索IDフィルタ
     */
    private String keywordSearchId;

    /**
     * セラー検索IDフィルタ
     */
    private String sellerSearchId;

    /**
     * カテゴリーフィルタ（選択されたカテゴリーIDのリスト）
     */
    private List<String> categoryIds;

    /**
     * 総販売数フィルタ - 最小値
     */
    private Integer minSalesCount;

    /**
     * 総販売数フィルタ - 最大値
     */
    private Integer maxSalesCount;

    /**
     * 期間フィルタ - 開始日
     */
    private LocalDate startDate;

    /**
     * 期間フィルタ - 終了日
     */
    private LocalDate endDate;

    /**
     * 価格フィルタ - 最小値
     */
    private Integer minPrice;

    /**
     * 価格フィルタ - 最大値
     */
    private Integer maxPrice;

    /**
     * フィルタが設定されているかどうかを確認する
     *
     * @return フィルタが設定されている場合はtrue
     */
    public boolean hasFilters() {
        return (nameFilter != null && !nameFilter.trim().isEmpty()) ||
                (sellerIdFilter != null && !sellerIdFilter.trim().isEmpty()) ||
                (keywordSearchId != null && !keywordSearchId.trim().isEmpty()) ||
                (sellerSearchId != null && !sellerSearchId.trim().isEmpty()) ||
                (categoryIds != null && !categoryIds.isEmpty()) ||
                (minSalesCount != null) ||
                (maxSalesCount != null) ||
                (minPrice != null) ||
                (maxPrice != null) ||
                (startDate != null) ||
                (endDate != null);
    }

    /**
     * 商品名フィルタが設定されているかどうかを確認する
     *
     * @return 商品名フィルタが設定されている場合はtrue
     */
    public boolean hasNameFilter() {
        return nameFilter != null && !nameFilter.trim().isEmpty();
    }

    /**
     * セラーIDフィルタが設定されているかどうかを確認する
     *
     * @return セラーIDフィルタが設定されている場合はtrue
     */
    public boolean hasSellerIdFilter() {
        return sellerIdFilter != null && !sellerIdFilter.trim().isEmpty();
    }

    /**
     * カテゴリーフィルタが設定されているかどうかを確認する
     *
     * @return カテゴリーフィルタが設定されている場合はtrue
     */
    public boolean hasCategoryFilter() {
        return categoryIds != null && !categoryIds.isEmpty();
    }

    /**
     * 総販売数フィルタが設定されているかどうかを確認する
     *
     * @return 総販売数フィルタが設定されている場合はtrue
     */
    public boolean hasSalesCountFilter() {
        return minSalesCount != null || maxSalesCount != null;
    }

    /**
     * 価格フィルタが設定されているかどうかを確認する
     *
     * @return 価格フィルタが設定されている場合はtrue
     */
    public boolean hasPriceFilter() {
        return minPrice != null || maxPrice != null;
    }

    /**
     * 期間フィルタが設定されているかどうかを確認する
     *
     * @return 期間フィルタが設定されている場合はtrue
     */
    public boolean hasDateFilter() {
        return startDate != null || endDate != null;
    }

    /**
     * 商品名を全角・半角スペースで分割してキーワードリストを取得する
     *
     * @return キーワードのリスト
     */
    public List<String> getNameKeywords() {
        if (!hasNameFilter()) {
            return List.of();
        }

        // 全角・半角スペースで分割
        String[] keywords = nameFilter.trim().split("[\\s　]+");
        return List.of(keywords).stream()
                .filter(keyword -> !keyword.isEmpty())
                .map(String::trim)
                .toList();
    }

    /**
     * フィルタ条件をクリアする
     */
    public void clearFilters() {
        this.nameFilter = null;
        this.sellerIdFilter = null;
        this.keywordSearchId = null;
        this.sellerSearchId = null;
        this.categoryIds = null;
        this.minSalesCount = null;
        this.maxSalesCount = null;
        this.minPrice = null;
        this.maxPrice = null;
        this.startDate = null;
        this.endDate = null;
    }

    /**
     * 別のSalesAggregationFilterModelからフィルター条件をコピーします。
     *
     * @param other コピー元のフィルターモデル
     */
    public void apply(SalesAggregationFilterModel other) {
        this.nameFilter = other.nameFilter;
        this.sellerIdFilter = other.sellerIdFilter;
        this.keywordSearchId = other.keywordSearchId;
        this.sellerSearchId = other.sellerSearchId;
        this.minSalesCount = other.minSalesCount;
        this.maxSalesCount = other.maxSalesCount;
        this.minPrice = other.minPrice;
        this.maxPrice = other.maxPrice;
        this.startDate = other.startDate;
        this.endDate = other.endDate;
        // リストはミュータブルなので、新しいArrayListを作成してディープコピーします
        this.categoryIds = (other.categoryIds != null) ? new ArrayList<>(other.categoryIds) : null;
    }
}
