package com.mrcresearch.screen.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Model class for keyword search result items reference/mapping table.
 * Represents a reference mapping between items and their status.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KeywordSearchItemModel {
    private int id;
    private String itemId;
    private int status;

    /**
     * Get status display text in Japanese using ItemStatusEnum
     */
    public String getStatusDisplayText() {
        return com.mrcresearch.service.enums.ItemStatusEnum.fromId(status).getDisplayName();
    }
}
