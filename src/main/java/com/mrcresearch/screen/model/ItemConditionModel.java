package com.mrcresearch.screen.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Model class for item condition options.
 * Represents the condition states available for Mercari items.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItemConditionModel {
    private int id;
    private String name;
    private boolean isDefault;

    @Override
    public String toString() {
        return name;
    }

    /**
     * Get all available item conditions
     *
     * @return Array of all item condition options
     */
    public static ItemConditionModel[] getAllConditions() {
        return new ItemConditionModel[]{
                new ItemConditionModel(0, "すべて", false),
                new ItemConditionModel(1, "新品・未使用", true),  // デフォルト
                new ItemConditionModel(2, "未使用に近い", false),
                new ItemConditionModel(3, "目立った傷や汚れなし", false),
                new ItemConditionModel(4, "やや傷や汚れあり", false),
                new ItemConditionModel(5, "傷や汚れあり", false),
                new ItemConditionModel(6, "全体的に状態が悪い", false)
        };
    }

    /**
     * Get default item condition (新品・未使用)
     *
     * @return Default item condition
     */
    public static ItemConditionModel getDefaultCondition() {
        return new ItemConditionModel(1, "新品・未使用", true);
    }
}
