package com.mrcresearch.screen.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Model class for seller searches
 */
@Data
@NoArgsConstructor
public class SellerSearchModel {
    private int id;
    private String sellerId;
    private String sellerName;
    private boolean sellerNameChanged;
    private Date lastResearchedAt;
    private Date addedDate;
    private Date updatedDate;

    /**
     * Constructor with all fields
     *
     * @param sellerId          The ID of the seller
     * @param sellerName        The name of the seller
     * @param sellerNameChanged Whether the seller name was changed
     * @param lastResearchedAt  The last time this seller was researched
     * @param addedDate         The date when the seller search was added
     * @param updatedDate       The date when the seller search was last updated
     */
    public SellerSearchModel(String sellerId, String sellerName, boolean sellerNameChanged,
                             Date lastResearchedAt, Date addedDate, Date updatedDate) {
        this.sellerId = sellerId;
        this.sellerName = sellerName;
        this.sellerNameChanged = sellerNameChanged;
        this.lastResearchedAt = lastResearchedAt;
        this.addedDate = addedDate;
        this.updatedDate = updatedDate;
    }

    /**
     * Constructor with ID
     *
     * @param id                The ID of the record
     * @param sellerId          The ID of the seller
     * @param sellerName        The name of the seller
     * @param sellerNameChanged Whether the seller name was changed
     * @param lastResearchedAt  The last time this seller was researched
     * @param addedDate         The date when the seller search was added
     * @param updatedDate       The date when the seller search was last updated
     */
    public SellerSearchModel(int id, String sellerId, String sellerName, boolean sellerNameChanged,
                             Date lastResearchedAt, Date addedDate, Date updatedDate) {
        this.id = id;
        this.sellerId = sellerId;
        this.sellerName = sellerName;
        this.sellerNameChanged = sellerNameChanged;
        this.lastResearchedAt = lastResearchedAt;
        this.addedDate = addedDate;
        this.updatedDate = updatedDate;
    }


}
