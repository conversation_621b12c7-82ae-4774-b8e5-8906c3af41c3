package com.mrcresearch.screen.model;

import java.util.Date;

/**
 * Model class for product research results.
 * Represents a single product item from keyword search results.
 */
public class ProductResearchResultModel {
    private int id;
    private int keywordSearchResultId;
    private String itemId;
    private String sellerId;
    private String status;
    private String name;
    private int price;
    private Date createdDate;
    private Date updatedDate;
    private String thumbnail;
    private String itemType;
    private int itemConditionId;
    private int shippingMethodId;
    private int categoryId;
    private Date savedDate;
    private String keyword; // From joined keyword_search_results table
    private int totalSalesCount; // Total sales count for sold-out/trading items (past 5 days)

    /**
     * Default constructor
     */
    public ProductResearchResultModel() {
    }

    /**
     * Constructor with all fields
     */
    public ProductResearchResultModel(int id, int keywordSearchResultId, String itemId, String sellerId,
                                      String status, String name, int price, Date createdDate, Date updatedDate,
                                      String thumbnail, String itemType, int itemConditionId, int shippingMethodId,
                                      int categoryId, Date savedDate, String keyword, int totalSalesCount) {
        this.id = id;
        this.keywordSearchResultId = keywordSearchResultId;
        this.itemId = itemId;
        this.sellerId = sellerId;
        this.status = status;
        this.name = name;
        this.price = price;
        this.createdDate = createdDate;
        this.updatedDate = updatedDate;
        this.thumbnail = thumbnail;
        this.itemType = itemType;
        this.itemConditionId = itemConditionId;
        this.shippingMethodId = shippingMethodId;
        this.categoryId = categoryId;
        this.savedDate = savedDate;
        this.keyword = keyword;
        this.totalSalesCount = totalSalesCount;
    }

    // Getters and setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getKeywordSearchResultId() {
        return keywordSearchResultId;
    }

    public void setKeywordSearchResultId(int keywordSearchResultId) {
        this.keywordSearchResultId = keywordSearchResultId;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getPrice() {
        return price;
    }

    public void setPrice(int price) {
        this.price = price;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(String thumbnail) {
        this.thumbnail = thumbnail;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public int getItemConditionId() {
        return itemConditionId;
    }

    public void setItemConditionId(int itemConditionId) {
        this.itemConditionId = itemConditionId;
    }

    public int getShippingMethodId() {
        return shippingMethodId;
    }

    public void setShippingMethodId(int shippingMethodId) {
        this.shippingMethodId = shippingMethodId;
    }

    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public Date getSavedDate() {
        return savedDate;
    }

    public void setSavedDate(Date savedDate) {
        this.savedDate = savedDate;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public int getTotalSalesCount() {
        return totalSalesCount;
    }

    public void setTotalSalesCount(int totalSalesCount) {
        this.totalSalesCount = totalSalesCount;
    }

    /**
     * Get formatted price string
     */
    public String getFormattedPrice() {
        return "¥" + String.format("%,d", price);
    }

    /**
     * Get status display text in Japanese
     */
    public String getStatusDisplayText() {
        if (status == null) return "不明";

        switch (status.toLowerCase()) {
            case "item_status_on_sale":
            case "on_sale":
                return "販売中";
            case "item_status_sold_out":
            case "sold_out":
            case "item_status_trading":
            case "trading":
                return "売り切れ";
            default:
                return status;
        }
    }

    @Override
    public String toString() {
        return "ProductResearchResultModel{" +
                "id=" + id +
                ", keywordSearchResultId=" + keywordSearchResultId +
                ", itemId='" + itemId + '\'' +
                ", sellerId='" + sellerId + '\'' +
                ", status='" + status + '\'' +
                ", name='" + name + '\'' +
                ", price=" + price +
                ", createdDate=" + createdDate +
                ", updatedDate=" + updatedDate +
                ", thumbnail='" + thumbnail + '\'' +
                ", itemType='" + itemType + '\'' +
                ", itemConditionId=" + itemConditionId +
                ", shippingMethodId=" + shippingMethodId +
                ", categoryId=" + categoryId +
                ", savedDate=" + savedDate +
                ", keyword='" + keyword + '\'' +
                ", totalSalesCount=" + totalSalesCount +
                '}';
    }
}
