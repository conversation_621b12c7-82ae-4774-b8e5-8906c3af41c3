package com.mrcresearch.screen.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Model class for favorite tags with research tracking
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FavoriteTagModel {
    private int id;
    private String tagName;
    private Date lastUpdated;
    private Date researchStartedAt;
}
