package com.mrcresearch.screen.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Model class for keyword search results.
 * Represents a single keyword search result with its associated data.
 */
@Data
@NoArgsConstructor
public class KeywordSearchResultModel {
    private int id;
    private String keyword;
    private String categories;
    private Date addedDate;
    private Date updatedDate;
    private String status;
    private String researchStatus;
    private int minPrice;
    private int maxPrice;
    private int pages;

    // New search parameters
    private boolean sortByNew;
    private boolean ignoreShops;
    private String salesStatus;

    // Detailed search parameters
    private String itemConditionIds;  // 商品の状態（カンマ区切り）
    private String excludeKeyword;    // 除外キーワード
    private String colorIds;          // 色（カンマ区切り）
    private String categoryId;        // カテゴリーID（カンマ区切り）

    /**
     * Constructor with all fields
     */
    public KeywordSearchResultModel(int id, String keyword, String categories, Date addedDate, Date updatedDate, String status, String researchStatus, int minPrice, int maxPrice, int pages, boolean sortByNew, boolean ignoreShops, String salesStatus) {
        this(id, keyword, categories, addedDate, updatedDate, status, researchStatus, minPrice, maxPrice, pages, sortByNew, ignoreShops, salesStatus, null, null, null, null);
    }

    /**
     * Constructor with all fields including detailed search parameters
     */
    public KeywordSearchResultModel(int id, String keyword, String categories, Date addedDate, Date updatedDate, String status, String researchStatus, int minPrice, int maxPrice, int pages, boolean sortByNew, boolean ignoreShops, String salesStatus, String itemConditionIds, String excludeKeyword, String colorIds, String categoryId) {
        this.id = id;
        this.keyword = keyword;
        this.categories = categories;
        this.addedDate = addedDate;
        this.updatedDate = updatedDate;
        this.status = status;
        this.researchStatus = researchStatus;
        this.minPrice = minPrice;
        this.maxPrice = maxPrice;
        this.pages = pages;
        this.sortByNew = sortByNew;
        this.ignoreShops = ignoreShops;
        this.salesStatus = salesStatus;
        this.itemConditionIds = itemConditionIds;
        this.excludeKeyword = excludeKeyword;
        this.colorIds = colorIds;
        this.categoryId = categoryId;
    }

    /**
     * Constructor with all fields (backward compatibility)
     */
    public KeywordSearchResultModel(int id, String keyword, String categories, Date addedDate, Date updatedDate, String status, String researchStatus, int minPrice, int maxPrice, int pages) {
        this(id, keyword, categories, addedDate, updatedDate, status, researchStatus, minPrice, maxPrice, pages, false, false, "すべて", null, null, null, null);
    }

    /**
     * Constructor with all fields except id (for new records)
     */
    public KeywordSearchResultModel(String keyword, String categories, Date addedDate, Date updatedDate, String status, String researchStatus, int minPrice, int maxPrice, int pages, boolean sortByNew, boolean ignoreShops, String salesStatus) {
        this(keyword, categories, addedDate, updatedDate, status, researchStatus, minPrice, maxPrice, pages, sortByNew, ignoreShops, salesStatus, null, null, null, null);
    }

    /**
     * Constructor with all fields except id (for new records) including detailed search parameters
     */
    public KeywordSearchResultModel(String keyword, String categories, Date addedDate, Date updatedDate, String status, String researchStatus, int minPrice, int maxPrice, int pages, boolean sortByNew, boolean ignoreShops, String salesStatus, String itemConditionIds, String excludeKeyword, String colorIds, String categoryId) {
        this.keyword = keyword;
        this.categories = categories;
        this.addedDate = addedDate;
        this.updatedDate = updatedDate;
        this.status = status;
        this.researchStatus = researchStatus;
        this.minPrice = minPrice;
        this.maxPrice = maxPrice;
        this.pages = pages;
        this.sortByNew = sortByNew;
        this.ignoreShops = ignoreShops;
        this.salesStatus = salesStatus;
        this.itemConditionIds = itemConditionIds;
        this.excludeKeyword = excludeKeyword;
        this.colorIds = colorIds;
        this.categoryId = categoryId;
    }

    /**
     * Constructor with all fields except id (for new records) - backward compatibility
     */
    public KeywordSearchResultModel(String keyword, String categories, Date addedDate, Date updatedDate, String status, String researchStatus, int minPrice, int maxPrice, int pages) {
        this(keyword, categories, addedDate, updatedDate, status, researchStatus, minPrice, maxPrice, pages, false, false, "すべて", null, null, null, null);
    }

    /**
     * Constructor without id (for new records) - backward compatibility
     */
    public KeywordSearchResultModel(String keyword, String categories, Date addedDate, Date updatedDate, String status, String researchStatus) {
        this(keyword, categories, addedDate, updatedDate, status, researchStatus, 0, 0, 0, false, false, "すべて", null, null, null, null);
    }

    /**
     * Constructor without id and research status (for backward compatibility)
     */
    public KeywordSearchResultModel(String keyword, String categories, Date addedDate, Date updatedDate, String status) {
        this(keyword, categories, addedDate, updatedDate, status, status, 0, 0, 0, false, false, "すべて", null, null, null, null);
    }


    @Override
    public String toString() {
        return "KeywordSearchResultModel{"
                + "id=" + id +
                ", keyword='" + keyword + "'" +
                ", categories='" + categories + "'" +
                ", addedDate=" + addedDate +
                ", updatedDate=" + updatedDate +
                ", status='" + status + "'" +
                ", researchStatus='" + researchStatus + "'" +
                ", minPrice=" + minPrice +
                ", maxPrice=" + maxPrice +
                ", pages=" + pages +
                ", sortByNew=" + sortByNew +
                ", ignoreShops=" + ignoreShops +
                ", salesStatus='" + salesStatus + "'" +
                ", itemConditionIds='" + itemConditionIds + "'" +
                ", excludeKeyword='" + excludeKeyword + "'" +
                ", colorIds='" + colorIds + "'" +
                ", categoryId='" + categoryId + "'" +
                '}';
    }
}