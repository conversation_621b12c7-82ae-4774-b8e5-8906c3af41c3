package com.mrcresearch.screen.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.awt.*;

/**
 * Model class for color options.
 * Represents the color options available for Mercari items.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ColorModel {
    private int id;
    private String name;
    private Color displayColor;

    @Override
    public String toString() {
        return name;
    }

    /**
     * Get all available colors
     *
     * @return Array of all color options
     */
    public static ColorModel[] getAllColors() {
        return new ColorModel[]{
                new ColorModel(2, "ホワイト系", Color.WHITE),
                new ColorModel(1, "ブラック系", Color.BLACK),
                new ColorModel(3, "グレイ系", Color.GRAY),
                new ColorModel(4, "ブラウン系", new Color(139, 69, 19)),
                new ColorModel(9, "ベージュ系", new Color(245, 245, 220)),
                new ColorModel(10, "グリーン系", Color.GREEN),
                new ColorModel(8, "ブルー系", Color.BLUE),
                new ColorModel(7, "パープル系", new Color(128, 0, 128)),
                new ColorModel(11, "イエロー系", Color.YELLOW),
                new ColorModel(6, "ピンク系", Color.PINK),
                new ColorModel(5, "レッド系", Color.RED),
                new ColorModel(12, "オレンジ系", Color.ORANGE)
        };
    }
}
