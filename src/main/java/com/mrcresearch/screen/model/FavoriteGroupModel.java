package com.mrcresearch.screen.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Model class for favorite groups with research tracking
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FavoriteGroupModel {
    private int id;
    private String groupName;
    private Date lastUpdated;
    private Date researchStartedAt;
}
