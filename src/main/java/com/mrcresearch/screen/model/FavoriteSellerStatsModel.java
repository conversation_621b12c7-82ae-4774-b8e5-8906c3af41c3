package com.mrcresearch.screen.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * Model class for favorite seller statistics
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FavoriteSellerStatsModel {
    private int id;
    private String sellerId;
    private String sellerName;
    private String groupName;
    private String tags;
    private Date lastUpdated;
    private String researchStatus;

    // Statistics
    private double averageDailySales30Days;  // 過去30日間の1日あたりの平均販売数
    private int totalSalesLast7Days;         // 過去7日間の売ったアイテム数
    private int totalItemsResearched;        // リサーチしたアイテム総数
    private Date lastResearchDate;           // 最後にリサーチした日時
    private List<Integer> dailySalesLast7Days; // 過去7日間の日毎の販売数（今日から7日前まで）

    // Constructor for basic seller info (without ID)
    public FavoriteSellerStatsModel(String sellerId, String sellerName, String groupName, String tags,
                                    Date lastUpdated, String researchStatus) {
        this.sellerId = sellerId;
        this.sellerName = sellerName;
        this.groupName = groupName;
        this.tags = tags;
        this.lastUpdated = lastUpdated;
        this.researchStatus = researchStatus;
        this.averageDailySales30Days = 0.0;
        this.totalSalesLast7Days = 0;
        this.totalItemsResearched = 0;
        this.lastResearchDate = null;
        this.dailySalesLast7Days = null;
    }

    // Constructor for basic seller info (with ID)
    public FavoriteSellerStatsModel(int id, String sellerId, String sellerName, String groupName, String tags,
                                    Date lastUpdated, String researchStatus) {
        this.id = id;
        this.sellerId = sellerId;
        this.sellerName = sellerName;
        this.groupName = groupName;
        this.tags = tags;
        this.lastUpdated = lastUpdated;
        this.researchStatus = researchStatus;
        this.averageDailySales30Days = 0.0;
        this.totalSalesLast7Days = 0;
        this.totalItemsResearched = 0;
        this.lastResearchDate = null;
        this.dailySalesLast7Days = null;
    }
}
