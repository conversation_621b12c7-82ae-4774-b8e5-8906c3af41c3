package com.mrcresearch.screen.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Model class for search items.
 * Represents a single item from search results stored in the search_items table.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SearchItemModel {
    private String itemId;
    private String sellerId;
    private int status;
    private String productName;
    private int price;
    private String listingDate;
    private String updatedDate;
    private String thumbnail;
    private int itemType;
    private String itemCondition;
    private String category;


    /**
     * Get formatted price string
     */
    public String getFormattedPrice() {
        return "¥" + String.format("%,d", price);
    }

    /**
     * Get status display text in Japanese using ItemStatusEnum
     */
    public String getStatusDisplayText() {
        return com.mrcresearch.service.enums.ItemStatusEnum.fromId(status).getDisplayName();
    }

    /**
     * Get item type display text in Japanese using ItemTypeEnum
     */
    public String getItemTypeDisplayText() {
        return com.mrcresearch.service.enums.ItemTypeEnum.fromId(itemType).getDisplayName();
    }

    /**
     * Check if this item is old based on the given settings
     *
     * @param soldDateDays Number of days to consider an item old
     * @return true if the item is old
     */
    public boolean isOldItem(int soldDateDays) {
        if (updatedDate == null) {
            return false;
        }

        try {
            // Parse the updated date string to Date object
            java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            java.util.Date itemUpdatedDate;

            // Try to parse the date string
            if (updatedDate.endsWith("Z")) {
                // Remove 'Z' suffix if present
                itemUpdatedDate = dateFormat.parse(updatedDate.replace("Z", ""));
            } else {
                itemUpdatedDate = dateFormat.parse(updatedDate);
            }

            long daysDifference = (System.currentTimeMillis() - itemUpdatedDate.getTime()) / (1000 * 60 * 60 * 24);
            return daysDifference > soldDateDays;
        } catch (Exception e) {
            System.err.println("Error checking if item is old: " + e.getMessage());
            return false;
        }
    }

    /**
     * Check if this item is on sale
     *
     * @return true if the item is on sale
     */
    public boolean isOnSale() {
        return com.mrcresearch.service.enums.ItemStatusEnum.fromId(status) ==
                com.mrcresearch.service.enums.ItemStatusEnum.ON_SALE;
    }

    /**
     * Check if this item is sold out or trading
     *
     * @return true if the item is sold out or trading
     */
    public boolean isSoldOut() {
        com.mrcresearch.service.enums.ItemStatusEnum statusEnum =
                com.mrcresearch.service.enums.ItemStatusEnum.fromId(status);
        return statusEnum == com.mrcresearch.service.enums.ItemStatusEnum.TRADING ||
                statusEnum == com.mrcresearch.service.enums.ItemStatusEnum.SOLD_OUT;
    }
}
