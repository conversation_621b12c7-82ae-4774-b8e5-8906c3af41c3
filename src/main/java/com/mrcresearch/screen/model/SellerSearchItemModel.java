package com.mrcresearch.screen.model;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Model class for seller search items (mapping table)
 */
@Data
@NoArgsConstructor
public class SellerSearchItemModel {
    private int id;
    private int sellerSearchId;
    private String itemId;
    private int status;

    /**
     * Constructor with all fields
     *
     * @param sellerSearchId The ID of the seller search
     * @param itemId         The ID of the item
     * @param status         The status of the item
     */
    public SellerSearchItemModel(int sellerSearchId, String itemId, int status) {
        this.sellerSearchId = sellerSearchId;
        this.itemId = itemId;
        this.status = status;
    }

    /**
     * Constructor with ID
     *
     * @param id             The ID of the record
     * @param sellerSearchId The ID of the seller search
     * @param itemId         The ID of the item
     * @param status         The status of the item
     */
    public SellerSearchItemModel(int id, int sellerSearchId, String itemId, int status) {
        this.id = id;
        this.sellerSearchId = sellerSearchId;
        this.itemId = itemId;
        this.status = status;
    }


}
