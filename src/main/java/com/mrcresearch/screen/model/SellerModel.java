package com.mrcresearch.screen.model;

// Note: Lombok annotations commented out for compilation compatibility
// import lombok.AllArgsConstructor;
// import lombok.Data;
// import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Model class for seller master information
 * This class represents the main seller entity with comprehensive information management.
 * Note: Manual getters/setters added for compilation compatibility when Lombok is not available.
 */
// @Data
// @NoArgsConstructor
// @AllArgsConstructor
public class SellerModel {

    /**
     * セラーID (主キー)
     */
    private String sellerId;

    /**
     * セラー名
     */
    private String sellerName;

    /**
     * セラー名変更フラグ
     * セラー名が変更されたかどうかを示す
     */
    private boolean sellerNameChanged;

    /**
     * 作成日時
     */
    private Date createdAt;

    /**
     * 更新日時
     */
    private Date updatedAt;

    /**
     * リサーチステータス
     * 待機中、リサーチ中、完了、エラー
     */
    private String researchStatus;

    /**
     * Default no-args constructor
     */
    public SellerModel() {
        // Default constructor for compatibility
    }

    /**
     * Constructor with basic seller information
     *
     * @param sellerId   The ID of the seller
     * @param sellerName The name of the seller
     */
    public SellerModel(String sellerId, String sellerName) {
        this.sellerId = sellerId;
        this.sellerName = sellerName;
        this.sellerNameChanged = false;
        this.researchStatus = "待機中"; // Default status
        Date now = new Date();
        this.createdAt = now;
        this.updatedAt = now;
    }

    /**
     * Constructor with seller name change flag
     *
     * @param sellerId          The ID of the seller
     * @param sellerName        The name of the seller
     * @param sellerNameChanged Whether the seller name was changed
     */
    public SellerModel(String sellerId, String sellerName, boolean sellerNameChanged) {
        this.sellerId = sellerId;
        this.sellerName = sellerName;
        this.sellerNameChanged = sellerNameChanged;
        this.researchStatus = "待機中"; // Default status
        Date now = new Date();
        this.createdAt = now;
        this.updatedAt = now;
    }

    /**
     * All-args constructor (for Lombok compatibility)
     *
     * @param sellerId          The ID of the seller
     * @param sellerName        The name of the seller
     * @param sellerNameChanged Whether the seller name was changed
     * @param createdAt         The creation date
     * @param updatedAt         The update date
     */
    public SellerModel(String sellerId, String sellerName, boolean sellerNameChanged, Date createdAt, Date updatedAt) {
        this.sellerId = sellerId;
        this.sellerName = sellerName;
        this.sellerNameChanged = sellerNameChanged;
        this.researchStatus = "待機中"; // Default status
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    /**
     * Complete constructor with all fields
     *
     * @param sellerId          The ID of the seller
     * @param sellerName        The name of the seller
     * @param sellerNameChanged Whether the seller name was changed
     * @param createdAt         The creation date
     * @param updatedAt         The update date
     * @param researchStatus    The research status
     */
    public SellerModel(String sellerId, String sellerName, boolean sellerNameChanged, Date createdAt, Date updatedAt, String researchStatus) {
        this.sellerId = sellerId;
        this.sellerName = sellerName;
        this.sellerNameChanged = sellerNameChanged;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.researchStatus = researchStatus != null ? researchStatus : "待機中";
    }

    /**
     * Update seller name and mark as changed if different
     *
     * @param newSellerName The new seller name
     * @return true if the name was actually changed, false otherwise
     */
    public boolean updateSellerName(String newSellerName) {
        if (newSellerName == null || newSellerName.trim().isEmpty()) {
            return false;
        }

        // Check if the name is actually different
        if (!newSellerName.equals(this.sellerName)) {
            this.sellerName = newSellerName;
            this.sellerNameChanged = true;
            this.updatedAt = new Date();
            return true;
        }

        return false;
    }

    /**
     * Check if this seller has a valid name (not just the seller ID)
     *
     * @return true if the seller has a proper name, false if it's just the ID or a placeholder
     */
    public boolean hasValidName() {
        return sellerName != null &&
                !sellerName.equals(sellerId) &&
                !sellerName.startsWith("セラー_") &&
                !sellerName.trim().isEmpty();
    }

    /**
     * Get display name for UI
     * Returns seller name if available, otherwise returns seller ID
     *
     * @return The display name for the seller
     */
    public String getDisplayName() {
        return hasValidName() ? sellerName : sellerId;
    }

    /**
     * Mark the seller as updated
     */
    public void markAsUpdated() {
        this.updatedAt = new Date();
    }

    // Manual getters and setters for compilation compatibility (Lombok generates these automatically)
    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public String getSellerName() {
        return sellerName;
    }

    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
    }

    public boolean isSellerNameChanged() {
        return sellerNameChanged;
    }

    public void setSellerNameChanged(boolean sellerNameChanged) {
        this.sellerNameChanged = sellerNameChanged;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getResearchStatus() {
        return researchStatus;
    }

    public void setResearchStatus(String researchStatus) {
        this.researchStatus = researchStatus;
    }

    @Override
    public String toString() {
        return "SellerModel{" +
                "sellerId='" + sellerId + '\'' +
                ", sellerName='" + sellerName + '\'' +
                ", sellerNameChanged=" + sellerNameChanged +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", researchStatus='" + researchStatus + '\'' +
                '}';
    }
}
