package com.mrcresearch.screen.favorite;

import com.formdev.flatlaf.FlatClientProperties;
import com.mrcresearch.model.ProgressItem;
import com.mrcresearch.screen.main.Main;
import com.mrcresearch.screen.model.FavoriteSellerModel;
import com.mrcresearch.screen.model.FavoriteSellerStatsModel;
import com.mrcresearch.screen.model.GroupTagInfoModel;
import com.mrcresearch.service.SellerManagementService;
import com.mrcresearch.service.analyze.service.SearchBySeller;
import com.mrcresearch.service.common.FontSettings;
import com.mrcresearch.util.SearchQueueManager;
import com.mrcresearch.util.TextFieldContextMenuUtil;
import com.mrcresearch.util.database.FavoriteSellerRepository;
import net.miginfocom.swing.MigLayout;
import org.kordamp.ikonli.antdesignicons.AntDesignIconsOutlined;
import org.kordamp.ikonli.swing.FontIcon;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

public class FavoriteSellerScreen extends JPanel {
    private static final Logger logger = LoggerFactory.getLogger(FavoriteSellerScreen.class);
    private JPanel leftPanel;
    private JPanel rightPanel;
    private JTabbedPane tabbedPane;
    private JList<GroupTagInfoModel> groupList;
    private JList<GroupTagInfoModel> tagList;
    private JPanel sellerListPanel;
    private DefaultListModel<GroupTagInfoModel> groupListModel;
    private DefaultListModel<GroupTagInfoModel> tagListModel;
    private List<FavoriteSellerStatsModel> favoriteSellerStats;
    private Main mainScreen; // Reference to main screen

    public FavoriteSellerScreen() {
        init();
        setVisible(false); // Initially not visible
    }

    private void init() {
        setLayout(new MigLayout("fill,insets 20", "[20%][80%]", "[grow, fill]"));

        // Initialize data from database
        loadDataFromDatabase();

        // Left panel - Tabbed pane for Groups and Tags
        leftPanel = new JPanel(new MigLayout("fillx,insets 10", "[grow]", "[grow]"));
        leftPanel.setBorder(BorderFactory.createLineBorder(Color.LIGHT_GRAY));

        // Create tabbed pane
        tabbedPane = new JTabbedPane();
        tabbedPane.putClientProperty(FlatClientProperties.TABBED_PANE_TAB_HEIGHT, 40);
        tabbedPane.putClientProperty(FlatClientProperties.STYLE, "font:+2");

        // Group tab
        JPanel groupPanel = new JPanel(new MigLayout("fillx,insets 10", "[grow]", "[grow]"));
        groupListModel = new DefaultListModel<>();
        updateGroupList();
        groupList = new JList<>(groupListModel);
        groupList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        groupList.setFont(FontSettings.getTextFont());
        groupList.setCellRenderer(new GroupTagCellRenderer());
        addGroupListContextMenu(groupList);
        JScrollPane groupScrollPane = new JScrollPane(groupList);
        groupPanel.add(groupScrollPane, "grow");

        // Tag tab
        JPanel tagPanel = new JPanel(new MigLayout("fillx,insets 10", "[grow]", "[grow]"));
        tagListModel = new DefaultListModel<>();
        updateTagList();
        tagList = new JList<>(tagListModel);
        tagList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        tagList.setFont(FontSettings.getLabelFont());
        tagList.setCellRenderer(new GroupTagCellRenderer());
        addTagListContextMenu(tagList);
        JScrollPane tagScrollPane = new JScrollPane(tagList);
        tagPanel.add(tagScrollPane, "grow");

        // Add tabs to tabbed pane
        tabbedPane.addTab("グループ", groupPanel);
        tabbedPane.addTab("タグ", tagPanel);

        // Add tabbed pane to left panel
        leftPanel.add(tabbedPane, "grow");

        // Right panel - Seller list
        rightPanel = new JPanel(new MigLayout("fill,insets 10", "[grow]", "[grow]"));
        rightPanel.setBorder(BorderFactory.createLineBorder(Color.LIGHT_GRAY));

        // Seller list panel
        sellerListPanel = new JPanel(new MigLayout("wrap 1,fillx,insets 0", "[fill]", "[]"));
        JScrollPane sellerScrollPane = new JScrollPane(sellerListPanel);
        sellerScrollPane.setBorder(BorderFactory.createEmptyBorder());

        // Improve scroll performance
        sellerScrollPane.getVerticalScrollBar().setUnitIncrement(20);
        sellerScrollPane.getHorizontalScrollBar().setUnitIncrement(20);
        sellerScrollPane.setWheelScrollingEnabled(true);

        rightPanel.add(sellerScrollPane, "grow");

        // Add panels to the main panel
        add(leftPanel, "grow");
        add(rightPanel, "grow");

        // Add selection listeners
        groupList.addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                GroupTagInfoModel selectedGroup = groupList.getSelectedValue();
                if (selectedGroup != null) {
                    tagList.clearSelection();
                    updateSellerList(selectedGroup.getName(), null);
                }
            }
        });

        tagList.addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                GroupTagInfoModel selectedTag = tagList.getSelectedValue();
                if (selectedTag != null) {
                    groupList.clearSelection();
                    updateSellerList(null, selectedTag.getName());
                }
            }
        });

        // Add tab change listener
        tabbedPane.addChangeListener(e -> {
            int selectedIndex = tabbedPane.getSelectedIndex();
            if (selectedIndex == 0) { // Group tab
                tagList.clearSelection();
                GroupTagInfoModel selectedGroup = groupList.getSelectedValue();
                if (selectedGroup != null) {
                    updateSellerList(selectedGroup.getName(), null);
                } else {
                    updateSellerList(null, null);
                }
            } else if (selectedIndex == 1) { // Tag tab
                groupList.clearSelection();
                GroupTagInfoModel selectedTag = tagList.getSelectedValue();
                if (selectedTag != null) {
                    updateSellerList(null, selectedTag.getName());
                } else {
                    updateSellerList(null, null);
                }
            }
        });

        // Initial update of a seller list
        updateSellerList(null, null);
    }

    private void updateGroupList() {
        groupListModel.clear();
        // Get enhanced group information with counts and last updated timestamps
        List<GroupTagInfoModel> groups = FavoriteSellerRepository.getGroupInfoWithCounts();
        // Add to a list model
        for (GroupTagInfoModel group : groups) {
            groupListModel.addElement(group);
        }
    }

    private void updateTagList() {
        tagListModel.clear();
        // Get enhanced tag information with counts and last updated timestamps
        List<GroupTagInfoModel> tags = FavoriteSellerRepository.getTagInfoWithCounts();
        // Add to a list model
        for (GroupTagInfoModel tag : tags) {
            tagListModel.addElement(tag);
        }
    }

    private void updateSellerList(String selectedGroup, String selectedTag) {
        sellerListPanel.removeAll();

        // Load sellers with statistics from database
        List<FavoriteSellerStatsModel> filteredSellers = FavoriteSellerRepository.getFavoriteSellersWithStats(
                1, 100, selectedGroup, selectedTag);

        // Add seller panels to the list
        if (filteredSellers.isEmpty()) {
            JLabel emptyLabel = new JLabel("セラーが見つかりません");
            emptyLabel.setFont(emptyLabel.getFont().deriveFont(16f));
            emptyLabel.setForeground(Color.GRAY);
            emptyLabel.setHorizontalAlignment(SwingConstants.CENTER);
            sellerListPanel.add(emptyLabel, "align center, gaptop 10, gapbottom 10");
        } else {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm");
            for (FavoriteSellerStatsModel seller : filteredSellers) {
                JPanel sellerPanel = createSellerStatsPanel(seller, dateFormat);
                sellerListPanel.add(sellerPanel, "grow");
            }
        }

        sellerListPanel.revalidate();
        sellerListPanel.repaint();
    }

    private JPanel createSellerStatsPanel(FavoriteSellerStatsModel seller, SimpleDateFormat dateFormat) {
        JPanel panel = new JPanel(new MigLayout("fillx,insets 10", "[60%][40%]", "[][][][]"));
        panel.setBorder(BorderFactory.createLineBorder(Color.LIGHT_GRAY));
        panel.putClientProperty(FlatClientProperties.STYLE, "arc:10;");

        // Left side - Basic info
        JPanel leftPanel = new JPanel(new MigLayout("fillx,insets 0", "[grow]", "[][][][]"));

        // Seller name and ID
        JLabel nameLabel = new JLabel(seller.getSellerName() + " (" + seller.getSellerId() + ")");
        nameLabel.setFont(FontSettings.getLabelFont());
        leftPanel.add(nameLabel, "wrap");

        // Last updated
        JLabel updatedLabel = new JLabel("最終更新: " + dateFormat.format(seller.getLastUpdated()));
        updatedLabel.setFont(FontSettings.getTextFont());
        leftPanel.add(updatedLabel, "wrap");

        // Research status
        JLabel statusLabel = new JLabel("ステータス: " + seller.getResearchStatus());
        statusLabel.setFont(FontSettings.getTextFont());
        leftPanel.add(statusLabel, "wrap");

        // Tags and group
        JLabel tagsLabel = new JLabel("タグ: " + seller.getTags() + " | グループ: " + seller.getGroupName());
        tagsLabel.setFont(FontSettings.getTextFont());
        leftPanel.add(tagsLabel);

        // Right side - Statistics
        JPanel rightPanel = new JPanel(new MigLayout("fillx,insets 0", "[grow]", "[][][][]"));

        // 30-day average
        JLabel avgLabel = new JLabel("30日平均: " + String.format("%.1f", seller.getAverageDailySales30Days()) + "件/日");
        avgLabel.setFont(FontSettings.getSmallerBoldFont());
        avgLabel.setForeground(new Color(0, 120, 215)); // Blue color
        rightPanel.add(avgLabel, "wrap");

        DateFormat dayFormat = new SimpleDateFormat("M/d");

        // 7-day daily sales
        if (seller.getDailySalesLast7Days() != null) {
            StringBuilder dailySalesText = new StringBuilder("<html><tr>");
            // 今日から7日間の日付を入れている
            for (int i = 0; i < 7; i++) {
                dailySalesText.append("<td>" + dayFormat.format(new Date(System.currentTimeMillis() - (i * 24 * 60 * 60 * 1000))) + "</td>");
            }
            dailySalesText.append("</tr><tr>");
            List<Integer> dailySales = seller.getDailySalesLast7Days();
            for (int i = 0; i < dailySales.size(); i++) {
                dailySalesText.append("<td>" + dailySales.get(i) + "</td>");
            }
            dailySalesText.append("</tr></html>");

            JLabel dailyLabel = new JLabel(dailySalesText.toString());
            dailyLabel.setFont(FontSettings.getTextFont());
            dailyLabel.setForeground(new Color(0, 150, 0)); // Green color
            rightPanel.add(dailyLabel, "wrap");
        }

        // Total items researched
        JLabel totalLabel = new JLabel("総リサーチ数: " + seller.getTotalItemsResearched() + "件");
        totalLabel.setFont(FontSettings.getSmallestTextFont());
        totalLabel.setForeground(Color.GRAY);
        rightPanel.add(totalLabel, "wrap");

        // Last research date
        if (seller.getLastResearchDate() != null) {
            JLabel lastResearchLabel = new JLabel("最終リサーチ: " + dateFormat.format(seller.getLastResearchDate()));
            lastResearchLabel.setFont(FontSettings.getSmallestTextFont());
            lastResearchLabel.setForeground(Color.GRAY);
            rightPanel.add(lastResearchLabel);
        }

        panel.add(leftPanel, "grow");
        panel.add(rightPanel, "grow");

        // Add right-click context menu to the panel
        addSellerCardContextMenu(panel, seller);

        return panel;
    }

    private void loadDataFromDatabase() {
        // Initialize the database if needed
        FavoriteSeller.init();

        // Check if we have data, if not add sample data
        List<String> groups = FavoriteSellerRepository.getAllGroups();
        if (groups.isEmpty()) {
//            FavoriteSeller.addSampleData();
        }

        // Load all sellers with statistics from the database (first page, 100 items)
        favoriteSellerStats = FavoriteSellerRepository.getFavoriteSellersWithStats(1, 100, null, null);

        // Update seller names with latest information
        updateSellerNamesFromDatabase();
    }

    /**
     * Update seller names with the latest information using the new SellerManagementService
     */
    private void updateSellerNamesFromDatabase() {
        for (FavoriteSellerStatsModel seller : favoriteSellerStats) {
            // Use the new SellerManagementService to get the best available seller name
            String bestSellerName = SellerManagementService.getBestSellerName(seller.getSellerId());

            // If we got a better name than what's currently stored, update it
            if (!bestSellerName.equals(seller.getSellerName()) &&
                    SellerManagementService.isValidSellerName(seller.getSellerId(), bestSellerName)) {

                // Update the seller name in the model
                seller.setSellerName(bestSellerName);

                // Update the seller in the master table
                SellerManagementService.updateSellerName(seller.getSellerId(), bestSellerName);

                // Create FavoriteSellerModel for database update
                FavoriteSellerModel updateModel = new FavoriteSellerModel(
                        seller.getId(), bestSellerName, seller.getSellerId(), seller.getLastUpdated(),
                        seller.getResearchStatus(), seller.getTags(), seller.getGroupName());

                // Update the favorite sellers database with the new name
                FavoriteSellerRepository.updateFavoriteSeller(updateModel);
            }
        }
    }

    /**
     * Set reference to main screen for navigation
     */
    public void setMainScreen(com.mrcresearch.screen.main.Main mainScreen) {
        this.mainScreen = mainScreen;
    }

    /**
     * Refresh the data and update the display
     */
    public void refreshData() {
        loadDataFromDatabase();

        // Update group and tag lists with latest data
        updateGroupList();
        updateTagList();

        // Refresh the current view
        GroupTagInfoModel selectedGroup = groupList.getSelectedValue();
        GroupTagInfoModel selectedTag = tagList.getSelectedValue();

        if (tabbedPane.getSelectedIndex() == 0) { // Group tab
            updateSellerList(selectedGroup != null ? selectedGroup.getName() : null, null);
        } else { // Tag tab
            updateSellerList(null, selectedTag != null ? selectedTag.getName() : null);
        }
    }

    /**
     * Add context menu to seller card panel
     */
    private void addSellerCardContextMenu(JPanel panel, FavoriteSellerStatsModel seller) {
        panel.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showSellerCardContextMenu(e, seller);
                }
            }

            @Override
            public void mouseReleased(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showSellerCardContextMenu(e, seller);
                }
            }
        });
    }

    /**
     * Show context menu for seller card
     */
    private void showSellerCardContextMenu(MouseEvent e, FavoriteSellerStatsModel seller) {
        JPopupMenu contextMenu = createSellerCardContextMenu(seller);
        contextMenu.show((Component) e.getSource(), e.getX(), e.getY());
    }

    /**
     * Create context menu for seller card
     */
    private JPopupMenu createSellerCardContextMenu(FavoriteSellerStatsModel seller) {
        JPopupMenu contextMenu = new JPopupMenu();

        JMenuItem showResearchResultItem = new JMenuItem("リサーチ結果を表示");
        showResearchResultItem.setIcon(FontIcon.of(AntDesignIconsOutlined.SEARCH));
        showResearchResultItem.addActionListener(e -> showSellerResearchResult(seller));

        JMenuItem editSellerItem = new JMenuItem("編集");
        editSellerItem.setIcon(FontIcon.of(AntDesignIconsOutlined.EDIT));
        editSellerItem.addActionListener(e -> showEditSellerDialog(seller));

        contextMenu.add(showResearchResultItem);
        contextMenu.addSeparator();
        contextMenu.add(editSellerItem);

        return contextMenu;
    }

    /**
     * Show seller research result in seller search screen
     */
    private void showSellerResearchResult(FavoriteSellerStatsModel seller) {
        if (mainScreen != null) {
            mainScreen.showSellerResearchResult(seller.getSellerId());
        } else {
            JOptionPane.showMessageDialog(this,
                    "メイン画面への参照が設定されていません。",
                    "エラー", JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Add context menu to group list for batch research functionality
     */
    private void addGroupListContextMenu(JList<GroupTagInfoModel> list) {
        list.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showGroupContextMenu(e, list);
                }
            }

            @Override
            public void mouseReleased(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showGroupContextMenu(e, list);
                }
            }
        });
    }

    /**
     * Add context menu to tag list for batch research functionality
     */
    private void addTagListContextMenu(JList<GroupTagInfoModel> list) {
        list.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showTagContextMenu(e, list);
                }
            }

            @Override
            public void mouseReleased(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showTagContextMenu(e, list);
                }
            }
        });
    }

    /**
     * Show context menu for group list
     */
    private void showGroupContextMenu(MouseEvent e, JList<GroupTagInfoModel> list) {
        int index = list.locationToIndex(e.getPoint());
        if (index >= 0) {
            list.setSelectedIndex(index);
            GroupTagInfoModel selectedGroup = list.getSelectedValue();
            if (selectedGroup != null) {
                JPopupMenu contextMenu = createGroupContextMenu(selectedGroup);
                contextMenu.show(list, e.getX(), e.getY());
            }
        }
    }

    /**
     * Show context menu for tag list
     */
    private void showTagContextMenu(MouseEvent e, JList<GroupTagInfoModel> list) {
        int index = list.locationToIndex(e.getPoint());
        if (index >= 0) {
            list.setSelectedIndex(index);
            GroupTagInfoModel selectedTag = list.getSelectedValue();
            if (selectedTag != null) {
                JPopupMenu contextMenu = createTagContextMenu(selectedTag);
                contextMenu.show(list, e.getX(), e.getY());
            }
        }
    }

    /**
     * Create context menu for group
     */
    private JPopupMenu createGroupContextMenu(GroupTagInfoModel groupInfo) {
        JPopupMenu contextMenu = new JPopupMenu();

        JMenuItem researchAllItem = new JMenuItem("全員をリサーチ" + (groupInfo.isResearchInProgress() ? "(リサーチ中)" : ""));
        researchAllItem.setIcon(FontIcon.of(AntDesignIconsOutlined.SEARCH));

        researchAllItem.addActionListener(e -> startGroupBatchResearch(groupInfo));

        contextMenu.add(researchAllItem);

        return contextMenu;
    }

    /**
     * Create context menu for tag
     */
    private JPopupMenu createTagContextMenu(GroupTagInfoModel tagInfo) {
        JPopupMenu contextMenu = new JPopupMenu();

        JMenuItem researchAllItem = new JMenuItem("全員をリサーチ");
        researchAllItem.setIcon(FontIcon.of(AntDesignIconsOutlined.SEARCH));

        // Check if research is in progress
        if (tagInfo.isResearchInProgress()) {
            researchAllItem.setText("全員をリサーチ (リサーチ中)");
            researchAllItem.setEnabled(false);
            researchAllItem.setToolTipText("現在リサーチが実行中です。完了後に再度お試しください。");
        } else {
            researchAllItem.addActionListener(e -> startTagBatchResearch(tagInfo));
        }

        contextMenu.add(researchAllItem);

        return contextMenu;
    }

    /**
     * Start batch research for all sellers in a group
     */
    private void startGroupBatchResearch(GroupTagInfoModel groupInfo) {
        // Get all sellers in the group
        List<FavoriteSellerModel> sellers = FavoriteSeller.getFavoriteSellersByGroup(groupInfo.getName());

        if (sellers.isEmpty()) {
            JOptionPane.showMessageDialog(this,
                    "グループ「" + groupInfo.getName() + "」にセラーが見つかりません。",
                    "情報", JOptionPane.INFORMATION_MESSAGE);
            return;
        }

        // Confirm with user
        int result = JOptionPane.showConfirmDialog(this,
                "グループ「" + groupInfo.getName() + "」の" + sellers.size() + "名のセラーをリサーチしますか？",
                "確認", JOptionPane.YES_NO_OPTION);

        if (result == JOptionPane.YES_OPTION) {
            // Update research started timestamp immediately
            Date now = new Date();
            boolean updateResult = FavoriteSeller.updateGroupResearchTimestamps(groupInfo.getName(), now, null);

            if (!updateResult) {
                JOptionPane.showMessageDialog(this,
                        "リサーチ開始の記録に失敗しました。",
                        "エラー", JOptionPane.ERROR_MESSAGE);
                return;
            }

            // Add tasks to queue
            addBatchResearchTasks(sellers, "グループ: " + groupInfo.getName(), true, groupInfo.getName());

            // Refresh display to show research in progress status
            refreshData();
        }
    }

    /**
     * Start batch research for all sellers in a tag
     */
    private void startTagBatchResearch(GroupTagInfoModel tagInfo) {
        // Get all sellers with the tag
        List<FavoriteSellerModel> sellers = FavoriteSeller.getFavoriteSellersByTag(tagInfo.getName());

        if (sellers.isEmpty()) {
            JOptionPane.showMessageDialog(this,
                    "タグ「" + tagInfo.getName() + "」にセラーが見つかりません。",
                    "情報", JOptionPane.INFORMATION_MESSAGE);
            return;
        }

        // Confirm with user
        int result = JOptionPane.showConfirmDialog(this,
                "タグ「" + tagInfo.getName() + "」の" + sellers.size() + "名のセラーをリサーチしますか？",
                "確認", JOptionPane.YES_NO_OPTION);

        if (result == JOptionPane.YES_OPTION) {
            // Update research started timestamp immediately
            Date now = new Date();
            boolean updateResult = FavoriteSeller.updateTagResearchTimestamps(tagInfo.getName(), now, null);

            if (!updateResult) {
                JOptionPane.showMessageDialog(this,
                        "リサーチ開始の記録に失敗しました。",
                        "エラー", JOptionPane.ERROR_MESSAGE);
                return;
            }

            // Add tasks to queue
            addBatchResearchTasks(sellers, "タグ: " + tagInfo.getName(), false, tagInfo.getName());

            // Refresh display to show research in progress status
            refreshData();
        }
    }

    /**
     * Add batch research tasks to the queue
     */
    private void addBatchResearchTasks(List<FavoriteSellerModel> sellers, String batchName, boolean isGroup, String groupOrTagName) {
        int totalSellers = sellers.size();

        for (int i = 0; i < totalSellers; i++) {
            FavoriteSellerModel seller = sellers.get(i);
            final int sellerIndex = i + 1;

            SearchQueueManager.SearchTask sellerSearchTask = new SearchQueueManager.SearchTask() {
                @Override
                public void execute() throws Exception {
                    // Create progress item
                    String progressId = "batch_" + (isGroup ? "group" : "tag") + "_" + groupOrTagName + "_" + seller.getSellerId();
                    ProgressItem progressItem = new ProgressItem(progressId,
                            batchName + " - " + seller.getSellerName(),
                            seller.getSellerId(),
                            sellerIndex,
                            totalSellers);

                    SearchQueueManager.getInstance().addProgressItem(progressItem);

                    try {
                        // Update progress to processing
                        progressItem.setStatus(ProgressItem.Status.PROCESSING);

                        // Execute seller research
                        SearchBySeller searchBySeller = new SearchBySeller();
                        searchBySeller.execute(seller.getSellerId(), progressId);

                        // Update progress to completed
                        progressItem.setStatus(ProgressItem.Status.COMPLETED);

                    } catch (Exception e) {
                        // Update progress to error
                        progressItem.setStatus(ProgressItem.Status.ERROR);
                        throw e;
                    }
                }

                @Override
                public String getTaskName() {
                    return batchName + " - " + seller.getSellerName() + " (" + sellerIndex + "/" + totalSellers + ")";
                }

                @Override
                public String getTaskType() {
                    return "FAVORITE_BATCH";
                }

                @Override
                public int getEstimatedTimeSeconds() {
                    return 30; // 30 seconds per seller
                }

                @Override
                public int getPriority() {
                    return 85; // High priority for favorite batch research
                }

                @Override
                public String getDetailedDescription() {
                    return seller.getSellerId();
                }

                @Override
                public int getItemCount() {
                    return 1;
                }

                @Override
                public boolean hasAsyncOperations() {
                    return false;
                }

                @Override
                public void onTaskCompleted() {
                    // If this is the last seller in the batch, update the completion timestamp
                    if (sellerIndex == totalSellers) {
                        Date completionTime = new Date();
                        boolean updateResult;

                        if (isGroup) {
                            // Update only last_updated, keep research_started_at unchanged
                            updateResult = FavoriteSeller.updateGroupResearchTimestamps(groupOrTagName, null, completionTime);
                        } else {
                            // Update only last_updated, keep research_started_at unchanged
                            updateResult = FavoriteSeller.updateTagResearchTimestamps(groupOrTagName, null, completionTime);
                        }

                        if (updateResult) {
                            logger.info((isGroup ? "グループ" : "タグ") + ": " + groupOrTagName + " の完了タイムスタンプを正常に更新しました");
                        } else {
                            logger.error("Failed to update completion timestamp for " +
                                    (isGroup ? "group" : "tag") + ": " + groupOrTagName);
                        }

                        // Refresh display on EDT
                        SwingUtilities.invokeLater(() -> refreshData());
                    }
                }
            };

            // Add task to queue
            SearchQueueManager.getInstance().addTask(sellerSearchTask);
        }

        // Show confirmation message
        JOptionPane.showMessageDialog(this,
                batchName + "の" + totalSellers + "名のセラーリサーチをキューに追加しました。",
                "情報", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * Show edit dialog for seller information
     */
    private void showEditSellerDialog(FavoriteSellerStatsModel seller) {
        // Create dialog
        JDialog dialog = new JDialog((Frame) SwingUtilities.getWindowAncestor(this), "セラー情報編集", true);
        dialog.setLayout(new MigLayout("fillx,insets 20", "[right][grow]", "[][][][][][]push[]"));

        // Seller ID (read-only)
        dialog.add(new JLabel("セラーID:"), "");
        JLabel sellerIdLabel = new JLabel(seller.getSellerId());
        sellerIdLabel.setFont(FontSettings.getLabelFont());
        dialog.add(sellerIdLabel, "wrap");

        // Seller name
        dialog.add(new JLabel("セラー名:"), "");
        JTextField sellerNameField = new JTextField(seller.getSellerName());
        sellerNameField.putClientProperty(FlatClientProperties.PLACEHOLDER_TEXT, "セラー名を入力");
        TextFieldContextMenuUtil.enableTextFieldContextMenu(sellerNameField);
        dialog.add(sellerNameField, "growx, wrap");

        // Group selection
        dialog.add(new JLabel("グループ:"), "");
        JComboBox<String> groupCombo = new JComboBox<>();
        groupCombo.setEditable(true);
        groupCombo.putClientProperty(FlatClientProperties.PLACEHOLDER_TEXT, "グループ名を入力または選択");

        // Add existing groups
        List<String> groups = FavoriteSeller.getAllGroups();
        for (String group : groups) {
            groupCombo.addItem(group);
        }

        // Set current group
        if (seller.getGroupName() != null && !seller.getGroupName().trim().isEmpty()) {
            groupCombo.setSelectedItem(seller.getGroupName());
        }

        dialog.add(groupCombo, "growx, wrap");

        // Tags input
        dialog.add(new JLabel("タグ (カンマ区切り):"), "");
        JTextField tagsField = new JTextField(seller.getTags() != null ? seller.getTags() : "");
        tagsField.putClientProperty(FlatClientProperties.PLACEHOLDER_TEXT, "タグ1, タグ2, タグ3");
        TextFieldContextMenuUtil.enableTextFieldContextMenu(tagsField);
        dialog.add(tagsField, "growx, wrap");

        // Buttons panel
        JPanel buttonsPanel = new JPanel(new MigLayout("insets 0", "[grow][][]"));

        JButton cancelButton = new JButton("キャンセル");
        cancelButton.addActionListener(e -> dialog.dispose());

        JButton saveButton = new JButton("保存");
        saveButton.addActionListener(e -> {
            String newSellerName = sellerNameField.getText().trim();
            String newGroupName = groupCombo.getSelectedItem().toString().trim();
            String newTags = tagsField.getText().trim();

            // Validation
            if (newSellerName.isEmpty()) {
                JOptionPane.showMessageDialog(dialog, "セラー名を入力してください。", "エラー", JOptionPane.ERROR_MESSAGE);
                return;
            }

            if (newGroupName.isEmpty()) {
                JOptionPane.showMessageDialog(dialog, "グループ名を入力してください。", "エラー", JOptionPane.ERROR_MESSAGE);
                return;
            }

            // Update the seller information
            boolean success = updateSellerInformation(seller, newSellerName, newGroupName, newTags);

            if (success) {
                JOptionPane.showMessageDialog(dialog, "セラー情報を更新しました。", "成功", JOptionPane.INFORMATION_MESSAGE);
                dialog.dispose();

                // Refresh the display
                refreshData();
            } else {
                JOptionPane.showMessageDialog(dialog, "セラー情報の更新に失敗しました。", "エラー", JOptionPane.ERROR_MESSAGE);
            }
        });

        buttonsPanel.add(new JLabel(), "grow"); // Spacer
        buttonsPanel.add(cancelButton);
        buttonsPanel.add(saveButton);

        dialog.add(buttonsPanel, "span 2, growx");

        // Set dialog properties
        dialog.setSize(500, 300);
        dialog.setLocationRelativeTo(this);
        dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
        dialog.setVisible(true);
    }

    /**
     * Update seller information in the database
     */
    private boolean updateSellerInformation(FavoriteSellerStatsModel seller, String newSellerName,
                                            String newGroupName, String newTags) {
        try {
            // Create updated model
            FavoriteSellerModel updatedModel = new FavoriteSellerModel(
                    seller.getId(),
                    newSellerName,
                    seller.getSellerId(),
                    new Date(), // Update the last updated date
                    seller.getResearchStatus(),
                    newTags,
                    newGroupName
            );

            // Update in database
            boolean success = FavoriteSellerRepository.updateFavoriteSeller(updatedModel);

            if (success) {
                // Update the seller management service if the name changed
                if (!newSellerName.equals(seller.getSellerName())) {
                    SellerManagementService.updateSellerName(seller.getSellerId(), newSellerName);
                }
            }

            return success;

        } catch (Exception e) {
            logger.error("Error updating seller information", e);
            return false;
        }
    }

    /**
     * Custom cell renderer for group/tag list items with enhanced display format
     */
    private static class GroupTagCellRenderer extends DefaultListCellRenderer {
        private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/M/d HH:mm");

        @Override
        public Component getListCellRendererComponent(JList<?> list, Object value, int index,
                                                      boolean isSelected, boolean cellHasFocus) {
            super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);

            if (value instanceof GroupTagInfoModel info) {

                // Create the display text with the required format
                StringBuilder displayText = new StringBuilder();
                displayText.append("<html>");
                displayText.append("<div style='padding: 5px;'>");

                // Group/Tag Name with seller count
                displayText.append("<div style='font-weight: bold; font-size: " + FontSettings.getTextFont().getSize() + "px;'>");
                displayText.append(info.getName()).append(" セラー：").append(info.getSellerCount()).append("名");
                displayText.append("</div>");

                // Last updated timestamp with research status
                displayText.append("<div style='font-size: " + FontSettings.getSmallestTextFont().getSize() + "px; margin-top: 2px;'>");
                if (info.isResearchInProgress()) {
                    displayText.append("<span style='color: orange;'>リサーチ中...</span>");
                    if (info.getResearchStartedAt() != null) {
                        displayText.append("<br>開始：").append(dateFormat.format(info.getResearchStartedAt()));
                    }
                } else if (info.isHasResearch() && info.getLastUpdated() != null) {
                    displayText.append("<span style='color: gray;'>最終更新：").append(dateFormat.format(info.getLastUpdated())).append("</span>");
                } else {
                    displayText.append("<span style='color: gray;'>最終更新：未実施</span>");
                }
                displayText.append("</div>");

                displayText.append("</div>");
                displayText.append("</html>");

                setText(displayText.toString());
            }

            return this;
        }
    }
}