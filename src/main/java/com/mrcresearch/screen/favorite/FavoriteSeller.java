package com.mrcresearch.screen.favorite;

import com.mrcresearch.screen.model.FavoriteSellerModel;
import com.mrcresearch.screen.model.GroupTagInfoModel;
import com.mrcresearch.util.database.DatabaseUtil;

import java.util.List;

/**
 * Class for handling favorite seller operations.
 * Provides methods to interact with the database for favorite sellers.
 */
public class FavoriteSeller {

    /**
     * Initialize the favorite seller functionality.
     * This should be called when the application starts.
     */
    public static void init() {
        // Initialize database tables
        DatabaseUtil.initDatabase();
    }

    /**
     * Add a new favorite seller.
     *
     * @param model The favorite seller model to add
     * @return The ID of the saved record, or -1 if an error occurred
     */
    public static int addFavoriteSeller(FavoriteSellerModel model) {
        return DatabaseUtil.saveFavoriteSeller(model);
    }

    /**
     * Update an existing favorite seller.
     *
     * @param model The favorite seller model to update
     * @return true if the update was successful, false otherwise
     */
    public static boolean updateFavoriteSeller(FavoriteSellerModel model) {
        return DatabaseUtil.updateFavoriteSeller(model);
    }

    /**
     * Delete a favorite seller.
     *
     * @param sellerId The seller ID to delete
     * @return true if the deletion was successful, false otherwise
     */
    public static boolean deleteFavoriteSeller(String sellerId) {
        return DatabaseUtil.deleteFavoriteSeller(sellerId);
    }

    /**
     * Get all favorite sellers with pagination.
     *
     * @param page     The page number (1-based)
     * @param pageSize The number of records per page
     * @return A list of favorite seller models
     */
    public static List<FavoriteSellerModel> getAllFavoriteSellers(int page, int pageSize) {
        return DatabaseUtil.getFavoriteSellers(page, pageSize);
    }

    /**
     * Get favorite sellers by group.
     *
     * @param groupName The group name to filter by
     * @return A list of favorite seller models
     */
    public static List<FavoriteSellerModel> getFavoriteSellersByGroup(String groupName) {
        return DatabaseUtil.getFavoriteSellersByGroup(groupName);
    }

    /**
     * Get favorite sellers by tag.
     *
     * @param tag The tag to filter by
     * @return A list of favorite seller models
     */
    public static List<FavoriteSellerModel> getFavoriteSellersByTag(String tag) {
        return DatabaseUtil.getFavoriteSellersByTag(tag);
    }

    /**
     * Get all unique group names.
     *
     * @return A list of group names
     */
    public static List<String> getAllGroups() {
        return DatabaseUtil.getAllGroups();
    }

    /**
     * Get all unique tags.
     *
     * @return A list of tags
     */
    public static List<String> getAllTags() {
        return DatabaseUtil.getAllTags();
    }

    /**
     * Get enhanced group information with seller counts and last updated timestamps.
     *
     * @return A list of group information models
     */
    public static List<GroupTagInfoModel> getGroupInfoWithCounts() {
        return DatabaseUtil.getGroupInfoWithCounts();
    }

    /**
     * Get enhanced tag information with seller counts and last updated timestamps.
     *
     * @return A list of tag information models
     */
    public static List<GroupTagInfoModel> getTagInfoWithCounts() {
        return DatabaseUtil.getTagInfoWithCounts();
    }

    /**
     * Update group research timestamps.
     *
     * @param groupName         The group name
     * @param researchStartedAt The research start timestamp
     * @param lastUpdated       The last updated timestamp
     * @return true if the update was successful, false otherwise
     */
    public static boolean updateGroupResearchTimestamps(String groupName, java.util.Date researchStartedAt, java.util.Date lastUpdated) {
        return DatabaseUtil.updateGroupResearchTimestamps(groupName, researchStartedAt, lastUpdated);
    }

    /**
     * Update tag research timestamps.
     *
     * @param tagName           The tag name
     * @param researchStartedAt The research start timestamp
     * @param lastUpdated       The last updated timestamp
     * @return true if the update was successful, false otherwise
     */
    public static boolean updateTagResearchTimestamps(String tagName, java.util.Date researchStartedAt, java.util.Date lastUpdated) {
        return DatabaseUtil.updateTagResearchTimestamps(tagName, researchStartedAt, lastUpdated);
    }

    /**
     * Check if a seller is already in favorites.
     *
     * @param sellerId The seller ID to check
     * @return true if the seller is in favorites, false otherwise
     */
    public static boolean isFavorite(String sellerId) {
        return DatabaseUtil.isFavoriteSeller(sellerId);
    }

    /**
     * Get a favorite seller by their seller ID.
     *
     * @param sellerId The seller ID
     * @return The favorite seller model, or null if not found
     */
    public static FavoriteSellerModel getFavoriteSellerBySellerId(String sellerId) {
        return DatabaseUtil.getFavoriteSellerBySellerId(sellerId);
    }
}
