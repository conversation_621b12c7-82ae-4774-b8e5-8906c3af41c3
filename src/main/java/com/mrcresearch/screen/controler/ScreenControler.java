package com.mrcresearch.screen.controler;

import com.mrcresearch.screen.main.Application;
import com.mrcresearch.service.auth.model.AuthStatus;
import com.mrcresearch.service.common.Version;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.io.IOException;

public class ScreenControler {
    private static final Logger logger = LoggerFactory.getLogger(ScreenControler.class);
    Application app;

    public ScreenControler(Application app) {
        this.app = app;
    }

    public void addLoginListener(JButton button) {
        // Set up the login success callback
        app.getLogin().setOnLoginSuccess(() -> {
            logger.debug("ログイン成功コールバックがトリガーされました");

            // Get the authentication result
            AuthStatus authResult = app.getLogin().getLastAuthResult();

            if (authResult != null && AuthStatus.ACTIVE.equals(authResult.getStatus())) {
                logger.debug("認証成功、メイン画面へ進みます");

                // Authentication successful, proceed to main screen
                app.getLogin().setVisible(false);
                app.getMain().setVisible(true);
                app.setContentPane(app.getMain());

                // Set expiration date in menu if available
                String expireDate = authResult.getExpireDate();
                logger.debug("認証ステータスからの有効期限: {}", expireDate);

                if (expireDate != null) {
                    logger.debug("setExpirationDate を呼び出します: {}", expireDate);
                    app.getMain().setExpirationDate(expireDate);
                } else {
                    logger.debug("有効期限が利用できません");
                }

                // Check for version updates after successful login
                checkForVersionUpdate();

                // Force revalidate and repaint to ensure the main panel is displayed
                app.getMain().revalidate();
                app.getMain().repaint();
                app.revalidate();
                app.repaint();
            }
        });

        // The button click now just triggers authentication
        // The screen transition will happen automatically on success
        button.addActionListener(e -> {
            logger.debug("ログインボタンがクリックされました - 認証はログインコンポーネントによって処理されます");
        });
    }

    /**
     * Check for version updates and show notification if available
     */
    private void checkForVersionUpdate() {
        // Run version check in background thread to avoid blocking UI
        SwingWorker<Boolean, Void> versionWorker = new SwingWorker<Boolean, Void>() {
            @Override
            protected Boolean doInBackground() throws Exception {
                try {
                    logger.debug("バージョン更新を確認中...");
                    boolean isLatest = Version.isLatestVersion();
                    logger.debug("最新バージョンです: {}", isLatest);
                    return isLatest;
                } catch (IOException e) {
                    logger.error("Error checking version: {}", e.getMessage());
                    return true; // Assume latest if check fails
                }
            }

            @Override
            protected void done() {
                try {
                    Boolean isLatest = get();
                    if (isLatest != null && !isLatest) {
                        // New version available, show notification
                        logger.debug("新しいバージョンが利用可能です。通知を表示します");
                        SwingUtilities.invokeLater(() -> {
                            app.getMain().showVersionNotification();
                        });
                    } else {
                        logger.debug("現在のバージョンは最新です、またはチェックに失敗しました");
                    }
                } catch (Exception e) {
                    logger.error("Error in version check done(): {}", e.getMessage(), e);
                }
            }
        };

        versionWorker.execute();
    }

}