package com.mrcresearch.screen.component;

import com.mrcresearch.screen.component.pagination.PaginationComponent;
import com.mrcresearch.screen.component.pagination.PaginationConfig;
import com.mrcresearch.screen.component.pagination.PaginationListener;
import com.mrcresearch.screen.model.SearchItemModel;
import com.mrcresearch.service.enums.ItemTypeEnum;
import com.mrcresearch.service.common.FontSettings;
import com.mrcresearch.util.TableBrowserContextMenuUtil;
import com.mrcresearch.util.database.SearchItemRepository;
import com.mrcresearch.util.database.SettingsRepository;
import net.miginfocom.swing.MigLayout;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Locale;

/**
 * Panel for displaying keyword search result items with pagination and old item control.
 */
public class KeywordSearchItemsPanel extends JPanel {
    private static final int PAGE_SIZE = 50; // 50 items per page as per requirements
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final NumberFormat PRICE_FORMAT = NumberFormat.getNumberInstance(Locale.JAPAN);

    // UI components
    private JTable itemsTable;
    private DefaultTableModel tableModel;
    private PaginationComponent paginationComponent;
    private JScrollPane scrollPane; // スクロール位置リセット用
    private JTextField minPriceField;
    private JTextField maxPriceField;

    // Pagination and data
    private int keywordSearchResultId = -1; // -1 means show all items

    // Settings
    private int soldDateDays = 30;
    private int oldItemDisplayCount = 10;

    // Data cache for context menu functionality
    private List<SearchItemModel> currentItems;

    /**
     * Constructor for showing all items
     */
    public KeywordSearchItemsPanel() {
        this(-1); // Show all items
    }

    /**
     * Constructor for showing items from a specific search result
     *
     * @param keywordSearchResultId The ID of the keyword search result to filter by, or -1 for all items
     */
    public KeywordSearchItemsPanel(int keywordSearchResultId) {
        this.keywordSearchResultId = keywordSearchResultId;
        loadSettings();
        init();
        loadData();
    }

    /**
     * Load settings from database
     */
    private void loadSettings() {
        Object[] settings = SettingsRepository.loadSettings();
        if (settings != null && settings.length > 5) {
            soldDateDays = (int) settings[0]; // soldDate
            oldItemDisplayCount = (int) settings[5]; // oldItemDisplayCount
        }
    }


    /**
     * Initialize the UI components
     */
    private void init() {
        setLayout(new MigLayout("fill,insets 10", "[grow]", "[][grow][]"));

        // Initialize pagination component
        initPaginationComponent();

        // Add title panel
        add(createTitlePanel(), "growx,wrap");

        // Add table panel
        add(createTablePanel(), "grow,wrap");

        // Add pagination component
        add(paginationComponent, "growx");
    }

    /**
     * Initialize pagination component
     */
    private void initPaginationComponent() {
        // Create pagination configuration for keyword search items
        PaginationConfig config = PaginationConfig.createForKeywordSearchItems();

        paginationComponent = new PaginationComponent(config);
        paginationComponent.setPaginationListener(new PaginationListener() {
            @Override
            public void onPageChanged(int newPage, int pageSize) {
                loadData();
            }
        });
    }

    /**
     * Create title panel
     */
    private JPanel createTitlePanel() {
        JPanel panel = new JPanel(new MigLayout("", "[grow][][][][]"));

        String titleText = "キーワード検索結果アイテム一覧";
        if (keywordSearchResultId != -1) {
            titleText += " (検索結果ID: " + keywordSearchResultId + ")";
        }

        JLabel titleLabel = new JLabel(titleText);
        titleLabel.setFont(FontSettings.getLabelFont());
        panel.add(titleLabel, "growx");

        panel.add(new JLabel("価格:"));
        minPriceField = new JTextField(5);
        panel.add(minPriceField);
        panel.add(new JLabel("~"));
        maxPriceField = new JTextField(5);
        panel.add(maxPriceField);

        JButton filterButton = new JButton("絞り込み");
        filterButton.addActionListener(e -> refresh());
        panel.add(filterButton);

        return panel;
    }

    /**
     * Create table panel
     */
    private JPanel createTablePanel() {
        JPanel panel = new JPanel(new MigLayout("fill", "[grow]", "[grow]"));

        // Create table model
        String[] columnNames = {"商品ID", "商品名", "価格", "ステータス", "出品者ID", "更新日時", "保存日時"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Make table read-only
            }
        };

        itemsTable = new JTable(tableModel);
        itemsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        itemsTable.setRowHeight(25);

        // Set column widths
        itemsTable.getColumnModel().getColumn(0).setPreferredWidth(100); // 商品ID
        itemsTable.getColumnModel().getColumn(1).setPreferredWidth(300); // 商品名
        itemsTable.getColumnModel().getColumn(2).setPreferredWidth(80);  // 価格
        itemsTable.getColumnModel().getColumn(3).setPreferredWidth(80);  // ステータス
        itemsTable.getColumnModel().getColumn(4).setPreferredWidth(100); // 出品者ID
        itemsTable.getColumnModel().getColumn(5).setPreferredWidth(150); // 更新日時
        itemsTable.getColumnModel().getColumn(6).setPreferredWidth(150); // 保存日時

        // Enable browser context menu functionality for the table
        // Column indices: 0=商品ID, 1=商品名, 2=価格, 3=ステータス, 4=出品者ID, 5=更新日時, 6=保存日時
        TableBrowserContextMenuUtil.enableBrowserContextMenu(
                itemsTable,
                0, // itemIdColumnIndex (商品ID)
                1, // productNameColumnIndex (商品名)
                4, // sellerIdColumnIndex (出品者ID)
                this::getItemTypeForRow // itemTypeProvider function
        );

        scrollPane = new JScrollPane(itemsTable);
        panel.add(scrollPane, "grow");

        // Set scroll pane for pagination component's scroll-to-top functionality
        paginationComponent.setScrollPane(scrollPane);

        return panel;
    }

    /**
     * Load data from the database and update the table
     */
    public void loadData() {
        // Clear existing data
        tableModel.setRowCount(0);

        try {
            Integer minPrice = null;
            Integer maxPrice = null;
            try {
                minPrice = Integer.parseInt(minPriceField.getText());
            } catch (NumberFormatException e) {
                // ignore
            }
            try {
                maxPrice = Integer.parseInt(maxPriceField.getText());
            } catch (NumberFormatException e) {
                // ignore
            }

            // Get total count for pagination from search_items table
            int totalCount = SearchItemRepository.getTotalSearchItemsCount(minPrice, maxPrice);

            // Update pagination component
            paginationComponent.updatePagination(totalCount, paginationComponent.getCurrentPage());

            // Get data for current page from search_items table
            List<SearchItemModel> items = SearchItemRepository.getSearchItems(paginationComponent.getCurrentPage(), PAGE_SIZE, minPrice, maxPrice);

            // Cache the items for context menu functionality
            this.currentItems = items;

            if (items.isEmpty()) {
                // Add a placeholder row to indicate no data
                Object[] noDataRow = {"データがありません", "", "", "", "", "", ""};
                tableModel.addRow(noDataRow);
            } else {
                // Add data to table
                for (SearchItemModel item : items) {
                    Object[] rowData = {
                            item.getItemId(),
                            item.getProductName(),
                            PRICE_FORMAT.format(item.getPrice()) + "円",
                            item.getStatusDisplayText(),
                            item.getSellerId(),
                            item.getUpdatedDate(),
                            item.getListingDate()  // Use listing date instead of saved date
                    };
                    tableModel.addRow(rowData);
                }
            }

            // Force table refresh
            itemsTable.revalidate();
            itemsTable.repaint();

        } catch (Exception e) {
            System.err.println("ERROR: Failed to load keyword search items: " + e.getMessage());
            e.printStackTrace();

            // Add error row to indicate the problem
            Object[] errorRow = {"データ読み込みエラー", e.getMessage(), "", "", "", "", ""};
            tableModel.addRow(errorRow);
        }
    }

    /**
     * Apply theme changes to the component
     */
    public void applyThemeChange() {
        // Apply theme changes to pagination component
        if (paginationComponent != null) {
            paginationComponent.applyThemeChange();
        }

        // Repaint the entire panel
        repaint();
    }

    /**
     * Refresh the data
     */
    public void refresh() {
        loadSettings();
        // Reset pagination to first page
        paginationComponent.updatePagination(0, 1);
        loadData();
    }

    // Note: scrollToTop functionality is now handled by PaginationComponent

    /**
     * Get item type for a specific row (used by context menu)
     *
     * @param row The row index
     * @return ItemTypeEnum for the row, or NORMAL as default
     */
    private ItemTypeEnum getItemTypeForRow(int row) {
        try {
            if (currentItems != null && row >= 0 && row < currentItems.size()) {
                SearchItemModel item = currentItems.get(row);
                return ItemTypeEnum.fromId(item.getItemType());
            }
        } catch (Exception e) {
            System.err.println("Error getting item type for row " + row + ": " + e.getMessage());
        }
        return ItemTypeEnum.NORMAL; // Default fallback
    }
}
