package com.mrcresearch.screen.component.category;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * カテゴリーを設定するモデル<br />
 * id カテゴリーID<br />
 * name カテゴリー名<br />
 * level　カテゴリーの階層<br />
 * parentId　親カテゴリーID<br />
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonPropertyOrder({
        "id",
        "name",
        "level",
        "parentId",
        "hasChild",
})
public class CategoryModel {
    @JsonProperty("id")
    private String id;
    @JsonProperty("name")
    private String name;
    @JsonProperty("level")
    private String level;
    @JsonProperty("parentId")
    private String parentId;
    @JsonProperty("hasChild")
    private boolean hasChild;
}
