package com.mrcresearch.screen.component.category;

import com.fasterxml.jackson.databind.MappingIterator;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * カテゴリーを扱うクラス
 */
public class Categories {
    private static final Logger logger = LoggerFactory.getLogger(Categories.class);
    public static final String CATEGORY_ALL = "すべて";
    private final List<CategoryModel> categoryList = new ArrayList<>();
    private final String filePath = "/category/category.csv";

    /**
     * カテゴリーリストを初期化する
     */
    public void init() {
        CsvMapper csvMapper = new CsvMapper();
        CsvSchema csvSchema = csvMapper.
                schemaFor(CategoryModel.class)
                .withHeader();
        try (MappingIterator<CategoryModel> mappingIterator = csvMapper.
                readerFor(CategoryModel.class)
                .with(csvSchema)
                .readValues(getClass().getResource(filePath))) {

            while (mappingIterator.hasNextValue()) {
                categoryList.add(mappingIterator.nextValue());
            }
        } catch (Exception e) {
            logger.error("Failed to load category csv: " + getClass().getResource(filePath), e);
        }
    }

    /**
     * コンボボックスに次の階層のカテゴリ一覧をコンボボックスにして返却する<br>
     *
     * @param convertList コンボボックスに変換するリスト
     * @return コンボボックスモデル
     */
    public DefaultComboBoxModel<String> getCategoryComboBoxModel(List<CategoryModel> convertList) {
        // 名前を取得し、配列形式に変換する
        List<String> comboNameList = new ArrayList<>();
        comboNameList.add(CATEGORY_ALL);
        if (convertList != null) {
            comboNameList.addAll(convertList.stream().map(CategoryModel::getName).toList());
        }
        // 引数を返す
        return new DefaultComboBoxModel<>(comboNameList.toArray(String[]::new));
    }

    /**
     * コンボボックスに次の階層のカテゴリ一覧をコンボボックスにして返却する<br>
     *
     * @param convertList 変換するカテゴリリスト
     * @return コンボボックスモデル
     */
    public DefaultComboBoxModel<String> getNextCategoryComboBoxModel(List<CategoryModel> convertList) { // NOSONAR SONARちゃんが文を読めていないだけで問題なし
        // 取ってきたモデルから名前を取得し、配列形式に変換する
        List<String> comboNameList = new ArrayList<>();
        comboNameList.add(CATEGORY_ALL);
        if (convertList != null) {
            comboNameList.addAll(convertList.stream().map(CategoryModel::getName).toList());
        }
        // 引数を返す
        return new DefaultComboBoxModel<>(comboNameList.toArray(String[]::new));
    }

    /**
     * カテゴリーリストを取得する
     *
     * @param convertList 変換したいカテゴリーリスト
     * @return カテゴリーリスト
     */
    public DefaultListModel<String> getCategoryListModel(List<CategoryModel> convertList) {
        // 名前を取得し、配列形式に変換する
        DefaultListModel<String> model = new DefaultListModel<>();
        model.addElement(CATEGORY_ALL);
        if (convertList != null) {
            convertList.forEach(n -> model.addElement(n.getName()));
        }
        // 引数を返す
        return model;
    }

    /**
     * 次のカテゴリのリストを取得する
     *
     * @param searchList 親IDのリスト
     * @param name       親IDの選択されているアイテム
     * @return 子カテゴリリスト
     */
    public List<CategoryModel> getNextCategoryList(List<CategoryModel> searchList, String name) {
        // カテゴリーから引数にあったモデルを取得する
        CategoryModel parentModel = searchList.stream()
                .filter(n -> n.getName().equals(name))
                .findFirst().orElse(null);

        if (Objects.nonNull(parentModel)) {
            return categoryList.stream().filter(n -> n.getParentId().equals(parentModel.getId())).toList();
        } else {
            return null; // NOSONAR 別にどうせnullチェックするうるせーよ
        }
    }

    /**
     * 親カテゴリIDから子カテゴリの情報を取得する
     *
     * @param parentId 親カテゴリID
     * @return 子カテゴリのリスト
     */
    public List<CategoryModel> getCategoryFromParentId(String parentId) {
        List<CategoryModel> returnList = new ArrayList<>();
        if (!"0".equals(parentId)) {
            returnList.add(setCategoryAll());
        }
        returnList.addAll(categoryList.stream().filter(n -> n.getParentId().equals(parentId)).toList());
        return returnList;
    }

    /**
     * 「すべて」を作成する
     *
     * @return すべて用のカテゴリモデル
     */
    public CategoryModel setCategoryAll() {
        CategoryModel cm = new CategoryModel();
        cm.setName(CATEGORY_ALL);
        return cm;
    }

    /**
     * カテゴリリストが有効かどうかを確認する
     *
     * @return カテゴリリストが初期化されているかどうか
     */
    public boolean isInitialized() {
        return !categoryList.isEmpty();
    }

    /**
     * カテゴリーIDからカテゴリー名を取得する
     *
     * @param categoryId カテゴリーID
     * @return カテゴリー名（見つからない場合はnull）
     */
    public String getCategoryNameById(String categoryId) {
        if (categoryId == null || categoryId.trim().isEmpty()) {
            return null;
        }

        return categoryList.stream()
                .filter(category -> categoryId.equals(category.getId()))
                .map(CategoryModel::getName)
                .findFirst()
                .orElse(null);
    }

    /**
     * カンマ区切りのカテゴリーIDリストをカテゴリー名リストに変換する
     *
     * @param categoryIds カンマ区切りのカテゴリーIDリスト
     * @return カンマ区切りのカテゴリー名リスト（IDが見つからない場合はそのIDを表示）
     */
    public String convertCategoryIdsToNames(String categoryIds) {
        if (categoryIds == null || categoryIds.trim().isEmpty()) {
            return "";
        }

        // カンマ区切りで分割
        String[] idArray = categoryIds.split(",");
        List<String> nameList = new ArrayList<>();

        for (String id : idArray) {
            String trimmedId = id.trim();
            if (!trimmedId.isEmpty()) {
                String categoryName = getCategoryNameById(trimmedId);
                if (categoryName != null) {
                    nameList.add(categoryName);
                } else {
                    // カテゴリー名が見つからない場合はIDをフォールバック表示
                    nameList.add("カテゴリID: " + trimmedId);
                }
            }
        }

        return String.join(", ", nameList);
    }

    /**
     * カテゴリー情報が既にカテゴリー名かどうかを判定する
     * （カテゴリーIDの場合は数値のみ、カテゴリー名の場合は日本語を含む）
     *
     * @param categoryInfo カテゴリー情報
     * @return カテゴリー名の場合true、カテゴリーIDの場合false
     */
    public boolean isCategoryName(String categoryInfo) {
        if (categoryInfo == null || categoryInfo.trim().isEmpty()) {
            return false;
        }

        // "カテゴリID: " で始まる場合はID表示
        if (categoryInfo.startsWith("カテゴリID: ")) {
            return false;
        }

        // 数値のみの場合はID
        try {
            Integer.parseInt(categoryInfo.trim());
            return false;
        } catch (NumberFormatException e) {
            // 数値でない場合はカテゴリー名と判定
            return true;
        }
    }

    /**
     * 指定されたカテゴリ名が有効かどうかを確認する
     *
     * @param categoryName カテゴリ名
     * @return 有効なカテゴリ名かどうか
     */
    public boolean isValidCategoryName(String categoryName) {
        if (categoryName == null || categoryName.trim().isEmpty()) {
            return false;
        }
        return CATEGORY_ALL.equals(categoryName) ||
                categoryList.stream().anyMatch(c -> c.getName().equals(categoryName));
    }

    /**
     * 空のコンボボックスモデルを作成する
     *
     * @return 空のコンボボックスモデル
     */
    public DefaultComboBoxModel<String> getEmptyComboBoxModel() {
        return new DefaultComboBoxModel<>(new String[]{CATEGORY_ALL});
    }

    /**
     * 子IDがチェックボックスのカテゴリなのかを確認する
     *
     * @param searchList 親カテゴリのリスト
     * @param name       選択したカテゴリ名
     * @return チェックボックスなのか
     */
    public boolean childHasCheckBox(List<CategoryModel> searchList, String name) {
        String id = searchList.stream().filter(n -> n.getName().equals(name)).findFirst().orElse(new CategoryModel()).getId();
        if (StringUtils.isEmpty(id)) {
            return false;
        }
        long childCnt = categoryList.stream()
                .filter(n -> n.getParentId().equals(id))
                .count();

        int parentIdx = categoryList.indexOf(categoryList.stream()
                .filter(n -> n.getId().equals(id))
                .findFirst().orElse(null));
        // 子IDを持っていなければFALSEを返す
        if (!categoryList.stream()
                .filter(n -> n.getId().equals(id))
                .findFirst()
                .orElse(new CategoryModel())
                .isHasChild()) {

            return false;
        }

        // 個IDが連続して出ててこない場合もFALSEを返す
        for (int i = 0; i < childCnt; i++) {
            if (!StringUtils.equals(id, categoryList.get(i + parentIdx + 1).getParentId())) {
                return false;
            }
        }
        return true;
    }

    /**
     * カテゴリーIDからルートまでのカテゴリーパスを取得する
     *
     * @param categoryId 検索するカテゴリーID
     * @return ルートから指定されたIDまでのカテゴリーモデルのリスト。見つからない場合は空のリスト。
     */
    public List<CategoryModel> findCategoryPathById(String categoryId) {
        if (categoryId == null || categoryId.trim().isEmpty()) {
            return Collections.emptyList();
        }

        List<CategoryModel> path = new ArrayList<>();
        CategoryModel current = categoryList.stream()
                .filter(c -> categoryId.equals(c.getId()))
                .findFirst()
                .orElse(null);

        while (current != null) {
            path.add(current);
            String parentId = current.getParentId();
            if ("0".equals(parentId)) {
                break;
            }
            String finalParentId = parentId;
            current = categoryList.stream()
                    .filter(c -> finalParentId.equals(c.getId()))
                    .findFirst()
                    .orElse(null);
        }

        Collections.reverse(path);
        return path;
    }

}