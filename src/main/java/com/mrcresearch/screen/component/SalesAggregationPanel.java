package com.mrcresearch.screen.component;

import com.mrcresearch.model.ProgressItem;
import com.mrcresearch.screen.component.pagination.PaginationComponent;
import com.mrcresearch.screen.component.pagination.PaginationConfig;
import com.mrcresearch.screen.component.pagination.PaginationListener;
import com.mrcresearch.screen.favorite.FavoriteSeller;
import com.mrcresearch.screen.model.FavoriteSellerModel;
import com.mrcresearch.screen.model.SalesAggregationFilterModel;
import com.mrcresearch.screen.model.SalesAggregationModel;
import com.mrcresearch.service.SalesAggregationService;
import com.mrcresearch.service.SellerManagementService;
import com.mrcresearch.service.analyze.service.SearchBySeller;
import com.mrcresearch.service.common.FontSettings;
import com.mrcresearch.service.enums.ItemTypeEnum;
import com.mrcresearch.util.*;
import com.mrcresearch.util.database.SettingsRepository;
import com.mrcresearch.util.ui.UIStyles;
import net.miginfocom.swing.MigLayout;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import javax.swing.Timer;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableRowSorter;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;

/**
 * 売り切れアイテムの集計結果を表示するパネルです。
 * 全体的なレイアウト、データ取得、テーブル表示を管理し、
 * フィルタリングUIとロジックは専用のコンポーネントに委譲します。
 */
public class SalesAggregationPanel extends JPanel {

    private static final Logger logger = LoggerFactory.getLogger(SalesAggregationPanel.class);

    private static final int SELLER_ID_COL = 0;
    private static final int IMAGE_COL = 1;
    private static final int ITEM_NAME_COL = 2;
    private static final int CATEGORY_NAME_COL = 3;
    private static final int PRICE_COL = 4;
    private static final int TOTAL_SALES_COL = 5;
    private static final int DAILY_SALES_START_COL = 6;

    private static final int AUTO_REFRESH_DELAY_MS = 15000;
    private static final String RESEARCHING_STATUS = "リサーチ中";
    private static final String NO_DATA_MESSAGE = "データなし";
    private static final String CURRENCY_SYMBOL = "¥";
    private static final DateTimeFormatter DISPLAY_DATE_FORMATTER = DateTimeFormatter.ofPattern("M/d");
    private static final DateTimeFormatter DATA_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static final String COLUMN_HEADER_SELLER_ID = "セラーID";
    private static final String COLUMN_HEADER_IMAGE = "画像";
    private static final String COLUMN_HEADER_ITEM_NAME = "商品名";
    private static final String COLUMN_HEADER_CATEGORY_NAME = "カテゴリー名";
    private static final String COLUMN_HEADER_PRICE = "価格";
    private static final String COLUMN_HEADER_TOTAL_SALES = "総件数";

    // --- UIコンポーネント ---
    private JTable aggregationTable;
    private DefaultTableModel tableModel;
    private JScrollPane scrollPane;
    private JLabel titleLabel;
    private JLabel statusLabel;
    private JButton refreshButton;
    private SalesAggregationFilterPanel filterPanel;
    private PaginationComponent paginationComponent;
    private Timer autoRefreshTimer;

    // --- サービスとデータ ---
    private final SalesAggregationService aggregationService;
    private List<SalesAggregationModel> currentAggregationData;
    private final SalesAggregationFilterModel currentFilter = new SalesAggregationFilterModel();
    private String[] currentColumnNames;

    // --- レンダラー ---
    private final MultiLineSellerCellRenderer sellerCellRenderer;

    /**
     * SalesAggregationPanelの新しいインスタンスを構築します。
     * 必要なコンポーネントを初期化し、レイアウトを設定し、
     * 集計データをロードして、自動更新タイマーを開始します。
     */
    public SalesAggregationPanel() {
        this.aggregationService = new SalesAggregationService();
        this.sellerCellRenderer = new MultiLineSellerCellRenderer();
        initializeComponents();
        setupLayout();
        loadAggregationData();
        startAutoRefreshTimer();
    }

    /**
     * パネルのUIコンポーネントを初期化します。
     * テーブルモデル、テーブル、ページネーションコンポーネント、ボタン、ステータスラベル、
     * フィルタパネルなどを設定します。
     */
    private void initializeComponents() {
        currentColumnNames = createBaseColumnNames();
        tableModel = createTableModel();
        aggregationTable = createAggregationTable();
        paginationComponent = createPaginationComponent();

        refreshButton = new JButton("データ更新");
        refreshButton.addActionListener(e -> loadAggregationData());

        statusLabel = new JLabel("集計データを読み込み中...");
        statusLabel.setFont(FontSettings.getSmallestTextFont());

        filterPanel = new SalesAggregationFilterPanel(this::performSearch, this::clearAllFilters);

        // 設定に基づいてデフォルトの日付範囲を初期化
        initializeDefaultDateRange();
    }

    /**
     * テーブルモデルを作成します。
     * このモデルは、テーブルのデータと列の識別子を管理します。
     *
     * @return 新しく作成されたDefaultTableModel
     */
    private DefaultTableModel createTableModel() {
        return new DefaultTableModel(currentColumnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // すべてのセルを編集不可にする
            }

            @Override
            public Class<?> getColumnClass(int column) {
                if (column == IMAGE_COL) {
                    return ImageIcon.class;
                }
                return Object.class;
            }
        };
    }

    /**
     * デフォルトの日付範囲を初期化します。
     * 設定リポジトリからデフォルトの日数を取得し、それに基づいて開始日と終了日を設定します。
     */
    private void initializeDefaultDateRange() {
        int defaultDays = SettingsRepository.getSalesAggregationDefaultDays();
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(defaultDays - 1);

        // Set default date range in the current filter
        currentFilter.setStartDate(startDate);
        currentFilter.setEndDate(endDate);
    }

    /**
     * パネルのレイアウトを設定します。
     * MigLayoutを使用して、タイトルラベル、フィルタパネル、コントロールパネル、
     * スクロール可能なテーブル、ページネーションコンポーネントを配置します。
     */
    private void setupLayout() {
        setLayout(new MigLayout("fill, insets 10", "[grow]", "[][][][grow,fill][pref!]"));

        int defaultDays = SettingsRepository.getSalesAggregationDefaultDays();
        titleLabel = new JLabel("過去" + defaultDays + "日間の集計結果");
        UIStyles.applyTitleStyle(titleLabel);
        UIStyles.setAccessibleName(titleLabel, "売上集計タイトル", "売上集計結果の期間を表示");
        add(titleLabel, "wrap");

        add(filterPanel, "growx, wrap");

        JPanel controlPanel = new JPanel(new MigLayout("insets 0", "[]push[]", ""));
        controlPanel.add(statusLabel);
        controlPanel.add(refreshButton);
        add(controlPanel, "growx, wrap");

        scrollPane = new JScrollPane(aggregationTable);
        add(scrollPane, "grow, wrap");

        paginationComponent.setScrollPane(scrollPane);
        add(paginationComponent, "growx, height pref!");
    }

    /**
     * 売り切れ・取引中アイテムの集計データを非同期で読み込み、テーブルに表示します。
     * データの読み込み中はローディング状態を設定し、完了後またはエラー発生後に状態を更新します。
     */
    public void loadAggregationData() {
        setLoadingState(true);

        SwingWorker<Object[], Void> worker = new SwingWorker<>() {
            @Override
            protected Object[] doInBackground() throws Exception {
                int currentPage = paginationComponent.getCurrentPage();
                int pageSize = paginationComponent.getPageSize();

                int count = aggregationService.getSalesAggregationDataCount(currentFilter);
                List<SalesAggregationModel> data = aggregationService.getSalesAggregationData(currentPage, pageSize, currentFilter);

                return new Object[]{data, count};
            }

            @Override
            protected void done() {
                try {
                    Object[] result = get();
                    @SuppressWarnings("unchecked")
                    List<SalesAggregationModel> results = (List<SalesAggregationModel>) result[0];
                    int totalCount = (Integer) result[1];

                    paginationComponent.updatePagination(totalCount, paginationComponent.getCurrentPage());
                    updateTable(results);
                    statusLabel.setText("集計データ読み込み完了");
                } catch (Exception e) {
                    logger.error("Failed to load aggregation data", e);
                    statusLabel.setText("データの読み込みに失敗しました: " + e.getMessage());
                    JOptionPane.showMessageDialog(
                            SalesAggregationPanel.this,
                            "集計データの読み込みに失敗しました。\n" + e.getMessage(),
                            "エラー",
                            JOptionPane.ERROR_MESSAGE
                    );
                } finally {
                    setLoadingState(false);
                }
            }
        };
        worker.execute();
    }

    /**
     * テーブルのデータを更新します。
     * 既存の行をクリアし、新しい集計データでテーブルを再構築します。
     * 列のヘッダーも日付キーと集計サマリーに基づいて更新されます。
     *
     * @param results テーブルに表示するSalesAggregationModelのリスト
     */
    private void updateTable(List<SalesAggregationModel> results) {
        this.currentAggregationData = results;
        tableModel.setRowCount(0);

        List<String> dateKeys = getDateKeysForFilter();
        updateTableColumns(dateKeys);
        updateColumnHeadersWithSummary(results, dateKeys);

        if (results.isEmpty()) {
            tableModel.addRow(new Object[]{NO_DATA_MESSAGE});
        } else {
            populateTableWithData(results, dateKeys);
        }

        aggregationTable.revalidate();
        aggregationTable.repaint();

        // リサーチ中のアイテムがあるかどうかに基づいて自動更新タイマーを更新
        updateAutoRefreshTimer();
    }

    /**
     * フィルタパネルから新しいフィルタ条件が適用されたときに検索を実行します。
     * 現在のフィルタを更新し、ページネーションをリセットして、集計データを再ロードします。
     *
     * @param newFilter 新しいSalesAggregationFilterModel
     */
    private void performSearch(SalesAggregationFilterModel newFilter) {
        currentFilter.apply(newFilter);
        paginationComponent.updatePagination(0, 1); // Reset to first page
        updateTitleLabel();
        loadAggregationData();
    }

    /**
     * すべてのフィルタ条件をクリアします。
     * ユーザーに確認ダイアログを表示し、承認された場合、フィルタパネルと現在のフィルタをリセットし、
     * デフォルトの日付範囲を再適用して、集計データを再ロードします。
     */
    private void clearAllFilters() {
        int result = JOptionPane.showConfirmDialog(this,
                "すべてのフィルタ条件をクリアしますか？", "フィルタクリア確認",
                JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);

        if (result == JOptionPane.YES_OPTION) {
            filterPanel.clearFilter();
            currentFilter.clearFilters();
            // Re-apply default date range after clearing filters
            initializeDefaultDateRange();
            paginationComponent.updatePagination(0, 1);
            updateTitleLabel();
            loadAggregationData();
        }
    }

    /**
     * 指定されたセラーIDで集計データをフィルタリングして表示します。
     *
     * @param sellerId フィルタリングするセラーのID
     */
    public void filterBySellerId(String sellerId) {
        if (sellerId == null || sellerId.trim().isEmpty()) return;
        filterPanel.filterBySellerId(sellerId);
    }

    /**
     * 指定されたキーワード検索IDで集計データをフィルタリングして表示します。
     *
     * @param keywordSearchId フィルタリングするキーワード検索のID
     */
    public void filterByKeywordSearchId(int keywordSearchId) {
        if (keywordSearchId <= 0) return;
        filterPanel.filterByKeywordSearchId(String.valueOf(keywordSearchId));
    }

    /**
     * テーマが変更されたときにテーブルの表示を更新します。
     * ページネーションコンポーネントと集計テーブルの再検証と再描画をスケジュールします。
     */
    public void applyThemeChange() {
        SwingUtilities.invokeLater(() -> {
            if (paginationComponent != null) {
                paginationComponent.applyThemeChange();
            }
            if (aggregationTable != null) {
                aggregationTable.revalidate();
                aggregationTable.repaint();
            }
        });
    }


    /**
     * 集計データを表示するためのJTableを作成します。
     * テーブルの選択モード、ソーター、グリッド表示、行の高さなどを設定し、
     * 列、レンダラー、コンテキストメニュー、ダブルクリックイベントを構成します。
     *
     * @return 新しく作成されたJTable
     */
    private JTable createAggregationTable() {
        JTable table = new JTable(tableModel);
        table.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        table.setAutoCreateRowSorter(true);
        table.setShowGrid(true);
        table.setRowHeight(120);
        table.getTableHeader().setToolTipText("表示されているデータをソートします");

        configureTableSorters(table);
        configureTableColumns(table);
        configureTableRenderers(table);
        configureTableContextMenu(table);
        addDoubleClickShowDetail(table);

        return table;
    }

    /**
     * ページネーションコンポーネントを作成し、設定します。
     * ページ変更およびページサイズ変更イベントのリスナーを設定し、
     * データロードとスクロールトップの動作をトリガーします。
     *
     * @return 新しく作成されたPaginationComponent
     */
    private PaginationComponent createPaginationComponent() {
        PaginationComponent component = new PaginationComponent(PaginationConfig.createForAggregation());

        // 修正: PaginationListenerインターフェースを実装する匿名クラスを1つ渡す
        component.setPaginationListener(new PaginationListener() {
            @Override
            public void onPageChanged(int newPage, int pageSize) {
                loadAggregationData();
                scrollToTop();
            }

            @Override
            public void onPageSizeChanged(int newPageSize, int currentPage) {
                loadAggregationData();
                scrollToTop();
            }
        });

        return component;
    }

    /**
     * 指定されたテーブルの列の幅を設定します。
     * 各列の推奨幅を定義し、日次売上列の幅を調整します。
     *
     * @param table 設定するJTable
     */
    private void configureTableColumns(JTable table) {
        table.getColumnModel().getColumn(SELLER_ID_COL).setPreferredWidth(80);
        table.getColumnModel().getColumn(IMAGE_COL).setPreferredWidth(80);
        table.getColumnModel().getColumn(ITEM_NAME_COL).setPreferredWidth(200);
        table.getColumnModel().getColumn(CATEGORY_NAME_COL).setPreferredWidth(100);
        table.getColumnModel().getColumn(PRICE_COL).setPreferredWidth(60);
        table.getColumnModel().getColumn(TOTAL_SALES_COL).setPreferredWidth(60);

        for (int i = DAILY_SALES_START_COL; i < table.getColumnCount(); i++) {
            table.getColumnModel().getColumn(i).setPreferredWidth(50);
        }
    }

    /**
     * 指定されたテーブルのセルレンダラーを設定します。
     * セラーID、画像、商品名、カテゴリー名の列にカスタムレンダラーを適用し、
     * すべての列のヘッダーにMultiLineHeaderRendererを設定します。
     *
     * @param table 設定するJTable
     */
    private void configureTableRenderers(JTable table) {
        if (table.getColumnCount() == 0) return;
        table.getColumnModel().getColumn(SELLER_ID_COL).setCellRenderer(sellerCellRenderer);
        table.getColumnModel().getColumn(IMAGE_COL).setCellRenderer(new ImageTableCellRenderer());
        table.getColumnModel().getColumn(ITEM_NAME_COL).setCellRenderer(TableTextWrapRenderer.createLeftAligned());
        table.getColumnModel().getColumn(CATEGORY_NAME_COL).setCellRenderer(TableTextWrapRenderer.createLeftAligned());

        for (int i = 0; i < table.getColumnCount(); i++) {
            table.getColumnModel().getColumn(i).setHeaderRenderer(new MultiLineHeaderRenderer());
        }
    }

    /**
     * 指定されたテーブルの行ソーターを設定します。
     * 数値、価格、日次売上列にカスタムコンパレータを適用し、
     * 総件数列を降順でソートするように設定します。
     *
     * @param table 設定するJTable
     */
    private void configureTableSorters(JTable table) {
        TableRowSorter<DefaultTableModel> sorter = (TableRowSorter<DefaultTableModel>) table.getRowSorter();
        if (sorter == null) return;

        // 数値比較用コンパレータ
        Comparator<Object> numericComparator = (o1, o2) -> {
            try {
                Integer i1 = (o1 instanceof Integer) ? (Integer) o1 : Integer.parseInt(o1.toString());
                Integer i2 = (o2 instanceof Integer) ? (Integer) o2 : Integer.parseInt(o2.toString());
                return i1.compareTo(i2);
            } catch (NumberFormatException e) {
                return o1.toString().compareTo(o2.toString());
            }
        };

        // 価格比較用コンパレータ (通貨記号とカンマを除去)
        Comparator<Object> priceComparator = (o1, o2) -> {
            try {
                String s1 = o1.toString().replaceAll("[¥,]", "");
                String s2 = o2.toString().replaceAll("[¥,]", "");
                return Integer.compare(Integer.parseInt(s1), Integer.parseInt(s2));
            } catch (NumberFormatException e) {
                return o1.toString().compareTo(o2.toString());
            }
        };

        // 日次売上比較用コンパレータ ('-' を 0 として扱う)
        Comparator<Object> dailySalesComparator = (o1, o2) -> {
            try {
                Integer i1 = "-".equals(o1.toString()) ? 0 : Integer.parseInt(o1.toString());
                Integer i2 = "-".equals(o2.toString()) ? 0 : Integer.parseInt(o2.toString());
                return i1.compareTo(i2);
            } catch (NumberFormatException e) {
                return o1.toString().compareTo(o2.toString());
            }
        };

        sorter.setComparator(TOTAL_SALES_COL, numericComparator);
        sorter.setComparator(PRICE_COL, priceComparator);
        for (int i = DAILY_SALES_START_COL; i < table.getColumnCount(); i++) {
            sorter.setComparator(i, dailySalesComparator);
        }

        sorter.setSortKeys(List.of(new RowSorter.SortKey(TOTAL_SALES_COL, SortOrder.DESCENDING)));
    }

    /**
     * 指定されたテーブルにコンテキストメニューを設定します。
     * TableBrowserContextMenuUtilを使用して、セラーオプションを含むブラウザコンテキストメニューを有効にします。
     *
     * @param table コンテキストメニューを設定するJTable
     */
    private void configureTableContextMenu(JTable table) {
        TableBrowserContextMenuUtil.enableBrowserContextMenuWithSellerOptions(
                table,
                -1,                        // itemIdColumnIndex (not displayed)
                ITEM_NAME_COL,             // productNameColumnIndex
                SELLER_ID_COL,             // sellerIdColumnIndex
                this::getItemTypeForRow,
                this::getSellerNameFromRow,
                this::addToFavorites,
                this::startSellerResearch,
                this::getItemIdForRow
        );
    }

    /**
     * テーブルの行にダブルクリックイベントリスナーを追加し、日次売上詳細ダイアログを表示します。
     *
     * @param table イベントリスナーを追加するJTable
     */
    private void addDoubleClickShowDetail(JTable table) {
        table.addMouseListener(new MouseAdapter() {
            /**
             * テーブル行のダブルクリックイベントを処理し、日次売上詳細ダイアログを表示します。
             */
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) {
                    int viewRow = table.getSelectedRow();
                    if (viewRow >= 0) {
                        int modelRow = table.convertRowIndexToModel(viewRow);
                        String itemName = (String) table.getModel().getValueAt(modelRow, ITEM_NAME_COL);
                        String sellerId = (String) table.getModel().getValueAt(modelRow, SELLER_ID_COL);

                        if (itemName != null && !itemName.isEmpty() && sellerId != null && !sellerId.isEmpty()) {
                            Frame owner = (Frame) SwingUtilities.getWindowAncestor(SalesAggregationPanel.this);
                            String thumbnailPath = (currentAggregationData != null && modelRow < currentAggregationData.size()) ? currentAggregationData.get(modelRow).getThumbnailPath() : null;
                            int totalSalesCount = (currentAggregationData != null && modelRow < currentAggregationData.size()) ? currentAggregationData.get(modelRow).getTotalSalesCount() : 0;
                            double totalSalesPrice = (currentAggregationData != null && modelRow < currentAggregationData.size()) ? currentAggregationData.get(modelRow).getPrice() * currentAggregationData.get(modelRow).getTotalSalesCount() : 0.0;
                            DailySalesDetailDialog dialog = new DailySalesDetailDialog(owner, itemName, sellerId, thumbnailPath, totalSalesCount, totalSalesPrice);
                            dialog.setVisible(true);
                        }
                    }
                }
            }
        });
    }

    /**
     * UIのローディング状態を設定します。
     * ステータスラベルのテキストを更新し、リフレッシュボタンとフィルタパネルのフィールドの有効/無効を切り替えます。
     *
     * @param isLoading ローディング状態である場合はtrue、そうでない場合はfalse
     */
    private void setLoadingState(boolean isLoading) {
        SwingUtilities.invokeLater(() -> {
            statusLabel.setText(isLoading ? "集計データを読み込み中..." : " ");
            refreshButton.setEnabled(!isLoading);
            filterPanel.setFieldsEnabled(!isLoading);
        });
    }

    /**
     * 指定された集計データと日付キーでテーブルにデータを入力します。
     * セラーレンダラーの情報をクリアし、各モデルのデータをテーブルの行に追加します。
     *
     * @param results  テーブルに入力するSalesAggregationModelのリスト
     * @param dateKeys 日次売上データの日付キーのリスト
     */
    private void populateTableWithData(List<SalesAggregationModel> results, List<String> dateKeys) {
        sellerCellRenderer.clearSellerInfo();
        results.forEach(model -> sellerCellRenderer.setSellerInfo(model.getSellerId(), model.getSellerName(), model.getLastUpdatedAt(), model.getResearchStatus()));

        for (SalesAggregationModel model : results) {
            Object[] rowData = new Object[currentColumnNames.length];
            rowData[SELLER_ID_COL] = model.getSellerId();
            rowData[IMAGE_COL] = ImageUtil.loadThumbnailImage(model.getThumbnailPath());
            rowData[ITEM_NAME_COL] = model.getItemName();
            rowData[CATEGORY_NAME_COL] = model.getCategoryName();
            rowData[PRICE_COL] = CURRENCY_SYMBOL + formatNumber(model.getPrice());
            rowData[TOTAL_SALES_COL] = model.getTotalSalesCount();

            Map<String, Integer> dailySales = model.getDailySalesCount();
            for (int i = 0; i < dateKeys.size() && (DAILY_SALES_START_COL + i) < rowData.length; i++) {
                String dateKey = dateKeys.get(i);
                Integer count = dailySales.get(dateKey);
                rowData[DAILY_SALES_START_COL + i] = (count != null && count > 0) ? count : "-";
            }
            tableModel.addRow(rowData);
        }
    }

    /**
     * 指定された日付キーに基づいてテーブルの列を更新します。
     * 基本列名に日次売上列を追加し、テーブルモデルの列識別子を更新します。
     * その後、テーブルの列、レンダラー、ソーターを再構成します。
     *
     * @param dateKeys 日次売上データの日付キーのリスト
     */
    private void updateTableColumns(List<String> dateKeys) {
        List<String> columnNames = new ArrayList<>(List.of(createBaseColumnNames()));
        LocalDate endDate = currentFilter.getEndDate() != null ? currentFilter.getEndDate() : LocalDate.now();
        int defaultDays = SettingsRepository.getSalesAggregationDefaultDays();
        LocalDate startDate = currentFilter.getStartDate() != null ? currentFilter.getStartDate() : endDate.minusDays(defaultDays - 1);

        for (LocalDate date = endDate; !date.isBefore(startDate); date = date.minusDays(1)) {
            columnNames.add(date.format(DISPLAY_DATE_FORMATTER));
        }

        currentColumnNames = columnNames.toArray(new String[0]);
        tableModel.setColumnIdentifiers(currentColumnNames);
        SwingUtilities.invokeLater(() -> {
            configureTableColumns(aggregationTable);
            configureTableRenderers(aggregationTable);
            configureTableSorters(aggregationTable);
        });
    }

    /**
     * 集計結果と日付キーに基づいてテーブルの列ヘッダーをサマリー情報で更新します。
     * ユニークなセラー数、平均価格、総売上数、日次合計売上数をヘッダーに表示します。
     *
     * @param results  集計結果のSalesAggregationModelのリスト
     * @param dateKeys 日次売上データの日付キーのリスト
     */
    private void updateColumnHeadersWithSummary(List<SalesAggregationModel> results, List<String> dateKeys) {
        if (results.isEmpty()) {
            tableModel.setColumnIdentifiers(createBaseColumnNames()); // Reset to basic headers
            return;
        }

        Set<String> uniqueSellers = new HashSet<>();
        double totalPrice = 0;
        int totalSalesCount = 0;
        int[] dailyTotals = new int[dateKeys.size()];

        for (SalesAggregationModel model : results) {
            uniqueSellers.add(model.getSellerId());
            totalPrice += model.getPrice();
            totalSalesCount += model.getTotalSalesCount();
            Map<String, Integer> dailySales = model.getDailySalesCount();
            for (int i = 0; i < dateKeys.size(); i++) {
                dailyTotals[i] += dailySales.getOrDefault(dateKeys.get(i), 0);
            }
        }

        double avgPrice = results.size() > 0 ? totalPrice / results.size() : 0;
        String[] updatedHeaders = currentColumnNames.clone();
        updatedHeaders[SELLER_ID_COL] = "<html>セラーID<br>(" + uniqueSellers.size() + "人)</html>";
        // HTMLコンテキストでのString.formatの問題を避けるためのStackOverflowErrorの修正
        updatedHeaders[PRICE_COL] = "<html>価格<br>平均：" + CURRENCY_SYMBOL + formatNumber((int) avgPrice) + "</html>";
        updatedHeaders[TOTAL_SALES_COL] = "<html>総件数<br>(" + totalSalesCount + "件)</html>";

        for (int i = 0; i < dailyTotals.length && (DAILY_SALES_START_COL + i) < updatedHeaders.length; i++) {
            updatedHeaders[DAILY_SALES_START_COL + i] = "<html>" + currentColumnNames[DAILY_SALES_START_COL + i] + "<br>(" + dailyTotals[i] + "件)</html>";
        }

        for (int i = 0; i < aggregationTable.getColumnCount(); i++) {
            aggregationTable.getColumnModel().getColumn(i).setHeaderValue(updatedHeaders[i]);
        }
        aggregationTable.getTableHeader().setPreferredSize(new Dimension(aggregationTable.getTableHeader().getWidth(), 40));
        aggregationTable.getTableHeader().repaint();
    }

    /**
     * タイトルラベルのテキストを更新します。
     * 現在のフィルタの日付範囲に基づいて、表示される期間（例：「過去X日間の集計結果」）を更新します。
     */
    private void updateTitleLabel() {
        int defaultDays = SettingsRepository.getSalesAggregationDefaultDays();
        LocalDate startDate = currentFilter.getStartDate() != null ? currentFilter.getStartDate() : LocalDate.now().minusDays(defaultDays - 1);
        LocalDate endDate = currentFilter.getEndDate() != null ? currentFilter.getEndDate() : LocalDate.now();
        long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate) + 1;
        titleLabel.setText("過去" + daysBetween + "日間の集計結果");
    }

    /**
     * 現在のフィルタ設定に基づいて、日次売上データの日付キーのリストを生成します。
     * 日付は「yyyy-MM-dd」形式でフォーマットされ、最新の日付から過去に遡ってリストに追加されます。
     *
     * @return 日付キーの文字列のリスト
     */
    private List<String> getDateKeysForFilter() {
        LocalDate endDate = currentFilter.getEndDate() != null ? currentFilter.getEndDate() : LocalDate.now();
        int defaultDays = SettingsRepository.getSalesAggregationDefaultDays();
        LocalDate startDate = currentFilter.getStartDate() != null ? currentFilter.getStartDate() : endDate.minusDays(defaultDays - 1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        List<String> dateKeys = new ArrayList<>();
        for (LocalDate date = endDate; !date.isBefore(startDate); date = date.minusDays(1)) {
            dateKeys.add(date.format(formatter));
        }
        return dateKeys;
    }

    /**
     * テーブルの基本列名（セラーID、画像、商品名、カテゴリー名、価格、総件数）を定義します。
     *
     * @return 基本列名の文字列配列
     */
    private String[] createBaseColumnNames() {
        return new String[]{
                COLUMN_HEADER_SELLER_ID, COLUMN_HEADER_IMAGE, COLUMN_HEADER_ITEM_NAME, COLUMN_HEADER_CATEGORY_NAME, COLUMN_HEADER_PRICE, COLUMN_HEADER_TOTAL_SALES
        };
    }


    /**
     * スクロールペインの垂直スクロールバーを最上部に設定し、テーブルの表示を一番上に戻します。
     */
    private void scrollToTop() {
        if (scrollPane != null) {
            SwingUtilities.invokeLater(() -> scrollPane.getVerticalScrollBar().setValue(0));
        }
    }

    /**
     * 指定された行のアイテムIDを取得します。
     *
     * @param row アイテムIDを取得する行のインデックス
     * @return アイテムIDの文字列、または見つからない場合は空の文字列
     */
    private String getItemIdForRow(int row) {
        if (currentAggregationData != null && row >= 0 && row < currentAggregationData.size()) {
            SalesAggregationModel model = currentAggregationData.get(row);
            return model.getItemId() != null ? model.getItemId() : "";
        }
        return "";
    }

    /**
     * 指定された行のアイテムタイプを取得します。
     * アイテムIDに基づいてItemTypeEnumを返します。
     *
     * @param row アイテムタイプを取得する行のインデックス
     * @return ItemTypeEnum、またはアイテムIDが見つからない場合はNORMAL
     */
    private ItemTypeEnum getItemTypeForRow(int row) {
        String itemId = getItemIdForRow(row);
        return !itemId.isEmpty() ? ItemTypeEnum.fromItemId(itemId) : ItemTypeEnum.NORMAL;
    }

    /**
     * 指定された行のセラー名を取得します。
     * セラーID列からセラーIDを取得し、SellerManagementServiceを使用して最適なセラー名を取得します。
     *
     * @param row セラー名を取得する行のインデックス
     * @return セラー名の文字列、または見つからない場合はnull
     */
    private String getSellerNameFromRow(int row) {
        if (row < 0 || row >= aggregationTable.getRowCount()) return null;
        String sellerId = (String) aggregationTable.getValueAt(row, SELLER_ID_COL);
        if (sellerId != null) {
            return SellerManagementService.getBestSellerName(sellerId);
        }
        return null;
    }


    /**
     * 指定されたセラーIDとセラー名でセラーリサーチを開始します。
     * ユーザーに確認ダイアログを表示し、承認された場合、リサーチタスクをキューに追加し、
     * テーブル内のセラーのステータスを「リサーチ中」に更新します。
     *
     * @param sellerId   リサーチするセラーのID
     * @param sellerName リサーチするセラーの名前（表示用）
     */
    private void startSellerResearch(String sellerId, String sellerName) {
        if (sellerId == null || sellerId.trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "セラーIDが取得できませんでした。", "エラー", JOptionPane.ERROR_MESSAGE);
            return;
        }
        String displayName = (sellerName != null && !sellerName.trim().isEmpty()) ? sellerName : sellerId;
        int result = JOptionPane.showConfirmDialog(this,
                "セラーリサーチを開始しますか？\n\nセラーID: " + sellerId + "\nセラー名: " + displayName,
                "セラーリサーチ確認", JOptionPane.YES_NO_OPTION);

        if (result != JOptionPane.YES_OPTION) return;

        try {
            SearchQueueManager queueManager = SearchQueueManager.getInstance();
            String progressId = "seller_research_" + System.currentTimeMillis();
            ProgressItem progressItem = new ProgressItem(progressId, "セラーリサーチ", displayName, 1, 1);
            SearchBySeller searchService = new SearchBySeller();

            // タスクがキューに入れられたらすぐにステータスを「リサーチ中」に設定
            SellerManagementService.updateSellerResearchStatus(sellerId, RESEARCHING_STATUS);

            // 「リサーチ中」ステータスを表示するためにテーブルをすぐに更新
            SwingUtilities.invokeLater(() -> {
                updateSellerStatusInTable(sellerId, RESEARCHING_STATUS);
                loadAggregationData(); // 一貫性を確保するために完全に更新
            });

            queueManager.addTask(new SearchQueueManager.SearchTask() {
                @Override
                public String getTaskName() {
                    return "セラーリサーチ: " + displayName;
                }

                @Override
                public String getTaskType() {
                    return "SELLER_SEARCH";
                }

                @Override
                public void execute() throws Exception {
                    queueManager.addProgressItem(progressItem);
                    try {
                        progressItem.setStatus(ProgressItem.Status.PROCESSING);
                        searchService.execute(sellerId, progressId);
                        progressItem.setStatus(ProgressItem.Status.COMPLETED);

                        // リサーチ完了後にテーブルを更新
                        SwingUtilities.invokeLater(() -> {
                            loadAggregationData();
                            logger.info("セラーリサーチ完了後にテーブルを更新しました: " + sellerId);
                        });
                    } catch (Exception e) {
                        progressItem.setStatus(ProgressItem.Status.ERROR);
                        progressItem.setErrorMessage(e.getMessage());

                        // エラー時でもエラー状態を表示するためにテーブルを更新
                        SwingUtilities.invokeLater(() -> {
                            loadAggregationData();
                            logger.info("セラーリサーチエラー後にテーブルを更新しました: " + sellerId);
                        });
                        throw e;
                    }
                }
            });
            JOptionPane.showMessageDialog(this, "セラーリサーチをキューに追加しました。", "リサーチ開始", JOptionPane.INFORMATION_MESSAGE);
        } catch (Exception e) {
            logger.error("Failed to start seller research", e);
            JOptionPane.showMessageDialog(this, "セラーリサーチの開始に失敗しました。\nエラー: " + e.getMessage(), "エラー", JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * 指定されたセラーをお気に入りに追加します。
     * セラーIDが有効な場合、お気に入り追加ダイアログを表示します。
     *
     * @param sellerId   お気に入りに追加するセラーのID
     * @param sellerName お気に入りに追加するセラーの名前
     */
    private void addToFavorites(String sellerId, String sellerName) {
        if (sellerId == null || sellerId.trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "セラーIDが取得できませんでした。", "エラー", JOptionPane.ERROR_MESSAGE);
            return;
        }
        showAddToFavoritesDialog(sellerId, sellerName);
    }

    /**
     * お気に入り追加ダイアログを表示します。
     * セラー名、セラーID、グループ、タグの入力フィールドを提供し、
     * お気に入りへの追加またはキャンセルを可能にします。
     *
     * @param sellerId   お気に入りに追加するセラーのID
     * @param sellerName お気に入りに追加するセラーの名前
     */
    private void showAddToFavoritesDialog(String sellerId, String sellerName) {
        JDialog dialog = new JDialog((Frame) SwingUtilities.getWindowAncestor(this), "お気に入り登録", true);
        dialog.setLayout(new MigLayout("fillx,insets 20", "[right][grow]", "[][][][]"));

        String displayName = (sellerName != null && !sellerName.trim().isEmpty()) ? sellerName : sellerId;
        dialog.add(new JLabel("セラー名:"));
        dialog.add(new JLabel(displayName), "wrap");
        dialog.add(new JLabel("セラーID:"));
        dialog.add(new JLabel(sellerId), "wrap");

        dialog.add(new JLabel("グループ:"));
        JComboBox<String> groupCombo = new JComboBox<>();
        groupCombo.setEditable(true);
        FavoriteSeller.getAllGroups().forEach(groupCombo::addItem);
        if (groupCombo.getItemCount() == 0) groupCombo.addItem("デフォルト");
        dialog.add(groupCombo, "growx, wrap");

        dialog.add(new JLabel("タグ:"));
        JTextField tagsField = new JTextField();
        dialog.add(tagsField, "growx, wrap");

        JButton addButton = new JButton("追加");
        addButton.addActionListener(e -> {
            String groupName = groupCombo.getSelectedItem().toString().trim();
            if (groupName.isEmpty()) {
                JOptionPane.showMessageDialog(dialog, "グループ名を入力してください。", "エラー", JOptionPane.ERROR_MESSAGE);
                return;
            }
            FavoriteSellerModel model = new FavoriteSellerModel(displayName, sellerId, new Date(), "完了", tagsField.getText().trim(), groupName);
            if (FavoriteSeller.addFavoriteSeller(model) > 0) {
                JOptionPane.showMessageDialog(dialog, "お気に入りに追加しました。", "成功", JOptionPane.INFORMATION_MESSAGE);
                dialog.dispose();
            } else {
                JOptionPane.showMessageDialog(dialog, "お気に入りの追加に失敗しました。", "エラー", JOptionPane.ERROR_MESSAGE);
            }
        });

        JButton cancelButton = new JButton("キャンセル");
        cancelButton.addActionListener(e -> dialog.dispose());

        JPanel buttonPanel = new JPanel(new MigLayout("insets 0", "push[][]"));
        buttonPanel.add(addButton);
        buttonPanel.add(cancelButton);
        dialog.add(buttonPanel, "span, growx");

        dialog.pack();
        dialog.setLocationRelativeTo(this);
        dialog.setVisible(true);
    }

    /**
     * HTMLコンテキストでのString.formatの問題を避けるため、数値をカンマ区切りでフォーマットします。
     *
     * @param number フォーマットする数値
     * @return カンマ区切りのフォーマットされた数値文字列
     */
    private String formatNumber(int number) {
        if (number == 0) return "0";

        String numStr = String.valueOf(Math.abs(number));
        StringBuilder result = new StringBuilder();

        int count = 0;
        for (int i = numStr.length() - 1; i >= 0; i--) {
            if (count > 0 && count % 3 == 0) {
                result.insert(0, ',');
            }
            result.insert(0, numStr.charAt(i));
            count++;
        }

        if (number < 0) {
            result.insert(0, '-');
        }

        return result.toString();
    }

    /**
     * リサーチが進行中の場合にテーブルを更新するための自動更新タイマーを開始します。
     * 15秒ごとに「リサーチ中」ステータスのアイテムがあるかを確認し、存在する場合はテーブルを自動更新します。
     */
    private void startAutoRefreshTimer() {
        // リサーチステータスを更新するために15秒ごとに自動更新
        autoRefreshTimer = new Timer(AUTO_REFRESH_DELAY_MS, e -> {
            // 「リサーチ中」ステータスのアイテムがある場合のみ更新
            if (hasResearchingItems()) {
                SwingUtilities.invokeLater(() -> {
                    loadAggregationData();
                    logger.info("進行中のリサーチのためテーブルを自動更新しました");
                });
            }
        });
        autoRefreshTimer.start();
    }

    /**
     * 現在の集計データに「リサーチ中」ステータスのアイテムがあるかどうかを確認します。
     *
     * @return 「リサーチ中」のアイテムがある場合はtrue、そうでない場合はfalse
     */
    private boolean hasResearchingItems() {
        if (currentAggregationData == null) return false;

        return currentAggregationData.stream()
                .anyMatch(model -> RESEARCHING_STATUS.equals(model.getResearchStatus()));
    }

    /**
     * 完全な更新なしで現在のテーブルデータ内のセラーのステータスを更新します。
     * 指定されたセラーIDのアイテムのリサーチステータスを更新し、テーブルを再描画します。
     *
     * @param sellerId  ステータスを更新するセラーのID
     * @param newStatus 設定する新しいリサーチステータス
     */
    private void updateSellerStatusInTable(String sellerId, String newStatus) {
        if (currentAggregationData != null) {
            for (SalesAggregationModel model : currentAggregationData) {
                if (sellerId.equals(model.getSellerId())) {
                    model.setResearchStatus(newStatus);
                }
            }
            // 新しいステータスでレンダラーを更新
            sellerCellRenderer.clearSellerInfo();
            currentAggregationData.forEach(model ->
                    sellerCellRenderer.setSellerInfo(model.getSellerId(), model.getSellerName(),
                            model.getLastUpdatedAt(), model.getResearchStatus()));
            aggregationTable.repaint();
        }
    }

    /**
     * 現在のリサーチステータスに基づいて自動更新タイマーを更新します。
     * 「リサーチ中」のアイテムがある場合はタイマーを開始し、ない場合は停止します。
     */
    private void updateAutoRefreshTimer() {
        if (autoRefreshTimer != null) {
            if (hasResearchingItems()) {
                if (!autoRefreshTimer.isRunning()) {
                    autoRefreshTimer.start();
                    logger.info("進行中のリサーチのため自動更新タイマーを開始しました");
                }
            } else {
                if (autoRefreshTimer.isRunning()) {
                    autoRefreshTimer.stop();
                    logger.info("自動更新タイマーを停止しました - 進行中のリサーチはありません");
                }
            }
        }
    }

    /**
     * 自動更新タイマーを停止します。
     */
    public void stopAutoRefresh() {
        if (autoRefreshTimer != null && autoRefreshTimer.isRunning()) {
            autoRefreshTimer.stop();
        }
    }
}
