package com.mrcresearch.screen.component;

import com.mrcresearch.service.SalesAggregationService;
import com.mrcresearch.service.common.FontSettings;
import com.mrcresearch.util.ImageUtil;
import net.miginfocom.swing.MigLayout;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Map;

public class DailySalesDetailDialog extends JDialog {

    private static final Logger logger = LoggerFactory.getLogger(DailySalesDetailDialog.class);
    private JTable salesTable;
    private DefaultTableModel tableModel;

    public DailySalesDetailDialog(Frame owner, String itemName, String sellerId, String thumbnailPath, int totalSalesCount, double totalSalesPrice) {
        super(owner, "売上詳細: " + itemName, true);
        init(itemName, sellerId, thumbnailPath, totalSalesCount, totalSalesPrice);
    }

    private void init(String itemName, String sellerId, String thumbnailPath, int totalSalesCount, double totalSalesPrice) {
        setLayout(new MigLayout("insets 0", "[grow]", "[200::200][grow][]"));
        setSize(1000, 600);
        setLocationRelativeTo(getOwner());

        // Header Panel for Item Name, Image, and Total Sales
        JPanel headerPanel = new JPanel(new MigLayout("insets 0", "[][]", "[]"));

        headerPanel.setBackground(UIManager.getColor("Panel.background"));

        // Item Image
        ImageIcon itemImage = ImageUtil.loadThumbnailImage(thumbnailPath);
        JLabel imageLabel = new JLabel(itemImage);
        imageLabel.setBorder(BorderFactory.createLineBorder(Color.LIGHT_GRAY));
        headerPanel.add(imageLabel, "cell 0 0");

        // Item Name
        StringBuilder sb = new StringBuilder();
        sb.append("<html>")
                .append("<b>商品名:</b> ").append(itemName).append("<br>")
                .append("<b>総売上数:</b> ").append(totalSalesCount).append("件<br>")
                .append("<b>総売上価格:</b> ¥").append(String.format("%,.0f", totalSalesPrice))
                .append("</html>");
        JLabel itemNameLabel = new JLabel(sb.toString());
        itemNameLabel.setFont(itemNameLabel.getFont().deriveFont(Font.BOLD, 14f));
        headerPanel.add(itemNameLabel, "cell 1 0, growx, wrap");

        add(headerPanel, "cell 0 0, growx, wrap");

        // Table Model
        tableModel = new DefaultTableModel(new String[]{"日付", "売上数"}, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        salesTable = new JTable(tableModel);
        salesTable.setFont(FontSettings.getTableFont());

        JScrollPane scrollPane = new JScrollPane(salesTable);
        add(scrollPane, "cell 0 1, growx, wrap");

        // Close Button
        JButton closeButton = new JButton("閉じる");
        closeButton.addActionListener(e -> setVisible(false));
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.add(closeButton);
        add(buttonPanel, "cell 0 2,growx");

        // Load data in a background thread
        loadSalesData(itemName, sellerId);
    }

    private void loadSalesData(String itemName, String sellerId) {
        SwingWorker<Map<LocalDate, Integer>, Void> worker = new SwingWorker<>() {
            @Override
            protected Map<LocalDate, Integer> doInBackground() throws Exception {
                SalesAggregationService service = new SalesAggregationService();
                Map<LocalDate, Integer> salesData = service.getDailySalesForProduct(itemName, sellerId, 30);
                logger.debug("[DailySalesDialog] サービスからデータを取得しました: " + salesData.size() + " 件のレコード。");
                return salesData;
            }

            @Override
            protected void done() {
                try {
                    Map<LocalDate, Integer> salesData = get();
                    if (salesData.isEmpty()) {
                        logger.debug("[DailySalesDialog] 売上データが空です。「データなし」行を追加します。");
                        tableModel.addRow(new Object[]{"データがありません", ""});
                    } else {
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd (E)");
                        salesData.entrySet().stream()
                                .sorted(Map.Entry.<LocalDate, Integer>comparingByKey().reversed())
                                .forEach(entry -> {
                                    logger.debug("[DailySalesDialog] 行を追加: 日付=" + entry.getKey() + ", カウント=" + entry.getValue());
                                    tableModel.addRow(new Object[]{
                                            entry.getKey().format(formatter),
                                            entry.getValue()
                                    });
                                });
                    }
                } catch (Exception e) {
                    logger.error("Error loading sales data", e);
                    tableModel.addRow(new Object[]{"エラーが発生しました", e.getMessage()});
                }
            }
        };
        worker.execute();
    }
}