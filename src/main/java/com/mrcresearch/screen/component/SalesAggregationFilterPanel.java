package com.mrcresearch.screen.component;

import com.mrcresearch.screen.component.category.Categories;
import com.mrcresearch.screen.component.category.CategoryModel;
import com.mrcresearch.screen.model.SalesAggregationFilterModel;
import com.mrcresearch.util.DatePicker;
import com.mrcresearch.util.TextFieldContextMenuUtil;
import com.mrcresearch.util.database.SettingsRepository;
import net.miginfocom.swing.MigLayout;

import javax.swing.*;
import java.awt.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

/**
 * 売上集計画面のすべてのフィルタリングコンポーネントとロジックをカプセル化するJPanel。
 */
public class SalesAggregationFilterPanel extends JPanel {

    private final Consumer<SalesAggregationFilterModel> onSearch;
    private final Runnable onClear;

    // --- フィルターコンポーネント ---
    private JTextField txtNameFilter;
    private JTextField txtSellerIdFilter;
    private JTextField txtKeywordSearchId;
    private JTextField txtSellerSearchId;
    private JComboBox<String> cmbCategoryFilter;
    private JTextField txtMinSalesCount;
    private JTextField txtMaxSalesCount;
    private JTextField txtMinPrice;
    private JTextField txtMaxPrice;
    private JTextField txtStartDate;
    private JTextField txtEndDate;

    // --- カテゴリデータ ---
    private final Categories categories;
    private List<CategoryModel> allCategories;

    /**
     * SalesAggregationFilterPanelのコンストラクタ。
     *
     * @param onSearch 検索実行時のコールバック
     * @param onClear  クリア実行時のコールバック
     */
    public SalesAggregationFilterPanel(Consumer<SalesAggregationFilterModel> onSearch, Runnable onClear) {
        this.onSearch = onSearch;
        this.onClear = onClear;
        this.categories = new Categories();
        this.categories.init();
        initializeComponents();
        setupLayout();
    }

    /**
     * コンポーネントを初期化します。
     */
    private void initializeComponents() {
        txtNameFilter = new JTextField(15);
        txtSellerIdFilter = new JTextField(10);
        txtKeywordSearchId = new JTextField(10);
        txtSellerSearchId = new JTextField(10);
        cmbCategoryFilter = createCategoryComboBox();
        txtMinSalesCount = new JTextField(5);
        txtMaxSalesCount = new JTextField(5);
        txtMinPrice = new JTextField(5);
        txtMaxPrice = new JTextField(5);

        // Enterキーで検索をトリガーするアクションリスナーを追加
        txtNameFilter.addActionListener(e -> triggerSearch());
        txtSellerIdFilter.addActionListener(e -> triggerSearch());
        txtKeywordSearchId.addActionListener(e -> triggerSearch());
        txtSellerSearchId.addActionListener(e -> triggerSearch());
        txtMinSalesCount.addActionListener(e -> triggerSearch());
        txtMaxSalesCount.addActionListener(e -> triggerSearch());
        txtMinPrice.addActionListener(e -> triggerSearch());
        txtMaxPrice.addActionListener(e -> triggerSearch());
        cmbCategoryFilter.addActionListener(e -> triggerSearch());

        TextFieldContextMenuUtil.enableTextFieldContextMenu(txtNameFilter, txtSellerIdFilter, txtKeywordSearchId, txtSellerSearchId, txtMinSalesCount, txtMaxSalesCount, txtMinPrice, txtMaxPrice);
    }

    /**
     * レイアウトを設定します。
     */
    private void setupLayout() {
        setLayout(new MigLayout("insets 0", "[][grow][][grow][][]", "[][][]"));

        // 1行目: 商品名、セラーID
        add(new JLabel("商品名:"));
        add(txtNameFilter, "growx");
        add(new JLabel("セラーID:"));
        add(txtSellerIdFilter, "growx, wrap");

        // 2行目: カテゴリ、販売数
        add(new JLabel("カテゴリー:"));
        add(cmbCategoryFilter, "growx");
        add(new JLabel("総販売数:"));
        add(createSalesCountPanel(), "growx");
        JButton btnSearch = new JButton("絞り込む");
        btnSearch.addActionListener(e -> triggerSearch());
        add(btnSearch, "w 60, wrap");

        // 3行目: 日付範囲
        add(new JLabel("期間:"));
        add(createDateRangePanel(), "growx");
        add(new JLabel("価格帯:"));
        add(createPriceRangePanel(), "growx");
        JButton btnClear = new JButton("クリア");
        btnClear.addActionListener(e -> onClear.run());
        add(btnClear, "wrap");

        // 4行目: キーワード検索ID、セラー検索ID
        add(new JLabel("キーワード検索ID:"));
        add(txtKeywordSearchId, "growx");
        add(new JLabel("セラー検索ID:"));
        add(txtSellerSearchId, "growx");
    }

    /**
     * 販売数フィルター用のパネルを作成します。
     *
     * @return 販売数フィルターパネル
     */
    private JPanel createSalesCountPanel() {
        JPanel panel = new JPanel(new MigLayout("insets 0", "[][][]", ""));
        panel.add(txtMinSalesCount, "w 60!");
        panel.add(new JLabel("～"));
        panel.add(txtMaxSalesCount, "w 60!");
        return panel;
    }

    /**
     * 価格フィルター用のパネルを作成します。
     *
     * @return 価格フィルターパネル
     */
    private JPanel createPriceRangePanel() {
        JPanel panel = new JPanel(new MigLayout("insets 0", "[][][]", ""));
        panel.add(txtMinPrice, "w 60!");
        panel.add(new JLabel("～"));
        panel.add(txtMaxPrice, "w 60!");
        return panel;
    }

    /**
     * 日付範囲選択用のパネルを作成します。
     *
     * @return 日付範囲選択パネル
     */
    private JPanel createDateRangePanel() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        int defaultDays = SettingsRepository.getSalesAggregationDefaultDays();
        txtStartDate = new JTextField(LocalDate.now().minusDays(defaultDays - 1).format(formatter), 10);
        txtEndDate = new JTextField(LocalDate.now().format(formatter), 10);
        TextFieldContextMenuUtil.enableTextFieldContextMenu(txtStartDate, txtEndDate);

        JButton btnStartDatePicker = new JButton("📅");
        btnStartDatePicker.addActionListener(e -> DatePicker.showDatePickerDialog(this, txtStartDate, "開始日を選択"));

        JButton btnEndDatePicker = new JButton("📅");
        btnEndDatePicker.addActionListener(e -> DatePicker.showDatePickerDialog(this, txtEndDate, "終了日を選択"));

        JPanel datePanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 5, 0));
        datePanel.add(createDateFieldWithPicker(txtStartDate, btnStartDatePicker));
        datePanel.add(new JLabel("～"));
        datePanel.add(createDateFieldWithPicker(txtEndDate, btnEndDatePicker));
        datePanel.add(new JLabel("(最大14日間)"));
        return datePanel;
    }

    /**
     * 日付フィールドとピッカーボタンを持つパネルを作成します。
     *
     * @param dateField    日付フィールド
     * @param pickerButton ピッカーボタン
     * @return 日付フィールドパネル
     */
    private JPanel createDateFieldWithPicker(JTextField dateField, JButton pickerButton) {
        JPanel panel = new JPanel(new BorderLayout(2, 0));
        panel.add(dateField, BorderLayout.CENTER);
        panel.add(pickerButton, BorderLayout.EAST);
        pickerButton.setPreferredSize(new Dimension(30, dateField.getPreferredSize().height));
        return panel;
    }

    /**
     * 検索をトリガーします。
     */
    private void triggerSearch() {
        if (validateInputs()) {
            onSearch.accept(getFilterModel());
        }
    }

    /**
     * 現在のフィルター設定からSalesAggregationFilterModelを生成します。
     *
     * @return SalesAggregationFilterModel
     */
    public SalesAggregationFilterModel getFilterModel() {
        SalesAggregationFilterModel model = new SalesAggregationFilterModel();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");

        model.setNameFilter(txtNameFilter.getText().trim());
        model.setSellerIdFilter(txtSellerIdFilter.getText().trim());
        model.setKeywordSearchId(txtKeywordSearchId.getText().trim());
        model.setSellerSearchId(txtSellerSearchId.getText().trim());

        try {
            model.setMinSalesCount(Integer.parseInt(txtMinSalesCount.getText().trim()));
        } catch (NumberFormatException e) { /* ignore */ }
        try {
            model.setMaxSalesCount(Integer.parseInt(txtMaxSalesCount.getText().trim()));
        } catch (NumberFormatException e) { /* ignore */ }

        try {
            model.setMinPrice(Integer.parseInt(txtMinPrice.getText().trim()));
        } catch (NumberFormatException e) { /* ignore */ }
        try {
            model.setMaxPrice(Integer.parseInt(txtMaxPrice.getText().trim()));
        } catch (NumberFormatException e) { /* ignore */ }

        try {
            model.setStartDate(LocalDate.parse(txtStartDate.getText().trim(), formatter));
            model.setEndDate(LocalDate.parse(txtEndDate.getText().trim(), formatter));
        } catch (Exception e) { /* ignore */ }

        String selectedCategory = (String) cmbCategoryFilter.getSelectedItem();
        if (selectedCategory != null && !"すべて".equals(selectedCategory)) {
            model.setCategoryIds(getSelectedCategoryIds(selectedCategory));
        }

        return model;
    }

    /**
     * フィルターをクリアします。
     */
    public void clearFilter() {
        txtNameFilter.setText("");
        txtSellerIdFilter.setText("");
        txtKeywordSearchId.setText("");
        txtSellerSearchId.setText("");
        cmbCategoryFilter.setSelectedIndex(0);
        txtMinSalesCount.setText("");
        txtMaxSalesCount.setText("");
        txtMinPrice.setText("");
        txtMaxPrice.setText("");

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        int defaultDays = SettingsRepository.getSalesAggregationDefaultDays();
        txtStartDate.setText(LocalDate.now().minusDays(defaultDays - 1).format(formatter));
        txtEndDate.setText(LocalDate.now().format(formatter));
    }

    /**
     * フィルターフィールドの有効/無効を設定します。
     *
     * @param enabled 有効にする場合はtrue
     */
    public void setFieldsEnabled(boolean enabled) {
        for (Component comp : getComponents()) {
            if (comp instanceof JPanel) { // サブパネルに再帰的に適用
                for (Component subComp : ((JPanel) comp).getComponents()) {
                    subComp.setEnabled(enabled);
                }
            }
            comp.setEnabled(enabled);
        }
    }

    /**
     * セラーIDでフィルターを設定し、検索をトリガーします。
     *
     * @param sellerId セラーID
     */
    public void filterBySellerId(String sellerId) {
        clearFilter();
        txtSellerIdFilter.setText(sellerId.trim());
        triggerSearch();
    }

    /**
     * キーワード検索IDでフィルターを設定し、検索をトリガーします。
     *
     * @param keywordSearchId キーワード検索ID
     */
    public void filterByKeywordSearchId(String keywordSearchId) {
        clearFilter();
        txtKeywordSearchId.setText(keywordSearchId.trim());
        triggerSearch();
    }

    /**
     * セラーリサーチIDでフィルターを設定し、検索をトリガーします。
     *
     * @param sellerSearchId セラー検索ID
     */
    public void filterBySellerSearchId(String sellerSearchId) {
        clearFilter();
        txtSellerSearchId.setText(sellerSearchId.trim());
        triggerSearch();
    }

    /**
     * 入力値を検証します。
     *
     * @return 検証が成功した場合はtrue
     */
    private boolean validateInputs() {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
            LocalDate startDate = LocalDate.parse(txtStartDate.getText().trim(), formatter);
            LocalDate endDate = LocalDate.parse(txtEndDate.getText().trim(), formatter);

            if (startDate.isAfter(endDate)) {
                JOptionPane.showMessageDialog(this, "開始日は終了日より前の日付を入力してください。", "日付エラー", JOptionPane.ERROR_MESSAGE);
                return false;
            }
            if (ChronoUnit.DAYS.between(startDate, endDate) + 1 > 14) {
                JOptionPane.showMessageDialog(this, "期間は最大14日間までです。", "期間エラー", JOptionPane.ERROR_MESSAGE);
                return false;
            }
            if (startDate.isAfter(LocalDate.now()) || endDate.isAfter(LocalDate.now())) {
                JOptionPane.showMessageDialog(this, "未来の日付は指定できません。", "日付エラー", JOptionPane.ERROR_MESSAGE);
                return false;
            }
            return true;
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "日付の形式が正しくありません (yyyy/MM/dd)。", "日付形式エラー", JOptionPane.ERROR_MESSAGE);
            return false;
        }
    }

    //<editor-fold desc="カテゴリコンボボックスのロジック">

    /**
     * カテゴリ選択用のコンボボックスを作成します。
     *
     * @return カテゴリコンボボックス
     */
    private JComboBox<String> createCategoryComboBox() {
        allCategories = new ArrayList<>();
        List<CategoryModel> level0Categories = categories.getCategoryFromParentId("0");
        allCategories.addAll(level0Categories);
        for (CategoryModel level0 : level0Categories) {
            addChildCategories(level0.getId(), allCategories);
        }

        DefaultComboBoxModel<String> model = new DefaultComboBoxModel<>();
        model.addElement("すべて");
        for (CategoryModel category : allCategories) {
            model.addElement(getIndentedCategoryName(category));
        }
        return new JComboBox<>(model);
    }

    /**
     * 子カテゴリを再帰的に追加します。
     *
     * @param parentId   親カテゴリID
     * @param targetList 追加先のリスト
     */
    private void addChildCategories(String parentId, List<CategoryModel> targetList) {
        List<CategoryModel> children = categories.getCategoryFromParentId(parentId);
        for (CategoryModel child : children) {
            if (!"すべて".equals(child.getName())) {
                targetList.add(child);
                addChildCategories(child.getId(), targetList);
            }
        }
    }

    /**
     * インデント付きのカテゴリ名を取得します。
     *
     * @param category カテゴリモデル
     * @return インデント付きカテゴリ名
     */
    private String getIndentedCategoryName(CategoryModel category) {
        return "　".repeat(Math.max(0, Integer.parseInt(category.getLevel()))) + category.getName();
    }

    /**
     * 選択された表示名に対応するカテゴリIDのリストを取得します。
     *
     * @param selectedDisplayName 選択された表示名
     * @return カテゴリIDのリスト
     */
    private List<String> getSelectedCategoryIds(String selectedDisplayName) {
        List<String> categoryIds = new ArrayList<>();
        for (CategoryModel category : allCategories) {
            if (getIndentedCategoryName(category).equals(selectedDisplayName)) {
                categoryIds.add(category.getId());
                addChildCategoryIds(category.getId(), categoryIds);
                break;
            }
        }
        return categoryIds;
    }

    /**
     * 指定された親カテゴリIDの子カテゴリIDを再帰的に収集し、ターゲットリストに追加します。
     *
     * @param parentId   親カテゴリのID
     * @param targetList 子カテゴリIDを追加するリスト
     */
    private void addChildCategoryIds(String parentId, List<String> targetList) {
        List<CategoryModel> children = categories.getCategoryFromParentId(parentId);
        for (CategoryModel child : children) {
            // "すべて" カテゴリは除外
            if (!"すべて".equals(child.getName())) {
                targetList.add(child.getId());
                // 再帰的に子カテゴリのIDを追加
                addChildCategoryIds(child.getId(), targetList);
            }
        }
    }
}