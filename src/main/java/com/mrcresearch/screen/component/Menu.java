package com.mrcresearch.screen.component;

import com.formdev.flatlaf.FlatClientProperties;
import com.mrcresearch.screen.model.MenuModel;
import com.mrcresearch.service.common.FontSettings;
import com.mrcresearch.util.ThemeIconUtil;
import lombok.Getter;
import net.miginfocom.swing.MigLayout;
import org.kordamp.ikonli.fontawesome5.FontAwesomeSolid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.net.URI;
import java.util.ArrayList;
import java.util.Objects;

/**
 * アプリケーションのサイドメニューコンポーネント。
 * ロゴ表示、メニューボタン群、バージョン通知、利用期限ラベルの表示を行います。
 */
public class Menu extends JPanel {
    private static final Logger logger = LoggerFactory.getLogger(Menu.class);
    @Getter
    private ArrayList<MenuModel> menus;
    private JLabel expirationLabel;
    private JPanel versionNotificationPanel;

    public Menu() {
        init();
    }

    private void init() {
        // パネル全体 - レイアウト制御しやすいBorderLayoutを採用
        setLayout(new BorderLayout());
        setBackground(new Color(50, 50, 50)); // ダークテーマのためのダークグレー

        // メインの中身パネル（MigLayout使用）
        JPanel panel = new JPanel();
        panel.setLayout(new MigLayout("fill, flowy", "[grow]", "[][][][][][][][grow][][]"));
        panel.setBackground(new Color(50, 50, 50)); // ダークテーマのためのダークグレー

        // アイコンとテキストを配置するパネル
        JPanel logoPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 5, 0));
        logoPanel.setOpaque(false);
        logoPanel.setBackground(new Color(50, 50, 50)); // 親パネルの背景に合わせる

        // アイコンを読み込み
        ImageIcon icon = new ImageIcon(Objects.requireNonNull(getClass().getResource("/img/icon.png")));
        // 適切なサイズへスケーリング
        Image img = icon.getImage().getScaledInstance(40, 40, Image.SCALE_SMOOTH);
        ImageIcon scaledIcon = new ImageIcon(img);

        // アイコンラベル
        JLabel iconLabel = new JLabel(scaledIcon);

        // テキストラベル
        JLabel textLabel = new JLabel("MrcResearch");
        textLabel.setForeground(Color.WHITE);
        textLabel.putClientProperty(FlatClientProperties.STYLE, "font:bold +15");

        // パネルへ追加
        logoPanel.add(iconLabel);
        logoPanel.add(textLabel);

        panel.add(logoPanel, "align center, gapbottom 10, gaptop 10");

        // メニューバー
        initMenuButton(panel);

        panel.add(Box.createVerticalStrut(300)); // 10px spacing

        // バージョン通知パネルを初期化
        initVersionNotificationPanel(panel);

        // 利用期限ラベルを初期化
        initExpirationLabel(panel);

        // Add main panel to center and ensure it fills the space
        add(panel, BorderLayout.CENTER);
    }

    private void initMenuButton(JPanel panel) {
        menus = new ArrayList<>();
        menus.add(new MenuModel(FontAwesomeSolid.SEARCH, "キーワード検索", 0));
        menus.add(new MenuModel(FontAwesomeSolid.USER, "セラー検索", 1));
        menus.add(new MenuModel(FontAwesomeSolid.HEART, "お気に入りのセラー", 2));
        menus.add(new MenuModel(FontAwesomeSolid.TABLE, "リサーチ結果", 3));
        menus.add(new MenuModel(FontAwesomeSolid.TOOLS, "設定", 4));
        menus.add(new MenuModel(FontAwesomeSolid.TRASH, "商品データ削除", 5));

        for (MenuModel menu : menus) {
            // Add menu buttons sequentially after logo
            panel.add(menu.getButton(), "grow, h 80px");
        }

        // パネルが表示されるように設定
        panel.setVisible(true);
    }

    /**
     * Initialize the version notification panel
     */
    private void initVersionNotificationPanel(JPanel panel) {
        versionNotificationPanel = new JPanel();
        versionNotificationPanel.setLayout(new MigLayout("fill, flowy", "[grow]", "[][]"));
        versionNotificationPanel.setBackground(new Color(50, 50, 50)); // Match menu background
        versionNotificationPanel.setVisible(false); // Initially hidden

        // Version notification message
        JLabel versionMessage = new JLabel("新しいバージョンがあります");
        versionMessage.setForeground(new Color(255, 165, 0)); // Orange color for attention
        versionMessage.setFont(FontSettings.getLargerTextFont());
        versionMessage.setHorizontalAlignment(SwingConstants.CENTER);
        versionNotificationPanel.add(versionMessage, "align center");

        // Download button
        JButton downloadButton = new JButton("ダウンロード");
        downloadButton.setFont(FontSettings.getSmallestTextFont());
        downloadButton.setPreferredSize(new Dimension(120, 25));
        downloadButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                openDownloadPage();
            }
        });
        versionNotificationPanel.add(downloadButton, "align center");

        // Add to panel
        panel.add(versionNotificationPanel, "align center");

        logger.debug("バージョン通知パネルが初期化されました");
    }

    /**
     * Initialize the expiration date label at the bottom of the menu
     */
    private void initExpirationLabel(JPanel panel) {
        expirationLabel = new JLabel();
        expirationLabel.setForeground(new Color(180, 180, 180)); // Light gray text
        expirationLabel.setFont(FontSettings.getSmallestTextFont()); // Smaller font
        expirationLabel.setHorizontalAlignment(SwingConstants.CENTER);
        expirationLabel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        expirationLabel.setVisible(false); // Initially hidden until expiration date is set

        // Debug: Set a background color to see if the label is positioned correctly
        expirationLabel.setOpaque(true);
        expirationLabel.setBackground(new Color(70, 70, 70)); // Slightly different gray for debugging

        // Add to the last row with push constraint to position at bottom
        panel.add(expirationLabel, "align center, pushy");

        logger.debug("有効期限ラベルが初期化され、パネルに追加されました");
    }

    /**
     * Set the expiration date to be displayed
     *
     * @param expireDate The expiration date in yyyy/MM/dd format
     */
    public void setExpirationDate(String expireDate) {
        logger.debug("setExpirationDate が呼び出されました: " + expireDate);

        if (expirationLabel == null) {
            logger.error("ERROR: expirationLabel is null!");
            return;
        }

        if (expireDate != null && !expireDate.trim().isEmpty()) {
            // Format the date according to the requirement: "利用期限：[expireDate](yyyy/m/d)"
            // Convert yyyy/MM/dd to yyyy/m/d format (remove leading zeros from month and day)
            String formattedDate = formatDateForDisplay(expireDate);
            String displayText = "利用期限：" + formattedDate;

            logger.debug("有効期限ラベルのテキストを以下に設定します: " + displayText);
            expirationLabel.setText(displayText);
            expirationLabel.setVisible(true);

            logger.debug("ラベルの表示状態: " + expirationLabel.isVisible());
            logger.debug("ラベルのテキスト: " + expirationLabel.getText());
        } else {
            logger.debug("有効期限ラベルを非表示にします (日付がnullまたは空)");
            expirationLabel.setVisible(false);
        }

        // Refresh the layout
        revalidate();
        repaint();

        logger.debug("メニューが再検証され、再描画されました");
    }

    /**
     * Format date from yyyy/MM/dd to yyyy/m/d (remove leading zeros)
     *
     * @param dateStr Date string in yyyy/MM/dd format
     * @return Formatted date string in yyyy/m/d format
     */
    private String formatDateForDisplay(String dateStr) {
        try {
            if (dateStr.matches("\\d{4}/\\d{2}/\\d{2}")) {
                String[] parts = dateStr.split("/");
                String year = parts[0];
                String month = String.valueOf(Integer.parseInt(parts[1])); // Remove leading zero
                String day = String.valueOf(Integer.parseInt(parts[2])); // Remove leading zero
                return year + "/" + month + "/" + day;
            }
        } catch (Exception e) {
            logger.error("Error formatting date: " + dateStr + " - " + e.getMessage());
        }
        // Return original if formatting fails
        return dateStr;
    }

    /**
     * Show version notification panel
     */
    public void showVersionNotification() {
        logger.debug("showVersionNotification が呼び出されました");
        if (versionNotificationPanel != null) {
            versionNotificationPanel.setVisible(true);
            revalidate();
            repaint();
            logger.debug("バージョン通知パネルが表示されました");
        } else {
            logger.error("ERROR: versionNotificationPanel is null!");
        }
    }

    /**
     * Hide version notification panel
     */
    public void hideVersionNotification() {
        logger.debug("hideVersionNotification が呼び出されました");
        if (versionNotificationPanel != null) {
            versionNotificationPanel.setVisible(false);
            revalidate();
            repaint();
            logger.debug("バージョン通知パネルが非表示になりました");
        }
    }

    /**
     * Open download page in browser
     */
    private void openDownloadPage() {
        try {
            Desktop.getDesktop().browse(new URI("https://mcrsldrschtl.com/download"));
            logger.info("Opening download page: https://mcrsldrschtl.com/download");
        } catch (Exception e) {
            logger.error("Error opening download page: " + e.getMessage());
            JOptionPane.showMessageDialog(this,
                    "ダウンロードページを開けませんでした。\n" + e.getMessage(),
                    "エラー",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Apply theme changes to menu buttons
     * Note: Menu icon colors are kept fixed and do not change with theme
     */
    public void applyThemeChange() {
        try {
            if (menus != null) {
                for (MenuModel menu : menus) {
                    JButton button = menu.getButton();
                    if (button != null) {
                        // Keep menu icon colors fixed - do not update them with theme changes
                        // The menu icons should maintain their original color regardless of theme

                        // Only update button background and text colors if needed
                        // but keep icon colors unchanged

                        // Force button to repaint with current settings
                        button.revalidate();
                        button.repaint();
                    }
                }
            }

            // Update expiration label color if needed
            if (expirationLabel != null) {
                Color textColor = ThemeIconUtil.getThemeIconColor();
                expirationLabel.setForeground(textColor);
            }

            // Update version notification panel colors if needed
            if (versionNotificationPanel != null) {
                // Update background color to match theme
                Color backgroundColor = UIManager.getColor("Panel.background");
                if (backgroundColor != null) {
                    versionNotificationPanel.setBackground(backgroundColor);
                }
            }

            // Repaint the entire menu
            revalidate();
            repaint();

        } catch (Exception e) {
            logger.error("Error updating menu icons for theme: " + e.getMessage());
        }
    }

}