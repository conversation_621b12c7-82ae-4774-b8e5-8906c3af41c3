package com.mrcresearch.screen.component;

import com.mrcresearch.service.common.FontSettings;
import com.mrcresearch.screen.model.ColorModel;

import javax.swing.*;
import java.awt.*;
import java.awt.geom.Ellipse2D;

/**
 * Custom checkbox component that displays a colored circle next to the text.
 * Used for color selection in detailed search.
 */
public class ColorCheckBox extends JC<PERSON>ckBox {
    private final ColorModel colorModel;
    private static final int CIRCLE_SIZE = 16;
    private static final int CIRCLE_MARGIN = 5;

    public ColorCheckBox(ColorModel colorModel) {
        super(colorModel.getName());
        this.colorModel = colorModel;
        setFont(FontSettings.getLabelFont());

        // Add extra space for the color circle
        setBorder(BorderFactory.createEmptyBorder(2, CIRCLE_SIZE + CIRCLE_MARGIN + 5, 2, 5));
    }

    public ColorModel getColor() {
        return colorModel;
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);

        // Don't draw circle for "すべて" option
        if (colorModel.getId() == 0) {
            return;
        }

        Graphics2D g2d = (Graphics2D) g.create();
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // Calculate circle position
        int circleX = 5;
        int circleY = (getHeight() - CIRCLE_SIZE) / 2;

        // Draw circle with color
        Color displayColor = colorModel.getDisplayColor();

        // For white color, add a border to make it visible
        if (displayColor.equals(Color.WHITE)) {
            g2d.setColor(Color.LIGHT_GRAY);
            g2d.fill(new Ellipse2D.Double(circleX - 1, circleY - 1, CIRCLE_SIZE + 2, CIRCLE_SIZE + 2));
        }

        // For beige color, make it more visible with a border
        if (displayColor.getRGB() == new Color(245, 245, 220).getRGB()) {
            g2d.setColor(Color.LIGHT_GRAY);
            g2d.draw(new Ellipse2D.Double(circleX - 1, circleY - 1, CIRCLE_SIZE + 2, CIRCLE_SIZE + 2));
        }

        g2d.setColor(displayColor);
        g2d.fill(new Ellipse2D.Double(circleX, circleY, CIRCLE_SIZE, CIRCLE_SIZE));

        // Add border for better visibility
        g2d.setColor(Color.DARK_GRAY);
        g2d.setStroke(new BasicStroke(1f));
        g2d.draw(new Ellipse2D.Double(circleX, circleY, CIRCLE_SIZE, CIRCLE_SIZE));

        g2d.dispose();
    }
}
