package com.mrcresearch.screen.component;

import com.formdev.flatlaf.FlatClientProperties;
import com.mrcresearch.util.ThemeIconUtil;
import lombok.Getter;
import net.miginfocom.swing.MigLayout;
import org.kordamp.ikonli.antdesignicons.AntDesignIconsOutlined;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionListener;

public class Header extends JPanel {
    private JLabel titleLabel;
    @Getter
    private JButton menuToggleButton;
    private ProgressPanel progressPanel;

    public Header() {
        init();
    }

    private void init() {
        // パネル全体
        setLayout(new MigLayout("insets 4 4 4 4, fill",
                "[fill][fill,grow][fill,200]", "[fill,grow]"));

        // Initialize menu toggle button with menu icon
        menuToggleButton = new JButton();
        menuToggleButton.setIcon(ThemeIconUtil.ofHeaderButton(AntDesignIconsOutlined.MENU, 20));
        menuToggleButton.setBackground(new Color(59, 89, 152));
        menuToggleButton.setForeground(Color.WHITE);
        menuToggleButton.setBorderPainted(false);
        menuToggleButton.setFocusPainted(false);
        menuToggleButton.setContentAreaFilled(true);
        menuToggleButton.setOpaque(true);
        menuToggleButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
        menuToggleButton.setToolTipText("メニューを表示/非表示");
        add(menuToggleButton, "cell 0 0, align left");

        // Add title label in the middle section
        titleLabel = new JLabel("");
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        titleLabel.putClientProperty(FlatClientProperties.STYLE, "font:bold +15");
        titleLabel.setForeground(Color.WHITE);
        add(titleLabel, "cell 1 0, align center");

        // Add progress panel on the right
        progressPanel = new ProgressPanel();
        add(progressPanel, "cell 2 0, align right");

        setBackground(new Color(59, 89, 152)); // A more subtle blue color
    }

    /**
     * Set the title text to be displayed in the header
     *
     * @param title The title text
     */
    public void setTitleText(String title) {
        titleLabel.setText(title);
    }

    /**
     * Set the action listener for the menu toggle button
     *
     * @param listener The action listener to be set
     */
    public void setMenuToggleListener(ActionListener listener) {
        menuToggleButton.addActionListener(listener);
    }

}
