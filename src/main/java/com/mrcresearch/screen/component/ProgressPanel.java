package com.mrcresearch.screen.component;

import com.formdev.flatlaf.FlatClientProperties;
import com.mrcresearch.model.ProgressItem;
import com.mrcresearch.service.common.FontSettings;
import com.mrcresearch.util.SearchQueueManager;
import com.mrcresearch.util.TextFieldContextMenuUtil;
import com.mrcresearch.util.ThemeIconUtil;
import com.mrcresearch.util.ui.UIStyles;
import net.miginfocom.swing.MigLayout;
import org.kordamp.ikonli.antdesignicons.AntDesignIconsOutlined;
import org.kordamp.ikonli.swing.FontIcon;

import javax.swing.*;
import java.awt.*;
import java.util.List;

/**
 * Progress panel for displaying search progress and history
 */
public class ProgressPanel extends JPanel {
    private JLabel currentProgressLabel;
    private JButton historyButton;
    private JPopupMenu historyPopup;
    private JPanel historyPanel;
    private JButton clearAllButton;

    private ProgressItem currentProgress;

    public ProgressPanel() {
        init();
        setupCallbacks();
    }

    private void init() {
        setLayout(new MigLayout("insets 0", "[grow][][][]", "[fill]"));
        setOpaque(false);

        // Current progress label
        currentProgressLabel = new JLabel("待機中");
        currentProgressLabel.setForeground(Color.WHITE);
        currentProgressLabel.putClientProperty(FlatClientProperties.STYLE, "font:bold +1");
        currentProgressLabel.setIcon(ThemeIconUtil.ofSuccess(16));
        currentProgressLabel.setCursor(new Cursor(Cursor.HAND_CURSOR));
        currentProgressLabel.setToolTipText("クリックでキューの詳細を表示");
        UIStyles.setAccessibleName(currentProgressLabel, "進捗状況", "クリックでキューの詳細を表示");

        // Add click listener to show queue details
        currentProgressLabel.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseClicked(java.awt.event.MouseEvent e) {
                showQueueDetails();
            }
        });

        add(currentProgressLabel, "grow");

        // History button
        historyButton = new JButton();
        historyButton.setIcon(ThemeIconUtil.ofButton(AntDesignIconsOutlined.HISTORY, 16));
        historyButton.setToolTipText("履歴を表示");
        historyButton.setBackground(new Color(59, 89, 152));
        historyButton.setBorderPainted(false);
        historyButton.setFocusPainted(false);
        historyButton.setContentAreaFilled(false);
        historyButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
        UIStyles.setAccessibleName(historyButton, "履歴ボタン", "履歴を表示");
        add(historyButton, "");

        // Clear all button
        clearAllButton = new JButton();
        clearAllButton.setIcon(ThemeIconUtil.ofButton(AntDesignIconsOutlined.CLEAR, 16));
        clearAllButton.setToolTipText("全てクリア");
        clearAllButton.setBackground(new Color(59, 89, 152));
        clearAllButton.setBorderPainted(false);
        clearAllButton.setFocusPainted(false);
        clearAllButton.setContentAreaFilled(false);
        clearAllButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
        UIStyles.setAccessibleName(clearAllButton, "全てクリアボタン", "全てクリア");
        add(clearAllButton, "");

        // Setup history popup
        setupHistoryPopup();

        // Add action listeners
        historyButton.addActionListener(e -> showHistoryPopup());
        clearAllButton.addActionListener(e -> clearAllProgress());
    }

    private void setupHistoryPopup() {
        historyPopup = new JPopupMenu();
        historyPopup.setPreferredSize(new Dimension(300, 200));

        historyPanel = new JPanel();
        historyPanel.setLayout(new BoxLayout(historyPanel, BoxLayout.Y_AXIS));

        JScrollPane scrollPane = new JScrollPane(historyPanel);
        scrollPane.setPreferredSize(new Dimension(280, 180));
        scrollPane.setBorder(BorderFactory.createEmptyBorder());

        historyPopup.add(scrollPane);
    }

    private void setupCallbacks() {
        SearchQueueManager queueManager = SearchQueueManager.getInstance();

        // Progress update callback
        queueManager.setProgressUpdateCallback(this::updateProgress);

        // Completion callback
        queueManager.setCompletionCallback(this::onItemCompleted);

        // Status update callback (for overall queue status) - always update immediately
        queueManager.setStatusUpdateCallback(status -> {
            updateCurrentProgressLabel(status, getIconForStatus(status), getColorForStatus(status));
        });

        // Status click callback
        queueManager.setStatusClickCallback(this::showQueueDetails);
    }

    private void updateProgress(ProgressItem item) {
        currentProgress = item;

        String text;
        FontIcon icon;
        Color color;

        switch (item.getStatus()) {
            case WAITING:
                text = item.getProgressText();
                icon = ThemeIconUtil.ofWaiting(16);
                color = Color.YELLOW;
                break;
            case PROCESSING:
                text = item.getProgressText();
                icon = ThemeIconUtil.ofLoading(16);
                color = Color.WHITE;
                break;
            case COMPLETED:
                text = item.getCompletionText();
                icon = ThemeIconUtil.ofSuccess(16);
                color = Color.GREEN;
                break;
            case ERROR:
                text = "エラー: " + item.getItemName();
                icon = ThemeIconUtil.ofError(16);
                color = Color.RED;
                break;
            default:
                text = "不明";
                icon = ThemeIconUtil.ofStatus(AntDesignIconsOutlined.QUESTION_CIRCLE, 16, Color.GRAY);
                color = Color.GRAY;
                break;
        }

        updateCurrentProgressLabel(text, icon, color);
    }

    private void onItemCompleted(ProgressItem item) {
        // Show completion for a few seconds, then reset if no more active items
        Timer timer = new Timer(3000, e -> {
            List<ProgressItem> activeItems = SearchQueueManager.getInstance().getActiveProgress();
            if (activeItems.isEmpty()) {
                currentProgress = null;
                updateCurrentProgressLabel("待機中",
                        ThemeIconUtil.ofSuccess(16),
                        Color.GREEN);
            }
        });
        timer.setRepeats(false);
        timer.start();

        // Update history
        updateHistoryPanel();
    }

    private void updateCurrentProgressLabel(String text, FontIcon icon, Color color) {
        SwingUtilities.invokeLater(() -> {
            currentProgressLabel.setText(text);
            currentProgressLabel.setIcon(icon);
            currentProgressLabel.setForeground(color);
        });
    }

    /**
     * Show queue details dialog when status is clicked
     */
    private void showQueueDetails() {
        String details = SearchQueueManager.getInstance().getQueueDetails();

        JTextArea textArea = new JTextArea(details);
        textArea.setEditable(false);
        textArea.setFont(FontSettings.getMonospacedFont12());
        textArea.setBackground(getBackground());

        // Enable context menu for text area
        TextFieldContextMenuUtil.enableTextFieldContextMenu(textArea);

        JScrollPane scrollPane = new JScrollPane(textArea);
        scrollPane.setPreferredSize(new Dimension(500, 300));
        scrollPane.setBorder(BorderFactory.createTitledBorder("キューの詳細"));

        JOptionPane.showMessageDialog(
                this,
                scrollPane,
                "検索キューの状況",
                JOptionPane.INFORMATION_MESSAGE
        );
    }

    private void showHistoryPopup() {
        updateHistoryPanel();
        historyPopup.show(historyButton, 0, historyButton.getHeight());
    }

    private void updateHistoryPanel() {
        historyPanel.removeAll();

        List<ProgressItem> completedItems = SearchQueueManager.getInstance().getCompletedProgress();

        if (completedItems.isEmpty()) {
            JLabel emptyLabel = new JLabel("履歴がありません");
            emptyLabel.setHorizontalAlignment(SwingConstants.CENTER);
            emptyLabel.setForeground(Color.GRAY);
            historyPanel.add(emptyLabel);
        } else {
            for (ProgressItem item : completedItems) {
                JPanel itemPanel = createHistoryItemPanel(item);
                historyPanel.add(itemPanel);
            }
        }

        historyPanel.revalidate();
        historyPanel.repaint();
    }

    private JPanel createHistoryItemPanel(ProgressItem item) {
        JPanel panel = new JPanel(new MigLayout("insets 2", "[grow][]", "[fill]"));
        panel.setBorder(BorderFactory.createEmptyBorder(2, 5, 2, 5));

        // Item info
        String text = String.format("%s - %s (%s)",
                item.getCompletionText(),
                item.getFormattedTime(),
                item.getStatus().getDisplayName());

        JLabel label = new JLabel(text);
        label.setToolTipText(item.getItemName()); // Show full name on hover

        FontIcon icon;
        Color color;
        if (item.getStatus() == ProgressItem.Status.COMPLETED) {
            icon = ThemeIconUtil.ofSuccess(12);
            color = Color.BLACK;
        } else {
            icon = ThemeIconUtil.ofError(12);
            color = Color.RED;
        }

        label.setIcon(icon);
        label.setForeground(color);
        panel.add(label, "grow");

        // Delete button
        JButton deleteButton = new JButton();
        deleteButton.setIcon(ThemeIconUtil.ofStatus(AntDesignIconsOutlined.CLOSE, 12, Color.GRAY));
        deleteButton.setToolTipText("削除");
        deleteButton.setBorderPainted(false);
        deleteButton.setFocusPainted(false);
        deleteButton.setContentAreaFilled(false);
        deleteButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
        deleteButton.addActionListener(e -> {
            SearchQueueManager.getInstance().removeProgressItem(item.getId());
            updateHistoryPanel();
        });
        panel.add(deleteButton, "");

        return panel;
    }

    private void clearAllProgress() {
        int result = JOptionPane.showConfirmDialog(
                this,
                "全ての進捗履歴をクリアしますか？",
                "確認",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.QUESTION_MESSAGE
        );

        if (result == JOptionPane.YES_OPTION) {
            SearchQueueManager.getInstance().clearAllProgress();
            currentProgress = null;
            updateCurrentProgressLabel("待機中",
                    ThemeIconUtil.ofSuccess(16),
                    Color.GREEN);
            updateHistoryPanel();
        }
    }

    private FontIcon getIconForStatus(String status) {
        if (status.contains("実行中")) {
            return ThemeIconUtil.ofLoading(16);
        } else if (status.contains("処理待ち")) {
            return ThemeIconUtil.ofWaiting(16);
        } else {
            return ThemeIconUtil.ofSuccess(16);
        }
    }

    private Color getColorForStatus(String status) {
        if (status.contains("実行中")) {
            return Color.WHITE;
        } else if (status.contains("処理待ち")) {
            return Color.YELLOW;
        } else {
            return Color.GREEN;
        }
    }
}
