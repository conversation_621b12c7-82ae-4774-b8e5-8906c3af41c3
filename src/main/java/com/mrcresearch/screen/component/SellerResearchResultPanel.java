package com.mrcresearch.screen.component;

import com.formdev.flatlaf.FlatClientProperties;
import com.mrcresearch.screen.component.pagination.PaginationComponent;
import com.mrcresearch.screen.component.pagination.PaginationConfig;
import com.mrcresearch.screen.favorite.FavoriteSeller;
import com.mrcresearch.screen.model.FavoriteSellerModel;
import com.mrcresearch.screen.model.SellerModel;
import com.mrcresearch.screen.model.SellerSearchModel;
import com.mrcresearch.service.SellerManagementService;
import com.mrcresearch.service.common.FontSettings;
import com.mrcresearch.util.ClipboardUtil;
import com.mrcresearch.util.TableTextWrapRenderer;
import com.mrcresearch.util.TextFieldContextMenuUtil;
import com.mrcresearch.util.ThemeIconUtil;
import com.mrcresearch.util.database.DatabaseConnectionManager;
import com.mrcresearch.util.database.DatabaseDateTimeUtil;
import com.mrcresearch.util.database.SellerRepository;
import com.mrcresearch.util.database.SellerSearchRepository;
import com.mrcresearch.util.ui.UIStyles;
import net.miginfocom.swing.MigLayout;
import org.kordamp.ikonli.antdesignicons.AntDesignIconsOutlined;

import javax.swing.*;
import javax.swing.Timer;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;

/**
 * セラーリサーチ結果をページネーション付きで表示するパネルです。
 */
public class SellerResearchResultPanel extends JPanel {
    // 1ページあたりの最大表示件数 (仕様)
    private static final int PAGE_SIZE = 20;
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private JTable resultTable;
    private DefaultTableModel tableModel;
    private PaginationComponent paginationComponent;
    private Timer autoRefreshTimer;
    private JScrollPane scrollPane;

    /**
     * コンストラクタ
     */
    public SellerResearchResultPanel() {
        init();
        loadData();
    }

    /**
     * UIコンポーネントの初期化と配置を行います。
     */
    private void init() {
        setLayout(new MigLayout("fillx,insets 10", "[grow]", "[][grow][]"));

        // UIコンポーネントを作成してパネルに追加
        add(createTitlePanel(), "growx, wrap");
        initTable();
        add(scrollPane, "grow, wrap");
        initPaginationComponent();
        add(paginationComponent, "grow");

        // ページネーションコンポーネントにスクロールペーンを設定（先頭へ移動機能のため）
        paginationComponent.setScrollPane(scrollPane);

        // リサーチステータスを自動更新するタイマーを開始
        startAutoRefreshTimer();
    }

    /**
     * タイトルと更新ボタンを含むパネルを作成します。
     *
     * @return タイトルパネル
     */
    private JPanel createTitlePanel() {
        JPanel titlePanel = new JPanel(new MigLayout("insets 0", "[grow][]", "[]"));
        JLabel titleLabel = new JLabel("セラーリサーチ結果");
        UIStyles.applyTitleStyle(titleLabel);
        UIStyles.setAccessibleName(titleLabel, "セラーリサーチ結果タイトル", "セラーリサーチ結果の一覧を表示");

        JButton refreshButton = new JButton("更新");
        refreshButton.setFont(FontSettings.getSmallestTextFont());
        UIStyles.setAccessibleName(refreshButton, "更新ボタン", "データを再読み込みします");
        refreshButton.addActionListener(e -> loadData());

        titlePanel.add(titleLabel, "grow");
        titlePanel.add(refreshButton);
        return titlePanel;
    }

    /**
     * 結果表示テーブルを初期化します。
     */
    private void initTable() {
        // テーブルモデルを定義
        String[] columnNames = {"ID", "セラー名", "セラーID", "作成日時", "更新日時", "ステータス", "アクション"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                // アクション列のみ編集可能（ボタンクリックのため）
                return column == 6;
            }
        };

        // テーブルを作成
        resultTable = new JTable(tableModel);
        resultTable.setRowHeight(35); // 行の高さ
        resultTable.setFont(FontSettings.getTextFont());
        resultTable.getTableHeader().setFont(FontSettings.getSmallerBoldFont());
        resultTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        resultTable.setAutoResizeMode(JTable.AUTO_RESIZE_ALL_COLUMNS);

        // テーブルのレンダラー（表示形式）とエディター（操作）を設定
        setupTableRenderersAndEditors();

        // 右クリックメニューを設定
        setupRightClickMenu();

        // テーブルでCtrl+Cによるコピーを有効化
        // TableCopyUtil.enableTableCopy(resultTable);

        // テーブルをスクロールペーンに追加
        scrollPane = new JScrollPane(resultTable);
        scrollPane.setPreferredSize(new Dimension(0, 400));
    }

    /**
     * テーブルの各列のレンダラーとエディターを設定します。
     */
    private void setupTableRenderersAndEditors() {
        // 「セラー名」列：テキストが長すぎる場合に折り返して表示
        resultTable.getColumnModel().getColumn(1).setCellRenderer(TableTextWrapRenderer.createLeftAligned());

        // 日時列：中央揃えで表示
        DefaultTableCellRenderer centerRenderer = new DefaultTableCellRenderer();
        centerRenderer.setHorizontalAlignment(JLabel.CENTER);
        resultTable.getColumnModel().getColumn(3).setCellRenderer(centerRenderer);
        resultTable.getColumnModel().getColumn(4).setCellRenderer(centerRenderer);

        // 「ステータス」列：状態に応じてアイコンと色を変更
        resultTable.getColumnModel().getColumn(5).setCellRenderer(new StatusCellRenderer());

        // 「アクション」列：ボタンとして表示・操作
        resultTable.getColumnModel().getColumn(6).setCellRenderer(new ButtonRenderer());
        resultTable.getColumnModel().getColumn(6).setCellEditor(new ButtonEditor(new JCheckBox()));
    }


    /**
     * ページネーションコンポーネントを初期化します。
     */
    private void initPaginationComponent() {
        PaginationConfig config = PaginationConfig.createForSellerResearch();
        paginationComponent = new PaginationComponent(config);
        paginationComponent.setPaginationListener((newPage, pageSize) -> loadData());
    }

    /**
     * リサーチステータスを定期的に更新するための自動更新タイマーを開始します。
     */
    private void startAutoRefreshTimer() {
        // 10秒ごとにステータスを自動更新
        autoRefreshTimer = new Timer(10000, e -> {
            // 「リサーチ中」の項目がある場合のみデータを再読み込み
            if (hasResearchingItems()) {
                SwingUtilities.invokeLater(this::loadData);
            }
        });
        autoRefreshTimer.start();
    }

    /**
     * 現在「リサーチ中」の項目がテーブルに存在するかどうかを確認します。
     * @return リサーチ中の項目があれば true
     */
    private boolean hasResearchingItems() {
        for (int i = 0; i < tableModel.getRowCount(); i++) {
            Object status = tableModel.getValueAt(i, 5); // ステータス列
            if ("リサーチ中".equals(status)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 自動更新タイマーを停止します。
     */
    public void stopAutoRefresh() {
        if (autoRefreshTimer != null && autoRefreshTimer.isRunning()) {
            autoRefreshTimer.stop();
        }
    }

    /**
     * 指定されたセラーIDの最新リサーチ日時を `search_items` テーブルから取得します。
     * @param sellerId セラーID
     * @return フォーマットされた日時文字列、見つからない場合は null
     */
    private String getLatestResearchDate(String sellerId) {
        String sql = "SELECT MAX(updated_date) as latest_date FROM search_items WHERE seller_id = ?";
        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, sellerId);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    String latestDateStr = rs.getString("latest_date");
                    if (latestDateStr != null) {
                        Date latestDate = DatabaseDateTimeUtil.parseDate(latestDateStr);
                        if (latestDate != null) {
                            return DATE_FORMAT.format(latestDate);
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("セラーの最新リサーチ日時取得エラー " + sellerId + ": " + e.getMessage());
        }
        return null;
    }

    /**
     * データベースからデータをロードし、テーブルを更新します。
     * `sellers` テーブルと `seller_searches` テーブルの両方からデータを取得して結合します。
     */
    public void loadData() {
        tableModel.setRowCount(0); // 既存のデータをクリア

        // ページネーションのために総件数を取得
        int sellersCount = SellerRepository.getTotalSellerCount();
        int sellerSearchesCount = SellerSearchRepository.getTotalSellerSearchCount();
        int totalCount = Math.max(sellersCount, sellerSearchesCount);
        paginationComponent.updatePagination(totalCount, paginationComponent.getCurrentPage());

        // 現在のページに対応するデータを取得
        int currentPage = paginationComponent.getCurrentPage();
        List<SellerModel> sellers = SellerRepository.getAllSellers(currentPage, PAGE_SIZE);
        List<SellerSearchModel> sellerSearches = SellerSearchRepository.getSellerSearches(currentPage, PAGE_SIZE);

        // 高速アクセスのためにセラーデータをMapに変換
        Map<String, SellerModel> sellersMap = new HashMap<>();
        for (SellerModel seller : sellers) {
            sellersMap.put(seller.getSellerId(), seller);
        }

        // テーブルに行を追加
        Set<String> processedSellerIds = new HashSet<>();

        // 1. `seller_searches` のデータ（実際のリサーチ結果）をテーブルに追加
        for (SellerSearchModel search : sellerSearches) {
            String buttonText = FavoriteSeller.isFavorite(search.getSellerId()) ? "お気に入りを編集" : "お気に入りに追加";
            SellerModel seller = sellersMap.get(search.getSellerId());
            String researchStatus = "完了"; // `seller_searches` にあるものは基本的に完了
            String lastUpdatedDate = getLatestResearchDate(search.getSellerId());
            if (lastUpdatedDate == null) {
                lastUpdatedDate = DATE_FORMAT.format(search.getLastResearchedAt()); // フォールバック
            }

            // `sellers` テーブルに現在のリサーチ状況があれば、それで上書き
            if (seller != null && seller.getResearchStatus() != null) {
                researchStatus = seller.getResearchStatus();
                if ("リサーチ中".equals(researchStatus)) {
                    lastUpdatedDate = DATE_FORMAT.format(seller.getUpdatedAt());
                }
            }

            Object[] rowData = {
                    search.getId(),
                    search.getSellerName() != null ? search.getSellerName() : search.getSellerId(),
                    search.getSellerId(),
                    DATE_FORMAT.format(search.getAddedDate()),
                    lastUpdatedDate,
                    researchStatus,
                    buttonText
            };
            tableModel.addRow(rowData);
            processedSellerIds.add(search.getSellerId());
        }

        // 2. `seller_searches` に対応しない `sellers` のデータを追加
        for (SellerModel seller : sellers) {
            if (!processedSellerIds.contains(seller.getSellerId())) {
                String buttonText = FavoriteSeller.isFavorite(seller.getSellerId()) ? "お気に入りを編集" : "お気に入りに追加";
                String researchStatus = seller.getResearchStatus() != null ? seller.getResearchStatus() : "待機中";
                Object[] rowData = {
                        "",
                        seller.getSellerName(),
                        seller.getSellerId(),
                        DATE_FORMAT.format(seller.getCreatedAt()),
                        DATE_FORMAT.format(seller.getUpdatedAt()),
                        researchStatus,
                        buttonText
                };
                tableModel.addRow(rowData);
            }
        }

        // 「リサーチ中」の項目があればタイマーを動かし、なければ止める
        if (autoRefreshTimer != null) {
            if (hasResearchingItems()) {
                if (!autoRefreshTimer.isRunning()) autoRefreshTimer.start();
            } else {
                if (autoRefreshTimer.isRunning()) autoRefreshTimer.stop();
            }
        }
    }

    /**
     * アプリケーションのテーマ変更を適用します。
     */
    public void applyThemeChange() {
        if (paginationComponent != null) {
            paginationComponent.applyThemeChange();
        }
        // アイコンの色などを再描画するためにデータをリロード
        SwingUtilities.invokeLater(() -> {
            loadData();
            if (resultTable != null) {
                resultTable.revalidate();
                resultTable.repaint();
            }
        });
        repaint();
    }

    /**
     * 新しいセラーをデータベースに追加し、テーブルを更新します。
     * @param sellerName セラー名
     * @param sellerId   セラーID
     * @return 処理が成功した場合は true
     */
    public boolean addSellerResearchResult(String sellerName, String sellerId) {
        try {
            // SellerManagementService を使ってセラーを作成または取得
            SellerManagementService.getOrCreateSeller(sellerId);

            // セラー名が有効な場合に更新
            if (sellerName != null && !sellerName.trim().isEmpty() &&
                    !sellerName.equals(sellerId) && !sellerName.startsWith("セラー_")) {
                SellerManagementService.updateSellerName(sellerId, sellerName);
            }

            loadData(); // データを再読み込み
            return true;
        } catch (Exception e) {
            System.err.println("セラーリサーチ結果の追加中に例外発生: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * @deprecated 代わりに {@link #addSellerResearchResult(String, String)} を使用してください。
     */
    @Deprecated
    public int addSellerResearchResult(String sellerName, String sellerId, String status) {
        return addSellerResearchResult(sellerName, sellerId) ? 1 : -1;
    }

    /**
     * サンプルデータを初期化します。
     */
    public void initWithSampleData() {
        loadData();
    }

    // --- 内部クラス ---

    /**
     * 「ステータス」列のセルレンダラー。ステータスに応じてアイコンと色を設定します。
     */
    static class StatusCellRenderer extends DefaultTableCellRenderer {
        @Override
        public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
            Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
            setHorizontalAlignment(JLabel.CENTER);
            String status = (value != null) ? value.toString() : "待機中";

            switch (status) {
                case "完了":
                    setText("完了");
                    setIcon(ThemeIconUtil.ofStatus(AntDesignIconsOutlined.CHECK_CIRCLE, 16, new Color(0, 128, 0)));
                    c.setForeground(new Color(0, 128, 0)); // Green
                    break;
                case "リサーチ中":
                    setText("リサーチ中");
                    setIcon(ThemeIconUtil.ofStatus(AntDesignIconsOutlined.HOURGLASS, 16, new Color(204, 204, 0)));
                    c.setForeground(new Color(204, 204, 0)); // Yellow
                    break;
                case "エラー":
                    setText("エラー");
                    setIcon(ThemeIconUtil.ofStatus(AntDesignIconsOutlined.EXCLAMATION_CIRCLE, 16, Color.RED));
                    c.setForeground(Color.RED); // Red
                    break;
                case "待機中":
                default:
                    setText("待機中");
                    setIcon(ThemeIconUtil.ofStatus(AntDesignIconsOutlined.CLOCK_CIRCLE, 16, new Color(128, 128, 128)));
                    c.setForeground(new Color(128, 128, 128)); // Gray
                    break;
            }
            return c;
        }
    }

    /**
     * 「アクション」列のボタンをレンダリングするためのクラスです。
     */
    class ButtonRenderer extends JButton implements javax.swing.table.TableCellRenderer {
        public ButtonRenderer() {
            setOpaque(true);
        }

        @Override
        public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
            String text = (value == null) ? "" : value.toString();
            setText(text);

            // お気に入り登録済みであれば色を変更
            if ("お気に入りを編集".equals(text)) {
                setBackground(new Color(82, 182, 93));
            } else {
                setBackground(UIManager.getColor("Button.background"));
            }

            setEnabled(true); // すべての行でボタンを有効化
            if (isSelected) {
                setForeground(table.getSelectionForeground());
            } else {
                setForeground(table.getForeground());
            }
            return this;
        }
    }

    /**
     * 「アクション」列のボタンクリックを処理するためのクラスです。
     */
    class ButtonEditor extends DefaultCellEditor {
        protected JButton button;
        private String label;
        private boolean isPushed;
        private int row;

        public ButtonEditor(JCheckBox checkBox) {
            super(checkBox);
            button = new JButton();
            button.setOpaque(true);
            button.addActionListener(e -> fireEditingStopped());
        }

        @Override
        public Component getTableCellEditorComponent(JTable table, Object value, boolean isSelected, int row, int column) {
            this.row = row;
            this.label = (value == null) ? "" : value.toString();
            button.setText(label);
            button.setEnabled(true);
            isPushed = true;
            return button;
        }

        @Override
        public Object getCellEditorValue() {
            if (isPushed) {
                String sellerName = (String) resultTable.getValueAt(row, 1);
                String sellerId = (String) resultTable.getValueAt(row, 2);

                if ("お気に入りを編集".equals(label)) {
                    showEditFavoritesDialog(sellerId);
                } else {
                    showAddToFavoritesDialog(sellerName, sellerId);
                }
            }
            isPushed = false;
            return label;
        }

        @Override
        public boolean stopCellEditing() {
            isPushed = false;
            return super.stopCellEditing();
        }
    }

    /**
     * セラーをお気に入りに追加するためのダイアログを表示します。
     * @param sellerName セラー名
     * @param sellerId   セラーID
     */
    private void showAddToFavoritesDialog(String sellerName, String sellerId) {
        JDialog dialog = new JDialog((Frame) SwingUtilities.getWindowAncestor(this), "お気に入りに追加", true);
        dialog.setLayout(new MigLayout("fillx,insets 20", "[grow]", "[][][][]push[]"));

        // グループ選択
        dialog.add(new JLabel("グループ:"), "wrap");
        JComboBox<String> groupCombo = new JComboBox<>();
        groupCombo.setEditable(true);
        groupCombo.putClientProperty(FlatClientProperties.PLACEHOLDER_TEXT, "新しいグループ名を入力または選択");
        FavoriteSeller.getAllGroups().forEach(groupCombo::addItem);
        dialog.add(groupCombo, "growx, wrap");

        // タグ入力
        dialog.add(new JLabel("タグ (カンマ区切り):"), "wrap");
        JTextField tagsField = new JTextField();
        tagsField.putClientProperty(FlatClientProperties.PLACEHOLDER_TEXT, "タグ1, タグ2, タグ3");
        TextFieldContextMenuUtil.enableTextFieldContextMenu(tagsField);
        dialog.add(tagsField, "growx, wrap");

        // ボタンパネル
        JButton cancelButton = new JButton("キャンセル");
        cancelButton.addActionListener(e -> dialog.dispose());

        JButton addButton = new JButton("追加");
        addButton.addActionListener(e -> {
            String groupName = Objects.toString(groupCombo.getSelectedItem(), "").trim();
            if (groupName.isEmpty()) {
                JOptionPane.showMessageDialog(dialog, "グループ名を入力してください。", "エラー", JOptionPane.ERROR_MESSAGE);
                return;
            }

            FavoriteSellerModel model = new FavoriteSellerModel(sellerName, sellerId, new Date(), "完了", tagsField.getText().trim(), groupName);
            if (FavoriteSeller.addFavoriteSeller(model) > 0) {
                JOptionPane.showMessageDialog(dialog, "お気に入りに追加しました。", "成功", JOptionPane.INFORMATION_MESSAGE);
                dialog.dispose();
                loadData(); // テーブルを更新
            } else {
                JOptionPane.showMessageDialog(dialog, "お気に入りの追加に失敗しました。", "エラー", JOptionPane.ERROR_MESSAGE);
            }
        });

        JPanel buttonsPanel = new JPanel(new MigLayout("insets 0", "[grow][][]"));
        buttonsPanel.add(new JLabel(), "grow");
        buttonsPanel.add(cancelButton);
        buttonsPanel.add(addButton);
        dialog.add(buttonsPanel, "growx");

        dialog.setSize(400, 250);
        dialog.setLocationRelativeTo(this);
        dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
        dialog.setVisible(true);
    }

    /**
     * お気に入り登録済みのセラー情報を編集するためのダイアログを表示します。
     *
     * @param sellerId 編集対象のセラーID
     */
    private void showEditFavoritesDialog(String sellerId) {
        FavoriteSellerModel existingFavorite = FavoriteSeller.getFavoriteSellerBySellerId(sellerId);
        if (existingFavorite == null) {
            JOptionPane.showMessageDialog(this, "お気に入り情報が見つかりません。", "エラー", JOptionPane.ERROR_MESSAGE);
            return;
        }

        JDialog dialog = new JDialog((Frame) SwingUtilities.getWindowAncestor(this), "お気に入りを編集", true);
        dialog.setLayout(new MigLayout("fillx,insets 20", "[grow]", "[][][][]push[]"));

        // グループ選択
        dialog.add(new JLabel("グループ:"), "wrap");
        JComboBox<String> groupCombo = new JComboBox<>();
        groupCombo.setEditable(true);
        FavoriteSeller.getAllGroups().forEach(groupCombo::addItem);
        groupCombo.setSelectedItem(existingFavorite.getGroupName());
        dialog.add(groupCombo, "growx, wrap");

        // タグ入力
        dialog.add(new JLabel("タグ (カンマ区切り):"), "wrap");
        JTextField tagsField = new JTextField(existingFavorite.getTags());
        TextFieldContextMenuUtil.enableTextFieldContextMenu(tagsField);
        dialog.add(tagsField, "growx, wrap");

        // ボタンパネル
        JButton cancelButton = new JButton("キャンセル");
        cancelButton.addActionListener(e -> dialog.dispose());

        JButton updateButton = new JButton("更新");
        updateButton.addActionListener(e -> {
            String groupName = Objects.toString(groupCombo.getSelectedItem(), "").trim();
            if (groupName.isEmpty()) {
                JOptionPane.showMessageDialog(dialog, "グループ名を入力してください。", "エラー", JOptionPane.ERROR_MESSAGE);
                return;
            }

            existingFavorite.setGroupName(groupName);
            existingFavorite.setTags(tagsField.getText().trim());
            existingFavorite.setLastUpdated(new Date());

            if (FavoriteSeller.updateFavoriteSeller(existingFavorite)) {
                JOptionPane.showMessageDialog(dialog, "お気に入りを更新しました。", "成功", JOptionPane.INFORMATION_MESSAGE);
                dialog.dispose();
                loadData(); // テーブルを更新
            } else {
                JOptionPane.showMessageDialog(dialog, "お気に入りの更新に失敗しました。", "エラー", JOptionPane.ERROR_MESSAGE);
            }
        });

        JPanel buttonsPanel = new JPanel(new MigLayout("insets 0", "[grow][][]"));
        buttonsPanel.add(new JLabel(), "grow");
        buttonsPanel.add(cancelButton);
        buttonsPanel.add(updateButton);
        dialog.add(buttonsPanel, "growx");

        dialog.setSize(400, 250);
        dialog.setLocationRelativeTo(this);
        dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
        dialog.setVisible(true);
    }

    private void setupRightClickMenu() {
        resultTable.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseReleased(java.awt.event.MouseEvent e) {
                if (e.isPopupTrigger()) {
                    int r = resultTable.rowAtPoint(e.getPoint());
                    if (r >= 0 && r < resultTable.getRowCount()) {
                        resultTable.setRowSelectionInterval(r, r);
                    } else {
                        resultTable.clearSelection();
                    }

                    int rowIndex = resultTable.getSelectedRow();
                    showPopupMenu(e, rowIndex);
                }
            }
        });
    }

    private void showPopupMenu(java.awt.event.MouseEvent e, int rowIndex) {
        JPopupMenu popupMenu = new JPopupMenu();

        // --- "リサーチ結果を表示" menu item ---
        JMenuItem showResultsItem = new JMenuItem("リサーチ結果を表示");
        if (rowIndex >= 0) {
            showResultsItem.addActionListener(event -> {
                String sellerId = (String) resultTable.getValueAt(rowIndex, 2);
                firePropertyChange("showSellerResearch", null, sellerId);
            });
        } else {
            showResultsItem.setEnabled(false);
        }
        popupMenu.add(showResultsItem);
        popupMenu.addSeparator();

        // --- Copy menu items (from TableCopyUtil) ---
        JMenuItem copyItem = new JMenuItem("コピー (Ctrl+C)");
        copyItem.addActionListener(event -> ClipboardUtil.copyTableSelection(resultTable));
        if (resultTable.getSelectedRowCount() == 0) {
            copyItem.setEnabled(false);
        }
        popupMenu.add(copyItem);

        JMenuItem copyAllItem = new JMenuItem("すべてコピー");
        copyAllItem.addActionListener(event -> ClipboardUtil.copyAllTableData(resultTable));
        if (resultTable.getRowCount() == 0) {
            copyAllItem.setEnabled(false);
        }
        popupMenu.add(copyAllItem);

        popupMenu.addSeparator();

        JMenuItem selectAllItem = new JMenuItem("すべて選択 (Ctrl+A)");
        selectAllItem.addActionListener(event -> resultTable.selectAll());
        if (resultTable.getRowCount() == 0) {
            selectAllItem.setEnabled(false);
        }
        popupMenu.add(selectAllItem);

        popupMenu.show(e.getComponent(), e.getX(), e.getY());
    }
}
