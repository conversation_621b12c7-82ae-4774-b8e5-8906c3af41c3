package com.mrcresearch.screen.component;

import com.mrcresearch.screen.component.pagination.PaginationComponent;
import com.mrcresearch.screen.component.pagination.PaginationConfig;
import com.mrcresearch.screen.model.ColorModel;
import com.mrcresearch.screen.model.ItemConditionModel;
import com.mrcresearch.screen.model.KeywordSearchResultModel;
import com.mrcresearch.service.common.FontSettings;
import com.mrcresearch.service.ui.KeywordSearchResultService;
import com.mrcresearch.util.ClipboardUtil;
import com.mrcresearch.util.TableTextWrapRenderer;
import com.mrcresearch.util.ThemeIconUtil;
import com.mrcresearch.util.ui.UIStyles;
import net.miginfocom.swing.MigLayout;
import org.kordamp.ikonli.antdesignicons.AntDesignIconsOutlined;

import javax.swing.*;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumnModel;
import java.awt.*;
import java.text.SimpleDateFormat;

/**
 * A panel for displaying keyword search results in a paginated table.
 * It shows the history of searches and their current status.
 */
public class KeywordSearchResultPanel extends JPanel {

    // --- Constants ---
    private static final int PAGE_SIZE = 20;
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    // Column Names
    private static final String[] COLUMN_NAMES = {
            "ID", "キーワード", "カテゴリー", "ページ数", "検索オプション", "更新", "ステータス", "復元"
    };

    // Column Indices
    private static final int COL_ID = 0;
    private static final int COL_KEYWORD = 1;
    private static final int COL_CATEGORY = 2;
    private static final int COL_PAGES = 3;
    private static final int COL_SEARCH_OPTIONS = 4;
    private static final int COL_UPDATED_DATE = 5;
    private static final int COL_STATUS = 6;
    private static final int COL_ACTION = 7;

    // Status Strings
    private static final String STATUS_COMPLETED = "完了";
    private static final String STATUS_RESEARCHING = "リサーチ中";
    private static final String STATUS_WAITING = "待機中";
    private static final String STATUS_ERROR = "エラー";

    // --- UI Components ---
    private JTable resultTable;
    private DefaultTableModel tableModel;
    private PaginationComponent paginationComponent;
    private final KeywordSearchResultService service = new KeywordSearchResultService();

    /**
     * Constructor for the KeywordSearchResultPanel.
     */
    public KeywordSearchResultPanel() {
        init();
        loadData();
    }

    /**
     * Initializes the panel layout and components.
     */
    private void init() {
        setLayout(new MigLayout("fill,insets 10", "[grow]", "[grow]"));
        setupResultsTable();
        initPaginationComponent();
        add(createMainPanel(), "grow");

        setupRightClickMenu();

        // Add component listener to refresh data when panel becomes visible
        addComponentListener(new java.awt.event.ComponentAdapter() {
            @Override
            public void componentShown(java.awt.event.ComponentEvent e) {
                SwingUtilities.invokeLater(() -> loadData());
            }
        });
    }

    /**
     * Sets up the main table for displaying search history.
     */
    private void setupResultsTable() {
        tableModel = new DefaultTableModel(COLUMN_NAMES, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return column == COL_ACTION;
            }

            @Override
            public Class<?> getColumnClass(int columnIndex) {
                return columnIndex == COL_ACTION ? JButton.class : Object.class;
            }
        };

        resultTable = new JTable(tableModel);
        resultTable.setRowHeight(35);
        resultTable.setFont(FontSettings.getTextFont());
        resultTable.getTableHeader().setFont(FontSettings.getSmallerBoldFont());
        resultTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        resultTable.setAutoResizeMode(JTable.AUTO_RESIZE_ALL_COLUMNS);

        configureTableColumns();
        setupTableRenderersAndEditors();

        // TableCopyUtil.enableTableCopy(resultTable);
    }

    /**
     * Configures the widths of the table columns.
     */
    private void configureTableColumns() {
        TableColumnModel cm = resultTable.getColumnModel();
        cm.getColumn(COL_ID).setPreferredWidth(50);
        cm.getColumn(COL_KEYWORD).setPreferredWidth(120);
        cm.getColumn(COL_CATEGORY).setPreferredWidth(150);
        cm.getColumn(COL_PAGES).setPreferredWidth(50);
        cm.getColumn(COL_SEARCH_OPTIONS).setPreferredWidth(300);
        cm.getColumn(COL_UPDATED_DATE).setPreferredWidth(120);
        cm.getColumn(COL_STATUS).setPreferredWidth(70);
        cm.getColumn(COL_ACTION).setPreferredWidth(150);
    }

    /**
     * Sets up the custom cell renderers and editors for the table.
     */
    private void setupTableRenderersAndEditors() {
        // Text wrapping renderers for long text columns
        Font textFont = FontSettings.getTextFont();
        TableTextWrapRenderer leftWrapRenderer = new TableTextWrapRenderer(textFont, JLabel.LEFT, JLabel.CENTER);
        resultTable.getColumnModel().getColumn(COL_KEYWORD).setCellRenderer(leftWrapRenderer);
        resultTable.getColumnModel().getColumn(COL_CATEGORY).setCellRenderer(leftWrapRenderer);
        resultTable.getColumnModel().getColumn(COL_SEARCH_OPTIONS).setCellRenderer(leftWrapRenderer);

        // Center-aligned renderer for numeric and date columns
        DefaultTableCellRenderer centerRenderer = new DefaultTableCellRenderer();
        centerRenderer.setHorizontalAlignment(JLabel.CENTER);
        resultTable.getColumnModel().getColumn(COL_PAGES).setCellRenderer(centerRenderer);
        resultTable.getColumnModel().getColumn(COL_UPDATED_DATE).setCellRenderer(centerRenderer);

        // Custom renderer for the status column
        resultTable.getColumnModel().getColumn(COL_STATUS).setCellRenderer(new StatusCellRenderer());

        // Custom renderer and editor for the action button column
        resultTable.getColumnModel().getColumn(COL_ACTION).setCellRenderer(new ButtonRenderer());
        resultTable.getColumnModel().getColumn(COL_ACTION).setCellEditor(new ButtonEditor(new JCheckBox()));
    }

    /**
     * Initializes the pagination component and its listener.
     */
    private void initPaginationComponent() {
        PaginationConfig config = new PaginationConfig()
                .setShowFirstLastButtons(true)
                .setShowPageSizeSelector(false)
                .setShowTotalInfo(false)
                .setEnableScrollToTop(false);

        paginationComponent = new PaginationComponent(config);
        paginationComponent.setPaginationListener((newPage, pageSize) -> loadData());
    }

    /**
     * Creates the main panel containing the title, table, and pagination controls.
     *
     * @return The main content panel.
     */
    private JPanel createMainPanel() {
        JPanel panel = new JPanel(new MigLayout("fillx,insets 10", "[grow]", "[][grow][]"));

        JLabel titleLabel = new JLabel("検索結果履歴");
        UIStyles.applyTitleStyle(titleLabel);
        UIStyles.setAccessibleName(titleLabel, "検索結果履歴タイトル", "キーワード検索結果の履歴を表示");
        panel.add(titleLabel, "wrap");

        JScrollPane scrollPane = new JScrollPane(resultTable);
        scrollPane.setPreferredSize(new Dimension(0, 300));
        panel.add(scrollPane, "grow, wrap");

        panel.add(paginationComponent, "grow");
        return panel;
    }

    /**
     * Loads data from the database for the current page and populates the table.
     */
    public void loadData() {
        showLoadingIndicator();
        setCursor(Cursor.getPredefinedCursor(Cursor.WAIT_CURSOR));

        SwingWorker<Object, Void> worker = new SwingWorker<Object, Void>() {
            private int totalCount;
            private java.util.List<KeywordSearchResultModel> results;

            @Override
            protected Object doInBackground() {
                totalCount = service.getTotalCount();
                results = service.getPage(paginationComponent.getCurrentPage(), PAGE_SIZE);
                return null;
            }

            @Override
            protected void done() {
                try {
                    get(); // rethrow if background failed
                    tableModel.setRowCount(0);
                    paginationComponent.updatePagination(totalCount, paginationComponent.getCurrentPage());
                    if (results == null || results.isEmpty()) {
                        tableModel.addRow(new Object[]{"No data available", "", "", "", "", "", "", ""});
                    } else {
                        for (KeywordSearchResultModel result : results) {
                            tableModel.addRow(createRowDataFromResult(result));
                        }
                    }
                } catch (Exception e) {
                    System.err.println("ERROR: Failed to load keyword search results: " + e.getMessage());
                    e.printStackTrace();
                    tableModel.setRowCount(0);
                    tableModel.addRow(new Object[]{"Error loading data", e.getMessage(), "", "", "", "", STATUS_ERROR, ""});
                } finally {
                    setCursor(Cursor.getDefaultCursor());
                }
            }
        };
        worker.execute();
    }

    /**
     * Converts a KeywordSearchResultModel into an Object array for the table.
     *
     * @param result The model object to convert.
     * @return An Object array representing a single table row.
     */
    private Object[] createRowDataFromResult(KeywordSearchResultModel result) {
        return new Object[]{
                result.getId(),
                result.getKeyword(),
                service.convertCategoryDisplayName(result.getCategories()),
                result.getPages() > 0 ? String.valueOf(result.getPages()) : "",
                formatSearchOptions(result),
                DATE_FORMAT.format(result.getUpdatedDate()),
                result.getResearchStatus(),
                "復元する" // Action button text
        };
    }

    /**
     * Displays a loading indicator in the table.
     */
    public void showLoadingIndicator() {
        tableModel.setRowCount(0);
        tableModel.addRow(new Object[]{"", "ロード中...", "", "", "", "", STATUS_RESEARCHING, ""});
    }

    /**
     * Adds a new keyword search result to the database and refreshes the table.
     *
     * @return The ID of the saved record, or -1 on failure.
     */
    public int addKeywordSearchResultWithDetailedOptions(String keyword, String categories, String researchStatus,
                                                         int minPrice, int maxPrice, int pages,
                                                         boolean sortByNew, boolean ignoreShops, String salesStatus,
                                                         String itemConditionIds, String excludeKeyword, String colorIds,
                                                         String categoryId) {
        KeywordSearchResultModel model = new KeywordSearchResultModel();
        model.setKeyword(keyword);
        model.setCategories(categories);
        model.setStatus(STATUS_WAITING);
        model.setResearchStatus(researchStatus);
        model.setMinPrice(minPrice);
        model.setMaxPrice(maxPrice);
        model.setPages(pages);
        model.setSortByNew(sortByNew);
        model.setIgnoreShops(ignoreShops);
        model.setSalesStatus(salesStatus);
        model.setItemConditionIds(itemConditionIds);
        model.setExcludeKeyword(excludeKeyword);
        model.setColorIds(colorIds);
        model.setCategoryId(categoryId);

        java.util.Date now = new java.util.Date();
        model.setAddedDate(now);
        model.setUpdatedDate(now);

        int recordId = service.saveResult(model);
        if (recordId > 0) {
            SwingUtilities.invokeLater(this::loadData);
        } else {
            System.err.println("ERROR: Failed to save keyword search result with detailed options to database");
        }
        return recordId;
    }

    /**
     * Applies theme changes to the component and its children.
     */
    public void applyThemeChange() {
        if (paginationComponent != null) {
            paginationComponent.applyThemeChange();
        }
        SwingUtilities.invokeLater(() -> {
            loadData(); // Reload data to update icons with new theme colors
            if (resultTable != null) {
                resultTable.revalidate();
                resultTable.repaint();
            }
        });
        repaint();
    }

    // --- Data Formatting Helpers ---

    /**
     * Formats search options into a single string for display.
     *
     * @param result The KeywordSearchResultModel containing search options.
     * @return A formatted string containing all active search options.
     */
    private String formatSearchOptions(KeywordSearchResultModel result) {
        StringBuilder options = new StringBuilder();

        // Price range
        if (result.getMinPrice() > 0 || result.getMaxPrice() > 0) {
            if (options.length() > 0) options.append(", ");
            options.append("価格:").append(result.getMinPrice()).append("-").append(result.getMaxPrice()).append("円");
        }

        // Sort by new
        if (result.isSortByNew()) {
            if (options.length() > 0) options.append(", ");
            options.append("新着順");
        }

        // Ignore shops
        if (result.isIgnoreShops()) {
            if (options.length() > 0) options.append(", ");
            options.append("個人のみ");
        }

        // Sales status
        if (result.getSalesStatus() != null && !result.getSalesStatus().equals("すべて")) {
            if (options.length() > 0) options.append(", ");
            options.append(result.getSalesStatus());
        }

        // Item conditions
        String itemConditions = formatItemConditions(result.getItemConditionIds());
        if (!itemConditions.isEmpty()) {
            if (options.length() > 0) options.append(", ");
            options.append("状態:").append(itemConditions);
        }

        // Exclude keywords
        if (result.getExcludeKeyword() != null && !result.getExcludeKeyword().trim().isEmpty()) {
            if (options.length() > 0) options.append(", ");
            options.append("除外:").append(result.getExcludeKeyword());
        }

        // Colors
        String colors = formatColors(result.getColorIds());
        if (!colors.isEmpty()) {
            if (options.length() > 0) options.append(", ");
            options.append("色:").append(colors);
        }

        return options.toString();
    }

    private String formatItemConditions(String ids) {
        if (ids == null || ids.trim().isEmpty()) return "";
        return formatNamesFromIds(ids, ItemConditionModel.getAllConditions());
    }

    private String formatColors(String ids) {
        if (ids == null || ids.trim().isEmpty()) return "";
        return formatNamesFromIds(ids, ColorModel.getAllColors());
    }

    private String formatNamesFromIds(String ids, ItemConditionModel[] models) {
        StringBuilder result = new StringBuilder();
        String[] idArray = ids.split(",");
        for (String idStr : idArray) {
            try {
                int id = Integer.parseInt(idStr.trim());
                for (ItemConditionModel model : models) {
                    if (model.getId() == id) {
                        if (result.length() > 0) result.append(", ");
                        result.append(model.getName());
                        break;
                    }
                }
            } catch (NumberFormatException e) { /* ignore */ }
        }
        return result.toString();
    }

    private String formatNamesFromIds(String ids, ColorModel[] models) {
        StringBuilder result = new StringBuilder();
        String[] idArray = ids.split(",");
        for (String idStr : idArray) {
            try {
                int id = Integer.parseInt(idStr.trim());
                for (ColorModel model : models) {
                    if (model.getId() == id) {
                        if (result.length() > 0) result.append(", ");
                        result.append(model.getName());
                        break;
                    }
                }
            } catch (NumberFormatException e) { /* ignore */ }
        }
        return result.toString();
    }

    private String convertItemConditionsToIds(String names) {
        if (names == null || names.trim().isEmpty()) return null;
        return convertNamesToIds(names, ItemConditionModel.getAllConditions());
    }

    private String convertColorsToIds(String names) {
        if (names == null || names.trim().isEmpty()) return null;
        return convertNamesToIds(names, ColorModel.getAllColors());
    }

    private String convertNamesToIds(String names, ItemConditionModel[] models) {
        StringBuilder result = new StringBuilder();
        String[] nameArray = names.split(",");
        for (String name : nameArray) {
            String trimmedName = name.trim();
            for (ItemConditionModel model : models) {
                if (model.getName().equals(trimmedName)) {
                    if (result.length() > 0) result.append(",");
                    result.append(model.getId());
                    break;
                }
            }
        }
        return result.length() > 0 ? result.toString() : null;
    }

    private String convertNamesToIds(String names, ColorModel[] models) {
        StringBuilder result = new StringBuilder();
        String[] nameArray = names.split(",");
        for (String name : nameArray) {
            String trimmedName = name.trim();
            for (ColorModel model : models) {
                if (model.getName().equals(trimmedName)) {
                    if (result.length() > 0) result.append(",");
                    result.append(model.getId());
                    break;
                }
            }
        }
        return result.length() > 0 ? result.toString() : null;
    }

    // --- Inner Classes for Table Rendering and Editing ---

    /**
     * Custom renderer for the status column to display icons and colors.
     */
    private static class StatusCellRenderer extends DefaultTableCellRenderer {
        @Override
        public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
            Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
            if (value instanceof String) {
                String status = (String) value;
                switch (status) {
                    case STATUS_COMPLETED:
                        setIcon(ThemeIconUtil.ofStatus(AntDesignIconsOutlined.CHECK_CIRCLE, 16, new Color(0, 128, 0)));
                        setForeground(new Color(0, 128, 0));
                        break;
                    case STATUS_RESEARCHING:
                        setIcon(ThemeIconUtil.ofStatus(AntDesignIconsOutlined.HOURGLASS, 16, new Color(204, 204, 0)));
                        setForeground(new Color(204, 204, 0));
                        break;
                    case STATUS_WAITING:
                    case STATUS_ERROR:
                        setIcon(ThemeIconUtil.ofStatus(AntDesignIconsOutlined.EXCLAMATION_CIRCLE, 16, Color.RED));
                        setForeground(Color.RED);
                        break;
                    default:
                        setIcon(null);
                        setForeground(table.getForeground());
                        break;
                }
            } else {
                setIcon(null);
            }
            return c;
        }
    }

    /**
     * Custom renderer for action buttons in the table.
     */
    private static class ButtonRenderer extends JButton implements javax.swing.table.TableCellRenderer {
        public ButtonRenderer() {
            setOpaque(true);
        }

        @Override
        public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
            setText((value == null) ? "" : value.toString());
            setBackground(new Color(70, 150, 70)); // Green background
            setForeground(Color.WHITE);
            return this;
        }
    }

    /**
     * Custom editor for handling button clicks in the action column.
     */
    private class ButtonEditor extends DefaultCellEditor {
        private final JButton button;
        private String label;
        private int row;

        public ButtonEditor(JCheckBox checkBox) {
            super(checkBox);
            button = new JButton();
            button.setOpaque(true);
            button.addActionListener(e -> fireEditingStopped());
        }

        @Override
        public Component getTableCellEditorComponent(JTable table, Object value, boolean isSelected, int row, int column) {
            this.row = row;
            this.label = (value == null) ? "" : value.toString();
            button.setText(label);
            return button;
        }

        @Override
        public Object getCellEditorValue() {
            handleButtonClick();
            return label;
        }

        private void handleButtonClick() {
            int result = JOptionPane.showConfirmDialog(KeywordSearchResultPanel.this, "復元しますか？", "復元を実行", JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);
            if (result != JOptionPane.YES_OPTION) {
                return;
            }

            int id = (int) resultTable.getValueAt(row, COL_ID);

            // Get the original data from the service using the ID
            KeywordSearchResultModel originalResult = service.getResultById(id);
            if (originalResult == null) {
                JOptionPane.showMessageDialog(KeywordSearchResultPanel.this, "データの取得に失敗しました。", "エラー", JOptionPane.ERROR_MESSAGE);
                return;
            }

            String keyword = originalResult.getKeyword();
            String categories = originalResult.getCategories();
            int minPrice = originalResult.getMinPrice();
            int maxPrice = originalResult.getMaxPrice();
            int pages = originalResult.getPages();
            boolean sortByNew = originalResult.isSortByNew();
            boolean ignoreShops = originalResult.isIgnoreShops();
            String salesStatus = originalResult.getSalesStatus();
            String itemConditionIds = originalResult.getItemConditionIds();
            String excludeKeyword = originalResult.getExcludeKeyword();
            String colorIds = originalResult.getColorIds();

            firePropertyChange("restoreSearch", null, new Object[]{
                    keyword, categories, minPrice, maxPrice, pages, sortByNew, ignoreShops, salesStatus,
                    itemConditionIds, excludeKeyword, colorIds, categories
            });
        }
    }

    private void setupRightClickMenu() {
        resultTable.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseReleased(java.awt.event.MouseEvent e) {
                if (e.isPopupTrigger()) {
                    int r = resultTable.rowAtPoint(e.getPoint());
                    if (r >= 0 && r < resultTable.getRowCount()) {
                        resultTable.setRowSelectionInterval(r, r);
                    } else {
                        resultTable.clearSelection();
                    }

                    int rowIndex = resultTable.getSelectedRow();
                    showPopupMenu(e, rowIndex);
                }
            }
        });
    }

    private void showPopupMenu(java.awt.event.MouseEvent e, int rowIndex) {
        JPopupMenu popupMenu = new JPopupMenu();

        // --- "リサーチ結果を表示" menu item ---
        JMenuItem showResultsItem = new JMenuItem("リサーチ結果を表示");
        if (rowIndex >= 0) {
            showResultsItem.addActionListener(event -> {
                int id = (int) resultTable.getValueAt(rowIndex, COL_ID);
                String keyword = (String) resultTable.getValueAt(rowIndex, COL_KEYWORD);
                firePropertyChange("showProductResearch", null, new Object[]{id, keyword});
            });
        } else {
            showResultsItem.setEnabled(false);
        }
        popupMenu.add(showResultsItem);
        popupMenu.addSeparator();

        // --- Copy menu items (from TableCopyUtil) ---
        JMenuItem copyItem = new JMenuItem("コピー (Ctrl+C)");
        copyItem.addActionListener(event -> ClipboardUtil.copyTableSelection(resultTable));
        if (resultTable.getSelectedRowCount() == 0) {
            copyItem.setEnabled(false);
        }
        popupMenu.add(copyItem);

        JMenuItem copyAllItem = new JMenuItem("すべてコピー");
        copyAllItem.addActionListener(event -> ClipboardUtil.copyAllTableData(resultTable));
        if (resultTable.getRowCount() == 0) {
            copyAllItem.setEnabled(false);
        }
        popupMenu.add(copyAllItem);

        popupMenu.addSeparator();

        JMenuItem selectAllItem = new JMenuItem("すべて選択 (Ctrl+A)");
        selectAllItem.addActionListener(event -> resultTable.selectAll());
        if (resultTable.getRowCount() == 0) {
            selectAllItem.setEnabled(false);
        }
        popupMenu.add(selectAllItem);

        popupMenu.show(e.getComponent(), e.getX(), e.getY());
    }
}
