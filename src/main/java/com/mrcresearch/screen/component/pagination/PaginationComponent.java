package com.mrcresearch.screen.component.pagination;

import com.mrcresearch.service.common.FontSettings;
import com.mrcresearch.util.ThemeIconUtil;
import net.miginfocom.swing.MigLayout;
import org.kordamp.ikonli.antdesignicons.AntDesignIconsOutlined;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;

/**
 * Reusable pagination component with theme support.
 * Based on the keyword search result history implementation.
 */
public class PaginationComponent extends JPanel {

    // Configuration
    private final PaginationConfig config;
    private PaginationListener listener;

    // State
    private int currentPage = 1;
    private int totalPages = 1;
    private int totalCount = 0;
    private int pageSize;

    // UI Components
    private JButton btnFirstPage;
    private JButton btnPrevPage;
    private JButton btnNextPage;
    private JButton btnLastPage;
    private JLabel lblPageInfo;
    private JLabel lblTotalInfo;
    private JComboBox<String> cmbPageSize;

    // Scroll component reference for scroll-to-top functionality
    private JScrollPane scrollPane;

    /**
     * Constructor with default configuration
     */
    public PaginationComponent() {
        this(PaginationConfig.createDefault());
    }

    /**
     * Constructor with custom configuration
     */
    public PaginationComponent(PaginationConfig config) {
        this.config = config;
        this.pageSize = config.getDefaultPageSize();
        initializeComponents();
        setupLayout();
        updateControls();
    }

    /**
     * Initialize UI components
     */
    private void initializeComponents() {
        // First page button
        if (config.isShowFirstLastButtons()) {
            btnFirstPage = new JButton(ThemeIconUtil.ofButton(AntDesignIconsOutlined.STEP_BACKWARD, 16));
            btnFirstPage.setText(config.getFirstButtonText());
            btnFirstPage.addActionListener(this::onFirstPageClicked);
        }

        // Previous page button
        btnPrevPage = new JButton(ThemeIconUtil.ofButton(AntDesignIconsOutlined.LEFT, 16));
        btnPrevPage.setText(config.getPrevButtonText());
        btnPrevPage.addActionListener(this::onPrevPageClicked);

        // Page info label
        lblPageInfo = new JLabel("1 / 1");
        lblPageInfo.setFont(FontSettings.getTextFont());

        // Next page button
        btnNextPage = new JButton(ThemeIconUtil.ofButton(AntDesignIconsOutlined.RIGHT, 16));
        btnNextPage.setText(config.getNextButtonText());
        btnNextPage.addActionListener(this::onNextPageClicked);

        // Last page button
        if (config.isShowFirstLastButtons()) {
            btnLastPage = new JButton(ThemeIconUtil.ofButton(AntDesignIconsOutlined.STEP_FORWARD, 16));
            btnLastPage.setText(config.getLastButtonText());
            btnLastPage.addActionListener(this::onLastPageClicked);
        }

        // Total info label
        if (config.isShowTotalInfo()) {
            lblTotalInfo = new JLabel("0件");
            lblTotalInfo.setFont(FontSettings.getSmallestTextFont());
        }

        // Page size selector
        if (config.isShowPageSizeSelector()) {
            String[] pageSizeOptions = new String[config.getPageSizeOptions().length];
            for (int i = 0; i < config.getPageSizeOptions().length; i++) {
                pageSizeOptions[i] = config.getPageSizeOptions()[i] + "件";
            }
            cmbPageSize = new JComboBox<>(pageSizeOptions);

            // Set default selection
            for (int i = 0; i < config.getPageSizeOptions().length; i++) {
                if (config.getPageSizeOptions()[i] == config.getDefaultPageSize()) {
                    cmbPageSize.setSelectedIndex(i);
                    break;
                }
            }

            cmbPageSize.addActionListener(this::onPageSizeChanged);
        }
    }

    /**
     * Setup component layout
     */
    private void setupLayout() {
        setLayout(new MigLayout("insets 0", "[]push[]push[]", ""));

        // Left side - Total info (if enabled)
        if (config.isShowTotalInfo()) {
            add(lblTotalInfo, "");
        } else {
            add(new JLabel(), ""); // Empty spacer
        }

        // Center - Pagination controls
        JPanel paginationPanel = new JPanel(new MigLayout("insets 0", "[]5[]5[]5[]5[]", ""));

        if (config.isShowFirstLastButtons()) {
            paginationPanel.add(btnFirstPage);
        }
        paginationPanel.add(btnPrevPage);
        paginationPanel.add(lblPageInfo);
        paginationPanel.add(btnNextPage);
        if (config.isShowFirstLastButtons()) {
            paginationPanel.add(btnLastPage);
        }

        add(paginationPanel, "");

        // Right side - Page size selector (if enabled)
        if (config.isShowPageSizeSelector()) {
            add(cmbPageSize, "");
        } else {
            add(new JLabel(), ""); // Empty spacer
        }
    }

    /**
     * Event handlers
     */
    private void onFirstPageClicked(ActionEvent e) {
        if (currentPage > 1) {
            setCurrentPage(1);
            notifyPageChanged();
            scrollToTopIfEnabled();
        }
    }

    private void onPrevPageClicked(ActionEvent e) {
        if (currentPage > 1) {
            setCurrentPage(currentPage - 1);
            notifyPageChanged();
            scrollToTopIfEnabled();
        }
    }

    private void onNextPageClicked(ActionEvent e) {
        if (currentPage < totalPages) {
            setCurrentPage(currentPage + 1);
            notifyPageChanged();
            scrollToTopIfEnabled();
        }
    }

    private void onLastPageClicked(ActionEvent e) {
        if (currentPage < totalPages) {
            setCurrentPage(totalPages);
            notifyPageChanged();
            scrollToTopIfEnabled();
        }
    }

    private void onPageSizeChanged(ActionEvent e) {
        if (cmbPageSize != null) {
            String selected = (String) cmbPageSize.getSelectedItem();
            int newPageSize = Integer.parseInt(selected.replace("件", ""));
            if (newPageSize != pageSize) {
                pageSize = newPageSize;
                setCurrentPage(1); // Reset to first page when page size changes
                notifyPageSizeChanged();
                scrollToTopIfEnabled();
            }
        }
    }

    /**
     * Public API methods
     */

    /**
     * Set the pagination listener
     */
    public void setPaginationListener(PaginationListener listener) {
        this.listener = listener;
    }

    /**
     * Update pagination with new data
     */
    public void updatePagination(int totalCount, int currentPage) {
        this.totalCount = totalCount;
        this.totalPages = (int) Math.ceil((double) totalCount / pageSize);
        if (this.totalPages == 0) this.totalPages = 1;

        // Adjust current page if needed
        if (currentPage > this.totalPages) {
            currentPage = this.totalPages;
        }

        setCurrentPage(currentPage);
        updateControls();
    }

    /**
     * Get current page number
     */
    public int getCurrentPage() {
        return currentPage;
    }

    /**
     * Get current page size
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * Set scroll pane for scroll-to-top functionality
     */
    public void setScrollPane(JScrollPane scrollPane) {
        this.scrollPane = scrollPane;
    }

    /**
     * Apply theme changes to the component
     */
    public void applyThemeChange() {
        // Update button icons with new theme colors
        if (btnFirstPage != null) {
            btnFirstPage.setIcon(ThemeIconUtil.ofButton(AntDesignIconsOutlined.STEP_BACKWARD, 16));
        }
        if (btnPrevPage != null) {
            btnPrevPage.setIcon(ThemeIconUtil.ofButton(AntDesignIconsOutlined.LEFT, 16));
        }
        if (btnNextPage != null) {
            btnNextPage.setIcon(ThemeIconUtil.ofButton(AntDesignIconsOutlined.RIGHT, 16));
        }
        if (btnLastPage != null) {
            btnLastPage.setIcon(ThemeIconUtil.ofButton(AntDesignIconsOutlined.STEP_FORWARD, 16));
        }

        // Update label colors based on theme
        Color textColor = ThemeIconUtil.getThemeIconColor();
        if (lblPageInfo != null) {
            lblPageInfo.setForeground(textColor);
        }
        if (lblTotalInfo != null) {
            lblTotalInfo.setForeground(textColor);
        }

        // Repaint the component
        repaint();
    }

    /**
     * Private helper methods
     */

    public void setCurrentPage(int page) {
        this.currentPage = page;
        updateControls();
    }

    private void updateControls() {
        // Update page info
        if (lblPageInfo != null) {
            lblPageInfo.setText(currentPage + " / " + totalPages);
        }

        // Update total info
        if (lblTotalInfo != null) {
            updateTotalInfo();
        }

        // Update button states
        if (btnFirstPage != null) {
            btnFirstPage.setEnabled(currentPage > 1);
        }
        if (btnPrevPage != null) {
            btnPrevPage.setEnabled(currentPage > 1);
        }
        if (btnNextPage != null) {
            btnNextPage.setEnabled(currentPage < totalPages);
        }
        if (btnLastPage != null) {
            btnLastPage.setEnabled(currentPage < totalPages);
        }
    }

    private void updateTotalInfo() {
        if (lblTotalInfo == null) return;

        int startItem = (currentPage - 1) * pageSize + 1;
        int endItem = Math.min(currentPage * pageSize, totalCount);

        if (totalCount == 0) {
            lblTotalInfo.setText("0件");
        } else {
            lblTotalInfo.setText(startItem + "-" + endItem + "件 / 全" + totalCount + "件");
        }
    }

    private void notifyPageChanged() {
        if (listener != null) {
            listener.onPageChanged(currentPage, pageSize);
        }
    }

    private void notifyPageSizeChanged() {
        if (listener != null) {
            listener.onPageSizeChanged(pageSize, currentPage);
        }
    }

    private void scrollToTopIfEnabled() {
        if (config.isEnableScrollToTop() && scrollPane != null) {
            SwingUtilities.invokeLater(() -> {
                scrollPane.getVerticalScrollBar().setValue(0);
                scrollPane.getHorizontalScrollBar().setValue(0);
            });
        }
    }
}
