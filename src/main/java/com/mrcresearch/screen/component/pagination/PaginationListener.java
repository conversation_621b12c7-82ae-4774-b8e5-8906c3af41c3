package com.mrcresearch.screen.component.pagination;

/**
 * Interface for handling pagination events.
 */
public interface PaginationListener {

    /**
     * Called when the page changes.
     *
     * @param newPage  The new page number (1-based)
     * @param pageSize The current page size
     */
    void onPageChanged(int newPage, int pageSize);

    /**
     * Called when the page size changes.
     *
     * @param newPageSize The new page size
     * @param currentPage The current page number (will be reset to 1)
     */
    default void onPageSizeChanged(int newPageSize, int currentPage) {
        // Default implementation - just call onPageChanged with page 1
        onPageChanged(1, newPageSize);
    }
}
