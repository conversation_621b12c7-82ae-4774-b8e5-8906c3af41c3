package com.mrcresearch.screen.component.pagination;

/**
 * Configuration class for pagination component.
 */
public class PaginationConfig {

    // Fields
    private boolean showFirstLastButtons = true;
    private boolean showPageSizeSelector = false;
    private int[] pageSizeOptions = {50, 100, 200, 300};
    private int defaultPageSize = 50;
    private boolean showTotalInfo = true;
    private boolean enableScrollToTop = true;
    private String firstButtonText = "最初へ";
    private String prevButtonText = "前へ";
    private String nextButtonText = "次へ";
    private String lastButtonText = "最後へ";

    // Constructor
    public PaginationConfig() {
    }

    // Getters
    public boolean isShowFirstLastButtons() {
        return showFirstLastButtons;
    }

    public boolean isShowPageSizeSelector() {
        return showPageSizeSelector;
    }

    public int[] getPageSizeOptions() {
        return pageSizeOptions;
    }

    public int getDefaultPageSize() {
        return defaultPageSize;
    }

    public boolean isShowTotalInfo() {
        return showTotalInfo;
    }

    public boolean isEnableScrollToTop() {
        return enableScrollToTop;
    }

    public String getFirstButtonText() {
        return firstButtonText;
    }

    public String getPrevButtonText() {
        return prevButtonText;
    }

    public String getNextButtonText() {
        return nextButtonText;
    }

    public String getLastButtonText() {
        return lastButtonText;
    }

    // Setters
    public PaginationConfig setShowFirstLastButtons(boolean showFirstLastButtons) {
        this.showFirstLastButtons = showFirstLastButtons;
        return this;
    }

    public PaginationConfig setShowPageSizeSelector(boolean showPageSizeSelector) {
        this.showPageSizeSelector = showPageSizeSelector;
        return this;
    }

    public PaginationConfig setPageSizeOptions(int[] pageSizeOptions) {
        this.pageSizeOptions = pageSizeOptions;
        return this;
    }

    public PaginationConfig setDefaultPageSize(int defaultPageSize) {
        this.defaultPageSize = defaultPageSize;
        return this;
    }

    public PaginationConfig setShowTotalInfo(boolean showTotalInfo) {
        this.showTotalInfo = showTotalInfo;
        return this;
    }

    public PaginationConfig setEnableScrollToTop(boolean enableScrollToTop) {
        this.enableScrollToTop = enableScrollToTop;
        return this;
    }

    public PaginationConfig setFirstButtonText(String firstButtonText) {
        this.firstButtonText = firstButtonText;
        return this;
    }

    public PaginationConfig setPrevButtonText(String prevButtonText) {
        this.prevButtonText = prevButtonText;
        return this;
    }

    public PaginationConfig setNextButtonText(String nextButtonText) {
        this.nextButtonText = nextButtonText;
        return this;
    }

    public PaginationConfig setLastButtonText(String lastButtonText) {
        this.lastButtonText = lastButtonText;
        return this;
    }

    /**
     * Create a default configuration for basic pagination
     */
    public static PaginationConfig createDefault() {
        return new PaginationConfig();
    }

    /**
     * Create a configuration for simple pagination (no first/last buttons)
     */
    public static PaginationConfig createSimple() {
        return new PaginationConfig()
                .setShowFirstLastButtons(false);
    }

    /**
     * Create a configuration with page size selector
     */
    public static PaginationConfig createWithPageSizeSelector() {
        return new PaginationConfig()
                .setShowPageSizeSelector(true);
    }

    /**
     * Create a configuration for aggregation screens
     */
    public static PaginationConfig createForAggregation() {
        return new PaginationConfig()
                .setShowPageSizeSelector(true)
                .setPageSizeOptions(new int[]{50, 100, 200, 300})
                .setDefaultPageSize(50);
    }

    /**
     * Create a configuration for seller research results
     */
    public static PaginationConfig createForSellerResearch() {
        return new PaginationConfig()
                .setShowFirstLastButtons(true)
                .setShowPageSizeSelector(false)
                .setShowTotalInfo(false)
                .setEnableScrollToTop(true)
                .setDefaultPageSize(20); // Seller research uses 20 items per page
    }

    /**
     * Create a configuration for keyword search items
     */
    public static PaginationConfig createForKeywordSearchItems() {
        return new PaginationConfig()
                .setShowFirstLastButtons(false) // Simple pagination for items
                .setShowPageSizeSelector(false)
                .setShowTotalInfo(true) // Show total info for items
                .setEnableScrollToTop(true)
                .setDefaultPageSize(50); // Keyword search items use 50 items per page
    }
}
