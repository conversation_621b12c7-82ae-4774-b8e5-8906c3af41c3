package com.mrcresearch.screen.seller;

import com.formdev.flatlaf.FlatClientProperties;
import com.mrcresearch.model.ProgressItem;
import com.mrcresearch.screen.component.SellerResearchResultPanel;
import com.mrcresearch.service.analyze.service.SearchBySeller;
import com.mrcresearch.service.common.FontSettings;
import com.mrcresearch.util.SearchQueueManager;
import com.mrcresearch.util.TextFieldContextMenuUtil;
import com.mrcresearch.util.ThemeIconUtil;
import net.miginfocom.swing.MigLayout;
import org.kordamp.ikonli.antdesignicons.AntDesignIconsOutlined;
import org.kordamp.ikonli.swing.FontIcon;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Seller extends JPanel {
    private static final Logger logger = LoggerFactory.getLogger(Seller.class);
    private JPanel panel;
    private JTextField txtSellerId;
    private JButton btnAdd;
    private JButton btnClear;
    private JButton btnStart;
    private JPanel sellerListPanel;
    private List<String> sellerIds;
    private SellerResearchResultPanel researchResultPanel;
    private JTabbedPane tabbedPane;

    public Seller() {
        init();
        setVisible(false); // Initially not visible
    }

    private void init() {
        setLayout(new MigLayout("fill,insets 20", "[grow]", "[grow]"));

        // Initialize components
        txtSellerId = new JTextField();
        btnAdd = new JButton(ThemeIconUtil.ofButton(AntDesignIconsOutlined.PLUS, 16));
        btnClear = new JButton(ThemeIconUtil.ofButton(AntDesignIconsOutlined.CLEAR, 16));
        btnStart = new JButton(ThemeIconUtil.ofButton(AntDesignIconsOutlined.PLAY_CIRCLE, 16));
        sellerIds = new ArrayList<>();

        // Enable context menu for text field
        TextFieldContextMenuUtil.enableTextFieldContextMenu(txtSellerId);

        // Add additional paste handling for seller ID field
        addSellerIdPasteHandling(txtSellerId);

        // Create tabbed pane for input and results
        tabbedPane = new JTabbedPane();
        tabbedPane.putClientProperty(FlatClientProperties.TABBED_PANE_TAB_HEIGHT, 40);
        tabbedPane.putClientProperty(FlatClientProperties.STYLE, "font:+2");

        // Input panel tab
        JPanel inputTab = new JPanel(new MigLayout("fill", "[grow]", "[grow]"));

        // Main panel
        panel = new JPanel(new MigLayout("wrap 2,fillx,insets 35 45 30 45", "[fill,200:220][fill,200:220]"));
        panel.putClientProperty(FlatClientProperties.STYLE, "arc:20;");

        // Seller ID input
        JLabel sellerIdLabel = new JLabel("セラーID:");
        sellerIdLabel.setFont(FontSettings.getLabelFont());
        panel.add(sellerIdLabel, "cell 0 1, gaptop 10");

        // Create a panel for the input field and add button
        JPanel inputPanel = new JPanel(new MigLayout("insets 0", "[grow][] "));
        txtSellerId.putClientProperty(FlatClientProperties.PLACEHOLDER_TEXT, "セラーIDを入力");
        txtSellerId.setFont(FontSettings.getLabelFont());
        inputPanel.add(txtSellerId, "grow");

        btnAdd.setText("追加");
        btnAdd.setFont(FontSettings.getButtonFont());
        inputPanel.add(btnAdd);

        panel.add(inputPanel, "cell 0 2, grow");

        // Seller list panel
        JLabel sellerListLabel = new JLabel("追加したセラーID:");
        sellerListLabel.setFont(FontSettings.getLabelFont());
        panel.add(sellerListLabel, "cell 0 3, gaptop 10, span 2");
        sellerListPanel = new JPanel(new MigLayout("wrap,fillx,insets 0", "fill"));
        sellerListPanel.setBorder(BorderFactory.createLineBorder(Color.LIGHT_GRAY));

        // Add an empty message when no sellers are added
        JLabel emptyLabel = new JLabel("セラーIDが追加されていません");
        emptyLabel.setFont(FontSettings.getLabelFont());
        emptyLabel.setForeground(Color.GRAY);
        emptyLabel.setHorizontalAlignment(SwingConstants.CENTER);
        sellerListPanel.add(emptyLabel, "align center, gaptop 10, gapbottom 10");

        JScrollPane scrollPane = new JScrollPane(sellerListPanel);
        scrollPane.setBorder(BorderFactory.createEmptyBorder());
        scrollPane.setPreferredSize(new Dimension(0, 200));
        panel.add(scrollPane, "grow, span 2");

        // Configure and add clear button (moved to left)
        btnClear.setText("クリア");
        btnClear.setFont(FontSettings.getButtonFont());
        panel.add(btnClear, "cell 0 5, gaptop 10, align left");

        // Configure and add start button (moved to right)
        btnStart.setText("スタート");
        btnStart.setFont(FontSettings.getButtonFont());
        panel.add(btnStart, "cell 1 5, gaptop 10, align right");

        // Add action listeners to buttons
        btnAdd.addActionListener(e -> addSellerId());
        btnClear.addActionListener(e -> clearSellerIds());
        btnStart.addActionListener(e -> {
            if (sellerIds.isEmpty()) {
                // Silently return without showing popup
                return;
            }

            // Create individual seller search tasks for sequential processing
            for (int i = 0; i < sellerIds.size(); i++) {
                final String sellerId = sellerIds.get(i);
                final int sellerIndex = i + 1;
                final int totalSellers = sellerIds.size();

                SearchQueueManager.SearchTask sellerSearchTask = new SearchQueueManager.SearchTask() {
                    @Override
                    public void execute() throws Exception {
                        // Execute the research for this specific seller
                        startIndividualSellerResearch(sellerId, sellerIndex, totalSellers);
                    }

                    @Override
                    public String getTaskName() {
                        return "セラー検索: " + sellerId + " (" + sellerIndex + "/" + totalSellers + ")";
                    }

                    @Override
                    public String getTaskType() {
                        return "SELLER";
                    }

                    @Override
                    public int getEstimatedTimeSeconds() {
                        // Estimate 30 seconds per seller
                        return 30;
                    }

                    @Override
                    public int getPriority() {
                        return 90; // Slightly higher priority than keyword search
                    }

                    @Override
                    public String getDetailedDescription() {
                        return sellerId;
                    }

                    @Override
                    public int getItemCount() {
                        return 1;
                    }

                    @Override
                    public boolean hasAsyncOperations() {
                        return false; // Seller search completes synchronously
                    }

                    @Override
                    public void onTaskCompleted() {
                        logger.info("セラー: {} の個別セラー検索タスクが完了しました", sellerId);
                    }
                };

                // Add task to queue
                SearchQueueManager.getInstance().addTask(sellerSearchTask);
            }

            // Clear the seller ID list after adding all tasks to queue
            SwingUtilities.invokeLater(() -> {
                clearSellerIds();
                // Switch to the results tab (index 1)
                tabbedPane.setSelectedIndex(1);
            });
        });

        // Wrap panel in a scroll pane and add to input tab
        JScrollPane scrollablePanel = new JScrollPane(panel);
        scrollablePanel.setBorder(BorderFactory.createEmptyBorder());
        scrollablePanel.getVerticalScrollBar().setUnitIncrement(16);
        inputTab.add(scrollablePanel, "grow");

        // Results tab
        JPanel resultsTab = new JPanel(new MigLayout("fill", "[grow]", "[grow]"));

        // Initialize research result panel
        researchResultPanel = new SellerResearchResultPanel();

        // Initialize with sample data
        researchResultPanel.initWithSampleData();

        // Add to results tab
        resultsTab.add(researchResultPanel, "grow");
        researchResultPanel.addPropertyChangeListener("showSellerResearch", this::handleShowSellerResearch);

        // Add tabs to tabbed pane
        tabbedPane.addTab("セラーID入力", inputTab);
        tabbedPane.addTab("履歴", resultsTab);

        // Add tab change listener to refresh data when switching to results tab
        tabbedPane.addChangeListener(e -> {
            int selectedIndex = tabbedPane.getSelectedIndex();
            if (selectedIndex == 1) { // Results tab
                researchResultPanel.loadData(); // 最新データを取得
            }
        });

        // Add tabbed pane to main panel
        add(tabbedPane, "grow");
    }

    private void addSellerId() {
        String sellerId = txtSellerId.getText().trim();
        if (!sellerId.isEmpty() && !sellerIds.contains(sellerId)) {
            sellerIds.add(sellerId);
            updateSellerList();
            txtSellerId.setText("");
        } else if (sellerIds.contains(sellerId)) {
            // Silently ignore duplicate seller IDs without showing popup
        }
    }

    private void updateSellerList() {
        sellerListPanel.removeAll();

        if (sellerIds.isEmpty()) {
            JLabel emptyLabel = new JLabel("セラーIDが追加されていません");
            emptyLabel.setFont(FontSettings.getLabelFont());
            emptyLabel.setForeground(Color.GRAY);
            emptyLabel.setHorizontalAlignment(SwingConstants.CENTER);
            sellerListPanel.add(emptyLabel, "align center, gaptop 10, gapbottom 10");
        } else {
            for (String sellerId : sellerIds) {
                JPanel itemPanel = new JPanel(new MigLayout("insets 5", "[grow][] "));
                JLabel label = new JLabel(sellerId);
                label.setFont(FontSettings.getLabelFont());
                JButton deleteButton = new JButton(FontIcon.of(AntDesignIconsOutlined.DELETE, 16));
                deleteButton.setToolTipText("削除");
                deleteButton.setFont(FontSettings.getButtonFont());

                itemPanel.add(label, "grow");
                itemPanel.add(deleteButton);

                deleteButton.addActionListener(e -> {
                    sellerIds.remove(sellerId);
                    updateSellerList();
                });

                sellerListPanel.add(itemPanel, "grow");
            }
        }

        sellerListPanel.revalidate();
        sellerListPanel.repaint();
    }

    // Clear all seller IDs
    private void clearSellerIds() {
        sellerIds.clear();
        updateSellerList();
    }

    // Getters
    public List<String> getSellerIds() {
        return new ArrayList<>(sellerIds);
    }

    public JButton getAddButton() {
        return btnAdd;
    }

    public JTextField getSellerIdField() {
        return txtSellerId;
    }

    public JButton getClearButton() {
        return btnClear;
    }

    public JButton getStartButton() {
        return btnStart;
    }


    /**
     * Start research for an individual seller (called from SearchQueueManager task)
     * This method executes synchronously to ensure proper sequential execution.
     *
     * @param sellerId     The seller ID to research
     * @param sellerIndex  The index of this seller in the batch (1-based)
     * @param totalSellers The total number of sellers in the batch
     */
    private void startIndividualSellerResearch(String sellerId, int sellerIndex, int totalSellers) {
        logger.info("セラーID: {} の個別リサーチを開始します ( {} / {} )", sellerId, sellerIndex, totalSellers);

        String progressId = "seller_" + sellerId + "_" + System.currentTimeMillis();

        try {
            // Create progress item for tracking
            ProgressItem progressItem = new ProgressItem(
                    progressId,
                    "セラー検索",
                    sellerId,
                    sellerIndex,
                    totalSellers
            );

            // Add to progress tracking
            SearchQueueManager.getInstance().addProgressItem(progressItem);

            // Add seller to the new seller management system (on EDT)
            final boolean[] success = new boolean[1];
            SwingUtilities.invokeAndWait(() -> {
                success[0] = researchResultPanel.addSellerResearchResult(sellerId, sellerId);
            });

            if (success[0]) {
                logger.info("セラーID: {} をセラー管理システムに正常に保存しました", sellerId);

                // Execute the research synchronously within the queue task
                executeSynchronousSellerResearch(sellerId, progressId);

                // Switch to results tab on first seller
                if (sellerIndex == 1) {
                    SwingUtilities.invokeLater(() -> {
                        tabbedPane.setSelectedIndex(1);
                        researchResultPanel.loadData();
                    });
                }
            } else {
                // Update progress to error
                SearchQueueManager.getInstance().updateProgressItem(progressId, ProgressItem.Status.ERROR);
                logger.error("Failed to save seller ID: {} to seller management system", sellerId);
                throw new RuntimeException("Failed to save seller ID: " + sellerId);
            }
        } catch (Exception e) {
            // Update progress to error
            SearchQueueManager.getInstance().updateProgressItem(progressId, ProgressItem.Status.ERROR);
            logger.error("Exception while processing seller ID: {} - {}", sellerId, e.getMessage(), e);
            throw new RuntimeException("Seller research failed for " + sellerId, e);
        }
    }

    /**
     * Execute synchronous seller research with database persistence.
     * This method runs within the SearchQueueManager task execution context.
     * Now uses the new seller management system.
     *
     * @param sellerId   The seller ID for research
     * @param progressId The progress item ID for tracking
     */
    private void executeSynchronousSellerResearch(String sellerId, String progressId) {
        try {
            // Update progress to processing
            SearchQueueManager.getInstance().updateProgressItem(progressId, ProgressItem.Status.PROCESSING);

            // Update research status to "リサーチ中"
            com.mrcresearch.service.SellerManagementService.updateSellerResearchStatus(sellerId, "リサーチ中");

            // Update the UI on the Event Dispatch Thread
            SwingUtilities.invokeLater(() -> {
                researchResultPanel.loadData();
            });

            // Execute actual seller search with database persistence
            SearchBySeller searchBySeller = new SearchBySeller();
            searchBySeller.execute(sellerId, progressId);

            // Update seller name when completed
            SimpleDateFormat timestampFormat = new SimpleDateFormat("yyyyMMdd_HHmmss");
            String timestamp = timestampFormat.format(new Date());
            String tempSellerName = "セラー_" + sellerId + "_" + timestamp;

            // Update seller name in the new seller management system
            com.mrcresearch.service.SellerManagementService.updateSellerName(sellerId, tempSellerName);

            // Update research status to "完了"
            com.mrcresearch.service.SellerManagementService.updateSellerResearchStatus(sellerId, "完了");

            // Update progress to completed
            SearchQueueManager.getInstance().updateProgressItem(progressId, ProgressItem.Status.COMPLETED);

            // Update the UI on the Event Dispatch Thread
            SwingUtilities.invokeLater(() -> {
                researchResultPanel.loadData();
            });

            logger.info("セラーID: {} のセラーリサーチが正常に完了しました", sellerId);

        } catch (Exception e) {
            // Update progress to error for any exception
            SearchQueueManager.getInstance().updateProgressItem(progressId, ProgressItem.Status.ERROR);

            // Update research status to "エラー"
            com.mrcresearch.service.SellerManagementService.updateSellerResearchStatus(sellerId, "エラー");

            logger.error("Error in seller research for seller {}: {}", sellerId, e.getMessage(), e);

            // Update the UI on the Event Dispatch Thread
            SwingUtilities.invokeLater(() -> {
                researchResultPanel.loadData();
            });

            // Re-throw to be handled by SearchQueueManager
            throw new RuntimeException("Seller research failed for " + sellerId, e);
        }
    }

    /**
     * Add special paste handling for seller ID field to handle multiple IDs
     *
     * @param textField The seller ID text field
     */
    private void addSellerIdPasteHandling(JTextField textField) {
        // Add key listener for Enter key to automatically add seller ID
        textField.addActionListener(e -> addSellerId());

        // Add document listener to handle pasted content with multiple lines
        textField.getDocument().addDocumentListener(new javax.swing.event.DocumentListener() {
            @Override
            public void insertUpdate(javax.swing.event.DocumentEvent e) {
                SwingUtilities.invokeLater(() -> handlePastedContent());
            }

            @Override
            public void removeUpdate(javax.swing.event.DocumentEvent e) {
                // Not needed for paste handling
            }

            @Override
            public void changedUpdate(javax.swing.event.DocumentEvent e) {
                // Not needed for paste handling
            }
        });
    }

    /**
     * Handle pasted content that might contain multiple seller IDs
     */
    private void handlePastedContent() {
        String content = txtSellerId.getText();
        if (content == null || content.trim().isEmpty()) {
            return;
        }

        // Check if content contains multiple lines or comma-separated values
        if (content.contains("\n") || content.contains(",") || content.contains("\t")) {
            // Split by various delimiters
            String[] parts = content.split("[\n\r\t,;\s]+");

            boolean addedAny = false;
            for (String part : parts) {
                String sellerId = part.trim();
                if (!sellerId.isEmpty() && !sellerIds.contains(sellerId)) {
                    sellerIds.add(sellerId);
                    addedAny = true;
                }
            }

            if (addedAny) {
                // Clear the text field and update the list
                txtSellerId.setText("");
                updateSellerList();

                // Show brief feedback
                txtSellerId.putClientProperty("JComponent.outline", "success");
                Timer timer = new Timer(1000, e -> {
                    txtSellerId.putClientProperty("JComponent.outline", null);
                    txtSellerId.repaint();
                });
                timer.setRepeats(false);
                timer.start();
            }
        }
    }

    private void handleShowSellerResearch(java.beans.PropertyChangeEvent evt) {
        // Re-fire the event for the parent component (Main) to catch
        firePropertyChange("showSellerResearch", null, evt.getNewValue());
    }
}
