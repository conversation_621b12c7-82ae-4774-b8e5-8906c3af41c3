package com.mrcresearch.screen.settings;

import com.formdev.flatlaf.FlatClientProperties;
import com.mrcresearch.screen.main.Application;
import com.mrcresearch.service.common.FontSettings;
import com.mrcresearch.util.TextFieldContextMenuUtil;
import com.mrcresearch.util.ThemeIconUtil;
import com.mrcresearch.util.database.DatabaseUtil;
import com.mrcresearch.util.database.SettingsRepository;
import lombok.Setter;
import net.miginfocom.swing.MigLayout;
import org.kordamp.ikonli.fontawesome5.FontAwesomeBrands;
import org.kordamp.ikonli.fontawesome5.FontAwesomeSolid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import javax.swing.text.AbstractDocument;
import javax.swing.text.AttributeSet;
import javax.swing.text.BadLocationException;
import javax.swing.text.DocumentFilter;
import java.awt.*;

import static com.mrcresearch.util.database.SettingsConstants.*;

public class Settings extends JPanel {

    private static final Logger logger = LoggerFactory.getLogger(Settings.class);

    // --- Constants ---
    private static final Color HEADLESS_ON_COLOR = new Color(70, 150, 70);

    // Default setting values are now defined in SettingsConstants.java

    // Database settings array indices
    private static final int IDX_SOLD_DATE = 0;
    private static final int IDX_BROWSER_TIMEOUT = 1;
    private static final int IDX_BROWSER_SLEEP = 2;
    private static final int IDX_BROWSER_INIT_SLEEP_TIME = 3;
    private static final int IDX_BROWSER_TYPE = 4;
    private static final int IDX_KEYWORD_PAGES = 5;
    private static final int IDX_OLD_ITEM_COUNT = 6;
    private static final int IDX_THEME_TYPE = 7;
    private static final int IDX_HEADLESS_MODE = 8;
    private static final int IDX_SALES_AGGREGATION_DAYS = 9;

    // --- UI Components ---
    private JPanel panel;
    private JTextField txtSoldDate;
    private JTextField txtBrowserLoadTimeout;
    private JTextField txtBrowserSleepTime;
    private JTextField txtBrowserInitSleepTime;
    private JComboBox browserSelectCombo;
    private JToggleButton headlessModeToggle;
    private JTextField txtDefaultKeywordPages;
    private JTextField txtOldItemDisplayCount;
    private JTextField txtSalesAggregationDefaultDays;
    private JRadioButton lightThemeRadio;
    private JRadioButton darkThemeRadio;
    private JComboBox<FontSettings.FontSize> fontSizeCombo;
    private JButton btnSave;

    /**
     * Sets the main screen reference for updating other components when settings change.
     *
     * @param mainScreen The main screen instance.
     */
    @Setter
    private com.mrcresearch.screen.main.Main mainScreen;

    public Settings() {
        init();
        loadSavedSettings();
    }

    private void init() {
        createComponents();
        configureComponents();
        layoutComponents();
        addListenersAndValidators();
    }

    /**
     * Creates all UI components.
     */
    private void createComponents() {
        txtSoldDate = new JTextField(String.valueOf(DEFAULT_SOLD_DATE));
        txtBrowserLoadTimeout = new JTextField(String.valueOf(DEFAULT_BROWSER_LOAD_TIMEOUT));
        txtBrowserSleepTime = new JTextField(String.valueOf(DEFAULT_BROWSER_SLEEP_TIME));
        txtBrowserInitSleepTime = new JTextField(String.valueOf(DEFAULT_BROWSER_INIT_SLEEP_TIME));
        browserSelectCombo = createBrowserSelectCombo();
        headlessModeToggle = new JToggleButton("OFF", DEFAULT_HEADLESS_MODE);
        txtDefaultKeywordPages = new JTextField(String.valueOf(DEFAULT_KEYWORD_PAGES));
        txtOldItemDisplayCount = new JTextField(String.valueOf(DEFAULT_OLD_ITEM_DISPLAY_COUNT));
        txtSalesAggregationDefaultDays = new JTextField(String.valueOf(DEFAULT_SALES_AGGREGATION_DAYS));
        lightThemeRadio = new JRadioButton("☀️ライト", DEFAULT_THEME.equals("light"));
        darkThemeRadio = new JRadioButton("🌙ダーク", DEFAULT_THEME.equals("dark"));
        fontSizeCombo = new JComboBox<>(FontSettings.FontSize.values());
        btnSave = new JButton("保存");
    }

    private JComboBox createBrowserSelectCombo() {
        BrowserItem[] items = {
                new BrowserItem("Firefox", FontAwesomeBrands.FIREFOX),
                new BrowserItem("Chrome", FontAwesomeBrands.CHROME)
        };
        JComboBox<BrowserItem> comboBox = new JComboBox<>(items);
        comboBox.setRenderer(new BrowserRenderer());
        return comboBox;
    }

    /**
     * Configures properties of the UI components (fonts, icons, etc.).
     */
    private void configureComponents() {
        txtSoldDate.setFont(FontSettings.getLabelFont());
        txtBrowserLoadTimeout.setFont(FontSettings.getLabelFont());
        txtBrowserSleepTime.setFont(FontSettings.getLabelFont());
        txtBrowserInitSleepTime.setFont(FontSettings.getLabelFont());
        browserSelectCombo.setFont(FontSettings.getLabelFont());
        headlessModeToggle.setFont(FontSettings.getLabelFont());
        txtDefaultKeywordPages.setFont(FontSettings.getLabelFont());
        txtOldItemDisplayCount.setFont(FontSettings.getLabelFont());
        txtSalesAggregationDefaultDays.setFont(FontSettings.getLabelFont());
        lightThemeRadio.setFont(FontSettings.getLabelFont());
        darkThemeRadio.setFont(FontSettings.getLabelFont());
        fontSizeCombo.setFont(FontSettings.getLabelFont());
        btnSave.setFont(FontSettings.getLabelFont());

        fontSizeCombo.setRenderer(new DefaultListCellRenderer() {
            @Override
            public Component getListCellRendererComponent(JList<?> list, Object value, int index, boolean isSelected, boolean cellHasFocus) {
                super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
                if (value instanceof FontSettings.FontSize) {
                    setText(((FontSettings.FontSize) value).getDisplayName());
                }
                return this;
            }
        });

        txtSoldDate.putClientProperty(FlatClientProperties.PLACEHOLDER_TEXT, "販売日数を入力");
        txtBrowserLoadTimeout.putClientProperty(FlatClientProperties.PLACEHOLDER_TEXT, "タイムアウト秒数を入力");
        txtBrowserSleepTime.putClientProperty(FlatClientProperties.PLACEHOLDER_TEXT, "スリープ秒数を入力");
        txtBrowserInitSleepTime.putClientProperty(FlatClientProperties.PLACEHOLDER_TEXT, "スリープ秒数を入力");
        txtDefaultKeywordPages.putClientProperty(FlatClientProperties.PLACEHOLDER_TEXT, "1-99の数字を入力");
        txtSalesAggregationDefaultDays.putClientProperty(FlatClientProperties.PLACEHOLDER_TEXT, "1-14の数字を入力");

        ButtonGroup themeButtonGroup = new ButtonGroup();
        themeButtonGroup.add(lightThemeRadio);
        themeButtonGroup.add(darkThemeRadio);

        btnSave.setIcon(ThemeIconUtil.ofButton(FontAwesomeSolid.SAVE, 16));

        TextFieldContextMenuUtil.enableTextFieldContextMenu(txtSoldDate, txtBrowserLoadTimeout, txtBrowserSleepTime, txtBrowserInitSleepTime, txtDefaultKeywordPages, txtOldItemDisplayCount, txtSalesAggregationDefaultDays);
    }

    /**
     * Lays out all components on the panel.
     */
    private void layoutComponents() {
        setLayout(new MigLayout("fill, insets 20", "[grow]", "[grow]"));

        panel = new JPanel(new MigLayout("wrap 2, fillx, insets 35 45 30 45", "[fill,200:220][fill,200:220]"));
        panel.putClientProperty(FlatClientProperties.STYLE, "arc:20;");

        // セラーリサーチの設定
        panel.add(createSectionHeaderLabel("セラーリサーチの設定"), "span 2, gapbottom 10");

        panel.add(createTitleLabel("販売日数 (日):"));
        panel.add(txtSoldDate, "growx");
        panel.add(createDescriptionLabel("過去何日間の販売データを分析するかを設定します"), "span 2, gapbottom 10");

        panel.add(createTitleLabel("古い商品表示数 (1-999):"));
        panel.add(createSteppedIncrementDecrementPanel(txtOldItemDisplayCount, 1, 999, 5, 10), "growx");
        panel.add(createDescriptionLabel("設定した販売日数よりも古い商品が設定された数よりも大きくなったらリサーチを自動停止します"), "span 2, gapbottom 20");

        // ブラウザ設定
        panel.add(createSectionHeaderLabel("ブラウザ設定"), "span 2, gapbottom 10");

        panel.add(createTitleLabel("ページ表示のタイムアウト時間 (秒):"));
        panel.add(txtBrowserLoadTimeout, "growx");

        panel.add(createTitleLabel("ページ表示後の待ち時間(秒):"), "gaptop 10");
        panel.add(txtBrowserSleepTime, "growx");
        panel.add(createDescriptionLabel("３秒よりも短くしないようにしてください"), "span 2");

        panel.add(createTitleLabel("ブラウザ初期化時の待ち時間(秒):"), "gaptop 10");
        panel.add(txtBrowserInitSleepTime, "growx");
        panel.add(createDescriptionLabel("通常は変更する必要はありません"), "span 2");

        panel.add(createTitleLabel("ブラウザ選択:"), "gaptop 10");
        panel.add(browserSelectCombo, "growx");

        panel.add(createTitleLabel("ヘッドレスモード:"), "gaptop 10");
        panel.add(headlessModeToggle, "growx");
        panel.add(createDescriptionLabel("「ON」推奨ですが、リサーチがうまくいかないときは「OFF」にすると改善することがあります"), "span 2, gapbottom 20");

        // その他設定
        panel.add(createSectionHeaderLabel("その他設定"), "span 2, gapbottom 10");

        panel.add(createTitleLabel("デフォルトキーワード検索ページ数 (1-99):"));
        panel.add(createSteppedIncrementDecrementPanel(txtDefaultKeywordPages, 1, 99, 5, 10), "growx");

        panel.add(createTitleLabel("リサーチ結果集計デフォルト日数 (1-14):"), "gaptop 10");
        panel.add(createSteppedIncrementDecrementPanel(txtSalesAggregationDefaultDays, 1, 14, 1, 7), "growx");
        panel.add(createDescriptionLabel("リサーチ結果画面を開いたときのデフォルト日数を設定します"), "span 2");

        panel.add(createTitleLabel("表示モード選択:"), "gaptop 10");
        JPanel themePanel = new JPanel(new MigLayout("insets 0", "[][] "));
        themePanel.add(lightThemeRadio);
        themePanel.add(darkThemeRadio);
        panel.add(themePanel, "growx");

        panel.add(createTitleLabel("フォントサイズ:"), "gaptop 10");
        panel.add(fontSizeCombo, "growx");

        panel.add(btnSave, "gaptop 20, span 2, align right");

        JScrollPane scrollablePanel = new JScrollPane(panel);
        scrollablePanel.setBorder(BorderFactory.createEmptyBorder());
        scrollablePanel.getVerticalScrollBar().setUnitIncrement(16);
        add(scrollablePanel, "grow");
    }

    /**
     * Creates and returns a JLabel configured as a title label with bold styling
     * and a font size of 16.
     *
     * @param text the text to display in the label
     * @return a JLabel configured to be used as a title label
     */
    private JLabel createTitleLabel(String text) {
        JLabel titleLabel = new JLabel(text);
        titleLabel.setFont(FontSettings.getLabelFont());
        return titleLabel;
    }

    /**
     * Creates and returns a JLabel configured as a section header with bold styling
     * and a larger font size of 18.
     *
     * @param text the text to display in the section header
     * @return a JLabel configured to be used as a section header
     */
    private JLabel createSectionHeaderLabel(String text) {
        JLabel headerLabel = new JLabel(text);
        headerLabel.setFont(FontSettings.getMenuFont());
        return headerLabel;
    }

    /**
     * Creates and returns a JLabel configured as a description label with
     * a smaller font size of 14 and gray color.
     *
     * @param text the description text to display
     * @return a JLabel configured to be used as a description
     */
    private JLabel createDescriptionLabel(String text) {
        JLabel descLabel = new JLabel(text);
        descLabel.setFont(FontSettings.getTextFont());
        descLabel.setForeground(Color.GRAY);
        return descLabel;
    }

    /**
     * Adds all necessary listeners and input validators.
     */
    private void addListenersAndValidators() {
        // Listeners
        headlessModeToggle.addActionListener(e -> updateHeadlessToggleVisuals());
        btnSave.addActionListener(e -> saveSettings());

        // Validators
        ((AbstractDocument) txtSoldDate.getDocument()).setDocumentFilter(new IntegerRangeFilter(1, 999, txtSoldDate));
        ((AbstractDocument) txtBrowserLoadTimeout.getDocument()).setDocumentFilter(new IntegerRangeFilter(1, 999, txtBrowserLoadTimeout));
        ((AbstractDocument) txtBrowserSleepTime.getDocument()).setDocumentFilter(new IntegerRangeFilter(1, 999, txtBrowserSleepTime));
        ((AbstractDocument) txtBrowserInitSleepTime.getDocument()).setDocumentFilter(new IntegerRangeFilter(1, 999, txtBrowserInitSleepTime));
        ((AbstractDocument) txtDefaultKeywordPages.getDocument()).setDocumentFilter(new IntegerRangeFilter(1, 99, txtDefaultKeywordPages));
        ((AbstractDocument) txtOldItemDisplayCount.getDocument()).setDocumentFilter(new IntegerRangeFilter(1, 999, txtOldItemDisplayCount));
    }

    /**
     * Creates a panel with a text field and buttons to increment/decrement its value.
     */
    private JPanel createSteppedIncrementDecrementPanel(JTextField textField, int min, int max, int smallStep, int largeStep) {
        JPanel container = new JPanel(new MigLayout("insets 0, fillx", "[][][grow][][]"));
        Font buttonFont = FontSettings.getButtonFont();

        JButton btnMinusLarge = new JButton("-" + largeStep);
        btnMinusLarge.setFont(buttonFont);
        btnMinusLarge.addActionListener(e -> updateTextFieldValue(textField, -largeStep, min, max));
        container.add(btnMinusLarge);

        JButton btnMinusSmall = new JButton("-" + smallStep);
        btnMinusSmall.setFont(buttonFont);
        btnMinusSmall.addActionListener(e -> updateTextFieldValue(textField, -smallStep, min, max));
        container.add(btnMinusSmall);

        container.add(textField, "growx");

        JButton btnPlusSmall = new JButton("+" + smallStep);
        btnPlusSmall.setFont(buttonFont);
        btnPlusSmall.addActionListener(e -> updateTextFieldValue(textField, smallStep, min, max));
        container.add(btnPlusSmall);

        JButton btnPlusLarge = new JButton("+" + largeStep);
        btnPlusLarge.setFont(buttonFont);
        btnPlusLarge.addActionListener(e -> updateTextFieldValue(textField, largeStep, min, max));
        container.add(btnPlusLarge);

        return container;
    }

    /**
     * Updates the value in a text field by a given delta, respecting min/max bounds.
     */
    private void updateTextFieldValue(JTextField textField, int delta, int min, int max) {
        try {
            int currentValue = Integer.parseInt(textField.getText());
            int newValue = Math.max(min, Math.min(max, currentValue + delta));
            textField.setText(String.valueOf(newValue));
        } catch (NumberFormatException ex) {
            textField.setText(String.valueOf(min)); // Reset to min on invalid input
        }
    }

    /**
     * Updates the text and background color of the headless mode toggle button based on its state.
     */
    private void updateHeadlessToggleVisuals() {
        if (headlessModeToggle.isSelected()) {
            headlessModeToggle.setText("ON");
            headlessModeToggle.setBackground(HEADLESS_ON_COLOR);
        } else {
            headlessModeToggle.setText("OFF");
            headlessModeToggle.setBackground(null); // Reset to default background
        }
    }

    // --- Data Loading and Saving ---

    /**
     * Loads saved settings from the database and updates UI components.
     */
    private void loadSavedSettings() {
        DatabaseUtil.initDatabase();
        try {
            Object[] settings = DatabaseUtil.loadSettings();

            txtSoldDate.setText(String.valueOf(getSetting(settings, IDX_SOLD_DATE, DEFAULT_SOLD_DATE)));
            txtBrowserLoadTimeout.setText(String.valueOf(getSetting(settings, IDX_BROWSER_TIMEOUT, DEFAULT_BROWSER_LOAD_TIMEOUT)));
            txtBrowserSleepTime.setText(String.valueOf(getSetting(settings, IDX_BROWSER_SLEEP, DEFAULT_BROWSER_SLEEP_TIME)));
            txtBrowserInitSleepTime.setText(String.valueOf(getSetting(settings, IDX_BROWSER_INIT_SLEEP_TIME, DEFAULT_BROWSER_INIT_SLEEP_TIME)));

            String browserType = getSetting(settings, IDX_BROWSER_TYPE, DEFAULT_BROWSER_TYPE);
            for (int i = 0; i < browserSelectCombo.getItemCount(); i++) {
                Object itemObject = browserSelectCombo.getItemAt(i);
                if (itemObject instanceof BrowserItem) {
                    BrowserItem item = (BrowserItem) itemObject;
                    if (item.getName().equals(browserType)) {
                        browserSelectCombo.setSelectedIndex(i);
                        break;
                    }
                }
            }

            txtDefaultKeywordPages.setText(String.valueOf(getSetting(settings, IDX_KEYWORD_PAGES, DEFAULT_KEYWORD_PAGES)));
            txtOldItemDisplayCount.setText(String.valueOf(getSetting(settings, IDX_OLD_ITEM_COUNT, DEFAULT_OLD_ITEM_DISPLAY_COUNT)));
            txtSalesAggregationDefaultDays.setText(String.valueOf(getSetting(settings, IDX_SALES_AGGREGATION_DAYS, DEFAULT_SALES_AGGREGATION_DAYS)));

            String themeType = getSetting(settings, IDX_THEME_TYPE, DEFAULT_THEME);
            if ("dark".equals(themeType)) {
                darkThemeRadio.setSelected(true);
            } else {
                lightThemeRadio.setSelected(true);
            }

            boolean isHeadless = getSetting(settings, IDX_HEADLESS_MODE, false);
            headlessModeToggle.setSelected(isHeadless);
            updateHeadlessToggleVisuals();

            String fontSizeName = SettingsRepository.getFontSize();
            FontSettings.FontSize fontSize = FontSettings.FontSize.fromString(fontSizeName);
            fontSizeCombo.setSelectedItem(fontSize);

        } catch (Exception e) {
            logger.error("Error loading settings, using defaults. Error: " + e.getMessage());
            // Set default values if loading fails
            lightThemeRadio.setSelected(true);
            headlessModeToggle.setSelected(false);
            updateHeadlessToggleVisuals();
        }
    }

    /**
     * Saves the current settings to the database.
     */
    private void saveSettings() {
        String currentTheme = getCurrentThemeFromDatabase();
        String newTheme = getSelectedTheme();
        FontSettings.FontSize currentFontSize = FontSettings.getCurrentFontSize();
        FontSettings.FontSize newFontSize = (FontSettings.FontSize) fontSizeCombo.getSelectedItem();

        boolean success = SettingsRepository.saveSettings(
                getSoldDate(),
                getBrowserLoadTimeout(),
                getBrowserSleepTime(),
                getBrowserInitSleepTime(),
                getSelectedBrowser(),
                getDefaultKeywordPages(),
                getOldItemDisplayCount(),
                newTheme,
                getHeadlessMode(),
                getSalesAggregationDefaultDays(),
                newFontSize.name()
        );

        if (success) {
            JOptionPane.showMessageDialog(this, "設定が保存されました", "成功", JOptionPane.INFORMATION_MESSAGE);

            // If the theme changed, restart the UI to apply it properly.
            if (!currentTheme.equals(newTheme)) {
                logger.debug("テーマが " + currentTheme + " から " + newTheme + " に変更されました - 即時適用します");
                applyThemeChange(newTheme);
            }

            if (currentFontSize != newFontSize) {
                logger.debug("フォントサイズが " + currentFontSize + " から " + newFontSize + " に変更されました - 即時適用します");
                applyFontChange(newFontSize);
            }

            // Notify other parts of the application about the settings change.
            if (mainScreen != null && mainScreen.getKeywordScreen() != null) {
                mainScreen.getKeywordScreen().refreshDefaultSettings();
                logger.info("Updated keyword screen with new default settings");
            }
        } else {
            JOptionPane.showMessageDialog(this, "設定の保存に失敗しました", "エラー", JOptionPane.ERROR_MESSAGE);
        }
    }

    private void applyThemeChange(String newTheme) {
        // Applying a theme change requires re-initializing UI components.
        // This is done on the Event Dispatch Thread to ensure thread safety.
        SwingUtilities.invokeLater(() -> {
            try {
                Application.applyThemeChange(newTheme);

                // A small delay can help ensure all components have time to repaint
                // after a Look and Feel change.
                Timer timer = new Timer(200, e -> {
                    if (mainScreen != null) {
                        mainScreen.applyThemeChange();
                    }
                });
                timer.setRepeats(false);
                timer.start();

            } catch (Exception e) {
                logger.error("Error applying theme change: " + e.getMessage(), e);
            }
        });
    }

    private void applyFontChange(FontSettings.FontSize newSize) {
        FontSettings.setFontSize(newSize);
        SwingUtilities.invokeLater(() -> {
            try {
                Application.applyFontChange();
            } catch (Exception e) {
                logger.error("Error applying font change: " + e.getMessage(), e);
            }
        });
    }

    /**
     * Gets a setting value from the settings array safely.
     *
     * @param settings     The array of settings from the database.
     * @param index        The index of the setting.
     * @param defaultValue The value to return if the setting is not found.
     * @return The setting value or the default.
     */
    @SuppressWarnings("unchecked")
    private <T> T getSetting(Object[] settings, int index, T defaultValue) {
        if (settings != null && settings.length > index && settings[index] != null) {
            return (T) settings[index];
        }
        return defaultValue;
    }

    /**
     * Gets the current theme from the database.
     * Note: This re-queries the DB. For frequent use, caching would be better.
     *
     * @return The current theme type ("light" or "dark").
     */
    private String getCurrentThemeFromDatabase() {
        try {
            Object[] settings = DatabaseUtil.loadSettings();
            return getSetting(settings, IDX_THEME_TYPE, DEFAULT_THEME);
        } catch (Exception e) {
            logger.error("Error loading current theme from database: " + e.getMessage());
        }
        return DEFAULT_THEME;
    }

    // --- Getters for Settings Values ---

    public int getSoldDate() {
        try {
            return Integer.parseInt(txtSoldDate.getText());
        } catch (NumberFormatException e) {
            return DEFAULT_SOLD_DATE;
        }
    }

    public int getBrowserLoadTimeout() {
        try {
            return Integer.parseInt(txtBrowserLoadTimeout.getText());
        } catch (NumberFormatException e) {
            return DEFAULT_BROWSER_LOAD_TIMEOUT;
        }
    }

    public int getBrowserSleepTime() {
        try {
            return Integer.parseInt(txtBrowserSleepTime.getText());
        } catch (NumberFormatException e) {
            return DEFAULT_BROWSER_SLEEP_TIME;
        }
    }

    public int getBrowserInitSleepTime() {
        try {
            return Integer.parseInt(txtBrowserInitSleepTime.getText());
        } catch (NumberFormatException e) {
            return DEFAULT_BROWSER_INIT_SLEEP_TIME;
        }
    }

    public String getSelectedBrowser() {
        Object selectedItem = browserSelectCombo.getSelectedItem();
        if (selectedItem instanceof BrowserItem) {
            return ((BrowserItem) selectedItem).getName();
        }
        return selectedItem != null ? selectedItem.toString() : "Firefox";
    }

    public int getDefaultKeywordPages() {
        try {
            return Integer.parseInt(txtDefaultKeywordPages.getText());
        } catch (NumberFormatException e) {
            return DEFAULT_KEYWORD_PAGES;
        }
    }

    public int getOldItemDisplayCount() {
        try {
            return Integer.parseInt(txtOldItemDisplayCount.getText());
        } catch (NumberFormatException e) {
            return DEFAULT_OLD_ITEM_DISPLAY_COUNT;
        }
    }

    public int getSalesAggregationDefaultDays() {
        try {
            return Integer.parseInt(txtSalesAggregationDefaultDays.getText());
        } catch (NumberFormatException e) {
            return DEFAULT_SALES_AGGREGATION_DAYS;
        }
    }

    public String getSelectedTheme() {
        return lightThemeRadio.isSelected() ? "light" : "dark";
    }

    public boolean getHeadlessMode() {
        return headlessModeToggle.isSelected();
    }

    /**
     * Updates icons to match the current theme.
     */
    public void updateIconsForTheme() {
        try {
            if (btnSave != null) {
                btnSave.setIcon(ThemeIconUtil.ofButton(FontAwesomeSolid.SAVE, 16));
            }
        } catch (Exception e) {
            logger.error("Error updating settings icons for theme: " + e.getMessage());
        }
    }

    // --- Inner classes for Browser ComboBox ---

    private static class BrowserItem {
        private final String name;
        private final org.kordamp.ikonli.Ikon icon;

        public BrowserItem(String name, org.kordamp.ikonli.Ikon icon) {
            this.name = name;
            this.icon = icon;
        }

        public String getName() {
            return name;
        }

        public org.kordamp.ikonli.Ikon getIcon() {
            return icon;
        }

        @Override
        public String toString() {
            return name;
        }
    }

    private static class BrowserRenderer extends DefaultListCellRenderer {
        @Override
        public Component getListCellRendererComponent(JList<?> list, Object value, int index, boolean isSelected, boolean cellHasFocus) {
            super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
            if (value instanceof BrowserItem) {
                BrowserItem item = (BrowserItem) value;
                setText(item.getName());
                setIcon(ThemeIconUtil.of(item.getIcon(), 16));
            }
            return this;
        }
    }

    // --- Inner Class for Validation ---

    /**
     * A DocumentFilter that restricts input to integers within a specified range.
     */
    private static class IntegerRangeFilter extends DocumentFilter {
        private final int min;
        private final int max;
        private final JComponent owner;

        public IntegerRangeFilter(int min, int max, JComponent owner) {
            this.min = min;
            this.max = max;
            this.owner = owner;
        }

        @Override
        public void replace(FilterBypass fb, int offset, int length, String text, AttributeSet attrs) throws BadLocationException {
            String currentText = fb.getDocument().getText(0, fb.getDocument().getLength());
            String newText = new StringBuilder(currentText).replace(offset, offset + length, text).toString();

            if (newText.isEmpty() || (newText.matches("\\d+") && isInRange(newText))) {
                super.replace(fb, offset, length, text, attrs);
            } else {
                UIManager.getLookAndFeel().provideErrorFeedback(owner);
            }
        }

        private boolean isInRange(String text) {
            try {
                int value = Integer.parseInt(text);
                return value >= min && value <= max;
            } catch (NumberFormatException e) {
                return false;
            }
        }
    }
}