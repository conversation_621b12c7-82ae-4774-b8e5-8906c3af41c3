package com.mrcresearch.service;

import com.browserup.harreader.model.HttpMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mrcresearch.service.common.MercariApi;
import com.mrcresearch.service.tools.DriverTool;
import com.mrcresearch.util.database.SettingsRepository;
import com.browserup.harreader.model.HarEntry;

import java.util.List;
import java.util.regex.Pattern;

/**
 * Service for retrieving seller names from Mercari API
 */
public class SellerNameService {

    private static final Logger logger = LoggerFactory.getLogger(SellerNameService.class);

    /**
     * Get seller name by seller ID using web scraping.
     *
     * @param sellerId The seller ID
     * @return The seller name, or null if not found or error occurred
     */
    public static String getSellerNameBySellerId(String sellerId) {
        DriverTool driverTool = null;

        try {
            // Load settings
            int sleepTime = SettingsRepository.getBrowserSleepTime() * 1000 + 120;
            boolean headless = SettingsRepository.getHeadlessMode();

            // Initialize driver
            driverTool = new DriverTool();
            driverTool.startDriver(headless);

            // Get profile HAR data
            driverTool.getProfileHar(sellerId);

            // Extract seller name from HAR data
            String sellerName = extractSellerNameFromHar(driverTool);

            logger.info("ID " + sellerId + " のセラー名を取得しました: " + sellerName);
            return sellerName;

        } catch (Exception e) {
            logger.error("Error retrieving seller name for ID " + sellerId + ": " + e.getMessage(), e);
            return null;

        } finally {
            // Always close the driver
            if (driverTool != null) {
                try {
                    driverTool.stopDriver();
                } catch (Exception e) {
                    logger.error("Error closing driver: " + e.getMessage(), e);
                }
            }
        }
    }

    /**
     * Extract seller name from HAR entries.
     *
     * @param driverTool The driver tool with HAR data
     * @return The seller name, or "noName" if not found
     */
    private static String extractSellerNameFromHar(DriverTool driverTool) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            List<HarEntry> harEntries = driverTool.getHarEntries()
                    .stream()
                    .filter(entry -> entry.getRequest().getUrl().contains(MercariApi.GET_USER_PROFILE)
                            && HttpMethod.GET.equals(entry.getRequest().getMethod()))
                    .toList();

            if (!harEntries.isEmpty()) {
                JsonNode items = mapper.readTree(harEntries.get(0).getResponse().getContent().getText());
                String rawName = items.path("data").path("name").asText();

                // Clean up the name by removing illegal file name characters
                Pattern illegalFileNamePattern = Pattern.compile("[\\\\/:*?\"<>|]");
                String cleanName = illegalFileNamePattern.matcher(rawName).replaceAll("");

                // Remove quotes if present
                cleanName = cleanName.replaceAll("^\"|\"$", "");

                return cleanName.trim().isEmpty() ? "noName" : cleanName;
            }

        } catch (Exception e) {
            logger.error("Error extracting seller name from HAR: " + e.getMessage(), e);
        }

        return "noName";
    }

    /**
     * Get seller name by seller ID with retry mechanism.
     *
     * @param sellerId   The seller ID
     * @param maxRetries Maximum number of retry attempts
     * @return The seller name, or null if all attempts failed
     */
    public static String getSellerNameBySellerIdWithRetry(String sellerId, int maxRetries) {
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            logger.info("ID " + sellerId + " のセラー名を取得しようとしています (試行 " + attempt + "/" + maxRetries + ")");

            String sellerName = getSellerNameBySellerId(sellerId);

            if (sellerName != null && !sellerName.equals("noName")) {
                return sellerName;
            }

            if (attempt < maxRetries) {
                try {
                    // Wait before retry
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        logger.error("Failed to retrieve seller name for ID " + sellerId + " after " + maxRetries + " attempts");
        return null;
    }
}
