package com.mrcresearch.service.ui;

import com.mrcresearch.screen.model.KeywordSearchResultModel;
import com.mrcresearch.util.database.DatabaseUtil;

import java.util.List;

/**
 * Service wrapper for keyword search result operations to decouple UI from DatabaseUtil.
 */
public class KeywordSearchResultService {

    public int getTotalCount() {
        return DatabaseUtil.getTotalKeywordSearchResults();
    }

    public List<KeywordSearchResultModel> getPage(int page, int pageSize) {
        return DatabaseUtil.getKeywordSearchResults(page, pageSize);
    }

    public int saveResult(KeywordSearchResultModel model) {
        return DatabaseUtil.saveKeywordSearchResult(model);
    }

    public String convertCategoryDisplayName(String categoryIds) {
        return DatabaseUtil.convertToDisplayCategoryName(categoryIds);
    }

    public KeywordSearchResultModel getResultById(int id) {
        return DatabaseUtil.getKeywordSearchResultById(id);
    }
}
