package com.mrcresearch.service.ui;

import com.mrcresearch.util.database.DatabaseUtil;

/**
 * Simple service wrapper for credentials-related persistence to decouple UI from DatabaseUtil.
 */
public class CredentialsService {

    public void init() {
        DatabaseUtil.initDatabase();
    }

    public String[] loadLoginInfo() {
        return DatabaseUtil.loadLoginInfo();
    }

    public String getPassword() {
        return DatabaseUtil.getPassword();
    }

    public boolean saveLoginInfo(String email, String password) {
        return DatabaseUtil.saveLoginInfo(email, password);
    }
}
