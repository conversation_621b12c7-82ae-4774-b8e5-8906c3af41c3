package com.mrcresearch.service.common;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;

public class Connection {
    /**
     * connectionから、JsonNodeを読み込み返却する
     *
     * @param connection 接続
     * @return JsonNode
     * @throws IOException connectionの読み込みに失敗したとき
     */
    public static JsonNode getJsonResponse(HttpURLConnection connection) throws IOException {
        // レスポンスの内容を取得する
        BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
        String line;
        StringBuilder sb = new StringBuilder();
        while ((line = reader.readLine()) != null) {
            sb.append(line);
        }
        String json = sb.toString();

        // JSON データをオブジェクトに変換する
        return new ObjectMapper().readTree(json);
    }
}
