package com.mrcresearch.service.common;

import com.mrcresearch.util.database.SettingsRepository;
import lombok.Getter;

import java.awt.*;
import java.io.IOException;
import java.io.InputStream;

public class FontSettings {

    @Getter
    public enum FontSize {
        SMALL("小"),
        MEDIUM("中"),
        LARGE("大");

        private final String displayName;

        FontSize(String displayName) {
            this.displayName = displayName;
        }

        public static FontSize fromString(String text) {
            for (FontSize size : FontSize.values()) {
                if (size.name().equalsIgnoreCase(text)) {
                    return size;
                }
            }
            return MEDIUM; // Default
        }
    }

    private static Font baseFontRegular;
    private static Font baseFontBold;
    @Getter
    private static FontSize currentFontSize = FontSize.MEDIUM; // Default size

    static {
        try {
            InputStream regularStream = FontSettings.class.getResourceAsStream("/fonts/MPLUSRounded1c-Regular.ttf");
            InputStream boldStream = FontSettings.class.getResourceAsStream("/fonts/MPLUSRounded1c-Bold.ttf");

            if (regularStream == null || boldStream == null) {
                throw new IOException("Font file not found in resources.");
            }

            baseFontRegular = Font.createFont(Font.TRUETYPE_FONT, regularStream);
            baseFontBold = Font.createFont(Font.TRUETYPE_FONT, boldStream);

            GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
            ge.registerFont(baseFontRegular);
            ge.registerFont(baseFontBold);

        } catch (IOException | FontFormatException e) {
            e.printStackTrace();
            baseFontRegular = new Font(Font.SANS_SERIF, Font.PLAIN, 16);
            baseFontBold = new Font(Font.SANS_SERIF, Font.BOLD, 16);
        }
    }

    public static void setFontSize(FontSize size) {
        currentFontSize = size;
        SettingsRepository.saveFontSize(size.name());
    }

    public static void loadFontSize() {
        String sizeName = SettingsRepository.getFontSize();
        currentFontSize = FontSize.fromString(sizeName);
    }

    private static float getAdjustedSize(float baseSize) {
        return switch (currentFontSize) {
            case SMALL -> baseSize - 2;
            case LARGE -> baseSize + 2;
            default -> baseSize;
        };
    }

    public static Font getRoundedFont(int style, float size) {
        float adjustedSize = getAdjustedSize(size);
        if (style == Font.BOLD) {
            return baseFontBold.deriveFont(adjustedSize);
        }
        return baseFontRegular.deriveFont(adjustedSize);
    }

    public static Font getMenuFont() {
        return getRoundedFont(Font.BOLD, 18);
    }

    public static Font getLabelFont() {
        return getRoundedFont(Font.BOLD, 16);
    }

    public static Font getButtonFont() {
        return getRoundedFont(Font.BOLD, 16);
    }

    public static Font getTextFont() {
        return getRoundedFont(Font.PLAIN, 14);
    }

    public static Font getBoxFont() {
        return getRoundedFont(Font.BOLD, 14);
    }

    public static Font getTableFont() {
        return getRoundedFont(Font.PLAIN, 14);
    }

    public static Font getSmallFont() {
        return getRoundedFont(Font.PLAIN, 10);
    }

    public static Font getMonospacedFont12() {
        return new Font(Font.MONOSPACED, Font.PLAIN, (int) getAdjustedSize(12));
    }

    public static Font getMonospacedFont14() {
        return new Font(Font.MONOSPACED, Font.PLAIN, (int) getAdjustedSize(14));
    }

    public static Font getSmallestTextFont() {
        return getRoundedFont(Font.PLAIN, 12);
    }

    public static Font getSmallerBoldFont() {
        return getRoundedFont(Font.BOLD, 14);
    }

    public static Font getLargerTextFont() {
        return getRoundedFont(Font.PLAIN, 20);
    }
}