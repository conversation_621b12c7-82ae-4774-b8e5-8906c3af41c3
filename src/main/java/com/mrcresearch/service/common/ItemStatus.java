package com.mrcresearch.service.common;

public class ItemStatus {
    public static final String ON_SALE = "ITEM_STATUS_ON_SALE";
    public static final String USER_ON_SALE = "on_sale";

    /**
     * Checks if the given string represents an item status.
     *
     * @param str The string to check.
     * @return True if it's a valid item status, false otherwise.
     */
    public static boolean isOnSale(String str) {
        return str.equals(ON_SALE) || str.equals(USER_ON_SALE);
    }
}
