package com.mrcresearch.service.common;

import com.mrcresearch.service.tools.DriverTool;

import java.util.concurrent.TimeUnit;

public class JavaScripts {
    public static final String NO_PRICE_SCRIPT = "document.querySelectorAll('.priceContainer__a6f874a2').forEach(element => element.style.display = 'none');";
    public static final String NO_SOLD_SCRIPT = "document.querySelectorAll('.merResourceBase').forEach(element => element.style.display = 'none');";
    private static final String SCROLL_BOTTOM_HEAD = "scroll(0, document.body.scrollHeight / 10 * ";
    private static final String SCROLL_BOTTOM_END = ");";
    private static final String HIDE_PHOTO_TUTORIAL = "try{document.querySelector('[data-testid=\"tutorial-overlay\"]').style.display = 'none'}{catch(e){}}";
    private static final String STOP_LAZY_LOAD = "document.querySelectorAll('img').forEach(elem => elem.removeAttribute('loading')) ";

    /**
     * 画面の一番下までスクロールする<br />
     * 画像情報読み込み用<br />
     *
     * @param driver WebDriver
     */
    public static void ScrollToBottom(DriverTool driver) {
        try {
            for (int i = 1; i <= 10; i++) {
                TimeUnit.MILLISECONDS.sleep(200);
                driver.executeJavaScript(SCROLL_BOTTOM_HEAD + i + SCROLL_BOTTOM_END);
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * ページの先頭を表示する
     *
     * @param driver WebDriver
     */
    public static void showPageHead(DriverTool driver) {
        driver.executeJavaScript(SCROLL_BOTTOM_HEAD + 0 + SCROLL_BOTTOM_END);
    }

    /**
     * 商品価格の画像を非表示にする<br />
     * スクリーンショットを取る場合に使う<br />
     *
     * @param driver 利用中のドライバー
     */
    public static void hidePrice(DriverTool driver) {
        driver.executeJavaScript(NO_PRICE_SCRIPT);
    }

    /**
     * 売り切れの画像を非表示にする<br />
     * スクリーンショットを取る場合に使う<br />
     *
     * @param driver 利用中のドライバー
     */
    public static void hideSoldImage(DriverTool driver) {
        driver.executeJavaScript(NO_SOLD_SCRIPT);
    }

    /**
     * 画像検索のチュートリアルを削除する
     *
     * @param driver 利用中のドライバー
     */
    public static void hidePhotoTutorial(DriverTool driver) {
        driver.executeJavaScript(HIDE_PHOTO_TUTORIAL);
    }

    /**
     * 画像のLAZYロードを無効にする
     *
     * @param driver 利用中のドライバー
     */
    public static void stopLazyLoad(DriverTool driver) {
        driver.executeJavaScript(STOP_LAZY_LOAD);
    }
}
