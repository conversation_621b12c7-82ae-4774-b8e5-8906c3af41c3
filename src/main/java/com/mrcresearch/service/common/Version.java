package com.mrcresearch.service.common;

import com.fasterxml.jackson.databind.JsonNode;
import io.netty.handler.codec.http.HttpResponseStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * バージョンとかについてはここを使う
 */
public class Version {
    /**
     * 今起動しているバージョン
     */
    public static final String THIS = "4.0.24";
    private static final Logger logger = LoggerFactory.getLogger(Version.class);

    /**
     * アップロードされている最新のバージョンを返す
     *
     * @return 最新バージョン
     * @throws IOException 例外が発生した場合
     */
    public static String getLastVersion() throws IOException {
        URL url = new URL("https://mcrsldrschtl.com/latest_version");
        // HTTP リクエストを送信する
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod(HttpMethod.GET);
        connection.connect();

        if (HttpResponseStatus.OK.code() == connection.getResponseCode()) {
            // レスポンスの内容を取得する
            JsonNode rootNode = Connection.getJsonResponse(connection);
            return rootNode.asText();
        }
        return null;
    }

    /**
     * 最新のバージョンなのかを返す
     *
     * @return 最新のバージョンなら true
     * @throws IOException 例外が発生した場合
     */
    public static boolean isLatestVersion() throws IOException {
        logger.info("現在のバージョン：{}", THIS);
        String latestVersion = getLastVersion();
        if (THIS.equals(latestVersion)) {
            return true;
        }
        String[] nowVersionNums = THIS.split("\\.");
        String[] latestVersionNums = latestVersion.split("\\.");
        for (int i = 0; i < 3; i++) {
            if (Integer.parseInt(nowVersionNums[i]) < Integer.parseInt(latestVersionNums[i])) {
                return false;
            } else if (Integer.parseInt(nowVersionNums[i]) > Integer.parseInt(latestVersionNums[i])) {
                return true;
            }
        }
        return true;
    }
}
