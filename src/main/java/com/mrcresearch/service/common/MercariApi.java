package com.mrcresearch.service.common;

public class MercariApi {
    /**
     * SHOPSのベースのURL
     */
    public static final String SHOPS_BASE_URL = "https://mercari-shops.com/";
    /**
     * SHOPSの最初のアイテムのリクエストURL
     */
    public static final String SHOPS_PROFILE_URL = SHOPS_BASE_URL + "shops/";
    /**
     * SHOPSの出品ごとのページのベースURL
     */
    public static final String PRODUCT_BASE_URL = SHOPS_PROFILE_URL + "product/";
    /**
     * SHOPSの評価ページのアイテム
     */
    public static final String SHOPS_GRAPHQL = SHOPS_BASE_URL + "graphql";
    /**
     * プロダクトページのベースURL
     */
    public static final String PRODUCT_PAGE_BASE_URL = "https://jp.mercari.com/shops/product/";
    /**
     * SHOPSの評価ページの最初のアイテム
     */
    public static final String SHOPS_FIRST_ITEMS = SHOPS_BASE_URL + "_next/data/";
    /**
     * SHOPSの評価ページを開くテスト用CSSセレクタ。
     */
    public static final String SHOPS_RATE_CLASS = "css-19p30tk";
    /**
     * SHOPSのロード中の表示のクラスid
     */
    public static final String SHOPS_LOADING_CLASS = "chakra-spinner";
    /**
     * 検索ページのアイテム一覧のURI
     */
    public static final String SEARCH_ITEMS = "https://api.mercari.jp/v2/entities:search";
    /**
     * セラー情報のアイテム一覧のURI
     */
    public static final String SELLER_ITEMS = "https://api.mercari.jp/items/get_items";
    /**
     * 検索ページのshops以外の画像の場合
     */
    public static final String SEARCH_IMAGES = "https://static.mercdn.net/c!/w=240,f=webp/thumb/photos/";
    /**
     * 検索ページのshops以外の画像の場合
     */
    public static final String USER_IMAGES = "https://static.mercdn.net/c!/w=240/thumb/photos/";
    /**
     * SHOPSに出品されているアイテムの画像
     */
    public static final String SHOP_IMAGES = "https://assets.mercari-shops-static.com/-/small/plain/";
    /**
     * アイテムページの基礎URL
     */
    public static final String ITEM_BASE_URL = "https://jp.mercari.com/item/";
    /**
     * セラーのページのベースURL
     */
    public static final String USER_PROFILE = "https://jp.mercari.com/user/profile/";
    /**
     * セラー情報を取得するときのAPIのURL
     */
    public static final String USER_PROFILE_API = "https://api.mercari.jp/users/get_profile";
    /**
     * ユーザーの評価ページ
     */
    public static final String USER_REVIEWS = "/user/reviews/";
    /**
     * 評価履歴
     */
    public static final String USER_REVIEWS_API = "https://api.mercari.jp/reviews/history";
    /**
     * セラー情報を取得するときのAPIのURL
     */
    public static final String GET_USER_PROFILE = "https://api.mercari.jp/users/get_profile";
    /**
     * 一般ユーザーの商品の正規表現
     */
    public static final String USER_ITEM_REGEX = "^m[0-9]{11}$";
    /**
     * ユーザーIDの正規表現
     */
    public static final String USER_PROFILE_REGEX = "^[0-9]{9}$";
    /**
     * 普通のアイテムの文字列の長さ
     */
    public static final int USUAL_ITEM_LENGTH = 12;
}
