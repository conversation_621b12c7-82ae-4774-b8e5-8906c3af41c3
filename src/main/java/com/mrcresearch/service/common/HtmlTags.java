package com.mrcresearch.service.common;

public class HtmlTags {
    public static final String html = "<html>";
    public static final String htmlEnd = "</html>";
    public static final String head = "<head>";
    public static final String headEnd = "</head>";
    public static final String body = "<body>";
    public static final String bodyEnd = "</body>";
    public static final String table = "<table>";
    public static final String tableEnd = "</table>";
    public static final String thead = "<thead>";
    public static final String theadEnd = "</thead>";
    public static final String tr = "<tr>";
    public static final String trEnd = "</tr>";
    public static final String tbody = "<tbody>";
    public static final String tbodyEnd = "</tbody>";
    public static final String td = "<td>";
    public static final String tdEnd = "</td>";
    public static final String img = "<img>";
    public static final String a = "<a>";
    public static final String aEnd = "</a>";
    public static final String br = "<br>";
    public static final String h3 = "<h3>";
    public static final String h3End = "</h3>";
    public static final String tfoot = "<tfoot>";
    public static final String tfootEnd = "</tfoot>";
    public static final String th = "<th>";
    public static final String thEnd = "</th>";

    public static class Options {
        public static final String CLASS = "class";
        public static final String STYLE = "style";
        public static final String ID = "id";
        public static final String NAME = "name";
        public static final String VALUE = "value";
        public static final String SRC = "src";
        public static final String ALT = "alt";
        public static final String TITLE = "title";
        public static final String INTEGRITY = "integrity";
        public static final String TYPE = "type";
        public static final String REL = "rel";
        public static final String LOADING = "loading";
        public static final String HREF = "href";
        public static final String TARGET = "target";
    }
}
