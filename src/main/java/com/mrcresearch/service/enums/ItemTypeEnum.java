package com.mrcresearch.service.enums;

/**
 * Enum for item type with ID mapping
 * 通常出品：1, SHOPS：2
 */
public enum ItemTypeEnum {
    NORMAL(1, "通常出品", "ITEM_TYPE_NORMAL"),
    SHOPS(2, "SHOPS", "ITEM_TYPE_SHOPS");

    private final int id;
    private final String displayName;
    private final String apiType;

    ItemTypeEnum(int id, String displayName, String apiType) {
        this.id = id;
        this.displayName = displayName;
        this.apiType = apiType;
    }

    public int getId() {
        return id;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getApiType() {
        return apiType;
    }

    /**
     * Convert item type string to ID
     *
     * @param itemType Item type string from API
     * @return Item type ID, or 1 (NORMAL) as default
     */
    public static int getIdFromType(String itemType) {
        if (itemType == null) {
            return NORMAL.getId();
        }

        for (ItemTypeEnum typeEnum : values()) {
            if (typeEnum.getApiType().equals(itemType)) {
                return typeEnum.getId();
            }
        }

        // Check for SHOPS type by shop name presence
        // If itemType contains "SHOPS" or similar indicators
        if (itemType.toUpperCase().contains("SHOPS") || itemType.toUpperCase().contains("SHOP")) {
            return SHOPS.getId();
        }

        // Default to NORMAL if type is not recognized
        return NORMAL.getId();
    }

    /**
     * Get enum from item type string
     *
     * @param itemType Item type string from API
     * @return ItemTypeEnum, or NORMAL as default
     */
    public static ItemTypeEnum fromType(String itemType) {
        if (itemType == null) {
            return NORMAL;
        }

        for (ItemTypeEnum typeEnum : values()) {
            if (typeEnum.getApiType().equals(itemType)) {
                return typeEnum;
            }
        }

        // Check for SHOPS type by shop name presence
        if (itemType.toUpperCase().contains("SHOPS") || itemType.toUpperCase().contains("SHOP")) {
            return SHOPS;
        }

        // Default to NORMAL if type is not recognized
        return NORMAL;
    }

    /**
     * Get enum from ID
     *
     * @param id Item type ID
     * @return ItemTypeEnum, or NORMAL as default
     */
    public static ItemTypeEnum fromId(int id) {
        for (ItemTypeEnum typeEnum : values()) {
            if (typeEnum.getId() == id) {
                return typeEnum;
            }
        }
        return NORMAL;
    }

    /**
     * Get the display name from ID
     *
     * @param id Item type ID
     * @return Display name or null if not found
     */
    public static String getDisplayNameFromId(int id) {
        for (ItemTypeEnum typeEnum : values()) {
            if (typeEnum.getId() == id) {
                return typeEnum.getDisplayName();
            }
        }
        return null;
    }

    /**
     * Get the display name from ID
     *
     * @param id Item type ID
     * @return Display name or null if not found
     */
    public static String getDisplayNameFromId(String id) {
        return getDisplayNameFromId(Integer.parseInt(id));
    }

    /**
     * Get enum from display name
     *
     * @param displayName Display name of the item type
     * @return ItemTypeEnum, or NORMAL as default
     */
    public static ItemTypeEnum fromDisplayName(String displayName) {
        if (displayName == null) {
            return NORMAL;
        }

        for (ItemTypeEnum typeEnum : values()) {
            if (typeEnum.getDisplayName().equals(displayName)) {
                return typeEnum;
            }
        }
        return NORMAL;
    }

    /**
     * Determine item type based on shop name
     *
     * @param shopName Shop name from item data
     * @return Item type ID
     */
    public static int getIdFromShopName(String shopName) {
        if (shopName != null && !shopName.trim().isEmpty()) {
            return SHOPS.getId();
        }
        return NORMAL.getId();
    }

    /**
     * Determine item type from item ID using regex patterns
     *
     * @param itemId The item ID to check
     * @return ItemTypeEnum based on item ID pattern
     */
    public static ItemTypeEnum fromItemId(String itemId) {
        if (itemId == null || itemId.trim().isEmpty()) {
            return NORMAL;
        }

        // Check if it matches the regular user item pattern (^m[0-9]{11}$)
        if (itemId.matches(com.mrcresearch.service.common.MercariApi.USER_ITEM_REGEX)) {
            return NORMAL;
        }

        // If it doesn't match the regular pattern, it's likely a SHOPS item
        // SHOPS items typically have different ID patterns
        return SHOPS;
    }
}
