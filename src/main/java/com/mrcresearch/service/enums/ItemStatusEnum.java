package com.mrcresearch.service.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * Enum for item status with ID mapping
 * 販売中：1, 取引中：2, 取引完了：3
 */
public enum ItemStatusEnum {
    ON_SALE(1, "販売中", "ITEM_STATUS_ON_SALE", "on_sale"),
    TRADING(2, "売り切れ", "ITEM_STATUS_TRADING", "trading"),
    SOLD_OUT(3, "売り切れ", "ITEM_STATUS_SOLD_OUT", "sold_out");

    private final int id;
    private final String displayName;
    private final String apiStatus;
    private final String userStatus;

    ItemStatusEnum(int id, String displayName, String apiStatus, String userStatus) {
        this.id = id;
        this.displayName = displayName;
        this.apiStatus = apiStatus;
        this.userStatus = userStatus;
    }

    public int getId() {
        return id;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getApiStatus() {
        return apiStatus;
    }

    public String getUserStatus() {
        return userStatus;
    }

    /**
     * Convert status string to ID
     *
     * @param status Status string from API
     * @return Status ID, or 1 (ON_SALE) as default
     */
    public static int getIdFromStatus(String status) {
        if (status == null) {
            return ON_SALE.getId();
        }

        if (StringUtils.isNumeric(status)) {
            return Integer.parseInt(status);
        }

        for (ItemStatusEnum statusEnum : values()) {
            if (statusEnum.getApiStatus().equals(status) || statusEnum.getUserStatus().equals(status)) {
                return statusEnum.getId();
            }
        }

        // Default to ON_SALE if status is not recognized
        return ON_SALE.getId();
    }

    /**
     * Get enum from status string
     *
     * @param status Status string from API
     * @return ItemStatusEnum, or ON_SALE as default
     */
    public static ItemStatusEnum fromStatus(String status) {
        if (status == null) {
            return ON_SALE;
        }

        for (ItemStatusEnum statusEnum : values()) {
            if (statusEnum.getApiStatus().equals(status) || statusEnum.getUserStatus().equals(status)) {
                return statusEnum;
            }
        }

        // Default to ON_SALE if status is not recognized
        return ON_SALE;
    }

    /**
     * Get enum from ID
     *
     * @param id Status ID
     * @return ItemStatusEnum, or ON_SALE as default
     */
    public static ItemStatusEnum fromId(int id) {
        for (ItemStatusEnum statusEnum : values()) {
            if (statusEnum.getId() == id) {
                return statusEnum;
            }
        }
        return ON_SALE;
    }

    /**
     * Check if status represents an on-sale item
     *
     * @param status Status string
     * @return true if item is on sale
     */
    public static boolean isOnSale(String status) {
        ItemStatusEnum statusEnum = fromStatus(status);
        return statusEnum == ON_SALE;
    }

    /**
     * get display name from status string
     *
     * @param status ステータス
     * @return 表示名
     */
    public static String getDisplayNameById(String status) {
        for (ItemStatusEnum statusEnum : values()) {
            if (statusEnum.getId() == Integer.parseInt(status)) {
                return statusEnum.getDisplayName();
            }
        }
        return "";
    }
}
