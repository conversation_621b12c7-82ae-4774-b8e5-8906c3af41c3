package com.mrcresearch.service.auth.model;

import lombok.Data;

@Data
public class AuthStatus {
    public static final String ACTIVE = "ACTIVE";
    public static final String INACTIVE = "INACTIVE";
    public static final String FAIL_LOGIN = "FAIL_LOGIN";

    String status;
    String expireDate;

    public AuthStatus(String status, String expireDate) {
        this.status = status;
        this.expireDate = expireDate;
    }

    public AuthStatus() {

    }
}
