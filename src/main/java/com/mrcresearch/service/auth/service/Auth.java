package com.mrcresearch.service.auth.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.mrcresearch.service.auth.model.AuthStatus;
import com.mrcresearch.service.common.Connection;
import com.mrcresearch.service.common.HttpMethod;
import io.netty.handler.codec.http.HttpResponseStatus;

import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * ログイン処理を実行するクラス
 */
public class Auth {
    private static final String DATE_FORMAT = "yyyy/MM/dd";
    private static final String HISTORY_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static final String CHARGE_DATE_FORMAT = "yyyy/MM/dd HH:mm:ss.SSS";

    /**
     * ログイン処理を実行する
     *
     * @param email    メールアドレス
     * @param password パスワード
     * @return 認証ステータス
     * @throws IOException 入出力エラー
     */
    public AuthStatus getLoginResult(String email, String password) throws IOException {
        System.setProperty("https.protocols", "TLSv1.2");
        AuthStatus authStatus = new AuthStatus(AuthStatus.FAIL_LOGIN, null);

        URL url = new URL("https://mcrsldrschtl.com/buyhistory_api");
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod(HttpMethod.POST);
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);

        // リクエストボディにJSONとして認証情報を含める
        String jsonInputString = "{\"email\":\"" + email + "\",\"password\":\"" + password + "\"}";
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = jsonInputString.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }

        connection.connect();

        if (HttpResponseStatus.OK.code() == connection.getResponseCode()) {
            // レスポンスの内容を取得する
            JsonNode rootNode = Connection.getJsonResponse(connection);

            // 購入履歴
            LocalDate date = LocalDate.parse(
                    rootNode.path("history").path("end_date").toString().replace("\"", ""),
                    DateTimeFormatter.ofPattern(HISTORY_DATE_FORMAT));

            if (date.isAfter(LocalDate.now())) {
                authStatus.setStatus(AuthStatus.ACTIVE);
                authStatus.setExpireDate(date.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
                return authStatus;
            }

            // サブスクリプション
            LocalDate next_charge = LocalDate.parse(
                    rootNode.path("subscription_next_charge").toString().replace("\"", ""),
                    DateTimeFormatter.ofPattern(CHARGE_DATE_FORMAT));

            if (next_charge.isAfter(LocalDate.now()) && AuthStatus.ACTIVE.equals(rootNode.path("subscription_status").toString().replace("\"", ""))) {
                authStatus.setStatus(AuthStatus.ACTIVE);
                authStatus.setExpireDate(next_charge.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
                return authStatus;
            }
        }
        // 成功しなかった場合は、ステータスだけを設定する
        return authStatus;
    }
}
