package com.mrcresearch.service;

import com.mrcresearch.screen.model.SellerModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.mrcresearch.screen.model.SellerSearchModel;
import com.mrcresearch.util.database.DatabaseConnectionManager;
import com.mrcresearch.util.database.SellerRepository;
import com.mrcresearch.util.database.SellerSearchRepository;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * Service class for comprehensive seller information management
 * Provides high-level operations for seller data management and name change detection
 */
public class SellerManagementService {

    private static final Logger logger = LoggerFactory.getLogger(SellerManagementService.class);

    /**
     * Get or create a seller with the latest information
     * This method checks both the sellers table and seller_searches table to get the most up-to-date information
     *
     * @param sellerId The seller ID
     * @return The seller model with the latest information
     */
    public static SellerModel getOrCreateSeller(String sellerId) {
        if (sellerId == null || sellerId.trim().isEmpty()) {
            return null;
        }

        // First, try to get from the sellers master table
        SellerModel seller = SellerRepository.getSellerById(sellerId);

        // If not found in sellers table, check seller_searches table for any existing information
        if (seller == null) {
            String sellerName = getSellerNameFromSellerSearchesDirect(sellerId);
            if (sellerName != null && !sellerName.equals(sellerId) && !sellerName.startsWith("セラー_")) {
                // Create new seller from seller_searches data
                seller = new SellerModel(sellerId, sellerName);
                // Save to sellers table
                SellerRepository.saveOrUpdateSeller(seller);
            } else {
                // Create new seller with just the ID
                seller = new SellerModel(sellerId, sellerId);
                SellerRepository.saveOrUpdateSeller(seller);
            }
        } else {
            // Update seller with latest information from seller_searches if available
            updateSellerWithLatestInfoDirect(seller);
        }

        return seller;
    }

    /**
     * Update seller information with the latest data from seller_searches table
     *
     * @param seller The seller to update
     * @return true if the seller was updated, false otherwise
     */
    public static boolean updateSellerWithLatestInfo(SellerModel seller) {
        if (seller == null || seller.getSellerId() == null) {
            return false;
        }

        // Get latest information from seller_searches table
        SellerSearchModel sellerSearch = SellerSearchRepository.getSellerSearchBySellerId(seller.getSellerId());

        if (sellerSearch != null && sellerSearch.getSellerName() != null &&
                !sellerSearch.getSellerName().equals(seller.getSellerId()) &&
                !sellerSearch.getSellerName().startsWith("セラー_")) {

            // Check if the name is different
            if (!sellerSearch.getSellerName().equals(seller.getSellerName())) {
                seller.updateSellerName(sellerSearch.getSellerName());
                return SellerRepository.saveOrUpdateSeller(seller);
            }
        }

        return false;
    }

    /**
     * Update seller information with the latest data from seller_searches table using direct query
     * to avoid circular reference
     *
     * @param seller The seller to update
     * @return true if the seller was updated, false otherwise
     */
    private static boolean updateSellerWithLatestInfoDirect(SellerModel seller) {
        if (seller == null || seller.getSellerId() == null) {
            return false;
        }

        // Get latest information from seller_searches table directly
        String sellerName = getSellerNameFromSellerSearchesDirect(seller.getSellerId());

        if (sellerName != null && !sellerName.equals(seller.getSellerId()) &&
                !sellerName.startsWith("セラー_")) {

            // Check if the name is different
            if (!sellerName.equals(seller.getSellerName())) {
                seller.updateSellerName(sellerName);
                return SellerRepository.saveOrUpdateSeller(seller);
            }
        }

        return false;
    }

    /**
     * Update seller name and detect changes
     *
     * @param sellerId      The seller ID
     * @param newSellerName The new seller name
     * @return true if the seller name was updated, false otherwise
     */
    public static boolean updateSellerName(String sellerId, String newSellerName) {
        if (sellerId == null || sellerId.trim().isEmpty() ||
                newSellerName == null || newSellerName.trim().isEmpty()) {
            return false;
        }

        // Skip if the new name is just the seller ID or a placeholder
        if (newSellerName.equals(sellerId) || newSellerName.startsWith("セラー_")) {
            return false;
        }

        SellerModel seller = getOrCreateSeller(sellerId);
        if (seller != null) {
            boolean nameChanged = seller.updateSellerName(newSellerName);
            if (nameChanged) {
                return SellerRepository.saveOrUpdateSeller(seller);
            }
        }

        return false;
    }

    /**
     * Get the best available seller name for display
     * This method prioritizes the sellers table, then falls back to seller_searches table
     *
     * @param sellerId The seller ID
     * @return The best available seller name, or the seller ID if no name is found
     */
    public static String getBestSellerName(String sellerId) {
        if (sellerId == null || sellerId.trim().isEmpty()) {
            return "";
        }

        // First check the sellers master table
        SellerModel seller = SellerRepository.getSellerById(sellerId);
        if (seller != null && seller.hasValidName()) {
            return seller.getSellerName();
        }

        // Fall back to seller_searches table with direct database query to avoid circular reference
        String sellerName = getSellerNameFromSellerSearchesDirect(sellerId);
        if (sellerName != null && !sellerName.equals(sellerId) && !sellerName.startsWith("セラー_")) {
            return sellerName;
        }

        // Return seller ID as fallback
        return sellerId;
    }

    /**
     * Get seller name directly from seller_searches table without using SellerSearchRepository
     * to avoid circular reference
     *
     * @param sellerId The seller ID to look up
     * @return The seller name from seller_searches table, or null if not found
     */
    private static String getSellerNameFromSellerSearchesDirect(String sellerId) {
        String sql = "SELECT seller_name FROM seller_searches WHERE seller_id = ? ORDER BY updated_date DESC LIMIT 1";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, sellerId);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("seller_name");
                }
            }
        } catch (SQLException e) {
            logger.error("Error getting seller name from seller_searches: " + e.getMessage(), e);
        }

        return null;
    }

    /**
     * Migrate seller information from seller_searches table to sellers table
     * This method helps with the transition to the new seller management system
     *
     * @param batchSize The number of records to process in each batch
     * @return The number of sellers migrated
     */
    public static int migrateFromSellerSearches(int batchSize) {
        int migratedCount = 0;
        int page = 1;

        while (true) {
            List<SellerSearchModel> sellerSearches = SellerSearchRepository.getSellerSearches(page, batchSize);

            if (sellerSearches.isEmpty()) {
                break;
            }

            for (SellerSearchModel sellerSearch : sellerSearches) {
                if (sellerSearch.getSellerId() != null) {
                    // Check if seller already exists in sellers table
                    SellerModel existingSeller = SellerRepository.getSellerById(sellerSearch.getSellerId());

                    if (existingSeller == null) {
                        // Create new seller from seller_searches data
                        SellerModel newSeller = new SellerModel(
                                sellerSearch.getSellerId(),
                                sellerSearch.getSellerName() != null ? sellerSearch.getSellerName() : sellerSearch.getSellerId(),
                                sellerSearch.isSellerNameChanged()
                        );

                        // Use the dates from seller_searches if available
                        if (sellerSearch.getAddedDate() != null) {
                            newSeller.setCreatedAt(sellerSearch.getAddedDate());
                        }
                        if (sellerSearch.getUpdatedDate() != null) {
                            newSeller.setUpdatedAt(sellerSearch.getUpdatedDate());
                        }

                        if (SellerRepository.saveOrUpdateSeller(newSeller)) {
                            migratedCount++;
                        }
                    } else {
                        // Update existing seller with latest information if needed
                        boolean updated = false;

                        if (sellerSearch.getSellerName() != null &&
                                !sellerSearch.getSellerName().equals(existingSeller.getSellerName())) {
                            existingSeller.updateSellerName(sellerSearch.getSellerName());
                            updated = true;
                        }

                        if (sellerSearch.isSellerNameChanged() && !existingSeller.isSellerNameChanged()) {
                            existingSeller.setSellerNameChanged(true);
                            updated = true;
                        }

                        if (updated) {
                            SellerRepository.saveOrUpdateSeller(existingSeller);
                        }
                    }
                }
            }

            page++;
        }

        logger.info("移行が完了しました。" + migratedCount + " 件のセラーを移行しました。");
        return migratedCount;
    }

    /**
     * Synchronize seller information across all tables
     * This method ensures consistency between sellers table and other seller-related tables
     *
     * @return The number of records synchronized
     */
    public static int synchronizeSellerInformation() {
        int synchronizedCount = 0;
        int page = 1;
        int pageSize = 100;

        while (true) {
            List<SellerModel> sellers = SellerRepository.getAllSellers(page, pageSize);

            if (sellers.isEmpty()) {
                break;
            }

            for (SellerModel seller : sellers) {
                if (updateSellerWithLatestInfo(seller)) {
                    synchronizedCount++;
                }
            }

            page++;
        }

        logger.info("同期が完了しました。" + synchronizedCount + " 件のセラーを同期しました。");
        return synchronizedCount;
    }

    /**
     * Get sellers that need name updates
     * Returns sellers where the name might be outdated compared to seller_searches table
     *
     * @param page     The page number (1-based)
     * @param pageSize The number of records per page
     * @return A list of sellers that might need updates
     */
    public static List<SellerModel> getSellersNeedingUpdate(int page, int pageSize) {
        // For now, return sellers with name changes
        // This can be enhanced to include more sophisticated logic
        return SellerRepository.getSellersWithNameChanges(page, pageSize);
    }

    /**
     * Check if a seller name is valid (not just the seller ID or placeholder)
     *
     * @param sellerId   The seller ID
     * @param sellerName The seller name to check
     * @return true if the name is valid, false otherwise
     */
    public static boolean isValidSellerName(String sellerId, String sellerName) {
        return sellerName != null &&
                !sellerName.trim().isEmpty() &&
                !sellerName.equals(sellerId) &&
                !sellerName.startsWith("セラー_");
    }

    /**
     * Update the research status of a seller
     *
     * @param sellerId       The seller ID
     * @param researchStatus The new research status (待機中, リサーチ中, 完了, エラー)
     * @return true if the update was successful, false otherwise
     */
    public static boolean updateSellerResearchStatus(String sellerId, String researchStatus) {
        if (sellerId == null || sellerId.trim().isEmpty()) {
            logger.error("Invalid seller ID for research status update");
            return false;
        }

        if (researchStatus == null || researchStatus.trim().isEmpty()) {
            logger.error("Invalid research status for seller: " + sellerId);
            return false;
        }

        try {
            // Ensure seller exists
            SellerModel seller = getOrCreateSeller(sellerId);
            if (seller == null) {
                logger.error("Failed to get or create seller: " + sellerId);
                return false;
            }

            // Update research status
            return SellerRepository.updateSellerResearchStatus(sellerId, researchStatus);
        } catch (Exception e) {
            logger.error("Error updating research status for seller " + sellerId + ": " + e.getMessage(), e);
            return false;
        }
    }
}
