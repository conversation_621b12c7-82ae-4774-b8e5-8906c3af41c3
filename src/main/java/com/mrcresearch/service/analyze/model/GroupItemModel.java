package com.mrcresearch.service.analyze.model;

import com.mrcresearch.service.common.MercariApi;
import com.mrcresearch.service.property.ApplicationsProperty;
import com.mrcresearch.service.tools.StringUtility;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Objects;

/**
 * アイテムごとにまとめるためのモデル<br />
 * リストとして扱うことを前提とする<br />
 */
@Data
public class GroupItemModel {
    private static final int MAX_DAY = Integer.parseInt(
            ApplicationsProperty.getProperty(ApplicationsProperty.SAVE_DAYS)) + 1; // 取得する日にちを設定する
    private String id; // 最初のアイテムID
    private String name; // アイテム名
    private String sellerId; // セラーID
    private String price; // 価格（最初に取得）
    private String imageUrl; // 画像URL
    private byte[] image; // 画像
    private int onTrade; // 販売中
    private int display; // 売れた数
    private int[] updated; // 売れた日付

    /**
     * GroupItemModelを生成する<br />
     * 1つのアイテムをGroupItemModelに変換する<br />
     *
     * @param item    アイテム
     * @param timeNow 現在の時刻
     */
    public GroupItemModel(GetItemModel item, LocalDateTime timeNow) {
        StringUtility stringUtility = new StringUtility();

        this.sellerId = item.getSellerId();
        this.id = item.getId();
        this.name = StringUtility.moldItemName(item.getName());
        this.price = item.getPrice();
        this.image = item.getImage();
        this.updated = new int[MAX_DAY];
        if (0 == item.getSoldStatus()) {
            this.onTrade = 1;
            this.display = 0;
        } else {
            this.onTrade = 0;
            this.display = 1;
            countUpdated(timeNow, item.getUpdated());
        }
    }

    /**
     * 売れた日付情報を変換して配列に組み込む<br />
     * メルカリで表示される5日(24*6日)で売れた数を計算する<br />
     *
     * @param timeNow     現在の時刻
     * @param updatedTime 商品のアップデート時刻
     */
    public void countUpdated(LocalDateTime timeNow, String updatedTime) {
        // 更新が何時間以内かで度の配列に入れるか決める
        // 日本時間（JST）を使用して計算
        Instant instant = Instant.ofEpochSecond(Long.parseLong(StringUtils.defaultIfEmpty(updatedTime, "0")));
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.of("Asia/Tokyo"));
        int indexNum = (int) (ChronoUnit.HOURS.between(localDateTime, timeNow) / 24);
        if (indexNum < MAX_DAY) {
            updated[indexNum]++;
        }
    }

    /**
     * 選択した日付内の売れた数を合計する<br />
     *
     * @return 売れた数
     */
    public int sumUpdated() {
        int sum = 0;
        for (int i = 0; i < MAX_DAY; i++) {
            sum += updated[i];
        }
        return sum;
    }

    /**
     * 売れた数を加算する<br />
     */
    // 売れた数を加算する
    public void addDisplay() {
        this.display++;
    }

    /**
     * 取引中を加算する<br />
     */
    public void addOnTrade() {
        this.onTrade++;
    }

    /**
     * ユーザーページのURLを取得する
     *
     * @return ユーザーページのURL
     */
    public String getUserPageUrl() {
        if (Objects.isNull(sellerId)) return null;
        return (sellerId.matches(MercariApi.USER_PROFILE_REGEX) ?
                MercariApi.USER_PROFILE :
                MercariApi.SHOPS_PROFILE_URL) + sellerId;
    }

    /**
     * 商品ページのURLを取得する
     *
     * @return 商品ページのURL
     */
    public String getItemIdPageUrl() {
        if (Objects.isNull(id)) return null;
        return (id.matches(MercariApi.USER_ITEM_REGEX) ?
                MercariApi.ITEM_BASE_URL :
                MercariApi.PRODUCT_PAGE_BASE_URL) + id;
    }
}
