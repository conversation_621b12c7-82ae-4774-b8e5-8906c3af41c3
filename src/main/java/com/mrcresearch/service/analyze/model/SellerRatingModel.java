package com.mrcresearch.service.analyze.model;

import lombok.Data;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

@Data
public class SellerRatingModel {
    private String id; // セラーID
    private String name; // 名前
    private String reviewCount; // 評価数
    private String sellItemCount; // 出品数
    private String latestRateDate; // 評価最新
    private String oldestRateDate; // 評価最古

    /**
     * 日付の差を返す
     *
     * @return 日付の差
     */
    public String getDateDiff() {
        // latestRateDate と oldestRateDate の差分を計算する
        // クラス定義、メソッド定義の省略
        LocalDateTime latestDate = LocalDateTime.ofInstant(Instant.ofEpochSecond(Long.parseLong(latestRateDate)), ZoneId.systemDefault());
        LocalDateTime oldestDate = LocalDateTime.ofInstant(Instant.ofEpochSecond(Long.parseLong(oldestRateDate)), ZoneId.systemDefault());
        Duration summerVacationDuration = Duration.between(oldestDate, latestDate);// 期間分の時間を取得する
        return String.valueOf(summerVacationDuration.toDays());
    }

    /**
     * 1日の平均販売数をすh得する
     *
     * @return 1日の平均販売数
     */
    public String getDayAverage() {
        int dateDiff = Integer.parseInt(getDateDiff());
        if (Integer.parseInt(reviewCount) > 100) {
            return String.valueOf(100 / dateDiff);
        } else {
            return String.valueOf(Integer.parseInt(reviewCount) / dateDiff);
        }
    }

    public String getLatestRateDateString() {
        LocalDateTime latestDate = LocalDateTime.ofInstant(Instant.ofEpochSecond(Long.parseLong(latestRateDate)), ZoneId.systemDefault());
        return latestDate.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
    }

    public String getOldestRateDateString() {
        LocalDateTime oldestDate = LocalDateTime.ofInstant(Instant.ofEpochSecond(Long.parseLong(oldestRateDate)), ZoneId.systemDefault());
        return oldestDate.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
    }
}
