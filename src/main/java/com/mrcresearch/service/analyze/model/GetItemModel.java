package com.mrcresearch.service.analyze.model;

import com.mrcresearch.service.enums.ItemStatusEnum;
import com.mrcresearch.service.models.json.shops.graphhql.Asset;
import com.mrcresearch.service.models.json.shops.graphhql.Node;
import com.mrcresearch.service.models.sellerItem.ItemData;
import com.mrcresearch.service.tools.StringUtility;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

/**
 * アイテム情報取得用のモデル
 */
@Data
@NoArgsConstructor
public class GetItemModel {
    /**
     * アイテムID
     */
    private String id;
    /**
     * 販売状況
     */
    private int soldStatus;
    /**
     * 販売者ID
     */
    private String sellerId;
    /**
     * 商品名
     */
    private String name;
    /**
     * カテゴリー
     */
    private String category;
    /**
     * 発送方法
     */
    private int shippingMethodId;
    /**
     * 画像
     */
    private byte[] image;
    /**
     * サムネイルURL
     */
    private String thumbnailUrl;
    /**
     * 価格
     */
    private String price;
    /**
     * 追加日時
     */
    private String created;
    /**
     * 更新日時
     */
    private String updated;
    /**
     * SHOPS用画像ID
     */
    private List<String> imageId;
    /**
     * SHOPS用画像URL
     */
    private String imageUrl;


    /**
     * アイテムの情報をItemDataから設定する
     *
     * @param item 　ItemData
     */
    public GetItemModel(ItemData item) {
        this.setSellerId(null);
        this.setId(item.getId());
        this.setName(item.getName());
        this.setCategory(item.getParentCategoriesNTiers().get(item.getParentCategoriesNTiers().size() - 1).id);
        this.setShippingMethodId(item.getShippingMethodId());
        this.setPrice(item.getPrice());
        this.setSoldStatus(ItemStatusEnum.getIdFromStatus(item.getStatus()));
        this.setThumbnailUrl(item.getThumbnails().get(0));
        this.setCreated(item.getCreated());
        this.setUpdated(item.getUpdated());
    }

    /**
     * ProductのNodeからGetItemModelを生成する
     *
     * @param node レビューのノード
     */
    public GetItemModel(Node node, String shopId) {
        String id = node.getProduct().getId();
        if (Objects.isNull(id)) {
            id = node.getProduct().getRef().replace("Product:", "");
        }
        String name = StringUtility.moldItemName(node.getProduct().getName());
        String createdAt = node.getCreatedAt();
        List<String> imageUrlList = null;
        if (node.getProduct().getAssets() != null) {
            imageUrlList = node.getProduct().getAssets().stream()
                    .map(Asset::getImageUrl)
                    .toList();
        }

        //とりあえずデフォルトのカテゴリーとかを設定する
        this.setCategory("0");
        this.setShippingMethodId(0);

        this.setSellerId(shopId);
        this.setId(id);
        this.setName(name);
        this.setSoldStatus(1);
        this.setUpdated(createdAt);
        this.setImageId(imageUrlList);
        this.setThumbnailUrl(Objects.isNull(imageUrlList) ? null : imageUrlList.get(0));
    }
}
