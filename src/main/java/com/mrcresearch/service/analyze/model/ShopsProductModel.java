package com.mrcresearch.service.analyze.model;

import com.fasterxml.jackson.databind.JsonNode;
import com.mrcresearch.service.tools.StringUtility;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ShopsProductModel {
    private String name;
    private String id;
    private int price;
    private String imageUrl;


    /**
     * ShopsProductModelを生成する
     *
     * @param name  　商品名
     * @param id    商品ID
     * @param price 価格
     */
    public ShopsProductModel(String name, String id, int price) {
        StringUtility stringUtility = new StringUtility();
        this.name = StringUtility.moldItemName(name);
        this.id = id;
        this.price = price;
    }

    /**
     * JsonNodeからShopsProductModelを生成する<br />
     * JsonNodeのpath("Product")を受け取る想定。
     *
     * @param node ProductのJsonNode
     */
    public ShopsProductModel(JsonNode node) {
        this.name = StringUtility.moldItemName(node.path("name").asText());
        this.id = node.path("id").asText();
        this.price = node.path("price").asInt();
    }
}
