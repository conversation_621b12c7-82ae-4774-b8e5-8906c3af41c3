package com.mrcresearch.service.analyze.service;

import com.browserup.harreader.model.HarEntry;
import com.browserup.harreader.model.HttpMethod;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mrcresearch.screen.model.SellerSearchModel;
import com.mrcresearch.service.analyze.model.GetItemModel;
import com.mrcresearch.service.analyze.model.GroupItemModel;
import com.mrcresearch.service.common.JavaScripts;
import com.mrcresearch.service.common.MercariApi;
import com.mrcresearch.service.models.item.Item;
import com.mrcresearch.service.tools.DriverTool;
import com.mrcresearch.service.tools.InfoUtil;
import com.mrcresearch.service.tools.StringUtility;
import com.mrcresearch.util.SellerSearchDataConverter;
import com.mrcresearch.util.database.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

/**
 * セラーリスト検索の解析を行う
 */
public class SellerListAnalyze {
    private static final Logger logger = LoggerFactory.getLogger(SellerListAnalyze.class);
    private static final StringUtility stringUtility = new StringUtility();
    private static final InfoUtil info = new InfoUtil();
    private static int sleepTime;
    // ツールを読み込む
    private DriverTool driverTool;

    /**
     * 取得したアイテム一覧を出力用の書式にする
     *
     * @param getItemList 解析したharのアイテムリスト
     * @return harのアイテムリストを一意のものに変換した内容
     */
    public static List<GroupItemModel> setAnalyzedList(List<GetItemModel> getItemList) {
        info.printDebug("Setting analyzed list");
        // いったんスリープさせる
        try {
            Thread.sleep(sleepTime);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(e);
        }
        List<GroupItemModel> analizedList = new ArrayList<>();
        LocalDateTime timeNow = LocalDateTime.now();
        List<String> uniqueList = new ArrayList<>();

        for (GetItemModel item : getItemList) {
            // csv出力の際に形式を整えるために「,」と全角スペースは半角スペースにする
            String itemName = StringUtility.moldItemName(item.getName());

            // もしも既にあればカウント、無ければ新しく入れていく
            if (uniqueList.contains(itemName)) {
                addUniqueList(analizedList, uniqueList, item, timeNow);
            } else {
                uniqueList.add(itemName);
                analizedList.add(new GroupItemModel(item, timeNow));
            }
        }
        return analizedList;
    }

    /**
     * 一意のアイテムリストに追加する
     *
     * @param analizedList すでに設定されている一意のアイテムリスト
     * @param uniqueList   一意のアイテム名のリスト
     * @param item         アイテムの情報
     * @param timeNow      現在の時刻
     */
    private static void addUniqueList(List<GroupItemModel> analizedList, List<String> uniqueList, GetItemModel item, LocalDateTime timeNow) {
        int indexNum = uniqueList.indexOf(StringUtility.moldItemName(item.getName()));
        if (0 == item.getSoldStatus()) {
            analizedList.get(indexNum).addOnTrade();
        } else {
            analizedList.get(indexNum).addDisplay();
            analizedList.get(indexNum).countUpdated(timeNow, item.getUpdated());
        }
        analizedList.get(indexNum).setImage(item.getImage());
    }

    /**
     * 実行するメソッド。引数にセラーリストを渡すことで、HARを取得して解析する
     *
     * @param sellerId セラーID
     * @throws Exception 失敗したときに例外をスローする
     */
    public void execute(String sellerId, DriverTool driverTool) throws Exception {
        // 設定の読み込み
        sleepTime = SettingsRepository.getBrowserSleepTime() * 1000 + 120;
        this.driverTool = driverTool;

        // 通常の出品とSHOPSで分かれる
        if (sellerId.matches(MercariApi.USER_PROFILE_REGEX)) {
            fromProfile(sellerId);
        } else {
            new FromShops().execute(driverTool, sellerId);
        }

    }

    /**
     * プロフィールを開き、取得していく<br />
     * データベースへの保存機能も含む
     *
     * @param seller 　セラー情報を取得する
     * @throws Exception 　失敗したときに例外をスローする
     */
    private void fromProfile(String seller) throws Exception {
        // プロファイルを開き、HARを取得する
        JavaScripts.ScrollToBottom(driverTool);

        // 商品のリサーチをする
        driverTool.getProfileHar(seller);
        // 名前をHARから取得
        String sellerName = getSellerName();

        // アイテムリストとして取得する
        List<GetItemModel> getItemList = driverTool.getItemListFromHAR();
        // 商品名を整形する
        getItemList = getItemList.stream().map(item -> {
            item.setName(StringUtility.moldItemName(item.getName()));
            return item;
        }).toList();

        logger.info("Found " + getItemList.size() + " items for seller: " + seller);

        // データベースに結果を保存
        saveSellerSearchResults(seller, sellerName, getItemList);
    }

    /**
     * セラー検索結果をデータベースに保存する
     *
     * @param sellerId    セラーID
     * @param sellerName  セラー名
     * @param getItemList 取得したアイテムリスト
     */
    private void saveSellerSearchResults(String sellerId, String sellerName, List<GetItemModel> getItemList) {
        try {
            Date now = new Date();

            logger.debug("セラー検索データ永続化を開始します: セラーID: " + sellerId);
            logger.debug("現在のタイムスタンプ: " + now);
            logger.debug("詳細タイムスタンプ: " + DatabaseDateTimeUtil.formatDetailedJapaneseTimezone(now));
            logger.debug("読みやすいタイムスタンプ: " + DatabaseDateTimeUtil.formatReadableDate(now));

            // セラー検索レコードを作成
            SellerSearchModel sellerSearch = new SellerSearchModel();
            sellerSearch.setSellerId(sellerId);
            sellerSearch.setSellerName(sellerName != null && !sellerName.trim().isEmpty() ? sellerName : sellerId);
            sellerSearch.setSellerNameChanged(false);
            sellerSearch.setLastResearchedAt(now);
            sellerSearch.setAddedDate(now);
            sellerSearch.setUpdatedDate(now);

            logger.debug("SellerSearchModel が以下の内容で作成されました:");
            logger.debug("  セラーID: " + sellerSearch.getSellerId());
            logger.debug("  セラー名: " + sellerSearch.getSellerName());
            logger.debug("  最終リサーチ日時: " + sellerSearch.getLastResearchedAt());
            logger.debug("  最終リサーチ日時 (詳細): " + DatabaseDateTimeUtil.formatDetailedDate(sellerSearch.getLastResearchedAt()));
            logger.debug("  最終リサーチ日時 (読みやすい): " + DatabaseDateTimeUtil.formatReadableDate(sellerSearch.getLastResearchedAt()));
            logger.debug("  追加日時: " + sellerSearch.getAddedDate());
            logger.debug("  追加日時 (詳細): " + DatabaseDateTimeUtil.formatDetailedDate(sellerSearch.getAddedDate()));
            logger.debug("  更新日時: " + sellerSearch.getUpdatedDate());
            logger.debug("  更新日時 (詳細): " + DatabaseDateTimeUtil.formatDetailedDate(sellerSearch.getUpdatedDate()));

            // セラー検索レコードを保存
            int sellerSearchId = SellerSearchRepository.saveSellerSearch(sellerSearch);
            if (sellerSearchId == -1) {
                logger.error("Failed to save seller search record for seller ID: " + sellerId);
                return;
            }

            logger.info("Saved seller search record with ID: " + sellerSearchId);

            // GetItemModelをItemオブジェクトに変換
            List<Item> items = SellerSearchDataConverter.convertGetItemListToItems(getItemList, sellerId);

            // アイテムをsearch_itemsテーブルに保存（メインデータテーブル）
            int savedItemCount = SearchItemRepository.saveSearchItems(items);
            logger.info("Saved " + savedItemCount + " items to search_items table");

            // アイテムIDを抽出してマッピングテーブル用に準備
            List<String> itemIds = SellerSearchDataConverter.extractItemIds(getItemList);

            // アイテム参照をseller_search_itemsテーブルに保存（マッピングテーブル）
            int defaultStatus = SellerSearchDataConverter.getDefaultSellerSearchStatus();
            int savedReferenceCount = SellerSearchItemRepository.saveSellerSearchItemReferences(
                    sellerSearchId, itemIds, defaultStatus
            );
            logger.info("Saved " + savedReferenceCount + " item references to seller_search_items table");

            logger.info("Successfully completed seller search data persistence for seller: " + sellerId);

        } catch (Exception e) {
            logger.error("Error saving seller search results for seller " + sellerId + ": " + e.getMessage(), e);
        }
    }

    /**
     * セラー名を取得する
     */
    private String getSellerName() throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        List<HarEntry> harEntries = driverTool.getHarEntries()
                .stream()
                .filter(
                        entry -> entry.getRequest().getUrl().contains(MercariApi.GET_USER_PROFILE)
                                && HttpMethod.GET.equals(entry.getRequest().getMethod()))
                .toList();

        if (!harEntries.isEmpty()) {
            JsonNode items = mapper.readTree(harEntries.get(0).getResponse().getContent().getText());

            Pattern illegalFileNamePattern = Pattern.compile("[/:*?\"<>|]");
            return illegalFileNamePattern.matcher(items.path("data").path("name").toString()).replaceAll("");
        }
        return "noName";
    }
}