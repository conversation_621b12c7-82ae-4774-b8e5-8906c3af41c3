package com.mrcresearch.service.analyze.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mrcresearch.screen.model.SellerSearchModel;
import com.mrcresearch.service.SellerManagementService;
import com.mrcresearch.service.analyze.model.GetItemModel;
import com.mrcresearch.service.analyze.model.GroupItemModel;
import com.mrcresearch.service.common.JavaScripts;
import com.mrcresearch.service.common.MercariApi;
import com.mrcresearch.service.enums.ItemStatusEnum;
import com.mrcresearch.service.models.item.Item;
import com.mrcresearch.service.models.json.shops.graphhql.Edge;
import com.mrcresearch.service.models.json.shops.graphhql.GraphHql;
import com.mrcresearch.service.models.json.shops.graphhql.Node;
import com.mrcresearch.service.models.json.shops.graphhql.Product;
import com.mrcresearch.service.tools.DecodeUtil;
import com.mrcresearch.service.tools.DriverTool;
import com.mrcresearch.service.tools.ImageTool;
import com.mrcresearch.service.tools.InfoUtil;
import com.mrcresearch.util.SellerSearchDataConverter;
import com.mrcresearch.util.database.SearchItemRepository;
import com.mrcresearch.util.database.SellerSearchItemRepository;
import com.mrcresearch.util.database.SellerSearchRepository;
import com.mrcresearch.util.database.SettingsRepository;
import com.browserup.harreader.model.HarEntry;
import com.browserup.harreader.model.HarResponse;
import org.apache.commons.lang3.StringUtils;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * mercari-shops.comの評価ページから評価結果を取得するためのクラス
 *
 * <AUTHOR>
 */
public class FromShops {

    private static final Logger logger = LoggerFactory.getLogger(FromShops.class);
    private static final InfoUtil info = new InfoUtil();
    private static final DecodeUtil decodeUtil = new DecodeUtil();
    private static DriverTool driverTool = new DriverTool();
    private static int sleepTime;
    private static String shopId;
    private static String shopName;
    private static List<Product> products;
    private int maxDay;
    private List<GetItemModel> getItemList;

    /**
     * SHOPSのURLを開き、評価ページを開く
     *
     * @throws Exception 例外
     */
    private void openRatePage() throws Exception {
        // devのdata-test-idがrating-countの要素をクリックする
        WebElement button = driverTool.driver.findElement(By.className(MercariApi.SHOPS_RATE_CLASS));
        if (Objects.nonNull(button)) {
            button.click();
            // 2秒スリープする
            Thread.sleep(2000);
        } else {
            throw new RuntimeException("評価ページを開けませんでした。");
        }
    }

    /**
     * 最新の日付が表示されるまでスクロールします。
     *
     * @throws InterruptedException    スレッドのスリープが中断された場合
     * @throws JsonProcessingException JSONの解析に失敗した場合
     */
    private void scrollToLatestDate() throws InterruptedException, IOException {
        Long beforeHeight = (Long) driverTool.executeJavaScriptWithReturn("document.body.scrollHeight;");
        driverTool.executeJavaScript("scroll(0, document.body.scrollHeight);");
        // いったん待ってから高さを確認する
        Thread.sleep(2000);
        Long afterHeight = (Long) driverTool.executeJavaScriptWithReturn("document.body.scrollHeight;");

        // 高さが変わらない　もしくは　ロード中であれば　スクロールしないで終わる
        if (Objects.equals(beforeHeight, afterHeight) || isStillLoading()) {
            return;
        }
        if (!isLatestDateDisplay()) {
            JavaScripts.stopLazyLoad(driverTool);
            scrollToLatestDate();
        }
    }

    /**
     * 最新の日付が表示されているか確認する
     *
     * @return 最新の日付が表示されている場合はtrue、そうでない場合はfalse
     * @throws JsonProcessingException JSONの解析に失敗した場合
     */
    private boolean isLatestDateDisplay() throws IOException {
        List<HarResponse> harResponses = driverTool.getHarEntries().stream().filter(
                entry -> entry.getRequest().getUrl().contains(MercariApi.SHOPS_GRAPHQL)
                        && "base64".equals(entry.getResponse().getContent().getEncoding())
        ).map(HarEntry::getResponse).toList();

        for (HarResponse response : harResponses) {
            String nodeTxt = decodeUtil.decodeNodeTxt(response.getContent().getText());

            if (!nodeTxt.isEmpty()) {
                JsonNode reviews = new ObjectMapper().readTree(nodeTxt).path("data").path("productReviews").path("edges");
                // レビュー日の判定
                if (Objects.nonNull(reviews) && isLatestDay(reviews)) {
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * mercari-shops.comの評価ページがまだ読み込み中かどうかを確認する
     *
     * @return 読み込み中の場合はtrue、そうでない場合はfalse
     */
    private boolean isStillLoading() {
        try {
            return driverTool.driver.findElement(By.className(MercariApi.SHOPS_LOADING_CLASS)).isDisplayed();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 最新のレビューの日付がMAX_DAY以上古い日付かどうかを確認する
     *
     * @param reviews reviewのJsonNode
     * @return MAX_DAY以上古い日付の場合はtrue、そうでない場合はfalse
     */
    private boolean isLatestDay(JsonNode reviews) {
        LocalDateTime timeNow = LocalDateTime.now();

        // レビューが存在しない場合はfalseを返す
        if (reviews == null || reviews.size() <= 1) {
            return false;
        }

        // 最後のノードだけを見る
        String lastReview = reviews.get(reviews.size() - 1).path("node").path("createdAt").asText();

        // 日付の処理をして結果を返す
        Instant instant = Instant.ofEpochSecond(Long.parseLong(lastReview));
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        long diffInDays = ChronoUnit.DAYS.between(localDateTime, timeNow);
        return diffInDays >= maxDay;
    }

    /**
     * Mercari Shopsで取得したHARのJsonNodeからGetItemModelのListを取得する
     *
     * @return GetItemModelのList
     * @throws IOException HARの取得に失敗した場合
     */
    private List<GetItemModel> getItemModelList() throws IOException {
        List<GetItemModel> itemList = new ArrayList<>();
        List<HarEntry> entries = driverTool.getHarEntries().stream()
                .filter(entry -> entry.getRequest().getUrl().contains(MercariApi.SHOPS_BASE_URL))
                .toList();

        for (HarEntry entry : entries) {
            if (entry.getRequest().getUrl().contains(MercariApi.SHOPS_GRAPHQL)
                    && entry.getResponse().getBodySize() > 200) {
                addModelFromGraphQl(entry, itemList);
            } else if (entry.getRequest().getUrl().contains(MercariApi.SHOPS_FIRST_ITEMS)) {
                addModelFromFirstReviews(entry, itemList);
            }
        }

        return itemList;
    }

    /**
     * GraphQLのJsonNodeからGetItemModelのリストを追加する
     *
     * @param entry    HARのエントリー
     * @param itemList GetItemModelのリスト
     * @throws IOException JSONの解析に失敗した場合
     */
    private void addModelFromGraphQl(HarEntry entry, List<GetItemModel> itemList) throws IOException {
        // そのままは解析できないため、いったんStringに変えてあげる
        String nodeTxt = decodeUtil.decodeNodeTxt(entry.getResponse().getContent().getText());
        ObjectMapper mapper = new ObjectMapper();

        // nodeに情報があれば入れていく
        if (!nodeTxt.isEmpty()) {
            try {
                GraphHql graphHql = mapper.readValue(nodeTxt, GraphHql.class);
                if (Objects.isNull(graphHql) || Objects.isNull(graphHql.getData().getProductReviews())) {
                    return;
                }

                for (Edge edge : graphHql.getData().getProductReviews().getEdges()) {
                    itemList.add(new GetItemModel(edge.getNode(), shopId));
                }
            } catch (Exception e) {
                logger.debug("GraphHqlの解析に失敗しました\n" + nodeTxt);
            }
        }
    }

    /**
     * FirstReviewsのJsonNodeからGetItemModelのリストを追加する
     *
     * @param entry    JSONデータを含むEntry
     * @param itemList GetItemModelのリスト
     * @throws IOException JSONの解析に失敗した場合
     */
    private void addModelFromFirstReviews(HarEntry entry, List<GetItemModel> itemList) throws IOException {
        // そのままは解析できないため、いったんStringに変えてあげる
        String nodeTxt = decodeUtil.decodeNodeTxt(entry.getResponse().getContent().getText());
        ObjectMapper mapper = new ObjectMapper();

        JsonNode jsonNode = mapper.readTree(nodeTxt);
        JsonNode states = jsonNode.path("pageProps").path("__APOLLO_STATE__");

        for (JsonNode state : states) {
            if ("ProductReview".equals(state.elements().next().asText())) {
                Node review = null;
                try {
                    review = mapper.readValue(state.toString(), Node.class);
                } catch (Exception e) {
                    continue;
                }
                itemList.add(new GetItemModel(review, shopId));
            }
        }
    }

    /**
     * 画像のURLを取得する
     *
     * @return 画像のURL
     */
    private String getImageUrl(String assetName) throws IOException {
        if (Objects.isNull(assetName)) {
            return null;
        }

        // 検索用の名前を設定しておく
        String jsonName = shopId + ".json";

        // 必要なHarDataを取得する
        List<HarEntry> entries = driverTool.proxy.getHar().getLog().getEntries().stream()
                .filter(entry -> entry.getRequest().getUrl().contains(jsonName))
                .toList();

        if (!entries.isEmpty()) {
            for (HarEntry entry : entries) {
                JsonNode filteredNode = new ObjectMapper().readTree(
                                entry.getResponse().getContent().getText())
                        .path("pageProps").path("__APOLLO_STATE__");

                // 基本情報を取得する
                for (JsonNode node : filteredNode) {
                    if ("ProductAsset".equals(node.elements().next().asText()) &&
                            assetName.equals(node.path("id").asText())) {
                        // 商品の情報を取得する
                        return node.path("imageUrl({\"options\":{\"presets\":[\"Small\"]}})").asText();
                    }
                }
            }
        }
        return null;
    }

    /**
     * アイテムの名前をもとに、shopsのプロダクト情報を取得します
     *
     * @param item アイテム
     * @return プロダクト情報、存在しない場合は初期化された状態
     */
    private Product getProductInfo(GetItemModel item) {
        return products.stream()
                .filter(product -> product.getId().equals(item.getId()))
                .findFirst()
                .orElse(new Product());
    }

    /**
     * SHOPSの情報を取得します。
     *
     * @throws JsonProcessingException JSONの解析に失敗した場合
     */
    private void getShopsInfo() throws Exception {
        // 検索用の名前を設定しておく
        String jsonName = shopId + ".json";

        // 必要なHarDataを取得する
        List<HarEntry> entries = driverTool.proxy.getHar().getLog().getEntries().stream()
                .filter(entry -> entry.getRequest().getUrl().contains(jsonName))
                .toList();

        if (!entries.isEmpty()) {
            JsonNode filteredNode = new ObjectMapper().readTree(
                            entries.get(0).getResponse().getContent().getText())
                    .path("pageProps").path("__APOLLO_STATE__");

            products = getProductsInfo(filteredNode);

            if (setProductsToItems()) {
                Long beforeHeight = (Long) driverTool.executeJavaScriptWithReturn("document.body.scrollHeight;");
                driverTool.scrollToBottom();
                Thread.sleep(3000);

                Long afterHeight = (Long) driverTool.executeJavaScriptWithReturn("document.body.scrollHeight;");
                // 一番下まで行って、高さに変更があれば、再帰呼び出しを行う
                if (!Objects.equals(beforeHeight, afterHeight)) {
                    logger.info("再度 getShopsInfo を実行します");
                    getShopsInfo();
                }
            }
        }
    }

    /**
     * アイテムの名前をもとに、shopsのプロダクト情報を取得します
     *
     * @return 取得できないものがある場合：true, 全部取得できた：false
     */
    private boolean setProductsToItems() throws IOException {
        for (GetItemModel item : getItemList) {
            Product product = getProductInfo(item);
            if (item.getName() == null || item.getName().isEmpty()) {
                item.setName(product.getName());
            }
            if (item.getPrice() == null) {
                item.setPrice(getProductInfo(item).getPrice());
            }
            if (product.getAssetsCountOne() != null) {
                item.setImageUrl(getImageUrl(StringUtils.mid(product.getAssetsCountOne().get(0).toString(), 26, 22)));
            }
        }
        return getItemList.stream()
                .filter(item -> item.getName() == null || item.getName().isEmpty())
                .findFirst().isEmpty();
    }

    /**
     * SHOPSのプロダクト情報を取得する
     *
     * @param nodes JsonNode
     * @return List<ShopsProductModel>
     */
    private List<Product> getProductsInfo(JsonNode nodes) throws IOException {
        List<Product> products = new ArrayList<>();

        // 基本情報を取得する
        for (JsonNode node : nodes) {
            if (Objects.isNull(shopName) && "\"Shop\"".equals(node.path("__typename").toString())) {
                // SHOPSの名前を取得する
                shopName = node.path("name").asText();

                // セラー名をsellersテーブルに保存する
                if (shopName != null && !shopName.isEmpty() && shopId != null) {
                    boolean updated = SellerManagementService.updateSellerName(shopId, shopName);
                    if (updated) {
                        logger.info("sellers テーブルのセラー名を更新しました: " + shopId + " -> " + shopName);
                    }
                }
            } else if ("Product".equals(node.elements().next().asText())) {
                // 商品の情報を取得する
                products.add(new ObjectMapper().readValue(node.toString(), Product.class));
            }
        }
        return products;
    }

    /**
     * SHOPSのURLを開き、レートのページを開く
     *
     * @param driverTool ドライバー一式
     * @param shopId     SHOPSのID
     * @throws Exception 例外が発生した場合
     */
    public void execute(DriverTool driverTool, String shopId) throws Exception {
        initializeContext(driverTool, shopId);
        initializeSettings();
        openShopProfile(shopId);
        collectItemsFromRatesPage();
        fetchAndAttachProductInfo();
        persistSearchData(shopId);
    }

    /**
     * 実行に必要なコンテキスト（ドライバーとショップID）を初期化する
     */
    private void initializeContext(DriverTool driverTool, String shopId) {
        FromShops.driverTool = driverTool;
        FromShops.shopId = shopId;
    }

    /**
     * 設定値（スリープ時間や検索期間）を初期化する
     */
    private void initializeSettings() {
        sleepTime = SettingsRepository.getBrowserSleepTime() * 1000;
        maxDay = SettingsRepository.getSoldDate() + 1;
    }

    /**
     * SHOPSのプロフィールページを開き、HARの収集を開始する
     */
    private void openShopProfile(String shopId) {
        driverTool.driver.get(MercariApi.SHOPS_PROFILE_URL + shopId);
        driverTool.proxy.newHar();
    }

    /**
     * レートページを開き、必要なデータが集まるまでスクロールしてアイテム一覧を取得する
     * 処理後は一度前のページに戻る
     */
    private void collectItemsFromRatesPage() throws Exception {
        openRatePage();
        scrollToLatestDate();
        getItemList = getItemModelList();
        driverTool.driver.navigate().back();
        try {
            TimeUnit.MILLISECONDS.sleep(sleepTime);
        } catch (InterruptedException e) {
            logger.debug(e.getLocalizedMessage());
            Thread.currentThread().interrupt();
        }
    }

    /**
     * プロダクト情報を取得してアイテムへ反映する
     */
    private void fetchAndAttachProductInfo() throws Exception {
        getShopsInfo();
    }

    /**
     * 収集した情報をデータベースへ保存する
     */
    private void persistSearchData(String shopId) {
        // GetItemListを保存
        saveGetItemListToDatabase(getItemList);

        // SellerSearchを保存
        SellerSearchModel sellerSearchModel = new SellerSearchModel();
        sellerSearchModel.setSellerId(shopId);
        sellerSearchModel.setSellerName(shopName);
        sellerSearchModel.setLastResearchedAt(new Date());
        sellerSearchModel.setAddedDate(new Date());
        sellerSearchModel.setUpdatedDate(new Date());
        int sellerSearchId = SellerSearchRepository.upsertSellerSearch(sellerSearchModel);

        // SellerSearchItemsを保存
        List<String> itemIds = getItemList.stream().map(GetItemModel::getId).collect(Collectors.toList());
        SellerSearchItemRepository.saveSellerSearchItemReferences(sellerSearchId, itemIds, ItemStatusEnum.SOLD_OUT.getId());
    }

    /**
     * GetItemListをデータベースに保存する
     *
     * @param getItemList GetItemModelのリスト
     */
    private void saveGetItemListToDatabase(List<GetItemModel> getItemList) {
        if (getItemList == null || getItemList.isEmpty()) {
            logger.info("データベースに保存する GetItemModel アイテムがありません");
            return;
        }

        List<Item> itemsToSave = SellerSearchDataConverter.convertGetItemListToItems(getItemList, shopId);

        // データベースに保存
        int savedCount = SearchItemRepository.saveSearchItems(itemsToSave);
        logger.info("データベースに " + savedCount + " 件の GetItemModel アイテムを保存しました");
    }
}