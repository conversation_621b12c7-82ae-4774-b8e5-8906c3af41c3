package com.mrcresearch.service.analyze.service;

import com.mrcresearch.service.analyze.model.SellerModel;
import com.mrcresearch.service.analyze.model.SellerResearchModeModel;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * セラーリストから解析を行う
 *
 * <AUTHOR>
 */
public class FromSellerList {
    private static final List<SellerModel> sellerList = new ArrayList<>();

    /**
     * セラーリストの情報を基に、HARを取得し、解析する
     *
     * @param sellerCsvPath       セラーリストの情報を保持するCSV
     * @param savePath            解析結果を保存するパス
     * @param sellerResearchModel 解析する内容
     * @throws Exception 例外
     */
    public void execute(String sellerCsvPath, String savePath, SellerResearchModeModel sellerResearchModel) throws Exception {
        // セラーリストを読み込む
        setSellerList(sellerCsvPath);

        // 実行する
//        new SellerListAnalyze().execute(sellerList, savePath, sellerResearchModel);
    }

    /**
     * セラーリストcsvから読み込み、設定する
     */
    private void setSellerList(String sellerCsvPath) {
        // パスを読み込む
        File file = new File(sellerCsvPath);
        String str;

        //csvから読み込み、設定していく
        try (
                FileInputStream input = new FileInputStream(file);
                InputStreamReader stream = new InputStreamReader(input, UTF_8);
                BufferedReader buffer = new BufferedReader(stream)) {

            //ファイルの最終行まで読み込む
            while ((str = buffer.readLine()) != null) {
                String[] col = str.split(",", -1);

                SellerModel addModel = new SellerModel(col[0], col[1], Integer.parseInt(col[2]));
                sellerList.add(addModel);
            }

        } catch (IOException e) {

        }

    }


}