package com.mrcresearch.service.analyze.service;

import com.mrcresearch.screen.model.SellerModel;
import com.mrcresearch.service.SellerManagementService;
import com.mrcresearch.service.models.item.Item;
import com.mrcresearch.service.tools.DriverTool;
import com.mrcresearch.util.database.SettingsRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * Service class for executing seller searches with data persistence.
 * Handles the complete seller search process including data collection and database storage.
 */
public class SearchBySeller {
    private static final Logger logger = LoggerFactory.getLogger(SearchBySeller.class);
    /**
     * Get the last search results.
     *
     * @return The list of items from the last search
     */
    // @Getter // Commented out for compilation compatibility
    private List<Item> lastSearchResults;


    /**
     * Execute seller search for a specific seller ID.
     * This method is called from within a SearchQueueManager task context,
     * so progress management is handled by the caller.
     *
     * @param sellerId   The seller ID to search
     * @param progressId The progress item ID for tracking
     * @throws Exception If the search execution fails
     */
    public void execute(String sellerId, String progressId) throws Exception {
        DriverTool driverTool = new DriverTool();
        try {
            // Set status to "リサーチ中" at the beginning
            updateSellerStatus(sellerId, "リサーチ中", false);

            driverTool.startDriver(SettingsRepository.getHeadlessMode());

            logger.info("セラーID: " + sellerId + " のセラー検索実行を開始します");

            // Execute the actual seller search
            new SellerListAnalyze().execute(sellerId, driverTool);

            logger.info("セラーID: " + sellerId + " のセラー検索が正常に完了しました");

            // Update seller status in sellers table for unified management
            updateSellerStatus(sellerId, "完了", false);

        } catch (Exception e) {
            // Log the error and re-throw to be handled by the queue manager
            updateSellerStatus(sellerId, "エラー", true);
            logger.error("Error during seller search execution: " + e.getMessage(), e);
            throw e;
        } finally {
            // 確実にリソースを解放する
            try {
                if (driverTool != null) {
                    driverTool.stopDriver();
                }
            } catch (Exception e) {
                logger.warn("Error during driver cleanup: " + e.getMessage());
            }

            // ガベージコレクションを促してメモリリークを軽減
            System.gc();
        }
    }

    /**
     * Update seller status in the sellers table for unified management.
     * This method ensures the seller exists and updates their research status.
     *
     * @param sellerId The seller ID that was researched
     * @param status   The research status ("完了", "エラー", etc.)
     * @param isError  Whether the research completed with an error
     */
    private void updateSellerStatus(String sellerId, String status, boolean isError) {
        try {
            // Ensure seller exists in sellers table
            SellerModel seller = SellerManagementService.getOrCreateSeller(sellerId);

            // Update research status in sellers table
            SellerManagementService.updateSellerResearchStatus(sellerId, status);

            if (!isError) {
                logger.info("セラーID: " + sellerId + " のセラーのステータスを '" + status + "' に正常に更新しました");
            } else {
                logger.info("エラーのため、セラーID: " + sellerId + " のセラーのステータスを '" + status + "' に更新しました");
            }

        } catch (Exception e) {
            logger.error("Error updating seller status in sellers table: " + e.getMessage(), e);
        }
    }

}