package com.mrcresearch.service.analyze.service;

import com.browserup.harreader.model.HarEntry;
import com.browserup.harreader.model.HttpMethod;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mrcresearch.model.ProgressItem;
import com.mrcresearch.screen.model.KeywordSearchResultModel;
import com.mrcresearch.service.analyze.model.KeywordSearchModel;
import com.mrcresearch.service.common.JavaScripts;
import com.mrcresearch.service.common.MercariApi;
import com.mrcresearch.service.models.item.Item;
import com.mrcresearch.service.models.item.ItemsRoot;
import com.mrcresearch.service.tools.DriverTool;
import com.mrcresearch.service.tools.DriverUtil;
import com.mrcresearch.service.tools.ImageTool;
import com.mrcresearch.service.tools.StringUtility;
import com.mrcresearch.util.KeywordSearchDataConverter;
import com.mrcresearch.util.SearchQueueManager;
import com.mrcresearch.util.database.DatabaseUtil;
import com.mrcresearch.util.database.SettingsRepository;
import lombok.Getter;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

public class SearchByKeyWord {
    private static final Logger logger = LoggerFactory.getLogger(SearchByKeyWord.class);
    // ツールを読み込む
    private static final DriverTool driverTool = new DriverTool();
    private int sleepTime;
    private KeywordSearchModel keywordModel = new KeywordSearchModel();
    /**
     * 最後の検索結果のアイテムリストを取得する
     */
    @Getter
    private List<Item> lastSearchResults;

    /**
     * キーワードを検索し、結果を取得する
     *
     * @param keywordSearchModel 検索キーワード
     * @throws Exception 例外
     */
    public void execute(KeywordSearchModel keywordSearchModel) throws Exception {
        try {
            // 設定の読み込み
            sleepTime = SettingsRepository.getBrowserSleepTime() * 1000;
            boolean headless = SettingsRepository.getHeadlessMode();

            // ドライバを読み込む
            driverTool.startDriver(headless);

            // HAR取得プロキシ起動
            driverTool.proxy.newHar();

            // キーワードモデルを取得する
            keywordModel = keywordSearchModel;

            //検索画面を開き、HARを取得する
            logger.info("キーワード検索を開始します: " + keywordModel.getBaseUrl());
            getSearchHar();

            // アイテムリストとして取得する
            List<Item> getItemList = getSearchResult();

            getImages(getItemList);

            // キーワード検索データを適切な形式に変換
            List<Item> convertedItemList = KeywordSearchDataConverter.convertKeywordSearchItems(getItemList);

            // 取得したアイテムリストを返す（データベース保存用）
            this.lastSearchResults = convertedItemList;

        } finally {
            // 確実にリソースを解放する
            try {
                if (driverTool != null) {
                    driverTool.stopDriver();
                }
            } catch (Exception e) {
                logger.warn("Error during driver cleanup: " + e.getMessage());
            }

            // ガベージコレクションを促してメモリリークを軽減
            System.gc();
        }
    }

    /**
     * アイテムの画像を取得する
     *
     * @param itemList アイテムリスト
     */
    private void getImages(List<Item> itemList) {
        for (Item item : itemList) {
            String imageUrl = item.getThumbnails().get(0);
            if (imageUrl != null) {
                // 画像を取得してファイルに保存
                byte[] imageData = ImageTool.getImageFromList(driverTool.getHarEntries(), imageUrl);

                // HARから取得できない場合は、サムネイルURLから直接ダウンロード
                if (imageData == null) {
                    imageData = ImageTool.downloadImage(imageUrl);
                }

                if (imageData != null) {
                    String savedPath = ImageTool.saveImageToUserFolder(imageData, item.getId());
                    if (savedPath != null) {
                        // 保存に成功した場合、imageUrlを保存パスに更新
                        ArrayList<String> thumbnail = new ArrayList<>();
                        thumbnail.add(savedPath);
                        item.setThumbnails(thumbnail);
                    }
                }
            }
        }
    }

    /**
     * 検索結果のHARファイルを取得する<br />
     * &#064;return　HARの内容のString
     */
    private void getSearchHar() {
        driverTool.openAndWait(keywordModel.getBaseUrl());
        logger.info("ブラウザの立ち上げが完了しました");

        // 次へボタンを指定の回数回押す
        try {
            for (int i = 0; i <= keywordModel.getMaxMore(); i++) {
                TimeUnit.MILLISECONDS.sleep(sleepTime);

                JavaScripts.ScrollToBottom(driverTool);

                if (i != keywordModel.getMaxMore()) {
                    // 次へボタンを取得
                    WebElement nextButton = driverTool.driver.findElements(By.className("merButton")).stream()
                            .filter(n -> n.getText().equals("次へ")).findFirst()
                            .orElse(DriverUtil.chooseByMercariTestId(driverTool.driver, "pagination-next-button"));

                    if (Objects.nonNull(nextButton) && Objects.requireNonNull(nextButton).isEnabled()) {
                        driverTool.clickElement(nextButton);
                        logger.debug("次へボタンをクリックしました");
                    } else { // ボタンが無ければ終了
                        logger.error("ボタンをクリックできませんでした。");
                        break;
                    }
                }
//                info.printDebug(driverTool.driver.getCurrentUrl());
            }
        } catch (InterruptedException e) {
//            info.showSystemWarn("スリープできませんでした。");
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 検索結果を取得する
     *
     * @return アイテムリスト
     * @throws Exception HARの解析に失敗した場合
     */
    private List<Item> getSearchResult() throws Exception {
        List<Item> itemList = new ArrayList<>();
        List<HarEntry> harEntries = driverTool.getHarEntries();

        HashMap<String, Integer> itemMap = new HashMap<>();

        // アイテム情報を取得する
        for (HarEntry entry : harEntries) {
            // リクエストの内容で対象のAPIからのリクエスト && GETメソッドで受信している
            if (HttpMethod.POST.equals(entry.getRequest().getMethod())
                    && entry.getRequest().getUrl().contains(MercariApi.SEARCH_ITEMS)) {
                // レスポンスから取得
                String contentText = entry.getResponse().getContent().getText();
                if (contentText != null) {
                    ItemsRoot itemsRoot = new ObjectMapper().readValue(contentText, ItemsRoot.class);
                    itemList.addAll(itemsRoot.getItems());
                }
            }
        }
        // 名前の整形
        itemList = itemList.stream().map(item -> {
            item.setName(StringUtility.moldItemName(item.getName()));
            return item;
        }).toList();

        return itemList;
    }

    /**
     * データベースからキーワード検索結果を取得し、実際のリサーチを実行する
     *
     * @param recordId データベースのレコードID
     */
    public void execute(int recordId) {
        ProgressItem progressItem = null;

        try {
            logger.info("レコードID: " + recordId + " のキーワード検索実行を開始します");

            // データベースからキーワード検索結果を取得
            KeywordSearchResultModel resultModel = DatabaseUtil.getKeywordSearchResultById(recordId);
            if (resultModel == null) {
                logger.error("Record not found for ID: " + recordId);
                logger.error("Please check if the record exists in the database and the ID is correct.");
                return;
            }

            logger.info("キーワード検索結果を取得しました: " + resultModel);

            // ProgressItemを取得（SearchQueueManagerから）
            String progressId = "keyword_" + recordId + "_";
            logger.info("IDが以下で始まる ProgressItem を探しています: " + progressId);
            List<ProgressItem> activeProgress = SearchQueueManager.getInstance().getActiveProgress();
            logger.info("アクティブな進捗アイテム数: " + activeProgress.size());
            for (ProgressItem item : activeProgress) {
                logger.info("ProgressItem ID を確認中: " + item.getId());
                if (item.getId().startsWith(progressId)) {
                    progressItem = item;
                    logger.info("一致する ProgressItem が見つかりました: " + item.getId());
                    break;
                }
            }
            if (progressItem == null) {
                logger.info("ID: " + progressId + " に一致する ProgressItem が見つかりませんでした");
            }

            // ステータスを「リサーチ中」に更新
            boolean statusUpdated = DatabaseUtil.updateKeywordSearchResultStatus(recordId, "リサーチ中");
            if (!statusUpdated) {
                logger.error("Failed to update status to リサーチ中 for record ID: " + recordId);
            }

            // ProgressItemのステータスを更新
            if (progressItem != null) {
                SearchQueueManager.getInstance().updateProgressItem(progressItem.getId(), ProgressItem.Status.PROCESSING);
            }

            // KeywordSearchResultModelからKeywordSearchModelに変換
            logger.info("KeywordSearchResultModel を KeywordSearchModel に変換中...");
            KeywordSearchModel keywordSearchModel = convertToKeywordSearchModel(resultModel);
            logger.info("変換が完了しました。検索URL: " + keywordSearchModel.getBaseUrl());
            logger.info("ナビゲートする最大ページ数: " + keywordSearchModel.getMaxMore());

            // 実際のキーワード検索を実行
            logger.info("実際のキーワード検索実行を開始します...");
            execute(keywordSearchModel);
            logger.info("キーワード検索実行が正常に完了しました。");

            // 検索結果のアイテム情報をデータベースに保存
            List<Item> searchResults = getLastSearchResults();
            if (searchResults != null && !searchResults.isEmpty()) {
                logger.info(searchResults.size() + " 件の検索結果アイテムをデータベースに保存中...");
                int savedItemCount = saveSearchResultItems(recordId, searchResults);
                logger.info("キーワード検索結果ID: " + recordId + " のデータベースに " + savedItemCount + " 件のアイテムを保存しました");
            } else {
                logger.info("キーワード検索結果ID: " + recordId + " に保存する検索結果がありません");
            }

            // 処理完了時にステータスを「完了」に更新
            boolean completionStatusUpdated = DatabaseUtil.updateKeywordSearchResultStatus(recordId, "完了");
            if (completionStatusUpdated) {
                logger.info("レコードID: " + recordId + " のキーワード検索が正常に完了しました");

                // ProgressItemのステータスを完了に更新
                if (progressItem != null) {
                    progressItem.setEndTime(java.time.LocalDateTime.now());
                    SearchQueueManager.getInstance().updateProgressItem(progressItem.getId(), ProgressItem.Status.COMPLETED);
                }
            } else {
                logger.error("Failed to update completion status for record ID: " + recordId);

                // ProgressItemのステータスをエラーに更新
                if (progressItem != null) {
                    progressItem.setEndTime(java.time.LocalDateTime.now());
                    SearchQueueManager.getInstance().updateProgressItem(progressItem.getId(), ProgressItem.Status.ERROR);
                }
            }

        } catch (SQLException sqlE) {
            logger.error("Database error executing keyword search for record ID " + recordId + ": " + sqlE.getMessage(), sqlE);
            logger.error("SQL State: " + sqlE.getSQLState());
            logger.error("Error Code: " + sqlE.getErrorCode());

            // エラー発生時にステータスを「エラー」に更新
            try {
                boolean errorStatusUpdated = DatabaseUtil.updateKeywordSearchResultStatus(recordId, "エラー");
                if (!errorStatusUpdated) {
                    logger.error("Failed to update error status for record ID: " + recordId);
                }
            } catch (Exception updateE) {
                logger.error("Failed to update error status due to exception: " + updateE.getMessage());
            }

            // ProgressItemのステータスをエラーに更新
            if (progressItem != null) {
                progressItem.setErrorMessage("Database error: " + sqlE.getMessage());
                progressItem.setEndTime(java.time.LocalDateTime.now());
                SearchQueueManager.getInstance().updateProgressItem(progressItem.getId(), ProgressItem.Status.ERROR);
            }
        } catch (Exception e) {
            logger.error("General error executing keyword search for record ID " + recordId + ": " + e.getMessage(), e);
            logger.error("Error class: " + e.getClass().getSimpleName());

            // エラー発生時にステータスを「エラー」に更新
            try {
                boolean errorStatusUpdated = DatabaseUtil.updateKeywordSearchResultStatus(recordId, "エラー");
                if (!errorStatusUpdated) {
                    logger.error("Failed to update error status for record ID: " + recordId);
                }
            } catch (Exception updateE) {
                logger.error("Failed to update error status due to exception: " + updateE.getMessage());
            }

            // ProgressItemのステータスをエラーに更新
            if (progressItem != null) {
                progressItem.setErrorMessage(e.getMessage());
                progressItem.setEndTime(java.time.LocalDateTime.now());
                SearchQueueManager.getInstance().updateProgressItem(progressItem.getId(), ProgressItem.Status.ERROR);
            }
        }
    }

    /**
     * KeywordSearchResultModelからKeywordSearchModelに変換する
     *
     * @param resultModel データベースから取得したキーワード検索結果
     * @return 変換されたKeywordSearchModel
     */
    private KeywordSearchModel convertToKeywordSearchModel(KeywordSearchResultModel resultModel) {
        logger.info("KeywordSearchResultModel を KeywordSearchModel に変換中...");
        KeywordSearchModel keywordModel = new KeywordSearchModel();

        // 基本情報を設定
        keywordModel.setWord(resultModel.getKeyword());

        // ページ数の設定 - データベースの設定値を優先し、なければresultModelの値を使用
        int pages = resultModel.getPages();
        if (pages <= 0) {
            // データベースから設定値を読み込み
            Object[] settings = DatabaseUtil.loadSettings();
            if (settings != null && settings.length > 4) {
                pages = (int) settings[4]; // defaultKeywordPages
                logger.info("設定からデフォルトのキーワードページを使用します: " + pages);
            } else {
                pages = 10; // フォールバック値
                logger.info("フォールバックのデフォルトページを使用します: " + pages);
            }
        }

        keywordModel.setMaxMore(pages - 1); // ページ数から最大ページ移動回数を計算
        logger.info("設定された単語: " + resultModel.getKeyword());
        logger.info("maxMore を設定: " + (pages - 1) + " (ページ数: " + pages + ")");

        // 検索URLを構築
        String searchUrl = buildSearchUrl(resultModel);
        keywordModel.setBaseUrl(searchUrl);
        logger.info("baseUrl を設定: " + searchUrl);

        // 保存名とパスを設定（必要に応じて）
        String saveName = resultModel.getKeyword() + "_" + System.currentTimeMillis();
        keywordModel.setSaveName(saveName);
        keywordModel.setSavePath(""); // 必要に応じて設定
        logger.info("saveName を設定: " + saveName);
        logger.info("KeywordSearchModel の変換が正常に完了しました");

        return keywordModel;
    }

    /**
     * 検索条件からMercariの検索URLを構築する
     *
     * @param resultModel データベースから取得したキーワード検索結果
     * @return 構築された検索URL
     */
    private String buildSearchUrl(KeywordSearchResultModel resultModel) {
        logger.info("キーワード: " + resultModel.getKeyword() + " の検索URLを構築中");
        logger.debug("詳細検索パラメータを受信しました:");
        logger.debug("  - 商品の状態ID: " + resultModel.getItemConditionIds());
        logger.debug("  - 除外キーワード: " + resultModel.getExcludeKeyword());
        logger.debug("  - 色ID: " + resultModel.getColorIds());
        StringBuilder urlBuilder = new StringBuilder("https://jp.mercari.com/search");

        List<String> params = new ArrayList<>();

        // キーワード
        if (resultModel.getKeyword() != null && !resultModel.getKeyword().trim().isEmpty()) {
            String encodedKeyword = URLEncoder.encode(resultModel.getKeyword(), StandardCharsets.UTF_8);
            params.add("keyword=" + encodedKeyword);
            logger.info("キーワードパラメータを追加しました: keyword=" + encodedKeyword);
        }

        // カテゴリー
        if (resultModel.getCategoryId() != null && !resultModel.getCategoryId().trim().isEmpty()) {
            String encodedCategoryId = URLEncoder.encode(resultModel.getCategoryId(), StandardCharsets.UTF_8);
            logger.info("カテゴリパラメータを追加しました: category_id=" + resultModel.getCategoryId());
            params.add("category_id=" + encodedCategoryId);
        }

        // 価格範囲
        if (resultModel.getMinPrice() > 300) {
            params.add("price_min=" + resultModel.getMinPrice());
            logger.info("最小価格パラメータを追加しました: price_min=" + resultModel.getMinPrice());
        }
        if (resultModel.getMaxPrice() < 9999999) {
            params.add("price_max=" + resultModel.getMaxPrice());
            logger.info("最大価格パラメータを追加しました: price_max=" + resultModel.getMaxPrice());
        }

        // ソート順（新着順）
        if (resultModel.isSortByNew()) {
            params.add("sort=created_time&order=desc");
            logger.info("ソートパラメータを追加しました: sort=created_time&order=desc");
        }

        // ショップ除外
        if (resultModel.isIgnoreShops()) {
            params.add("item_types=1"); // 1 = 個人出品のみ
            logger.info("アイテムタイプパラメータを追加しました: item_types=1");
        }

        // 販売状況
        if (resultModel.getSalesStatus() != null) {
            switch (resultModel.getSalesStatus()) {
                case "販売中":
                    params.add("status=on_sale");
                    logger.info("販売ステータスパラメータを追加しました: status=on_sale");
                    break;
                case "販売済み":
                    params.add("status=sold_out");
                    logger.info("販売ステータスパラメータを追加しました: status=sold_out");
                    break;
                // "すべて"の場合はパラメータを追加しない
                default:
                    logger.info("販売ステータスが「すべて」のため、ステータスパラメータは追加されません");
                    break;
            }
        }

        // 商品の状態
        if (resultModel.getItemConditionIds() != null && !resultModel.getItemConditionIds().trim().isEmpty()) {
            String encodedConditionIds = URLEncoder.encode(resultModel.getItemConditionIds(), StandardCharsets.UTF_8);
            params.add("item_condition_id=" + encodedConditionIds);
            logger.debug("商品状態パラメータを追加しました: item_condition_id=" + encodedConditionIds);
            logger.debug("元の商品状態ID: " + resultModel.getItemConditionIds());
        } else {
            logger.debug("追加する商品状態IDがありません (nullまたは空)");
        }

        // 除外キーワード
        if (resultModel.getExcludeKeyword() != null && !resultModel.getExcludeKeyword().trim().isEmpty()) {
            String encodedExcludeKeyword = URLEncoder.encode(resultModel.getExcludeKeyword(), StandardCharsets.UTF_8);
            params.add("exclude_keyword=" + encodedExcludeKeyword);
            logger.debug("除外キーワードパラメータを追加しました: exclude_keyword=" + encodedExcludeKeyword);
            logger.debug("元の除外キーワード: " + resultModel.getExcludeKeyword());
        } else {
            logger.debug("追加する除外キーワードがありません (nullまたは空)");
        }

        // 色
        if (resultModel.getColorIds() != null && !resultModel.getColorIds().trim().isEmpty()) {
            String encodedColorIds = URLEncoder.encode(resultModel.getColorIds(), StandardCharsets.UTF_8);
            params.add("color_id=" + encodedColorIds);
            logger.debug("色パラメータを追加しました: color_id=" + encodedColorIds);
            logger.debug("元の色ID: " + resultModel.getColorIds());
        } else {
            logger.debug("追加する色IDがありません (nullまたは空)");
        }

        // オークションは除外する
        params.add("d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088");

        // パラメータをURLに追加
        if (!params.isEmpty()) {
            urlBuilder.append("?").append(String.join("&", params));
            logger.info("最終URLパラメータ: " + String.join("&", params));
        } else {
            logger.info("追加するURLパラメータがありません");
        }

        String finalUrl = urlBuilder.toString();
        logger.info("構築された検索URL: " + finalUrl);
        return finalUrl;
    }

    /**
     * キーワード検索結果のアイテム情報をデータベースに保存する
     *
     * @param keywordSearchResultId キーワード検索結果のID
     * @param items                 保存するアイテムリスト
     * @return 保存に成功したアイテム数
     */
    public int saveSearchResultItems(int keywordSearchResultId, List<Item> items) {
        if (items == null || items.isEmpty()) {
            logger.info("キーワード検索結果ID: " + keywordSearchResultId + " に保存するアイテムがありません");
            return 0;
        }

        try {
            logger.info("キーワード検索結果ID: " + keywordSearchResultId + " のアイテム " + items.size() + " 件の保存を開始します");

            // データベースにアイテム情報を保存（キーワード検索IDを渡す）
            int savedCount = DatabaseUtil.saveKeywordSearchItems(keywordSearchResultId, items);

            if (savedCount > 0) {
                logger.info("キーワード検索結果ID: " + keywordSearchResultId + " のデータベースに " + savedCount + " 件のアイテムを正常に保存しました");
            } else {
                logger.error("Failed to save any items to database for keyword search result ID: " + keywordSearchResultId);
            }

            return savedCount;

        } catch (Exception e) {
            logger.error("Error saving search result items for keyword search result ID " + keywordSearchResultId + ": " + e.getMessage(), e);
            return 0;
        }
    }

}