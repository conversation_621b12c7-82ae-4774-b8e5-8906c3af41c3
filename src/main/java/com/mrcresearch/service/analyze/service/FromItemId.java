package com.mrcresearch.service.analyze.service;

import com.mrcresearch.service.analyze.model.GetItemModel;
import com.mrcresearch.service.analyze.model.GroupItemModel;
import com.mrcresearch.service.analyze.model.SellerModel;
import com.mrcresearch.service.common.MercariApi;
import com.mrcresearch.service.models.ImageModel;
import com.mrcresearch.service.property.ApplicationsProperty;
import com.mrcresearch.service.tools.DriverTool;
import com.mrcresearch.service.tools.InfoUtil;
import com.mrcresearch.service.tools.StringUtility;
import org.openqa.selenium.By;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public class FromItemId {
    // ツールを読み込む
    DriverTool driverTool = new DriverTool();
    StringUtility stringUtility = new StringUtility();
    //    ExportTool exportTool = new ExportTool();
    private static final List<ImageModel> images = new ArrayList<>();
    InfoUtil info = new InfoUtil();
    String extension;
    private static final String ITEM_NAME_XPATH = "/html/body/div[1]/div/div/main/article/div[2]/section[1]/div[1]/div/div/h1"; // NOSONAR これでいいのだ
    private int maxMore; // 「もっと見る」を押す回数
    private static final int SHOPS_LENGTH = 40; // 通常の出品ページのURLの長さ
    private List<GroupItemModel> items;
    private int progressCnt;

    /**
     * アイテムID検索を実行する
     *
     * @param ids          IDのリスト
     * @param saveFileName 保存ファイル名
     * @param savePath     保存先パス
     */
    public void execute(List<String> ids, String saveFileName, String savePath) {
        // 設定の読み込み
        maxMore = Integer.parseInt(ApplicationsProperty.getProperty(ApplicationsProperty.ITEM_ID_MAX_MORE));
        extension = ApplicationsProperty.getProperty(ApplicationsProperty.EXTENSION);
//        progress = new Progress(ids.size() * maxMore);
        progressCnt = 0;

        try {
            // ドライバを読み込む
            driverTool.startDriver(false);

            items = new ArrayList<>();

            // 情報を取得する
            for (String id : ids) {
//                progress.updateProgress(id + "を確認しています" + (ids.indexOf(id) + 1) + "/" + ids.size());
                getItemInf(MercariApi.ITEM_BASE_URL + id);
            }

            // ファイルに出力する
//            exportTool.export(items, saveFileName, savePath);
        } finally {
            // ドライバを閉じる
            driverTool.stopDriver();
//            progress.dispose();
        }
    }

    /**
     * アイテムの情報を取得する
     *
     * @param url アイテムのURL
     */
    private void getItemInf(String url) {
        info.printDebug("opening url[" + url + "]");
        driverTool.openAndWait(url);
        if (url.length() != SHOPS_LENGTH) return;

        // アイテム名取得
        String itemName = StringUtility.moldItemName(
                driverTool.driver.findElement(By.xpath(ITEM_NAME_XPATH))
                        .getText());

        // プロファイルIDを取得し、sellerモデルに設定する
        SellerModel seller = new SellerModel();
        String userUrl = Objects.requireNonNull(driverTool.driver.findElements(By.tagName("a")).stream()
                        .filter(element -> element.getAttribute("href").contains("user/profile")).findFirst().orElse(null))
                .getAttribute("href");
        String userId = userUrl.substring(userUrl.length() - 9);
        seller.setId(userId);
        seller.setMaxMore(maxMore);

        // HARを取得
        info.printDebug("start collect HAR");
//        driverTool.getProfileHar(seller, progress, progressCnt);

        // アイテムリストを取得する
        info.printDebug("Analyzing data...");
        List<GetItemModel> itemList = null;
        try {
            itemList = driverTool.getItemListFromHAR();
        } catch (IOException e) {
            info.showSystemWarn("failed analyzing HAR data :(");
            info.printDebug(Arrays.toString(e.getStackTrace()));
        }

        // アイテム一覧の情報を解析、同じ名前のものを引っ張ってくる
        GroupItemModel analyzedItem = SellerListAnalyze.setAnalyzedList(itemList)
                .stream().filter(n -> n.getName().equals(itemName)).findFirst().orElse(null);
        if (Objects.nonNull(analyzedItem)) {
            Objects.requireNonNull(analyzedItem).setSellerId(userId);
        }
        // 取ってきたアイテムを一覧に追加する
        items.add(analyzedItem);
        info.printDebug("Analyzing done!!");
    }
}
