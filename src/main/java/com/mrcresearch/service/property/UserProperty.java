package com.mrcresearch.service.property;

import com.mrcresearch.security.CryptoUtil;
import com.mrcresearch.security.KeyStoreUtil;
import com.mrcresearch.service.tools.InfoUtil;
import com.mrcresearch.service.tools.PlatFormUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemProperties;

import javax.crypto.SecretKey;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Properties;

/**
 * ユーザーの設定情報（user.properties）を扱うユーティリティ。
 * 将来的には設定リポジトリへ集約予定のため非推奨です。
 *
 * @deprecated 将来的に削除予定の旧設定管理クラス
 */
public class UserProperty {
    private static final String INIT_FOLDER = PlatFormUtils.getSettingsPath();
    private static final String FILE_NAME = "user.properties";
    private static final String INIT_FILE_PATH = INIT_FOLDER + SystemProperties.getProperty("file.separator") + FILE_NAME;
    private static final Properties properties;
    private static final InfoUtil info = new InfoUtil();

    static {
        properties = new Properties();
        // 設定ディレクトリが無ければ作成
        Path path = Paths.get(INIT_FOLDER);
        if (!Files.exists(path)) {
            try {
                Files.createDirectories(path);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        try {
            path = Paths.get(INIT_FILE_PATH);
            if (Files.exists(path)) {
                properties.load(Files.newBufferedReader(path, StandardCharsets.UTF_8));
            } else {
                Files.createFile(path);
                new UserProperty().setDefaultValues();
            }
            // 平文のパスワードがあれば可能なら暗号化へ移行
            migrateSensitiveValues();
        } catch (IOException e) {
            // ファイル読み込みに失敗
            info.showSystemWarn(String.format("ファイルの読み込みに失敗しました。ファイル名:%s%n", INIT_FILE_PATH));
        }
    }

    private final HashMap<String, String> initValueMap = new HashMap<>() {
        {
            put("userName", "");
            put("password", "");
        }
    };

    /**
     * プロパティ値を取得する
     *
     * @param key キー
     * @return 値
     */
    public static String getProperty(final String key) {
        return getProperty(key, "");
    }

    /**
     * プロパティ値を取得する
     *
     * @param key          キー
     * @param defaultValue デフォルト値
     * @return キーが存在しない場合、デフォルト値
     * 存在する場合、値
     */
    public static String getProperty(final String key, final String defaultValue) {
        String val = properties.getProperty(key, defaultValue);
        if (!isSensitiveKey(key)) return val;
        if (StringUtils.isEmpty(val)) return val;
        SecretKey keyObj = lazyLoadKey();
        if (keyObj == null) return val;
        try {
            return CryptoUtil.decrypt(keyObj, val);
        } catch (RuntimeException e) {
            return val;
        }
    }

    /**
     * プロパティ値を設定します。
     * センシティブなキーの場合は自動的に暗号化して保存します。
     *
     * @param key   設定キー
     * @param value 設定値
     * @throws RuntimeException ファイルアクセスや保存に失敗した場合
     */
    public static void setProperty(final String key, final String value) {
        // 呼び出し側に透過的にセンシティブなキーを自動暗号化
        if (isSensitiveKey(key)) {
            setEncryptedProperty(key, value);
            return;
        }
        properties.setProperty(key, value);
        try (FileOutputStream fos = new FileOutputStream(INIT_FILE_PATH)) {
            properties.store(fos, "Last Update");
        } catch (FileNotFoundException e) {
            info.showSystemWarn("failed save property");
            throw new RuntimeException(e);
        } catch (IOException e) {
            info.showSystemWarn("failed access to property file");
            throw new RuntimeException(e);
        }
    }

    /**
     * 初期値を設定する
     */
    private void setDefaultValues() {
        // ファイル作成
        try (FileOutputStream fos = new FileOutputStream(INIT_FILE_PATH)) {
            initValueMap.forEach(properties::setProperty);
            properties.store(fos, "Last Update");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    // ===== Security helpers (Task 26) for user.properties =====
    private static volatile SecretKey cachedKey;

    private static SecretKey lazyLoadKey() {
        if (cachedKey != null) return cachedKey;
        try {
            Path dir = Paths.get(INIT_FOLDER);
            var ks = KeyStoreUtil.loadOrCreateKeyStore(dir);
            cachedKey = KeyStoreUtil.loadOrCreateSecretKey(ks, dir);
            return cachedKey;
        } catch (RuntimeException ex) {
            info.showSystemWarn("暗号化用KeyStoreの初期化に失敗しました。環境変数 SCRAPINGMERCARI_KS_PASS を設定してください。");
            return null;
        }
    }

    private static boolean isSensitiveKey(String key) {
        if (key == null) return false;
        String k = key.toLowerCase();
        return k.endsWith("password") || k.endsWith("secret") || k.endsWith("token");
    }

    public static String getDecryptedPropertyValue(final String key) {
        String val = getProperty(key, "");
        if (StringUtils.isEmpty(val)) return "";
        if (!isSensitiveKey(key)) return val;
        SecretKey keyObj = lazyLoadKey();
        if (keyObj == null) return val;
        try {
            return CryptoUtil.decrypt(keyObj, val);
        } catch (RuntimeException e) {
            return val;
        }
    }

    public static void setEncryptedProperty(final String key, final String value) {
        if (!isSensitiveKey(key)) {
            setProperty(key, value);
            return;
        }
        SecretKey keyObj = lazyLoadKey();
        if (keyObj == null) {
            info.showSystemWarn("KeyStore未初期化のため機密値を平文で保存します。SCRAPINGMERCARI_KS_PASS を設定してください。");
            setProperty(key, value);
            return;
        }
        String enc = CryptoUtil.encrypt(keyObj, value == null ? "" : value);
        // write encrypted value directly to avoid recursion with setProperty()
        properties.setProperty(key, enc);
        try (FileOutputStream fos = new FileOutputStream(INIT_FILE_PATH)) {
            properties.store(fos, "Last Update");
        } catch (FileNotFoundException e) {
            info.showSystemWarn("failed save property");
            throw new RuntimeException(e);
        } catch (IOException e) {
            info.showSystemWarn("failed access to property file");
            throw new RuntimeException(e);
        }
    }

    private static void migrateSensitiveValues() {
        SecretKey keyObj = lazyLoadKey();
        if (keyObj == null) return;
        boolean changed = false;
        for (String k : properties.stringPropertyNames()) {
            if (!isSensitiveKey(k)) continue;
            String v = properties.getProperty(k);
            if (StringUtils.isEmpty(v)) continue;
            try {
                CryptoUtil.decrypt(keyObj, v);
            } catch (RuntimeException e) {
                String enc = CryptoUtil.encrypt(keyObj, v);
                properties.setProperty(k, enc);
                changed = true;
            }
        }
        if (changed) {
            try (FileOutputStream fos = new FileOutputStream(INIT_FILE_PATH)) {
                properties.store(fos, "Last Update");
            } catch (IOException e) {
                info.showSystemWarn("failed access to user property file during migration");
                throw new RuntimeException(e);
            }
        }
    }
}
