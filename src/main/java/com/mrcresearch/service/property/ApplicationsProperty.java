package com.mrcresearch.service.property;

import com.mrcresearch.security.CryptoUtil;
import com.mrcresearch.security.KeyStoreUtil;
import com.mrcresearch.service.tools.InfoUtil;
import com.mrcresearch.service.tools.PlatFormUtils;
import com.mrcresearch.service.tools.StringUtility;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemProperties;
import org.openqa.selenium.remote.Browser;

import javax.crypto.SecretKey;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Properties;

/**
 * @deprecated 不要になったやーつ
 */
public class ApplicationsProperty {
    //参照用定数
    public static final String EXTENSION = "extension";
    public static final String HEADLESS = "headless";
    public static final String INIT_SLEEP_TIME = "init-sleep-time";
    public static final String ITEM_ID_MAX_MORE = "item-id-max-more";
    public static final String LOAD_IMAGE = "load-image";
    public static final String SAVE_DAYS = "save-days";
    public static final String SLEEP_TIME = "sleep-time";
    public static final String SAVE_FOLDER = "save-folder";
    public static final String IGNORE_SHOPS = "ignore-shops";
    public static final String SEARCH_PAGES = "search_pages";
    public static final String BROWSER_TYPE = "browser-type";
    //内部用
    private static final Properties properties;
    private static final InfoUtil info = new InfoUtil();
    private static final String INIT_FOLDER = PlatFormUtils.getSettingsPath();
    private static final String FILE_NAME = "application.properties";
    private static final String INIT_FILE_PATH = INIT_FOLDER + SystemProperties.getProperty("file.separator") + FILE_NAME;

    // デフォルト値のマップ
    private static final HashMap<String, String> initValueMap = new HashMap<>() {
        {
            put(EXTENSION, "2");
            put(HEADLESS, "false");
            put(INIT_SLEEP_TIME, "10000");
            put(ITEM_ID_MAX_MORE, "10");
            put(LOAD_IMAGE, "true");
            put(IGNORE_SHOPS, "true");
            put(SAVE_DAYS, "13");
            put(SEARCH_PAGES, "5");
            put(SLEEP_TIME, "3000");
            put(SAVE_FOLDER, System.getProperty("user.home") + StringUtility.osFolderDemilitter());
            put(BROWSER_TYPE, Browser.FIREFOX.toString());
        }
    };

    static {
        properties = new Properties();
        // 設定ディレクトリが無ければ作成
        Path path = Paths.get(INIT_FOLDER);
        if (!Files.exists(path)) {
            try {
                Files.createDirectories(path);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        try {
            path = Paths.get(INIT_FILE_PATH);
            if (Files.exists(path)) {
                properties.load(Files.newBufferedReader(path, StandardCharsets.UTF_8));
            } else {
                Files.createFile(path);
                new ApplicationsProperty().setDefaultValues();
            }
            // 読み込み後、センシティブ値を平文から暗号化へ移行
            migrateSensitiveValues();
        } catch (IOException e) {
            // ファイル読み込みに失敗
            info.showSystemWarn(String.format("ファイルの読み込みに失敗しました。ファイル名:%s%n", INIT_FILE_PATH));
        }
    }

    /**
     * プロパティ値を取得する
     * もしも未設定の場合は、初期値を設定してあげる
     *
     * @param key キー
     * @return 値
     */
    public static String getProperty(final String key) {
        String value = getProperty(key, "");
        if (StringUtils.isEmpty(value)) {
            setProperty(key, initValueMap.get(key));
            return initValueMap.get(key);
        }
        return value;
    }

    /**
     * プロパティ値を取得する
     *
     * @param key          キー
     * @param defaultValue デフォルト値
     * @return キーが存在しない場合、デフォルト値
     * 存在する場合、値
     */
    public static String getProperty(final String key, final String defaultValue) {
        String val = properties.getProperty(key, defaultValue);
        if (!isSensitiveKey(key)) return val;
        if (StringUtils.isEmpty(val)) return val;
        SecretKey keyObj = lazyLoadKey();
        if (keyObj == null) return val; // cannot decrypt without keystore
        try {
            return CryptoUtil.decrypt(keyObj, val);
        } catch (RuntimeException e) {
            // Not encrypted yet (plaintext) or invalid - return as-is for backward compatibility
            return val;
        }
    }

    /**
     * プロパティ値を設定する
     *
     * @param key   キー
     * @param value デフォルト値
     */
    public static void setProperty(final String key, final String value) {
        // Auto-encrypt sensitive keys to ensure new security without changing call sites
        if (isSensitiveKey(key)) {
            setEncryptedProperty(key, value);
            return;
        }
        properties.setProperty(key, value);
        try (FileOutputStream fos = new FileOutputStream(INIT_FILE_PATH)) {
            properties.store(fos, "Last Update");
        } catch (FileNotFoundException e) {
            info.showSystemWarn("failed save property");
            throw new RuntimeException(e);
        } catch (IOException e) {
            info.showSystemWarn("failed access to property file");
            throw new RuntimeException(e);
        }
    }

    /**
     * 新しい値が設定済みでない場合は値の入れ替えをする。
     *
     * @param key      キー
     * @param chkValue 比較値
     */
    public static void setIfHasDiff(String key, String chkValue) {
        if (!ApplicationsProperty.getProperty(key).equals(chkValue)) {
            ApplicationsProperty.setProperty(key, chkValue);
        }
    }

    /**
     * 初期値を設定する
     */
    private void setDefaultValues() {
        // ファイル作成
        try (FileOutputStream fos = new FileOutputStream(INIT_FILE_PATH)) {
            initValueMap.forEach(properties::setProperty);
            properties.store(fos, "Last Update");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    // ===== Security helpers (Task 26) =====
    private static volatile SecretKey cachedKey;

    private static SecretKey lazyLoadKey() {
        if (cachedKey != null) return cachedKey;
        try {
            Path dir = Paths.get(INIT_FOLDER);
            var ks = KeyStoreUtil.loadOrCreateKeyStore(dir);
            cachedKey = KeyStoreUtil.loadOrCreateSecretKey(ks, dir);
            return cachedKey;
        } catch (RuntimeException ex) {
            // KeyStore password missing or other errors: skip encryption but keep app running
            info.showSystemWarn("暗号化用KeyStoreの初期化に失敗しました。環境変数 SCRAPINGMERCARI_KS_PASS を設定してください。");
            return null;
        }
    }

    private static boolean isSensitiveKey(String key) {
        if (key == null) return false;
        String k = key.toLowerCase();
        return k.endsWith("password") || k.endsWith("secret") || k.endsWith("token");
    }

    public static String getDecryptedProperty(final String key) {
        String val = getProperty(key, "");
        if (StringUtils.isEmpty(val)) return "";
        if (!isSensitiveKey(key)) return val;
        SecretKey keyObj = lazyLoadKey();
        if (keyObj == null) return val; // cannot decrypt without keystore
        try {
            return CryptoUtil.decrypt(keyObj, val);
        } catch (RuntimeException e) {
            // Not encrypted yet (plaintext) or invalid - return as-is for backward compatibility
            return val;
        }
    }

    public static void setEncryptedProperty(final String key, final String value) {
        if (!isSensitiveKey(key)) {
            setProperty(key, value);
            return;
        }
        SecretKey keyObj = lazyLoadKey();
        if (keyObj == null) {
            // Fallback to plaintext if keystore not available, but strongly warn
            info.showSystemWarn("KeyStore未初期化のため機密値を平文で保存します。SCRAPINGMERCARI_KS_PASS を設定してください。");
            setProperty(key, value);
            return;
        }
        String enc = CryptoUtil.encrypt(keyObj, value == null ? "" : value);
        // write encrypted value directly to avoid recursion with setProperty()
        properties.setProperty(key, enc);
        try (FileOutputStream fos = new FileOutputStream(INIT_FILE_PATH)) {
            properties.store(fos, "Last Update");
        } catch (FileNotFoundException e) {
            info.showSystemWarn("failed save property");
            throw new RuntimeException(e);
        } catch (IOException e) {
            info.showSystemWarn("failed access to property file");
            throw new RuntimeException(e);
        }
    }

    private static void migrateSensitiveValues() {
        SecretKey keyObj = lazyLoadKey();
        if (keyObj == null) return; // cannot migrate without keystore
        boolean changed = false;
        for (String k : properties.stringPropertyNames()) {
            if (!isSensitiveKey(k)) continue;
            String v = properties.getProperty(k);
            if (StringUtils.isEmpty(v)) continue;
            try {
                // If decrypt succeeds, assume already encrypted
                CryptoUtil.decrypt(keyObj, v);
            } catch (RuntimeException e) {
                // Treat as plaintext and encrypt
                String enc = CryptoUtil.encrypt(keyObj, v);
                properties.setProperty(k, enc);
                changed = true;
            }
        }
        if (changed) {
            try (FileOutputStream fos = new FileOutputStream(INIT_FILE_PATH)) {
                properties.store(fos, "Last Update");
            } catch (IOException e) {
                info.showSystemWarn("failed access to property file during migration");
                throw new RuntimeException(e);
            }
        }
    }
}