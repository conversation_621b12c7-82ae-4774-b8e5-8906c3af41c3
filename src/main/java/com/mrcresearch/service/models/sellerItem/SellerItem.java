package com.mrcresearch.service.models.sellerItem;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SellerItem {
    @JsonProperty("result")
    private String result;

    @JsonProperty("meta")
    private Meta meta;

    @JsonProperty("data")
    private ArrayList<ItemData> itemData;
}
