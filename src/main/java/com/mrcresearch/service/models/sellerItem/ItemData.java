package com.mrcresearch.service.models.sellerItem;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.mrcresearch.service.models.common.IdName;
import lombok.Data;

import java.util.ArrayList;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ItemData {
    @JsonProperty("id")
    private String id;

    @JsonProperty("status")
    private String status;

    @JsonProperty("name")
    private String name;

    @JsonProperty("price")
    private String price;

    @JsonProperty("thumbnails")
    private ArrayList<String> thumbnails;

    @JsonProperty("num_likes")
    private int numLikes;

    @JsonProperty("num_comments")
    private int numComments;

    @JsonProperty("created")
    private String created;

    @JsonProperty("updated")
    private String updated;

    @JsonProperty("parent_categories_ntiers")
    private ArrayList<IdName> parentCategoriesNTiers;

    @JsonProperty("shipping_method_id")
    private int shippingMethodId;

    @JsonProperty("item_pv")
    private int itemPv;
}
