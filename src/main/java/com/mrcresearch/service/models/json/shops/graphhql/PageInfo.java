package com.mrcresearch.service.models.json.shops.graphhql;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class PageInfo {
    @JsonProperty("hasNextPage")
    private boolean hasNextPage;

    @JsonProperty("endCursor")
    private String endCursor;

    @JsonProperty("__typename")
    private String typeName;
}
