package com.mrcresearch.service.models.json.shops.graphhql;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * GraphQL のエンティティ参照（キャッシュ参照）を表すモデル。
 * __ref に対象エンティティの参照キーが入ります。
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class AssetRef {
    /** 参照キー */
    @JsonProperty("__ref")
    private String ref;
}
