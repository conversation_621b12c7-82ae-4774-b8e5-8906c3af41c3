package com.mrcresearch.service.models.json.shops.graphhql;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class Product {
    @JsonProperty("id")
    private String id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("assets")
    private ArrayList<Asset> assets;

    @JsonProperty("__typename")
    private String typeName;

    @JsonProperty("__ref")
    private String ref;

    // ここから下は、json取得時の内容
    @JsonProperty("price")
    private String price;

    @JsonProperty("assets({\"count\":1})")
    private ArrayList<AssetRef> assetsCountOne;
}
