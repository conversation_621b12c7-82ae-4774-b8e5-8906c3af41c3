package com.mrcresearch.service.models.json.shops.graphhql;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class Shop {
    @JsonProperty("id")
    private String id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("reviewStats")
    private ReviewStats reviewStats;

    @JsonProperty("followersCount")
    private int followersCount;

    @JsonProperty("thumbnail")
    private Thumbnail thumbnail;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    private static class ReviewStats {
        @JsonProperty("count")
        private int count;

        @JsonProperty("score")
        private double score;

        @JsonProperty("__typename")
        private String typeName;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    private static class Thumbnail {
        @JsonProperty("id")
        private String id;

        @JsonProperty("imageUrl")
        private String imageUrl;

        @JsonProperty("__typename")
        private String typeName;
    }
}
