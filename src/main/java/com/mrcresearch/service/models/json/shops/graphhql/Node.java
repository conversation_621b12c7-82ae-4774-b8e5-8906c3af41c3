package com.mrcresearch.service.models.json.shops.graphhql;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class Node {
    @JsonProperty("product")
    private Product product;

    @JsonProperty("id")
    private String id;

    @JsonProperty("account")
    private Account account;

    @JsonProperty("rating")
    private String rating;

    @JsonProperty("comment")
    private String comment;

    @JsonProperty("createdAt")
    private String createdAt;

    @JsonProperty("thumbnails")
    private ArrayList<Thumbnail> thumbnails;

    @JsonProperty("reply")
    private Reply reply;

    @JsonProperty("__typename")
    private String typeName;
}
