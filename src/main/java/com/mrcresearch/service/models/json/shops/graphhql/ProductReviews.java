package com.mrcresearch.service.models.json.shops.graphhql;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;

/**
 * Mercari Shops の GraphQL 応答に含まれるレビュー一覧を表すモデル。
 * pageInfo と edges（レビューエッジの配列）を保持します。
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ProductReviews {
    /** ページング情報 */
    @JsonProperty("pageInfo")
    private PageInfo pageInfo;

    /** レビューのエッジ一覧 */
    @JsonProperty("edges")
    private ArrayList<Edge> edges;
}
