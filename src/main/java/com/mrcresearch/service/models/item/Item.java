package com.mrcresearch.service.models.item;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class Item {
    @JsonProperty("id")
    private String id;

    @JsonProperty("sellerId")
    private String sellerId;

    @JsonProperty("buyerId")
    private String buyerId;

    @JsonProperty("status")
    private String status;

    @JsonProperty("name")
    private String name;

    @JsonProperty("price")
    private int price;

    @JsonProperty("created")
    private String created;

    @JsonProperty("updated")
    private String updated;

    @JsonProperty("thumbnails")
    private ArrayList<String> thumbnails;

    @JsonProperty("itemType")
    private String itemType;

    @JsonProperty("itemConditionId")
    private int itemConditionId;

    @JsonProperty("shippingPayerId")
    private int shippingPayerId;

    @JsonProperty("itemBrand")
    private ItemBrand itemBrand;

    @JsonProperty("shopName")
    private String shopName;

    @JsonProperty("itemSize")
    private ItemSize itemSize;

    @JsonProperty("shippingMethodId")
    private int shippingMethodId;

    @JsonProperty("categoryId")
    private int categoryId;

    @JsonProperty("isNoPrice")
    private String isNoPrice;

    @JsonProperty("title")
    private String title;

    @JsonProperty("isLiked")
    private String isLiked;
}
