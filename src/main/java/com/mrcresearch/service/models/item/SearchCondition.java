package com.mrcresearch.service.models.item;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SearchCondition {
    @JsonProperty("keyword")
    private String keyword;

    @JsonProperty("excludeKeyword")
    private String excludeKeyword;

    @JsonProperty("sort")
    private String sort;

    @JsonProperty("order")
    private String order;

    @JsonProperty("status")
    private ArrayList<String> status;

    @JsonProperty("sizeId")
    private ArrayList<Integer> sizeId;

    @JsonProperty("categoryId")
    private ArrayList<Integer> categoryId;

    @JsonProperty("brandId")
    private ArrayList<Integer> brandId;

    @JsonProperty("sellerId")
    private ArrayList<String> sellerId;

    @JsonProperty("priceMin")
    private Integer priceMin;

    @JsonProperty("priceMax")
    private Integer priceMax;

    @JsonProperty("itemConditionId")
    private ArrayList<Integer> itemConditionId;

    @JsonProperty("shippingPayerId")
    private ArrayList<Integer> shippingPayerId;

    @JsonProperty("shippingFromArea")
    private ArrayList<String> shippingFromArea;

    @JsonProperty("shippingMethod")
    private ArrayList<String> shippingMethod;

    @JsonProperty("colorId")
    private ArrayList<String> colorId;

    @JsonProperty("hasCoupon")
    private Boolean hasCoupon;

    @JsonProperty("createdAfterDate")
    private String createdAfterDate;

    @JsonProperty("createdBeforeDate")
    private String createdBeforeDate;

    @JsonProperty("attributes")
    private ArrayList<String> attributes;

    @JsonProperty("itemTypes")
    private ArrayList<String> itemTypes;

    @JsonProperty("skuIds")
    private ArrayList<String> skuIds;

    @JsonProperty("shopIds")
    private ArrayList<String> shopIds;

    @JsonProperty("promotionValidAt")
    private String promotionValidAt;

    @JsonProperty("excludeShippingMethodIds")
    private ArrayList<String> excludeShippingMethodIds;
}
