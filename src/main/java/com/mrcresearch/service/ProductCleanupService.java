package com.mrcresearch.service;

import com.mrcresearch.util.ImageCleanupUtil;
import com.mrcresearch.util.database.DatabaseConnectionManager;
import com.mrcresearch.util.database.KeywordSearchItemRepository;
import com.mrcresearch.util.database.SearchItemRepository;
import com.mrcresearch.util.database.SellerSearchItemRepository;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

/**
 * 商品データ削除サービス
 * 古い商品データとその関連データを削除する機能を提供
 */
public class ProductCleanupService {

    /**
     * 削除モード
     */
    public enum CleanupMode {
        ALL_DATA,    // 全データ削除
        IMAGES_ONLY  // 画像のみ削除
    }

    /**
     * 削除結果を格納するクラス
     */
    public static class CleanupResult {
        private final int deletedItems;
        private final int deletedImages;
        private final int errorCount;

        public CleanupResult(int deletedItems, int deletedImages, int errorCount) {
            this.deletedItems = deletedItems;
            this.deletedImages = deletedImages;
            this.errorCount = errorCount;
        }

        public int getDeletedItems() {
            return deletedItems;
        }

        public int getDeletedImages() {
            return deletedImages;
        }

        public int getErrorCount() {
            return errorCount;
        }
    }

    /**
     * 指定日数より古い売り切れ商品の件数を取得
     *
     * @param daysOld 日数
     * @return 削除対象の商品件数
     */
    public static int getOldItemsCount(int daysOld) throws SQLException {
        String sql = "SELECT COUNT(*) FROM search_items " +
                "WHERE status IN (2, 3) " +  // 2:取引中, 3:取引完了
                "AND datetime(updated_date) <= datetime('now', '-' || ? || ' days')";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, daysOld);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }

        return 0;
    }

    /**
     * 指定日数より古い売り切れ商品のIDリストを取得
     *
     * @param daysOld 日数
     * @return 削除対象の商品IDリスト
     */
    public static List<String> getOldItemIds(int daysOld) throws SQLException {
        List<String> itemIds = new ArrayList<>();

        String sql = "SELECT item_id FROM search_items " +
                "WHERE status IN (2, 3) " +  // 2:取引中, 3:取引完了
                "AND datetime(updated_date) <= datetime('now', '-' || ? || ' days')";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, daysOld);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    itemIds.add(rs.getString("item_id"));
                }
            }
        }

        return itemIds;
    }

    /**
     * 商品データ削除を実行
     *
     * @param daysOld          日数
     * @param progressCallback 進捗報告用コールバック
     * @return 削除結果
     */
    public static CleanupResult executeCleanup(int daysOld, Consumer<String> progressCallback) {
        return executeCleanup(daysOld, CleanupMode.ALL_DATA, progressCallback);
    }

    /**
     * 商品データ削除を実行（削除モード指定）
     *
     * @param daysOld          日数
     * @param mode             削除モード（ALL_DATA: 全データ削除、IMAGES_ONLY: 画像のみ削除）
     * @param progressCallback 進捗報告用コールバック
     * @return 削除結果
     */
    public static CleanupResult executeCleanup(int daysOld, CleanupMode mode, Consumer<String> progressCallback) {
        int deletedItems = 0;
        int deletedImages = 0;
        int errorCount = 0;

        try {
            progressCallback.accept("削除対象商品を取得中...");
            List<String> itemIds = getOldItemIds(daysOld);

            if (itemIds.isEmpty()) {
                progressCallback.accept("削除対象の商品が見つかりませんでした");
                return new CleanupResult(0, 0, 0);
            }

            String modeText = (mode == CleanupMode.IMAGES_ONLY) ? "画像のみ" : "全データ";
            progressCallback.accept("削除対象商品: " + itemIds.size() + " 件（削除モード: " + modeText + "）");

            // 商品データとその関連データを削除
            for (int i = 0; i < itemIds.size(); i++) {
                String itemId = itemIds.get(i);

                try {
                    // 進捗報告
                    if (i % 10 == 0 || i == itemIds.size() - 1) {
                        progressCallback.accept("処理中: " + (i + 1) + "/" + itemIds.size() + " (" + itemId + ")");
                    }

                    boolean success = true;

                    // 削除モードに応じた処理
                    if (mode == CleanupMode.ALL_DATA) {
                        // 全データ削除モード: 関連データを削除
                        success = deleteItemAndRelatedData(itemId);
                        if (success) {
                            deletedItems++;
                        }
                    }

                    if (success) {
                        // 画像ファイルを削除（両方のモードで実行）
                        boolean imageDeleted = ImageCleanupUtil.deleteImageFile(itemId);
                        if (imageDeleted) {
                            deletedImages++;
                        }
                    } else {
                        errorCount++;
                        progressCallback.accept("エラー: 商品 " + itemId + " の削除に失敗");
                    }

                } catch (Exception e) {
                    errorCount++;
                    progressCallback.accept("エラー: 商品 " + itemId + " - " + e.getMessage());
                }
            }

            progressCallback.accept("削除処理完了");

        } catch (Exception e) {
            progressCallback.accept("削除処理中にエラーが発生: " + e.getMessage());
            errorCount++;
        }

        return new CleanupResult(deletedItems, deletedImages, errorCount);
    }

    /**
     * 商品とその関連データを削除
     *
     * @param itemId 商品ID
     * @return 削除成功の場合true
     */
    private static boolean deleteItemAndRelatedData(String itemId) {
        boolean success = true;

        try {
            // 1. keyword_search_items から削除
            if (!KeywordSearchItemRepository.deleteKeywordSearchItem(itemId)) {
                System.err.println("Failed to delete keyword search item: " + itemId);
                // 関連データが存在しない場合もあるので、エラーとしない
            }

            // 2. seller_search_items から削除
            if (!SellerSearchItemRepository.deleteSellerSearchItem(itemId)) {
                System.err.println("Failed to delete seller search item: " + itemId);
                // 関連データが存在しない場合もあるので、エラーとしない
            }

            // 3. search_items から削除（メインデータ）
            if (!SearchItemRepository.deleteSearchItem(itemId)) {
                System.err.println("Failed to delete search item: " + itemId);
                success = false;
            }

        } catch (Exception e) {
            System.err.println("Error deleting item and related data for " + itemId + ": " + e.getMessage());
            e.printStackTrace();
            success = false;
        }

        return success;
    }

    /**
     * 削除処理のテスト用メソッド（実際の削除は行わない）
     *
     * @param daysOld 日数
     * @return 削除対象の詳細情報
     */
    public static String getCleanupPreviewDetails(int daysOld) {
        StringBuilder details = new StringBuilder();

        try {
            List<String> itemIds = getOldItemIds(daysOld);

            details.append("削除対象商品数: ").append(itemIds.size()).append("\n");
            details.append("削除条件: 売り切れから").append(daysOld).append("日以上経過\n");
            details.append("削除対象ステータス: 取引中(2), 取引完了(3)\n\n");

            if (!itemIds.isEmpty()) {
                details.append("削除対象商品ID（最初の10件）:\n");
                for (int i = 0; i < Math.min(10, itemIds.size()); i++) {
                    details.append("- ").append(itemIds.get(i)).append("\n");
                }

                if (itemIds.size() > 10) {
                    details.append("... 他 ").append(itemIds.size() - 10).append(" 件\n");
                }
            }

        } catch (Exception e) {
            details.append("プレビュー取得エラー: ").append(e.getMessage());
        }

        return details.toString();
    }

    /**
     * 現在の日時を取得（ログ用）
     */
    private static String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}
