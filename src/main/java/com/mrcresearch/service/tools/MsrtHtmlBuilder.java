package com.mrcresearch.service.tools;

import com.mrcresearch.service.analyze.model.GroupItemModel;
import com.mrcresearch.service.analyze.model.SellerRatingModel;
import com.mrcresearch.service.common.HtmlTags;
import com.mrcresearch.service.common.MercariApi;
import com.mrcresearch.service.property.ApplicationsProperty;
import com.mrcresearch.service.tools.model.TagOptionModel;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Objects;

import static com.mrcresearch.service.tools.service.ExportValues.*;

public class MsrtHtmlBuilder extends HtmlTags {
    public static final boolean LAZY = true;
    public static final boolean NOT_LAZY = false;
    //    private static final InfoUtil info = new InfoUtil();
    private final StringBuilder sb = new StringBuilder();
    // 固定値関連
    private static final String logoFilePath = "/html/logosvg";
    private static final String styleFilePath = "/html/style";
    private static final String headerScriptsFilePath = "/html/headerscripts";
    private static final String tableScriptHeadPath = "/html/tablescripthead";
    private static final String tableScriptTailPath = "/html/tablescripttail";
    private static final String tableColSum = "\t\t\t\tapi.column(X).footer().innerHTML = api.column(X, { page: 'current' }).data().reduce((a, b) => intVal(a) + intVal(b), 0);";

    /**
     * ビルドしたHtmlをStringにして返却する
     *
     * @return Html
     */
    @Override
    public String toString() {
//        info.printDebug("Html Generated");
        return sb.toString();
    }

    /**
     * 行追加<br />
     * 形式：text + \n
     *
     * @param text 追加する文章
     */
    public void line(String text) {
        sb.append(Objects.toString(text, ""));
        sb.append("\n");
    }

    /**
     * 改行せずに追加<br />
     * 形式：text
     *
     * @param text 追加する文章
     */
    public void text(String text) {
        sb.append(text);
    }

    /**
     * タグにオプションを追加する<br />
     * 形式：{@literal <tag name="value">}
     *
     * @param tag     追加するタグ
     * @param options オプション情報
     */
    public void addTagWithOptions(String tag, List<TagOptionModel> options) {
        sb.append(tag);
        for (TagOptionModel option : options) {
            sb.insert(sb.length() - 1, " " + option.name() + "=\"" + option.value() + "\"");
        }
    }

    /**
     * Base64の画像を設定する<br />
     * {@literal <img src="data:image/png;base64,image">}
     *
     * @param image  Base64画像のテキスト
     * @param isLazy 画像の読み込みを遅延させるか
     */
    public void addBase64Image(String image, boolean isLazy) {
        List<TagOptionModel> options = new ArrayList<>();
        options.add(new TagOptionModel(Options.SRC, "data:image/png;base64," + image));
        if (isLazy) {
            options.add(new TagOptionModel(Options.LOADING, "lazy"));
        }
        addTagWithOptions(img, options);
    }

    /**
     * 画像のURLを埋め込む<br />
     * {@literal <img src="url" loading="lazy">}
     *
     * @param url    画像のURL
     * @param isLazy 画像の読み込みを遅延させるか
     */
    public void addImageUrl(String url, boolean isLazy) {
        List<TagOptionModel> options = new ArrayList<>();
        options.add(new TagOptionModel(Options.SRC, url));
        if (isLazy) {
            options.add(new TagOptionModel(Options.LOADING, "lazy"));
        }
        addTagWithOptions(img, options);
    }

    /**
     * td要素を設定する<br />
     * 形式：{@literal <td options>tagInfo</td>}
     *
     * @param tagInfo タグに埋め込む情報 Cannot be {@code null}
     */
    public void setSimpleTd(String tagInfo) {
        sb.append(td);
        sb.append(tagInfo);
        sb.append(tdEnd);
        sb.append("\n");
    }

    /**
     * td要素を設定する<br />
     * 形式：{@literal <td options>tagInfo</td>}
     *
     * @param tagInfo タグに埋め込む情報 Cannot be {@code null}
     * @param options オプション情報 May be {@code null}
     */
    public void setSimpleTd(String tagInfo, List<TagOptionModel> options) {
        // オプションが設定されていれば
        if (Objects.isNull(options)) {
            sb.append(td);
        } else {
            addTagWithOptions(td, options);
        }
        sb.append(tagInfo);
        sb.append(tdEnd);
        sb.append("\n");
    }

    /**
     * リンクをつけたtd要素を追加する
     * 形式：{@literal <td>tagInfo<br><a href="link" target="_blank>リンクを開く</a></td>}
     *
     * @param tagInfo タグ成功
     * @param link    リンク
     */
    public void setTdWithLink(String tagInfo, String link) {
        sb.append(td);
        sb.append(tagInfo);
        sb.append(br);
        addTagWithOptions(a, List.of(new TagOptionModel(Options.HREF, link),
                new TagOptionModel(Options.TARGET, "_blank")));
        sb.append("リンクを開く");
        sb.append(aEnd);
        sb.append(tdEnd);
        sb.append("\n");
    }

    /**
     * ロゴを設定する
     */
    public void setLogoImage() {
        appendFromFile(logoFilePath);
    }

    /**
     * スタイルを設定する
     */
    public void setStyle() {
        appendFromFile(styleFilePath);
    }

    /**
     * Htmlのヘッダ情報を設定する
     */
    public void setHtmlHeader(String fileName) {
        line("<!DOCTYPE html>");
        line("<html lang=\"en\">");
        line(HtmlTags.head);
        line("<meta charset=\"UTF-8\">");
        line("<title>" + fileName + "</title>");
        setHeader();
        line(HtmlTags.headEnd);
    }

    /**
     * ヘッダにいれるスクリプトを書き込む
     */
    public void setHeader() {
        appendFromFile(headerScriptsFilePath);
        appendFromFile(tableScriptHeadPath);
        int saveDays = Integer.parseInt(ApplicationsProperty.getProperty(ApplicationsProperty.SAVE_DAYS));
        for (int i = 0; i <= saveDays; i++) {
            sb.append(tableColSum.replace("column(X", "column(" + (i + 8)));
            sb.append("\n");
        }
        appendFromFile(tableScriptTailPath);
    }

    /**
     * ファイルパスからappendする
     *
     * @param filePath 入力ファイルパス
     */
    private void appendFromFile(String filePath) {
        BufferedReader reader;
        try {
            URL resource = getClass().getResource(filePath);
            if (resource != null) {
                reader = new BufferedReader(new InputStreamReader(resource.openStream()));
            } else {
                throw new RuntimeException("Resource not found: " + filePath);
            }
            String line = reader.readLine();

            // 1行ずつ読み込み
            while (line != null) {
                sb.append(line).append("\n");
                line = reader.readLine();
            }

            reader.close();
        } catch (IOException e) {
//            info.printDebug("failed to load file : " + filePath);
        }
    }

    /**
     * セラー評価のテーブルをBuilderに書き込む
     *
     * @param srm セラーの評価
     */
    public void setReviewTable(SellerRatingModel srm) {
        line(HtmlTags.h3 + "セラー評価" + HtmlTags.h3End);

        List<TagOptionModel> tableOption = new ArrayList<>();
        tableOption.add(new TagOptionModel(Options.CLASS, "table table-sm"));
        addTagWithOptions(HtmlTags.table, tableOption);
        // テーブルヘッダ
        line(HtmlTags.thead);
        List<TagOptionModel> trOption = new ArrayList<>();
        trOption.add(new TagOptionModel(Options.CLASS, "table-primary"));
        addTagWithOptions(HtmlTags.tr, trOption);
        line(HtmlTags.td + "ID" + HtmlTags.tdEnd);
        line(HtmlTags.td + "名前" + HtmlTags.tdEnd);
        line(HtmlTags.td + "評価最新" + HtmlTags.tdEnd);
        line(HtmlTags.td + "評価最古" + HtmlTags.tdEnd);
        line(HtmlTags.td + "販売数/日" + HtmlTags.tdEnd);
        line(HtmlTags.td + "差分" + HtmlTags.tdEnd);
        line(HtmlTags.td + "評価件数" + HtmlTags.tdEnd);
        line(HtmlTags.td + "総販売数" + HtmlTags.tdEnd);
        line(HtmlTags.trEnd + HtmlTags.theadEnd);
        // 評価内容
        line(HtmlTags.tr);
        setSimpleTd(srm.getId());
        setSimpleTd(srm.getName());
        setSimpleTd(srm.getLatestRateDateString());
        setSimpleTd(srm.getOldestRateDateString());
        setSimpleTd(srm.getDayAverage());
        setSimpleTd(srm.getDateDiff());
        setSimpleTd(srm.getReviewCount());
        setSimpleTd(srm.getSellItemCount());
        line(HtmlTags.trEnd);
        line(HtmlTags.tableEnd);
    }

    /**
     * Htmlのヘッダの配列を生成する<br />
     * {@literal <thead><tr><td>~~~</td></tr></thead>}
     */
    public void setHtmlTableHeader(int saveDays) {
        line(HtmlTags.thead);
        List<TagOptionModel> options = new ArrayList<>();
        options.add(new TagOptionModel(Options.CLASS, "table-primary"));
        addTagWithOptions(HtmlTags.tr, options);

        setSimpleTd(SELLER_ID);
        setSimpleTd(ITEM_NAME);
        setSimpleTd(ITEM_ID);
        setSimpleTd(ITEM_IMAGE);
        setSimpleTd(TOTAL_SOLD);
        setSimpleTd("24h～" + saveDays + "日");
        setSimpleTd(ON_SAIL);
        setSimpleTd(ITEM_PRICE);
        setSimpleTd(TWENTY_FOUR_HOURS);

        for (int i = 1; i <= saveDays; i++) {
            setSimpleTd(i + "日");
        }

        line(HtmlTags.trEnd);
        line(HtmlTags.theadEnd);
    }

    /**
     * Htmlのデータ（テーブル行）の追加<br />
     *
     * @param item 追加する行の情報
     */
    public void setHtmlTableRow(int saveDays, GroupItemModel item) {
        line(HtmlTags.tr);

        setTdWithLink(item.getSellerId(), item.getUserPageUrl());
        setSimpleTd(item.getName()); // 商品名
        setTdWithLink(item.getId(), item.getItemIdPageUrl());
        // 画像
        text(HtmlTags.td);
        if (Objects.nonNull(item.getImage())) {
            addBase64Image(Base64.getEncoder().encodeToString(item.getImage()), LAZY);
        } else {
            String url = Objects.isNull(item.getImageUrl()) && item.getId().matches(MercariApi.USER_ITEM_REGEX) ? "https://static.mercdn.net/item/detail/orig/photos/" + item.getId() + "_1.jpg" : item.getImageUrl();
            addImageUrl(url, LAZY);
        }
        line(HtmlTags.tdEnd);
        setSimpleTd(String.valueOf(item.getDisplay())); // 表示数
        setSimpleTd(String.valueOf(item.sumUpdated())); // 期限内に売れた数
        setSimpleTd(String.valueOf(item.getOnTrade())); // 出品数
        setSimpleTd(String.valueOf(item.getPrice())); // 価格

        // 日毎の売上を出力
        for (int i = 0; i <= saveDays; i++) {
            setSimpleTd(String.valueOf(item.getUpdated()[i]));
        }

        line(HtmlTags.trEnd);
    }

}
