package com.mrcresearch.service.tools;

import com.browserup.harreader.model.HarEntry;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.List;
import java.util.Objects;

/**
 * 画像関連の共通処理
 */
public class ImageTool {
//    private static final InfoUtil info = new InfoUtil();

    /**
     * HARから画像を取得する
     *
     * @param entries HARのエントリー
     * @param url     アイテムのURL
     */
    public static byte[] getImageFromList(List<HarEntry> entries, String url) {
        // 対象を取得する
        HarEntry imageEntry = entries.stream()
                .filter(entry -> entry.getRequest().getUrl().equals(url))
                .findFirst().orElse(null);
        // 対象が見つかれば、画像データを取得する
        if (Objects.nonNull(imageEntry)) {
            String imageStr = imageEntry.getResponse().getContent().getText();
            if (Objects.nonNull(imageStr)) {
                return Base64.getDecoder().decode(imageStr);
            }
        }
        // 何も見つからなければ、nullを返却する
        return null;
    }

    /**
     * URLから画像をダウンロードしてbyte配列にする
     *
     * @param imageUrl 取得する画像のURL
     * @return 画像データ
     */
    public static byte[] downloadImage(String imageUrl) {
        try (InputStream inputStream = new URL(imageUrl).openStream()) {
            return inputStream.readAllBytes();
        } catch (Exception e) {
//            info.printDebug("Failed to download image : " + imageUrl);
            return null;
        }
    }

    /**
     * 画像をユーザーのフォルダに保存する
     *
     * @param imageData 画像データ
     * @param itemId    アイテムID（ファイル名として使用）
     * @return 保存されたファイル名（保存に失敗した場合はnull）
     */
    public static String saveImageToUserFolder(byte[] imageData, String itemId) {
        if (imageData == null || itemId == null) {
            return null;
        }

        try {
            // ユーザーホームディレクトリを取得
            String userHome = System.getProperty("user.home");
            String separator = System.getProperty("file.separator");

            // 保存先ディレクトリのパスを作成
            String imageDirPath = userHome + separator + ".mrcresearch" + separator + "img";
            Path imageDir = Paths.get(imageDirPath);

            // ディレクトリが存在しない場合は作成
            if (!Files.exists(imageDir)) {
                Files.createDirectories(imageDir);
            }

            // ファイル名を作成（拡張子は.jpg固定）
            String fileName = itemId + ".jpg";
            Path filePath = imageDir.resolve(fileName);

            // ファイルに書き込み
            Files.write(filePath, imageData);

            // ファイル名のみを返す（フルパスではなく）
            return fileName;
        } catch (IOException e) {
            // 保存に失敗した場合はnullを返す
            return null;
        }
    }
}
