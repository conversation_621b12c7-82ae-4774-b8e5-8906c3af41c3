package com.mrcresearch.service.tools;

import org.apache.hc.client5.http.utils.Base64;
import org.brotli.dec.BrotliInputStream;

import java.io.ByteArrayInputStream;

/**
 * HAR解析のためにテキストをデコードする
 */
public class DecodeUtil {
    private final InfoUtil info = new InfoUtil();

    /**
     * ノードをデコードして返却する
     *
     * @param text ノードテキスト
     * @return デコードしたテキスト
     */
    public String decodeNodeTxt(String text) {
        try {
            ByteArrayInputStream bas = new ByteArrayInputStream(Base64.decodeBase64(text));
            BrotliInputStream brotliInputStream = new BrotliInputStream(bas);
            return new String(brotliInputStream.readAllBytes());
        } catch (Exception e) {
            info.printDebug("Failed to decode node text");
            return text;
        }
    }
}
