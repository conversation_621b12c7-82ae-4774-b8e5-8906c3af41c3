package com.mrcresearch.service.tools;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

public class DriverUtil {

    /**
     * data-testid属性に指定されたvalueを探し、見つかった要素を返却する。
     *
     * @param driver ドライバー
     * @param value  値
     * @return 検出した要素
     */
    public static WebElement chooseByMercariTestId(WebDriver driver, String value) {
        return driver.findElements(By.tagName("data-testid")).stream()
                .filter(n -> n.getAttribute("data-testid").contains(value)).findFirst().orElse(null);
    }
}
