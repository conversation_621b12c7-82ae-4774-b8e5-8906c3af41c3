package com.mrcresearch.service.tools;

import java.util.logging.Logger;

public class InfoUtil {
    /**
     * デフォルトの出力メッセージ
     */
    private static final String DEFAULT_MESSAGE = "MercariResearchTool - ";
    public static final Logger logger = Logger.getLogger(InfoUtil.class.getName());

    /**
     * 通常のログ出力に使う
     *
     * @param message 出力メッセージ
     */
    public void printDebug(String message) {
        logger.info(DEFAULT_MESSAGE + message);
    }

    /**
     * エラー系の出力に使う
     *
     * @param message 出力メッセージ
     */
    public void showSystemWarn(String message) {
        logger.warning(DEFAULT_MESSAGE + message);
    }

    /**
     * 画面に出力するメッセージ
     *
     * @param message 出力メッセージ
     */
    public void showScreenMessage(String message) {
        logger.info(DEFAULT_MESSAGE + message);
    }
}
