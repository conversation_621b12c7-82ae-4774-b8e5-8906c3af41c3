package com.mrcresearch.service.tools;

import java.util.Objects;

public class StringUtility {
    /**
     * 名前を整形する
     *
     * @param name 商品名
     * @return 整形した名前
     */
    public static String moldItemName(String name) {
        return Objects.isNull(name) ? null : name.replace(",", "") // csv出力時に崩れないように
                .replace("　", " ") // 全角スペースは半角にする
                .replaceAll(" {2,}", " ") // スペース2以上→1つ
                .replaceAll("\"", "") // "を削除
                .replaceAll("[<>]", ""); // htmlのタグ情報になるものは削除
    }

    /**
     * OSごとのフォルダでミリタを返す
     *
     * @return
     */
    public static String osFolderDemilitter() {
        String osname = System.getProperty("os.name");
        if (osname.contains("Windows")) {
            return "\\";
        } else if (osname.contains("Linux")) {
            return "/";
        } else if (osname.contains("<PERSON>")) {
            return "/";
        } else {
            return "/";
        }
    }
}
