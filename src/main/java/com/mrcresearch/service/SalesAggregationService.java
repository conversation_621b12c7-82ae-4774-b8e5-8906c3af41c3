package com.mrcresearch.service;

import com.mrcresearch.screen.component.category.Categories;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.mrcresearch.screen.model.SalesAggregationFilterModel;
import com.mrcresearch.screen.model.SalesAggregationModel;
import com.mrcresearch.util.database.DatabaseUtil;
import com.mrcresearch.util.database.SalesAggregationRepository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 売上集計関連の操作を処理するためのサービスクラスです。
 * このクラスは、様々なフィルターに基づいて売上データを取得するメソッドを提供します。
 */
public class SalesAggregationService {

    private static final Logger logger = LoggerFactory.getLogger(SalesAggregationService.class);

    private final Categories categories;

    /**
     * コンストラクタ
     */
    public SalesAggregationService() {
        this.categories = new Categories();
        this.categories.init();
    }

    /**
     * 過去7日間の売り切れ・取引中アイテム集計データを取得する（フィルタ対応）
     * データはデータベースレベルで総販売数の降順でソートされて返される
     *
     * @param page     ページ番号（1ベース）
     * @param pageSize 1ページあたりの件数
     * @param filter   フィルタ条件
     * @return 集計結果のリスト（総販売数降順、データベースレベルでソート済み）
     */
    public List<SalesAggregationModel> getSalesAggregationData(int page, int pageSize, SalesAggregationFilterModel filter) {
        List<SalesAggregationModel> results = SalesAggregationRepository.getSalesAggregationDataWithFilter(page, pageSize, filter);

        // カテゴリ名を正しく設定
        for (SalesAggregationModel model : results) {
            String categoryName = getCategoryNameById(model.getCategoryName());
            model.setCategoryName(categoryName);
        }

        logger.debug("フィルタが適用され、データベースレベルで総販売数降順にソートされた {} 件のアイテムを取得しました。", results.size());

        return results;
    }

    /**
     * 売上集計データの総件数を取得する（フィルタ対応）
     *
     * @param filter フィルタ条件
     * @return 総件数
     */
    public int getSalesAggregationDataCount(SalesAggregationFilterModel filter) {
        return SalesAggregationRepository.getSalesAggregationDataCountWithFilter(filter);
    }

    /**
     * カテゴリIDからカテゴリ名を取得する
     * DatabaseUtilの統一されたメソッドを使用
     *
     * @param categoryIdStr カテゴリIDの文字列
     * @return カテゴリ名
     */
    private String getCategoryNameById(String categoryIdStr) {
        if (categoryIdStr == null || categoryIdStr.isEmpty()) {
            return "カテゴリなし";
        }

        // DatabaseUtilの統一されたメソッドを使用
        return DatabaseUtil.convertToDisplayCategoryName(categoryIdStr);
    }

    /**
     * 過去N日間の特定商品の日次売上数を取得します。
     *
     * @param productName 売上データを取得する商品の名前。
     * @param sellerId    売上データを取得するセラーのID。
     * @param days        データを取得する過去の日数。
     * @return キーが日付、値がその日の売上数であるマップ。
     */
    public Map<LocalDate, Integer> getDailySalesForProduct(String productName, String sellerId, int days) {
        String sql = "SELECT DATE(updated_date) as sold_date, COUNT(*) as count " +
                "FROM search_items " +
                "WHERE product_name = ? AND seller_id = ? AND status in (2, 3) AND DATE(updated_date) >= ? " +
                "GROUP BY sold_date ORDER BY sold_date DESC";

        LocalDate startDate = LocalDate.now().minusDays(days);
        Map<LocalDate, Integer> salesData = new HashMap<>();

        logger.debug("[DailySales] メソッドが呼び出されました。商品名: {}, セラーID: {}, 日数: {}", productName, sellerId, days);
        logger.debug("[DailySales] 計算された開始日: {}", startDate);
        logger.debug("[DailySales] SQLクエリ: {}", sql);

        try (java.sql.Connection conn = com.mrcresearch.util.database.DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, productName);
            pstmt.setString(2, sellerId);
            pstmt.setString(3, startDate.toString());

            logger.debug("[DailySales] パラメータ設定: 1={}, 2={}, 3={}", productName, sellerId, startDate.toString());

            ResultSet rs = pstmt.executeQuery();
            if (!rs.isBeforeFirst()) { // ResultSetが空かどうかを確認
                logger.debug("[DailySales] ResultSetは空です。product_name: {}, seller_id: {} (status 3, date >= {}) のデータは見つかりませんでした。", productName, sellerId, startDate);
            }

            while (rs.next()) {
                LocalDate soldDate = LocalDate.parse(rs.getString("sold_date"));
                int count = rs.getInt("count");
                salesData.put(soldDate, count);
                logger.debug("[DailySales] 取得した行 - 日付: {}, カウント: {}", soldDate, count);
            }
            logger.debug("[DailySales] 取得した日次売上レコードの総数: {}", salesData.size());
        } catch (SQLException e) {
            logger.error("ERROR: [DailySales] 商品の日次売上取得に失敗しました: " + e.getMessage(), e);
        }
        return salesData;
    }
}
