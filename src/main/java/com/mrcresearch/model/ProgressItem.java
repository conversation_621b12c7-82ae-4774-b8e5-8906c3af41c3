package com.mrcresearch.model;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Progress item model for tracking individual task progress
 */
@Getter
@Setter
public class ProgressItem {
    public enum Status {
        WAITING("待機中"),
        PROCESSING("処理中"),
        COMPLETED("完了"),
        ERROR("エラー");

        private final String displayName;

        Status(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    private String id;
    private String taskName;
    private String itemName; // e.g., seller ID
    private Status status;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private int currentIndex;
    private int totalCount;
    private String errorMessage;

    public ProgressItem(String id, String taskName, String itemName, int currentIndex, int totalCount) {
        this.id = id;
        this.taskName = taskName;
        this.itemName = itemName;
        this.status = Status.WAITING;
        this.currentIndex = currentIndex;
        this.totalCount = totalCount;
        this.startTime = LocalDateTime.now();
    }

    // Custom setter for status to handle endTime logic
    public void setStatus(Status status) {
        this.status = status;
        if (status == Status.COMPLETED || status == Status.ERROR) {
            this.endTime = LocalDateTime.now();
        }
    }

    /**
     * Get display text for current progress
     */
    public String getProgressText() {
        String shortItemName = itemName.length() > 10 ? itemName.substring(0, 10) + "..." : itemName;
        return String.format("%s: %s (%d/%d)", taskName, shortItemName, currentIndex, totalCount);
    }

    /**
     * Get completion text
     */
    public String getCompletionText() {
        String shortItemName = itemName.length() > 10 ? itemName.substring(0, 10) + "..." : itemName;
        return String.format("完了: %s", shortItemName);
    }

    /**
     * Get formatted time string
     */
    public String getFormattedTime() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        if (endTime != null) {
            return endTime.format(formatter);
        } else {
            return startTime.format(formatter);
        }
    }

    /**
     * Check if item is completed
     */
    public boolean isCompleted() {
        return status == Status.COMPLETED || status == Status.ERROR;
    }


}
