package com.mrcresearch.util;

import com.mrcresearch.service.SellerManagementService;

import javax.swing.*;
import javax.swing.table.DefaultTableCellRenderer;
import java.awt.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * セラーID列用のマルチライン表示セルレンダラー
 * セラーID、セラー名、最終取得日を複数行で表示する
 */
public class MultiLineSellerCellRenderer extends DefaultTableCellRenderer {

    private static final int MIN_ROW_HEIGHT = 80;
    private static final int PADDING = 10;

    // セラー情報を格納するためのマップ
    public java.util.Map<String, SellerInfo> sellerInfoMap = new java.util.HashMap<>();

    /**
     * セラー情報を格納するクラス
     */
    public static class SellerInfo {
        public final String sellerName;
        public final String lastUpdatedAt;
        public final String researchStatus;

        public SellerInfo(String sellerName, String lastUpdatedAt, String researchStatus) {
            this.sellerName = sellerName;
            this.lastUpdatedAt = lastUpdatedAt;
            this.researchStatus = researchStatus;
        }

        // 後方互換性のためのコンストラクタ
        public SellerInfo(String sellerName, String lastUpdatedAt) {
            this(sellerName, lastUpdatedAt, null);
        }
    }

    /**
     * セラー情報を設定する（新しいSellerManagementServiceを使用してセラー名を改善）
     */
    public void setSellerInfo(String sellerId, String sellerName, String lastUpdatedAt, String researchStatus) {
        if (sellerId != null) {
            // Use the new SellerManagementService to get the best available seller name
            String bestSellerName = SellerManagementService.getBestSellerName(sellerId);

            // If we got a better name than what was provided, use it
            String finalSellerName = sellerName;
            if (SellerManagementService.isValidSellerName(sellerId, bestSellerName) &&
                    !bestSellerName.equals(sellerName)) {
                finalSellerName = bestSellerName;
                // Update the seller information in the master table
                SellerManagementService.updateSellerName(sellerId, bestSellerName);
            }

            sellerInfoMap.put(sellerId, new SellerInfo(finalSellerName, lastUpdatedAt, researchStatus));
        }
    }

    /**
     * セラー情報を設定する（後方互換性のためのメソッド）
     */
    public void setSellerInfo(String sellerId, String sellerName, String lastUpdatedAt) {
        setSellerInfo(sellerId, sellerName, lastUpdatedAt, null);
    }

    /**
     * セラー情報をクリアする
     */
    public void clearSellerInfo() {
        sellerInfoMap.clear();
    }

    @Override
    public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected,
                                                   boolean hasFocus, int row, int column) {

        // Get the default component first
        super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

        if (value == null) {
            setText("");
            return this;
        }

        // セラーIDを取得
        String sellerId = value.toString();

        // セラー情報を取得
        SellerInfo sellerInfo = sellerInfoMap.get(sellerId);
        String sellerName = null;
        String lastUpdatedAt = null;
        String researchStatus = null;

        if (sellerInfo != null) {
            sellerName = sellerInfo.sellerName;
            lastUpdatedAt = sellerInfo.lastUpdatedAt;
            researchStatus = sellerInfo.researchStatus;
        }

        // HTMLを使用してマルチライン表示を作成
        String htmlText = createMultiLineHtml(sellerId, sellerName, lastUpdatedAt, researchStatus);
        setText(htmlText);

        // 垂直方向の配置を上揃えに設定
        setVerticalAlignment(SwingConstants.TOP);

        // 行の高さを調整
        adjustRowHeight(table, row);

        return this;
    }


    /**
     * マルチライン表示用のHTMLを作成する
     */
    private String createMultiLineHtml(String sellerId, String sellerName, String lastUpdatedAt, String researchStatus) {
        StringBuilder html = new StringBuilder();
        html.append("<html><body style='margin: 2px; padding: 2px; font-family: sans-serif;'>");

        // 1行目：セラーID（常に表示、リサーチ中の場合はアイコン付き）
        String sellerIdText = "リサーチ中".equals(researchStatus) ? "🔄 " + escapeHtml(sellerId) : escapeHtml(sellerId);
        html.append("<div style='font-weight: bold; font-size: 12px; margin-bottom: 2px;'>")
                .append(sellerIdText).append("</div>");

        // セラー情報が存在するかチェック
        boolean hasSellerInfo = (sellerName != null && !sellerName.trim().isEmpty() && !sellerName.equals(sellerId)) ||
                (lastUpdatedAt != null && !lastUpdatedAt.trim().isEmpty());

        if (hasSellerInfo) {
            // テーマに応じたセラー名と日付の色を取得
            String sellerNameColor = getSellerNameColor();
            String dateColor = getDateColor();
            String noInfoColor = getNoInfoColor();

            // 2行目：セラー名（存在する場合）
            if (sellerName != null && !sellerName.trim().isEmpty() && !sellerName.equals(sellerId)) {
                html.append("<div style='color: ").append(sellerNameColor).append("; font-size: 11px; margin-bottom: 2px;'>")
                        .append(escapeHtml(sellerName)).append("</div>");
            } else {
                // セラー名がない場合でも行を確保
                html.append("<div style='font-size: 11px; margin-bottom: 2px;'>&nbsp;</div>");
            }

            // 3行目：リサーチステータスまたは最終取得日
            if ("リサーチ中".equals(researchStatus)) {
                // リサーチ中の場合は特別な表示
                String researchingColor = getResearchingColor();
                html.append("<div style='color: ").append(researchingColor).append("; font-size: 10px; font-weight: bold;'>")
                        .append("🔄 リサーチ中...</div>");
            } else if (lastUpdatedAt != null && !lastUpdatedAt.trim().isEmpty()) {
                String formattedDate = formatDate(lastUpdatedAt);
                html.append("<div style='color: ").append(dateColor).append("; font-size: 10px;'>最終取得日:")
                        .append(formattedDate).append("</div>");
            } else {
                // 最終取得日がない場合でも行を確保
                html.append("<div style='font-size: 10px;'>&nbsp;</div>");
            }
        } else {
            // セラー情報がない場合は、セラーIDのみ表示（デバッグ情報を追加）
            String noInfoColor = getNoInfoColor();
            html.append("<div style='color: ").append(noInfoColor).append("; font-size: 10px; font-style: italic;'>セラー情報なし</div>");
        }

        html.append("</body></html>");

        return html.toString();
    }

    /**
     * テーマに応じたセラーIDの色を取得する
     */
    private String getSellerIdColor() {
        // ダークテーマかどうかを判定
        if (isDarkTheme()) {
            return "#FFFFFF"; // ダークテーマでは白色
        } else {
            return "#000000"; // ライトテーマでは黒色
        }
    }

    /**
     * テーマに応じたセラー名の色を取得する
     */
    private String getSellerNameColor() {
        if (isDarkTheme()) {
            return "#CCCCCC"; // ダークテーマでは明るいグレー
        } else {
            return "#666666"; // ライトテーマでは暗いグレー
        }
    }

    /**
     * テーマに応じた日付の色を取得する
     */
    private String getDateColor() {
        if (isDarkTheme()) {
            return "#AAAAAA"; // ダークテーマでは中程度のグレー
        } else {
            return "#888888"; // ライトテーマでは中程度のグレー
        }
    }

    /**
     * テーマに応じた情報なしテキストの色を取得する
     */
    private String getNoInfoColor() {
        if (isDarkTheme()) {
            return "#888888"; // ダークテーマでは暗めのグレー
        } else {
            return "#999999"; // ライトテーマでは明るめのグレー
        }
    }

    /**
     * リサーチ中表示用の色を取得する
     */
    private String getResearchingColor() {
        return isDarkTheme() ? "#FFA500" : "#FF8C00"; // オレンジ色（ダークテーマでは明るめ、ライトテーマでは暗め）
    }

    /**
     * リサーチ中の背景色を取得する
     */
    private Color getResearchingBackgroundColor() {
        return isDarkTheme() ? new Color(60, 45, 30) : new Color(255, 248, 240); // 薄いオレンジ系の背景色
    }

    /**
     * 現在のテーマがダークテーマかどうかを判定する
     */
    private boolean isDarkTheme() {
        try {
            // UIManagerからLookAndFeelの名前を取得してダークテーマかどうかを判定
            String lafName = UIManager.getLookAndFeel().getClass().getSimpleName();
            return lafName.contains("Dark") || lafName.contains("dark");
        } catch (Exception e) {
            // エラーが発生した場合はライトテーマとして扱う
            return false;
        }
    }

    /**
     * 日付をyyyy/m/d形式にフォーマットする
     */
    private String formatDate(String dateStr) {
        try {
            // データベースの日付形式（yyyy-MM-dd HH:mm:ss）からyyyy/m/d形式に変換
            if (dateStr.contains(" ")) {
                dateStr = dateStr.split(" ")[0]; // 日付部分のみ取得
            }

            LocalDate date = LocalDate.parse(dateStr);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/M/d");
            return date.format(formatter);
        } catch (Exception e) {
            // パースに失敗した場合は元の文字列を返す
            return dateStr;
        }
    }

    /**
     * HTMLエスケープ処理
     */
    private String escapeHtml(String text) {
        if (text == null) {
            return "";
        }
        return text.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&#39;");
    }

    /**
     * 行の高さを調整する
     */
    private void adjustRowHeight(JTable table, int row) {
        // 現在の行の高さを取得
        int currentHeight = table.getRowHeight(row);

        // 最小高さを確保
        if (currentHeight < MIN_ROW_HEIGHT) {
            table.setRowHeight(row, MIN_ROW_HEIGHT);
        }

        // テーブル全体の行の高さも調整（すべての行を同じ高さにする）
        if (table.getRowHeight() < MIN_ROW_HEIGHT) {
            table.setRowHeight(MIN_ROW_HEIGHT);
        }
    }
}
