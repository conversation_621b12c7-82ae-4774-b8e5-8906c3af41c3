package com.mrcresearch.util;

import com.mrcresearch.model.ProgressItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

/**
 * Search Queue Manager for handling sequential execution of search operations.
 * Ensures that keyword search and seller search operations are executed one at a time
 * in the order they were requested.
 */
public class SearchQueueManager {
    private static final Logger logger = LoggerFactory.getLogger(SearchQueueManager.class);
    private static SearchQueueManager instance;

    private final BlockingQueue<SearchTask> taskQueue;
    private final AtomicBoolean isProcessing;
    private final AtomicInteger queueSize;
    private Thread processingThread;

    // UI update callbacks
    private Consumer<String> statusUpdateCallback;
    private Consumer<Integer> queueSizeUpdateCallback;
    private Consumer<ProgressItem> progressUpdateCallback;
    private Consumer<ProgressItem> completionCallback;
    private Runnable statusClickCallback;

    // Progress tracking
    private final List<ProgressItem> activeProgress;
    private final List<ProgressItem> completedProgress;

    // Queue tracking for better status display
    private final List<SearchTask> queuedTasks;
    private SearchTask currentTask;

    // Task completion tracking for async operations
    private final Object taskCompletionLock = new Object();
    private volatile boolean waitingForTaskCompletion = false;

    /**
     * Private constructor for singleton pattern
     */
    private SearchQueueManager() {
        this.taskQueue = new LinkedBlockingQueue<>();
        this.isProcessing = new AtomicBoolean(false);
        this.queueSize = new AtomicInteger(0);
        this.activeProgress = Collections.synchronizedList(new ArrayList<>());
        this.completedProgress = Collections.synchronizedList(new ArrayList<>());
        this.queuedTasks = Collections.synchronizedList(new ArrayList<>());
        this.currentTask = null;
        startProcessingThread();
    }

    /**
     * Get the singleton instance
     */
    public static synchronized SearchQueueManager getInstance() {
        if (instance == null) {
            instance = new SearchQueueManager();
        }
        return instance;
    }

    /**
     * Set callback for status updates
     */
    public void setStatusUpdateCallback(Consumer<String> callback) {
        this.statusUpdateCallback = callback;
    }

    /**
     * Set callback for queue size updates
     */
    public void setQueueSizeUpdateCallback(Consumer<Integer> callback) {
        this.queueSizeUpdateCallback = callback;
    }

    /**
     * Set callback for progress updates
     */
    public void setProgressUpdateCallback(Consumer<ProgressItem> callback) {
        this.progressUpdateCallback = callback;
    }

    /**
     * Set callback for completion notifications
     */
    public void setCompletionCallback(Consumer<ProgressItem> callback) {
        this.completionCallback = callback;
    }

    /**
     * Set callback for status click events
     */
    public void setStatusClickCallback(Runnable callback) {
        this.statusClickCallback = callback;
    }

    /**
     * Notify that the current task has completed all operations (including async)
     * This method should be called by tasks when they are completely finished
     */
    public void notifyTaskCompleted() {
        synchronized (taskCompletionLock) {
            if (waitingForTaskCompletion) {
                waitingForTaskCompletion = false;
                taskCompletionLock.notifyAll();

                logger.info("タスク完了通知を受信しました");

                // Update UI on EDT
                SwingUtilities.invokeLater(() -> {
                    updateQueueStatus();
                });
            }
        }
    }

    /**
     * Add a search task to the queue
     */
    public void addTask(SearchTask task) {
        try {
            taskQueue.put(task);
            queuedTasks.add(task);
            int newSize = queueSize.incrementAndGet();

            logger.info("タスクをキューに追加しました: {} (キューサイズ: {})", task.getTaskName(), newSize);

            // Update UI on EDT
            SwingUtilities.invokeLater(() -> {
                if (queueSizeUpdateCallback != null) {
                    queueSizeUpdateCallback.accept(newSize);
                }
                if (statusUpdateCallback != null) {
                    updateQueueStatus();
                }
            });

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("Failed to add task to queue: {}", e.getMessage());
        }
    }

    /**
     * Start the processing thread
     */
    private void startProcessingThread() {
        processingThread = new Thread(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    // Wait for a task
                    SearchTask task = taskQueue.take();

                    // Update processing state
                    isProcessing.set(true);
                    currentTask = task;
                    queuedTasks.remove(task);
                    int remainingTasks = queueSize.decrementAndGet();

                    logger.info("タスクを開始します: {} (残り: {})", task.getTaskName(), remainingTasks);

                    // Update UI on EDT with detailed status immediately
                    SwingUtilities.invokeLater(() -> {
                        updateQueueStatus();
                        if (queueSizeUpdateCallback != null) {
                            queueSizeUpdateCallback.accept(remainingTasks);
                        }
                    });

                    // Execute the task
                    try {
                        task.execute();
                        logger.info("タスクの実行が完了しました: {}", task.getTaskName());

                        // If task has async operations, wait for completion
                        if (task.hasAsyncOperations()) {
                            logger.info("タスクに非同期操作があります。完了を待機中...");
                            synchronized (taskCompletionLock) {
                                waitingForTaskCompletion = true;

                                // Update status to show waiting for completion
                                SwingUtilities.invokeLater(() -> {
                                    updateQueueStatus();
                                });

                                // Wait for task completion notification
                                while (waitingForTaskCompletion) {
                                    try {
                                        taskCompletionLock.wait();
                                    } catch (InterruptedException ie) {
                                        Thread.currentThread().interrupt();
                                        return;
                                    }
                                }
                            }
                            logger.info("タスクの非同期操作が完了しました: {}", task.getTaskName());
                        }

                        // Call task completion callback
                        task.onTaskCompleted();

                    } catch (Exception e) {
                        logger.error("Task failed: {} - {}", task.getTaskName(), e.getMessage(), e);
                    }

                    // Update processing state - task is completely finished
                    isProcessing.set(false);
                    currentTask = null;
                    waitingForTaskCompletion = false;

                    logger.info("タスクが完全に完了しました: {}", task.getTaskName());

                    // Update UI on EDT
                    SwingUtilities.invokeLater(() -> {
                        updateQueueStatus();
                    });

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        });

        processingThread.setDaemon(true);
        processingThread.setName("SearchQueueProcessor");
        processingThread.start();
    }

    /**
     * Get current queue size
     */
    public int getQueueSize() {
        return queueSize.get();
    }

    /**
     * Check if currently processing
     */
    public boolean isProcessing() {
        return isProcessing.get();
    }

    /**
     * Get status string
     */
    public String getStatusString() {
        if (isProcessing.get() && currentTask != null) {
            return "実行中: " + currentTask.getTaskName();
        } else if (queueSize.get() > 0) {
            return "処理待ち: " + queueSize.get() + "件";
        } else {
            return "待機中";
        }
    }

    /**
     * Update queue status with detailed information
     */
    private void updateQueueStatus() {
        if (statusUpdateCallback != null) {
            String status = buildDetailedStatusMessage();
            statusUpdateCallback.accept(status);
        }
    }

    /**
     * Build detailed status message according to requirements
     */
    private String buildDetailedStatusMessage() {
        int queueCount = queueSize.get();

        if (isProcessing.get() && currentTask != null) {
            String taskType = currentTask.getTaskType();
            String baseStatus;

            if ("KEYWORD".equals(taskType)) {
                // キーワード検索：[具体的な検索条件]（キュー：[待機中のタスク数]件）
                String detailedDesc = currentTask.getDetailedDescription();
                baseStatus = "キーワード検索：" + detailedDesc;
            } else if ("SELLER".equals(taskType)) {
                // セラー検索：[セラーID数]件（キュー：[待機中のタスク数]件）
                int itemCount = currentTask.getItemCount();
                if (itemCount > 0) {
                    baseStatus = "セラー検索：" + itemCount + "件";
                } else {
                    baseStatus = "セラー検索：" + currentTask.getDetailedDescription();
                }
            } else {
                // その他のタスクタイプ
                String taskTypeDisplay = getTaskTypeDisplay(taskType);
                baseStatus = taskTypeDisplay + "：" + currentTask.getDetailedDescription();
            }

            // Add async operation status if waiting
            if (waitingForTaskCompletion) {
                baseStatus += "（処理完了待ち）";
            }

            // Add queue information
            if (queueCount > 0) {
                baseStatus += "（キュー：" + queueCount + "件）";
            }

            return baseStatus;

        } else if (queueCount > 0) {
            // 待機中（キュー：[待機中のタスク数]件）
            return "待機中（キュー：" + queueCount + "件）";
        } else {
            // 待機中
            return "待機中";
        }
    }

    /**
     * Get display name for task type
     */
    private String getTaskTypeDisplay(String taskType) {
        switch (taskType) {
            case "KEYWORD":
                return "キーワード検索";
            case "SELLER":
                return "セラー検索";
            case "FAVORITE":
                return "お気に入り検索";
            default:
                return "検索";
        }
    }

    /**
     * Get count of queued tasks by type
     */
    private Map<String, Integer> getQueuedTaskTypeCounts() {
        Map<String, Integer> counts = new HashMap<>();
        for (SearchTask task : queuedTasks) {
            String type = task.getTaskType();
            counts.put(type, counts.getOrDefault(type, 0) + 1);
        }
        return counts;
    }

    /**
     * Get list of queued tasks (newest first)
     */
    public List<SearchTask> getQueuedTasks() {
        List<SearchTask> tasks = new ArrayList<>(queuedTasks);
        Collections.reverse(tasks); // Show newest first
        return tasks;
    }

    /**
     * Get currently executing task
     */
    public SearchTask getCurrentTask() {
        return currentTask;
    }

    /**
     * Get detailed queue information for display
     */
    public String getQueueDetails() {
        StringBuilder details = new StringBuilder();

        if (isProcessing.get() && currentTask != null) {
            details.append("【実行中】\n");
            details.append("タスク: ").append(currentTask.getTaskName()).append("\n");
            details.append("詳細: ").append(currentTask.getDetailedDescription()).append("\n");
            if (waitingForTaskCompletion) {
                details.append("状態: 処理完了待ち\n");
            } else {
                details.append("状態: 実行中\n");
            }
            details.append("\n");
        }

        if (!queuedTasks.isEmpty()) {
            details.append("【待機中のタスク】\n");
            for (int i = 0; i < Math.min(queuedTasks.size(), 5); i++) {
                SearchTask task = queuedTasks.get(i);
                details.append((i + 1)).append(". ").append(task.getTaskName()).append("\n");
                details.append("   詳細: ").append(task.getDetailedDescription()).append("\n");
            }

            if (queuedTasks.size() > 5) {
                details.append("   ... 他 ").append(queuedTasks.size() - 5).append("件\n");
            }
        } else if (!isProcessing.get()) {
            details.append("待機中のタスクはありません");
        }

        return details.toString();
    }

    /**
     * Trigger status click callback
     */
    public void onStatusClicked() {
        if (statusClickCallback != null) {
            statusClickCallback.run();
        }
    }

    /**
     * Shutdown the queue manager
     */
    public void shutdown() {
        if (processingThread != null) {
            processingThread.interrupt();
        }
    }

    /**
     * Add progress item for tracking
     */
    public void addProgressItem(ProgressItem item) {
        activeProgress.add(item);

        SwingUtilities.invokeLater(() -> {
            if (progressUpdateCallback != null) {
                progressUpdateCallback.accept(item);
            }
        });
    }

    /**
     * Update progress item status
     */
    public void updateProgressItem(String itemId, ProgressItem.Status status) {
        synchronized (activeProgress) {
            for (ProgressItem item : activeProgress) {
                if (item.getId().equals(itemId)) {
                    item.setStatus(status);

                    SwingUtilities.invokeLater(() -> {
                        if (progressUpdateCallback != null) {
                            progressUpdateCallback.accept(item);
                        }

                        if (item.isCompleted() && completionCallback != null) {
                            completionCallback.accept(item);
                        }
                    });

                    // Move to completed list if finished
                    if (item.isCompleted()) {
                        activeProgress.remove(item);
                        completedProgress.add(item);
                    }
                    break;
                }
            }
        }
    }

    /**
     * Get active progress items
     */
    public List<ProgressItem> getActiveProgress() {
        return new ArrayList<>(activeProgress);
    }

    /**
     * Get completed progress items
     */
    public List<ProgressItem> getCompletedProgress() {
        return new ArrayList<>(completedProgress);
    }

    /**
     * Clear all progress items
     */
    public void clearAllProgress() {
        activeProgress.clear();
        completedProgress.clear();
    }

    /**
     * Clear completed progress items
     */
    public void clearCompletedProgress() {
        completedProgress.clear();
    }

    /**
     * Remove specific progress item
     */
    public void removeProgressItem(String itemId) {
        completedProgress.removeIf(item -> item.getId().equals(itemId));
    }

    /**
     * Interface for search tasks
     */
    public interface SearchTask {
        void execute() throws Exception;

        String getTaskName();

        /**
         * Get task type for better categorization
         *
         * @return task type (e.g., "KEYWORD", "SELLER", "FAVORITE")
         */
        default String getTaskType() {
            return "UNKNOWN";
        }

        /**
         * Get estimated execution time in seconds
         *
         * @return estimated time or -1 if unknown
         */
        default int getEstimatedTimeSeconds() {
            return -1;
        }

        /**
         * Get task priority (lower number = higher priority)
         *
         * @return priority level (default: 100)
         */
        default int getPriority() {
            return 100;
        }

        /**
         * Get detailed task description for status display
         *
         * @return detailed description including search conditions
         */
        default String getDetailedDescription() {
            return getTaskName();
        }

        /**
         * Get the number of items being processed (for display purposes)
         *
         * @return number of items or -1 if not applicable
         */
        default int getItemCount() {
            return -1;
        }

        /**
         * Called when task execution is completely finished (including async operations)
         * This method should be called by the task implementation when all processing is done
         */
        default void onTaskCompleted() {
            // Default implementation does nothing
            // Tasks should override this if they have async operations
        }

        /**
         * Check if this task has async operations that need to complete
         *
         * @return true if task has async operations, false otherwise
         */
        default boolean hasAsyncOperations() {
            return false;
        }
    }
}
