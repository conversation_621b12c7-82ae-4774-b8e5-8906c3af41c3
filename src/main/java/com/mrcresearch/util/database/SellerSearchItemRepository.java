package com.mrcresearch.util.database;

import com.mrcresearch.screen.model.SellerSearchItemModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Repository class for managing seller search item mappings in the database.
 * Handles CRUD operations for the seller_search_items table.
 */
public class SellerSearchItemRepository {

    private static final Logger logger = LoggerFactory.getLogger(SellerSearchItemRepository.class);

    /**
     * Private constructor to prevent instantiation.
     * This is a utility class with static methods only.
     */
    private SellerSearchItemRepository() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * Save seller search item references to the database.
     * This method saves only the item IDs and status for a specific seller search.
     *
     * @param sellerSearchId The seller search ID
     * @param itemIds        The list of item IDs to save
     * @param status         The status ID for all items
     * @return The number of items successfully saved
     */
    public static int saveSellerSearchItemReferences(int sellerSearchId, List<String> itemIds, int status) {
        if (itemIds == null || itemIds.isEmpty()) {
            logger.info("seller_search_items テーブルに保存するアイテムIDがありません");
            return 0;
        }

        String sql = "INSERT INTO seller_search_items (seller_search_id, item_id, status) VALUES (?, ?, ?)";
        int savedCount = 0;

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            for (String itemId : itemIds) {
                try {
                    pstmt.setInt(1, sellerSearchId);
                    pstmt.setString(2, itemId);
                    pstmt.setInt(3, status);

                    int affectedRows = pstmt.executeUpdate();
                    if (affectedRows > 0) {
                        savedCount++;
                    }

                } catch (SQLException e) {
                    logger.error("Error saving seller search item reference {}: {}", itemId, e.getMessage(), e);
                }
            }

        } catch (SQLException e) {
            logger.error("Error saving seller search item references: {}", e.getMessage(), e);
        }

        logger.info("seller_search_items テーブルに {} 件のアイテム参照を正常に保存しました", savedCount);
        return savedCount;
    }

    /**
     * Get seller search items with pagination.
     *
     * @param page     The page number (1-based)
     * @param pageSize The number of records per page
     * @return A list of seller search item models
     */
    public static List<SellerSearchItemModel> getSellerSearchItems(int page, int pageSize) {
        List<SellerSearchItemModel> items = new ArrayList<>();

        String sql = "SELECT * FROM seller_search_items ORDER BY id DESC LIMIT ? OFFSET ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, pageSize);
            pstmt.setInt(2, (page - 1) * pageSize);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    SellerSearchItemModel item = new SellerSearchItemModel();
                    item.setId(rs.getInt("id"));
                    item.setSellerSearchId(rs.getInt("seller_search_id"));
                    item.setItemId(rs.getString("item_id"));
                    item.setStatus(rs.getInt("status"));
                    items.add(item);
                }
            }

        } catch (SQLException e) {
            logger.error("Error retrieving seller search items: {}", e.getMessage(), e);
        }

        return items;
    }

    /**
     * Get seller search items by seller search ID.
     *
     * @param sellerSearchId The seller search ID
     * @return A list of seller search item models
     */
    public static List<SellerSearchItemModel> getSellerSearchItemsBySellerSearchId(int sellerSearchId) {
        List<SellerSearchItemModel> items = new ArrayList<>();

        String sql = "SELECT * FROM seller_search_items WHERE seller_search_id = ? ORDER BY id DESC";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, sellerSearchId);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    SellerSearchItemModel item = new SellerSearchItemModel();
                    item.setId(rs.getInt("id"));
                    item.setSellerSearchId(rs.getInt("seller_search_id"));
                    item.setItemId(rs.getString("item_id"));
                    item.setStatus(rs.getInt("status"));
                    items.add(item);
                }
            }

        } catch (SQLException e) {
            logger.error("Error retrieving seller search items by seller search ID: {}", e.getMessage(), e);
        }

        return items;
    }

    /**
     * Get the total count of seller search items.
     *
     * @return The total count of seller search items
     */
    public static int getSellerSearchItemCount() {
        String sql = "SELECT COUNT(*) FROM seller_search_items";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            if (rs.next()) {
                return rs.getInt(1);
            }

        } catch (SQLException e) {
            logger.error("Error getting seller search item count: {}", e.getMessage(), e);
        }

        return 0;
    }

    /**
     * Get the count of seller search items by seller search ID.
     *
     * @param sellerSearchId The seller search ID
     * @return The count of seller search items
     */
    public static int getSellerSearchItemCountBySellerSearchId(int sellerSearchId) {
        String sql = "SELECT COUNT(*) FROM seller_search_items WHERE seller_search_id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, sellerSearchId);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }

        } catch (SQLException e) {
            logger.error("Error getting seller search item count by seller search ID: {}", e.getMessage(), e);
        }

        return 0;
    }

    /**
     * Delete a seller search item reference by item ID.
     *
     * @param itemId The item ID to delete
     * @return true if the deletion was successful, false otherwise
     */
    public static boolean deleteSellerSearchItem(String itemId) {
        String sql = "DELETE FROM seller_search_items WHERE item_id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, itemId);

            int affectedRows = pstmt.executeUpdate();
            logger.debug("アイテムID: {} のセラー検索アイテム参照を {} 件削除しました", affectedRows, itemId);
            return affectedRows > 0;

        } catch (SQLException e) {
            logger.error("Error deleting seller search item: {}", e.getMessage(), e);
        }

        return false;
    }

    /**
     * Delete all seller search item references by seller search ID.
     *
     * @param sellerSearchId The seller search ID
     * @return true if the deletion was successful, false otherwise
     */
    public static boolean deleteSellerSearchItemsBySellerSearchId(int sellerSearchId) {
        String sql = "DELETE FROM seller_search_items WHERE seller_search_id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, sellerSearchId);

            int affectedRows = pstmt.executeUpdate();
            logger.debug("セラー検索ID: {} のセラー検索アイテム参照を {} 件削除しました", affectedRows, sellerSearchId);
            return affectedRows > 0;

        } catch (SQLException e) {
            logger.error("Error deleting seller search items by seller search ID: {}", e.getMessage(), e);
        }

        return false;
    }

    /**
     * Update the status of a seller search item.
     *
     * @param itemId The item ID
     * @param status The new status
     * @return true if the update was successful, false otherwise
     */
    public static boolean updateSellerSearchItemStatus(String itemId, int status) {
        String sql = "UPDATE seller_search_items SET status = ? WHERE item_id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, status);
            pstmt.setString(2, itemId);

            int affectedRows = pstmt.executeUpdate();
            logger.info("{} 件のセラー検索アイテムステータスレコードを更新しました", affectedRows);
            return affectedRows > 0;

        } catch (SQLException e) {
            logger.error("Error updating seller search item status: {}", e.getMessage(), e);
        }

        return false;
    }
}