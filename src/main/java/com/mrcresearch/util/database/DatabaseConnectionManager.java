package com.mrcresearch.util.database;

import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.logging.Logger;

/**
 * Manages database connections for the application.
 * Provides centralized connection management and configuration.
 *
 * Note: This class has been updated to use HikariCP connection pooling for better performance.
 * For testing purposes, it can still fall back to direct JDBC connections.
 */
public class DatabaseConnectionManager {
    private static final Logger logger = Logger.getLogger(DatabaseConnectionManager.class.getName());
    private static final String DB_URL = initializeDatabasePath();
    private static String testDatabasePath = null;
    private static volatile boolean poolingEnabled = true;

    /**
     * Initialize the database path in the user's home directory.
     * Creates the directory if it doesn't exist.
     *
     * @return The database URL string
     */
    private static String initializeDatabasePath() {
        String userHome = System.getProperty("user.home");
        String separator = System.getProperty("file.separator");
        String dbDir = userHome + separator + ".mrcresearch";
        String dbPath = dbDir + separator + "research.db";

        // Create directory if it doesn't exist
        File directory = new File(dbDir);
        if (!directory.exists()) {
            directory.mkdirs();
        }

        return "jdbc:sqlite:" + dbPath;
    }

    /**
     * Private constructor to prevent instantiation.
     * This is a utility class with static methods only.
     */
    private DatabaseConnectionManager() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * Get a database connection.
     * Uses HikariCP connection pool when available, falls back to direct connection for testing.
     *
     * @return A database connection
     * @throws SQLException if a database access error occurs
     */
    public static Connection getConnection() throws SQLException {
        // テストモード用の直接接続
        if (testDatabasePath != null) {
            logger.fine("テストモード: 直接データベース接続を使用します - " + testDatabasePath);
            return DriverManager.getConnection(testDatabasePath);
        }

        // プール無効化モードの場合は直接接続
        if (!poolingEnabled) {
            logger.fine("プール無効化モード: 直接データベース接続を使用します");
            return DriverManager.getConnection(DB_URL);
        }

        // HikariCPコネクションプールを使用
        try {
            // プールが初期化されていない場合は自動初期化
            if (!DatabaseConnectionPool.isInitialized()) {
                logger.info("コネクションプールを自動初期化します");
                DatabaseConnectionPool.initializeDefault();
            }

            // プール監視（定期的なヘルスチェック）
            DatabaseConnectionPool.monitorPoolHealth();

            return DatabaseConnectionPool.getConnection();

        } catch (Exception e) {
            logger.warning("コネクションプールからの接続取得に失敗しました。直接接続にフォールバックします: " + e.getMessage());

            // フォールバック: 直接JDBC接続
            return DriverManager.getConnection(DB_URL);
        }
    }

    /**
     * Get the database URL.
     *
     * @return The database URL string
     */
    public static String getDbUrl() {
        return testDatabasePath != null ? testDatabasePath : DB_URL;
    }

    /**
     * Test if the database connection is available.
     *
     * @return true if connection is successful, false otherwise
     */
    public static boolean testConnection() {
        try (Connection conn = getConnection()) {
            boolean isValid = conn != null && !conn.isClosed() && conn.isValid(5);

            if (isValid) {
                logger.fine("データベース接続テスト成功");
            } else {
                logger.warning("データベース接続テスト失敗: 接続が無効です");
            }

            return isValid;
        } catch (SQLException e) {
            logger.severe("データベース接続テスト失敗: " + e.getMessage());
            return false;
        }
    }

    /**
     * Set a test database path for testing purposes.
     * When set, connection pooling is bypassed for direct JDBC connections.
     *
     * @param testPath The test database path
     */
    public static void setTestDatabasePath(String testPath) {
        testDatabasePath = testPath;
        logger.info("テストデータベースパスを設定しました: " + testPath);
    }

    /**
     * Reset the test database path to use the default database.
     */
    public static void resetTestDatabasePath() {
        testDatabasePath = null;
        logger.info("テストデータベースパスをリセットしました。デフォルトデータベースを使用します。");
    }

    /**
     * Enable or disable connection pooling.
     * When disabled, direct JDBC connections will be used.
     *
     * @param enabled true to enable connection pooling, false to disable
     */
    public static void setPoolingEnabled(boolean enabled) {
        poolingEnabled = enabled;
        logger.info("コネクションプーリング " + (enabled ? "有効" : "無効") + " に設定しました");
    }

    /**
     * Check if connection pooling is enabled.
     *
     * @return true if pooling is enabled
     */
    public static boolean isPoolingEnabled() {
        return poolingEnabled;
    }

    /**
     * Get connection pool status information.
     *
     * @return Pool status string, or information about current connection mode
     */
    public static String getConnectionStatus() {
        if (testDatabasePath != null) {
            return "テストモード: 直接接続 - " + testDatabasePath;
        }

        if (!poolingEnabled) {
            return "プール無効化モード: 直接接続 - " + DB_URL;
        }

        if (!DatabaseConnectionPool.isInitialized()) {
            return "コネクションプール未初期化";
        }

        return DatabaseConnectionPool.getPoolStatus();
    }

    /**
     * Initialize connection pool manually.
     * This is automatically called when needed, but can be called explicitly for early initialization.
     */
    public static void initializeConnectionPool() {
        if (testDatabasePath != null) {
            logger.info("テストモードのため、コネクションプール初期化をスキップします");
            return;
        }

        if (!poolingEnabled) {
            logger.info("プール無効化モードのため、コネクションプール初期化をスキップします");
            return;
        }

        try {
            DatabaseConnectionPool.initializeDefault();
            logger.info("コネクションプールの手動初期化が完了しました");
        } catch (Exception e) {
            logger.severe("コネクションプールの手動初期化に失敗しました: " + e.getMessage());
            throw new RuntimeException("Connection pool initialization failed", e);
        }
    }

    /**
     * Shutdown connection pool gracefully.
     * This is automatically called on JVM shutdown, but can be called explicitly for controlled shutdown.
     */
    public static void shutdownConnectionPool() {
        try {
            DatabaseConnectionPool.shutdown();
            logger.info("コネクションプールのシャットダウンが完了しました");
        } catch (Exception e) {
            logger.severe("コネクションプールのシャットダウン中にエラーが発生しました: " + e.getMessage());
        }
    }
}
