package com.mrcresearch.util.database;

import java.sql.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.logging.Logger;

/**
 * クエリ実行時間の監視とパフォーマンス分析を行うユーティリティクラス
 * <p>
 * 主な機能:
 * - クエリ実行時間の計測
 * - スロークエリの検出（100ms以上）
 * - 統計情報の収集と出力
 * - クエリ最適化の推奨事項提示
 */
public class QueryPerformanceMonitor {
    private static final Logger logger = Logger.getLogger(QueryPerformanceMonitor.class.getName());
    private static final long SLOW_QUERY_THRESHOLD_MS = 100;

    // クエリ統計情報の保存
    private static final ConcurrentHashMap<String, QueryStats> queryStatsMap = new ConcurrentHashMap<>();

    /**
     * プライベートコンストラクタ（ユーティリティクラスのため）
     */
    private QueryPerformanceMonitor() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * クエリ統計情報を格納するクラス
     */
    private static class QueryStats {
        private final AtomicLong totalExecutions = new AtomicLong(0);
        private final AtomicLong totalExecutionTime = new AtomicLong(0);
        private final AtomicLong slowQueryCount = new AtomicLong(0);
        private volatile long maxExecutionTime = 0;
        private volatile long minExecutionTime = Long.MAX_VALUE;

        void addExecution(long executionTime) {
            totalExecutions.incrementAndGet();
            totalExecutionTime.addAndGet(executionTime);

            if (executionTime >= SLOW_QUERY_THRESHOLD_MS) {
                slowQueryCount.incrementAndGet();
            }

            synchronized (this) {
                if (executionTime > maxExecutionTime) {
                    maxExecutionTime = executionTime;
                }
                if (executionTime < minExecutionTime) {
                    minExecutionTime = executionTime;
                }
            }
        }

        long getAverageExecutionTime() {
            long executions = totalExecutions.get();
            return executions > 0 ? totalExecutionTime.get() / executions : 0;
        }
    }

    /**
     * PreparedStatementの実行時間を計測し、統計情報を記録する
     *
     * @param stmt        実行するPreparedStatement
     * @param queryType   クエリの種類（SELECT、INSERT等）
     * @param description クエリの説明
     * @return ResultSet（SELECT文の場合）またはnull
     * @throws SQLException SQL実行エラー
     */
    public static ResultSet executeQueryWithMonitoring(PreparedStatement stmt, String queryType, String description) throws SQLException {
        long startTime = System.currentTimeMillis();
        ResultSet result = null;

        try {
            if ("SELECT".equalsIgnoreCase(queryType)) {
                result = stmt.executeQuery();
            } else {
                stmt.executeUpdate();
            }

            return result;
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            recordQueryExecution(description, executionTime);

            if (executionTime >= SLOW_QUERY_THRESHOLD_MS) {
                logger.warning(String.format(
                        "スロークエリ検出: %s - 実行時間: %dms - 説明: %s",
                        queryType, executionTime, description
                ));
            }
        }
    }

    /**
     * Statementの実行時間を計測し、統計情報を記録する
     *
     * @param stmt        実行するStatement
     * @param sql         実行するSQL文
     * @param queryType   クエリの種類（SELECT、INSERT等）
     * @param description クエリの説明
     * @return ResultSet（SELECT文の場合）またはnull
     * @throws SQLException SQL実行エラー
     */
    public static ResultSet executeQueryWithMonitoring(Statement stmt, String sql, String queryType, String description) throws SQLException {
        long startTime = System.currentTimeMillis();
        ResultSet result = null;

        try {
            if ("SELECT".equalsIgnoreCase(queryType)) {
                result = stmt.executeQuery(sql);
            } else {
                stmt.execute(sql);
            }

            return result;
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            recordQueryExecution(description, executionTime);

            if (executionTime >= SLOW_QUERY_THRESHOLD_MS) {
                logger.warning(String.format(
                        "スロークエリ検出: %s - 実行時間: %dms - SQL: %s",
                        queryType, executionTime, sql.substring(0, Math.min(sql.length(), 100)) + "..."
                ));
            }
        }
    }

    /**
     * クエリ実行統計を記録する
     *
     * @param queryDescription クエリの説明
     * @param executionTime    実行時間（ミリ秒）
     */
    private static void recordQueryExecution(String queryDescription, long executionTime) {
        String key = normalizeQueryDescription(queryDescription);
        QueryStats stats = queryStatsMap.computeIfAbsent(key, k -> new QueryStats());
        stats.addExecution(executionTime);
    }

    /**
     * クエリの説明を正規化する（統計収集のため）
     *
     * @param description 元の説明
     * @return 正規化された説明
     */
    private static String normalizeQueryDescription(String description) {
        if (description == null) return "Unknown Query";

        // パラメータや具体的な値を除去して、クエリパターンのみ残す
        return description.replaceAll("\\d+", "?")
                .replaceAll("'[^']*'", "'?'")
                .replaceAll("\"[^\"]*\"", "\"?\"");
    }

    /**
     * パフォーマンス統計レポートを出力する
     */
    public static void printPerformanceReport() {
        logger.info("=== データベースクエリ パフォーマンスレポート ===");

        if (queryStatsMap.isEmpty()) {
            logger.info("統計データがありません");
            return;
        }

        queryStatsMap.forEach((queryDesc, stats) -> {
            logger.info(String.format(
                    "クエリ: %s\n" +
                            "  総実行回数: %d\n" +
                            "  平均実行時間: %dms\n" +
                            "  最大実行時間: %dms\n" +
                            "  最小実行時間: %dms\n" +
                            "  スロークエリ回数: %d\n",
                    queryDesc,
                    stats.totalExecutions.get(),
                    stats.getAverageExecutionTime(),
                    stats.maxExecutionTime,
                    stats.minExecutionTime == Long.MAX_VALUE ? 0 : stats.minExecutionTime,
                    stats.slowQueryCount.get()
            ));
        });

        // 最適化推奨事項
        printOptimizationRecommendations();
    }

    /**
     * 最適化推奨事項を出力する
     */
    private static void printOptimizationRecommendations() {
        logger.info("=== クエリ最適化推奨事項 ===");

        queryStatsMap.forEach((queryDesc, stats) -> {
            if (stats.slowQueryCount.get() > 0) {
                logger.info(String.format(
                        "要最適化: %s - スロークエリ発生率: %.2f%%",
                        queryDesc,
                        (double) stats.slowQueryCount.get() / stats.totalExecutions.get() * 100
                ));

                // 具体的な推奨事項
                if (queryDesc.contains("SELECT") && queryDesc.contains("WHERE")) {
                    logger.info("  推奨: WHERE句で使用されるカラムにインデックスを追加してください");
                }
                if (queryDesc.contains("ORDER BY")) {
                    logger.info("  推奨: ORDER BY句で使用されるカラムにインデックスを追加してください");
                }
                if (queryDesc.contains("COUNT(*)")) {
                    logger.info("  推奨: COUNT()クエリの最適化を検討してください");
                }
            }
        });
    }

    /**
     * 統計情報をクリアする
     */
    public static void clearStatistics() {
        queryStatsMap.clear();
        logger.info("クエリ統計情報をクリアしました");
    }

    /**
     * 現在の統計情報サマリーを取得する
     *
     * @return 統計サマリー文字列
     */
    public static String getStatisticsSummary() {
        if (queryStatsMap.isEmpty()) {
            return "統計データなし";
        }

        long totalQueries = queryStatsMap.values().stream()
                .mapToLong(stats -> stats.totalExecutions.get())
                .sum();

        long totalSlowQueries = queryStatsMap.values().stream()
                .mapToLong(stats -> stats.slowQueryCount.get())
                .sum();

        double avgExecutionTime = queryStatsMap.values().stream()
                .mapToLong(QueryStats::getAverageExecutionTime)
                .average()
                .orElse(0.0);

        return String.format(
                "総クエリ数: %d, スロークエリ数: %d (%.2f%%), 平均実行時間: %.2fms",
                totalQueries,
                totalSlowQueries,
                totalQueries > 0 ? (double) totalSlowQueries / totalQueries * 100 : 0,
                avgExecutionTime
        );
    }
}