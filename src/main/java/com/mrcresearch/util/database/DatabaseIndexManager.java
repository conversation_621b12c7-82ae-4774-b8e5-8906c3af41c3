package com.mrcresearch.util.database;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

/**
 * データベースインデックスの管理と最適化を行うクラス
 * <p>
 * 主な機能:
 * - 重要なインデックスの作成
 * - 既存インデックスの確認
 * - インデックスの最適化推奨事項
 * - パフォーマンス向上のための複合インデックス管理
 */
public class DatabaseIndexManager {
    private static final Logger logger = Logger.getLogger(DatabaseIndexManager.class.getName());

    /**
     * プライベートコンストラクタ（ユーティリティクラスのため）
     */
    private DatabaseIndexManager() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * インデックス情報を格納するクラス
     */
    private static class IndexInfo {
        final String tableName;
        final String indexName;
        final String[] columns;
        final String description;
        final boolean unique;

        IndexInfo(String tableName, String indexName, String[] columns, String description, boolean unique) {
            this.tableName = tableName;
            this.indexName = indexName;
            this.columns = columns;
            this.description = description;
            this.unique = unique;
        }

        String getCreateSQL() {
            StringBuilder sql = new StringBuilder();
            sql.append("CREATE ");
            if (unique) {
                sql.append("UNIQUE ");
            }
            sql.append("INDEX IF NOT EXISTS ").append(indexName);
            sql.append(" ON ").append(tableName);
            sql.append("(").append(String.join(", ", columns)).append(")");
            return sql.toString();
        }
    }

    /**
     * パフォーマンス向上のための重要なインデックスを全て作成する
     */
    public static void createOptimizedIndexes() {
        List<IndexInfo> indexes = getOptimizedIndexList();

        try (Connection conn = DatabaseConnectionManager.getConnection()) {
            logger.info("データベースインデックス最適化を開始します...");

            int createdCount = 0;
            int existingCount = 0;

            for (IndexInfo index : indexes) {
                if (createIndexIfNotExists(conn, index)) {
                    createdCount++;
                } else {
                    existingCount++;
                }
            }

            logger.info(String.format(
                    "インデックス最適化完了: 新規作成=%d, 既存=%d, 総数=%d",
                    createdCount, existingCount, indexes.size()
            ));

            // インデックス作成後の統計情報更新（SQLiteの場合）
            analyzeDatabase(conn);

        } catch (SQLException e) {
            logger.severe("インデックス作成中にエラーが発生しました: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 最適化インデックスのリストを取得する
     */
    private static List<IndexInfo> getOptimizedIndexList() {
        List<IndexInfo> indexes = new ArrayList<>();

        // keyword_search_results テーブル
        indexes.add(new IndexInfo(
                "keyword_search_results", "idx_keyword_search_keyword",
                new String[]{"keyword"},
                "キーワード検索の高速化", false
        ));
        indexes.add(new IndexInfo(
                "keyword_search_results", "idx_keyword_search_updated_date",
                new String[]{"updated_date"},
                "更新日時での並び替え高速化", false
        ));
        indexes.add(new IndexInfo(
                "keyword_search_results", "idx_keyword_search_status",
                new String[]{"research_status"},
                "リサーチステータス検索の高速化", false
        ));
        indexes.add(new IndexInfo(
                "keyword_search_results", "idx_keyword_search_composite",
                new String[]{"research_status", "updated_date DESC"},
                "ステータス別更新日時検索の高速化", false
        ));

        // search_items テーブル
        indexes.add(new IndexInfo(
                "search_items", "idx_search_items_seller_id",
                new String[]{"seller_id"},
                "出品者別商品検索の高速化", false
        ));
        indexes.add(new IndexInfo(
                "search_items", "idx_search_items_status",
                new String[]{"status"},
                "商品ステータス検索の高速化", false
        ));
        indexes.add(new IndexInfo(
                "search_items", "idx_search_items_updated_date",
                new String[]{"updated_date"},
                "更新日時での並び替え高速化", false
        ));
        indexes.add(new IndexInfo(
                "search_items", "idx_search_items_price",
                new String[]{"price"},
                "価格範囲検索の高速化", false
        ));
        indexes.add(new IndexInfo(
                "search_items", "idx_search_items_listing_date",
                new String[]{"listing_date"},
                "出品日検索の高速化", false
        ));
        indexes.add(new IndexInfo(
                "search_items", "idx_search_items_category",
                new String[]{"category"},
                "カテゴリ別検索の高速化", false
        ));
        indexes.add(new IndexInfo(
                "search_items", "idx_search_items_composite_seller_status",
                new String[]{"seller_id", "status", "updated_date DESC"},
                "出品者別ステータス検索の高速化", false
        ));

        // favorite_sellers テーブル
        indexes.add(new IndexInfo(
                "favorite_sellers", "idx_favorite_sellers_group",
                new String[]{"group_name"},
                "グループ別検索の高速化", false
        ));
        indexes.add(new IndexInfo(
                "favorite_sellers", "idx_favorite_sellers_tags",
                new String[]{"tags"},
                "タグ検索の高速化", false
        ));
        indexes.add(new IndexInfo(
                "favorite_sellers", "idx_favorite_sellers_updated",
                new String[]{"updated_date DESC"},
                "更新日時での並び替え高速化", false
        ));
        indexes.add(new IndexInfo(
                "favorite_sellers", "idx_favorite_sellers_seller_id",
                new String[]{"seller_id"},
                "出品者ID検索の高速化", true  // ユニークインデックス
        ));

        // sellers テーブル（既にDatabaseSchemaManagerで一部作成済み）
        indexes.add(new IndexInfo(
                "sellers", "idx_sellers_research_status",
                new String[]{"research_status"},
                "リサーチステータス検索の高速化", false
        ));
        indexes.add(new IndexInfo(
                "sellers", "idx_sellers_composite_status_updated",
                new String[]{"research_status", "updated_at DESC"},
                "ステータス別更新日時検索の高速化", false
        ));

        // keyword_search_items テーブル
        indexes.add(new IndexInfo(
                "keyword_search_items", "idx_keyword_search_items_status",
                new String[]{"status"},
                "アイテムステータス検索の高速化", false
        ));
        indexes.add(new IndexInfo(
                "keyword_search_items", "idx_keyword_search_items_item_id",
                new String[]{"item_id"},
                "商品ID検索の高速化", false
        ));

        // seller_search_items テーブル
        indexes.add(new IndexInfo(
                "seller_search_items", "idx_seller_search_items_seller_id",
                new String[]{"seller_search_id"},
                "出品者検索ID検索の高速化", false
        ));
        indexes.add(new IndexInfo(
                "seller_search_items", "idx_seller_search_items_item_id",
                new String[]{"item_id"},
                "商品ID検索の高速化", false
        ));

        // seller_searches テーブル
        indexes.add(new IndexInfo(
                "seller_searches", "idx_seller_searches_seller_id",
                new String[]{"seller_id"},
                "出品者ID検索の高速化", false
        ));
        indexes.add(new IndexInfo(
                "seller_searches", "idx_seller_searches_researched_at",
                new String[]{"last_researched_at DESC"},
                "最終リサーチ日時での並び替え高速化", false
        ));

        return indexes;
    }

    /**
     * インデックスが存在しない場合のみ作成する
     */
    private static boolean createIndexIfNotExists(Connection conn, IndexInfo index) throws SQLException {
        // インデックスの存在確認
        if (indexExists(conn, index.indexName)) {
            logger.fine("インデックスは既に存在します: " + index.indexName);
            return false;
        }

        // インデックス作成
        try (Statement stmt = conn.createStatement()) {
            String createSQL = index.getCreateSQL();

            long startTime = System.currentTimeMillis();
            stmt.execute(createSQL);
            long executionTime = System.currentTimeMillis() - startTime;

            logger.info(String.format(
                    "インデックス作成完了: %s (%dms) - %s",
                    index.indexName, executionTime, index.description
            ));

            return true;
        }
    }

    /**
     * 指定されたインデックスが存在するかチェックする
     */
    private static boolean indexExists(Connection conn, String indexName) throws SQLException {
        String sql = "SELECT name FROM sqlite_master WHERE type='index' AND name=?";

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, indexName);
            try (ResultSet rs = stmt.executeQuery()) {
                return rs.next();
            }
        }
    }

    /**
     * データベースの統計情報を更新する（SQLite ANALYZE コマンド）
     */
    private static void analyzeDatabase(Connection conn) throws SQLException {
        logger.info("データベース統計情報を更新中...");

        try (Statement stmt = conn.createStatement()) {
            long startTime = System.currentTimeMillis();
            stmt.execute("ANALYZE");
            long executionTime = System.currentTimeMillis() - startTime;

            logger.info("データベース統計情報の更新完了 (" + executionTime + "ms)");
        }
    }

    /**
     * 既存のインデックス情報をレポートする
     */
    public static void reportExistingIndexes() {
        try (Connection conn = DatabaseConnectionManager.getConnection()) {
            logger.info("=== 既存インデックス情報 ===");

            String sql = "SELECT name, sql FROM sqlite_master WHERE type='index' AND sql IS NOT NULL ORDER BY name";

            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {

                int count = 0;
                while (rs.next()) {
                    String indexName = rs.getString("name");
                    String indexSQL = rs.getString("sql");

                    logger.info(String.format("インデックス: %s", indexName));
                    logger.info(String.format("  SQL: %s", indexSQL));
                    count++;
                }

                logger.info("総インデックス数: " + count);
            }

        } catch (SQLException e) {
            logger.severe("インデックス情報の取得中にエラーが発生しました: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * インデックスの使用状況を分析する（EXPLAIN QUERY PLANを使用）
     */
    public static void analyzeQueryPlan(String sql, String description) {
        try (Connection conn = DatabaseConnectionManager.getConnection()) {
            String explainSQL = "EXPLAIN QUERY PLAN " + sql;

            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(explainSQL)) {

                logger.info("=== クエリ実行計画分析: " + description + " ===");
                logger.info("SQL: " + sql);

                while (rs.next()) {
                    // SQLite EXPLAIN QUERY PLANの結果
                    int id = rs.getInt("id");
                    int parent = rs.getInt("parent");
                    int notused = rs.getInt("notused");
                    String detail = rs.getString("detail");

                    logger.info(String.format("  %d|%d|%d|%s", id, parent, notused, detail));

                    // インデックス使用の確認
                    if (detail.contains("USING INDEX")) {
                        logger.info("  ✓ インデックスが使用されています");
                    } else if (detail.contains("SCAN TABLE")) {
                        logger.warning("  ⚠ テーブルスキャンが発生しています - インデックス最適化を検討してください");
                    }
                }

            }

        } catch (SQLException e) {
            logger.warning("クエリプラン分析中にエラーが発生しました: " + e.getMessage());
        }
    }

    /**
     * よく使用されるクエリの実行計画を一括分析する
     */
    public static void analyzeCommonQueries() {
        logger.info("=== よく使用されるクエリの実行計画分析 ===");

        // よく使用されるクエリパターンを分析
        String[] commonQueries = {
                "SELECT * FROM favorite_sellers ORDER BY updated_date DESC LIMIT 20",
                "SELECT * FROM keyword_search_results WHERE research_status = 'completed' ORDER BY updated_date DESC",
                "SELECT * FROM search_items WHERE seller_id = 'test123' AND status = 3",
                "SELECT COUNT(*) FROM search_items WHERE status = 3 AND updated_date >= date('now', '-30 days')",
                "SELECT seller_id, COUNT(*) FROM search_items GROUP BY seller_id HAVING COUNT(*) > 10",
                "SELECT * FROM sellers WHERE research_status = 'waiting' ORDER BY updated_at DESC"
        };

        String[] descriptions = {
                "お気に入り出品者一覧取得",
                "完了済みキーワード検索結果取得",
                "特定出品者の取引完了商品検索",
                "過去30日の売り上げ商品数カウント",
                "活発な出品者の特定",
                "待機中出品者の取得"
        };

        for (int i = 0; i < commonQueries.length; i++) {
            analyzeQueryPlan(commonQueries[i], descriptions[i]);
        }
    }
}