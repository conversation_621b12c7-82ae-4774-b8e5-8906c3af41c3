package com.mrcresearch.util.database;

import com.mrcresearch.screen.component.category.Categories;
import com.mrcresearch.screen.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Utility class for database operations.
 * Handles SQLite database connection and CRUD operations for keyword search results.
 * <p>
 * This class serves as a facade to maintain backward compatibility while delegating
 * operations to specialized repository classes for better code organization.
 */
public class DatabaseUtil {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseUtil.class);
    private static final String DB_URL = DatabaseConnectionManager.getDbUrl();
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final int CURRENT_DB_VERSION = 2;

    // Japanese timezone for timestamp conversion
    private static final ZoneId JAPAN_TIMEZONE = ZoneId.of("Asia/Tokyo");

    /**
     * Convert timestamp string to Japanese timezone Date object.
     * Handles various timestamp formats that might come from the Mercari API.
     *
     * @param timestampStr The timestamp string to convert (e.g., "1234567890", "2023-12-01T10:30:00Z")
     * @return Date object in Japanese timezone, or null if conversion fails
     * @deprecated Use DatabaseDateTimeUtil.convertToJapaneseTimezone() instead
     */
    @Deprecated
    public static java.util.Date convertToJapaneseTimezone(String timestampStr) {
        return DatabaseDateTimeUtil.convertToJapaneseTimezone(timestampStr);
    }

    /**
     * Format a Date object to Japanese timezone string for database storage.
     *
     * @param date The Date object to format
     * @return Formatted date string in Japanese timezone, or null if date is null
     * @deprecated Use DatabaseDateTimeUtil.formatJapaneseTimezone() instead
     */
    @Deprecated
    public static String formatJapaneseTimezone(java.util.Date date) {
        return DatabaseDateTimeUtil.formatJapaneseTimezone(date);
    }

    /**
     * Check the current database version and apply migrations if needed.
     *
     * @deprecated Use DatabaseSchemaManager.checkAndMigrate() instead
     */
    @Deprecated
    public static void checkAndMigrate() {
        DatabaseSchemaManager.checkAndMigrate();
    }


    /**
     * Initialize the database by creating tables if they don't exist.
     *
     * @deprecated Use DatabaseSchemaManager.initDatabase() instead
     */
    @Deprecated
    public static void initDatabase() {
        DatabaseSchemaManager.initDatabase();
    }

    /**
     * Save a keyword search result to the database.
     *
     * @param model The keyword search result model to save
     * @return The ID of the saved record, or -1 if an error occurred
     * @deprecated Use KeywordSearchRepository.saveKeywordSearchResult() instead
     */
    @Deprecated
    public static int saveKeywordSearchResult(KeywordSearchResultModel model) {
        return KeywordSearchRepository.saveKeywordSearchResult(model);
    }

    /**
     * Save keyword search result items to the database.
     *
     * @param keywordSearchResultId The ID of the keyword search result
     * @param items                 The list of items to save
     * @return The number of items successfully saved
     * @deprecated Use KeywordSearchRepository.saveKeywordSearchItems() instead
     */
    @Deprecated
    public static int saveKeywordSearchItems(int keywordSearchResultId, List<com.mrcresearch.service.models.item.Item> items) {
        return KeywordSearchRepository.saveKeywordSearchItems(keywordSearchResultId, items);
    }

    /**
     * Get keyword search items with pagination for a specific search result.
     * Note: With the new table structure, this method now returns all search items regardless of keywordSearchResultId.
     *
     * @param keywordSearchResultId The ID of the keyword search result (ignored in new implementation)
     * @param page                  The page number (1-based)
     * @param pageSize              The number of records per page
     * @return A list of keyword search item models
     * @deprecated Use SearchItemRepository.getSearchItems() instead for complete item data
     */
    @Deprecated
    public static List<com.mrcresearch.screen.model.KeywordSearchItemModel> getKeywordSearchItems(int keywordSearchResultId, int page, int pageSize) {
        // Note: keywordSearchResultId is ignored as the new table structure doesn't support filtering by search result ID
        return com.mrcresearch.util.database.KeywordSearchItemRepository.getKeywordSearchItems(page, pageSize);
    }

    /**
     * Get the total count of keyword search items for a specific search result.
     * Note: With the new table structure, this method now returns the total count of all search items.
     *
     * @param keywordSearchResultId The ID of the keyword search result (ignored in new implementation)
     * @return The total count of items
     * @deprecated Use SearchItemRepository.getTotalSearchItemsCount() instead
     */
    @Deprecated
    public static int getTotalKeywordSearchItemsCount(int keywordSearchResultId) {
        // Note: keywordSearchResultId is ignored as the new table structure doesn't support filtering by search result ID
        return com.mrcresearch.util.database.KeywordSearchItemRepository.getTotalKeywordSearchItemsCount();
    }

    /**
     * Update an existing keyword search result in the database.
     *
     * @param model The keyword search result model to update
     * @return true if the update was successful, false otherwise
     * @deprecated Use KeywordSearchRepository.updateKeywordSearchResult() instead
     */
    @Deprecated
    public static boolean updateKeywordSearchResult(KeywordSearchResultModel model) {
        return KeywordSearchRepository.updateKeywordSearchResult(model);
    }

    /**
     * Get a list of keyword search results with pagination.
     *
     * @param page     The page number (1-based)
     * @param pageSize The number of records per page
     * @return A list of keyword search result models
     * @deprecated Use KeywordSearchRepository.getKeywordSearchResults() instead
     */
    @Deprecated
    public static List<KeywordSearchResultModel> getKeywordSearchResults(int page, int pageSize) {
        return KeywordSearchRepository.getKeywordSearchResults(page, pageSize);
    }

    /**
     * Get a list of product research results with pagination and optional name filter.
     *
     * @param page       The page number (1-based)
     * @param pageSize   The number of records per page
     * @param nameFilter Optional product name filter (null or empty for no filter)
     * @return A list of product research result models
     * @deprecated Use ProductResearchRepository.getProductResearchResults() instead
     */
    @Deprecated
    public static List<ProductResearchResultModel> getProductResearchResults(int page, int pageSize, String nameFilter) {
        return ProductResearchRepository.getProductResearchResults(page, pageSize, nameFilter);
    }

    /**
     * Get the total count of product research results with optional name filter.
     *
     * @param nameFilter Optional product name filter (null or empty for no filter)
     * @return The total count of matching records
     * @deprecated Use ProductResearchRepository.getProductResearchResultsCount() instead
     */
    @Deprecated
    public static int getProductResearchResultsCount(String nameFilter) {
        return ProductResearchRepository.getProductResearchResultsCount(nameFilter);
    }

    /**
     * Save settings to the database.
     * If settings already exist, they will be updated.
     *
     * @param soldDate            The sold date setting (in days)
     * @param browserLoadTimeout  The browser load timeout setting (in seconds)
     * @param browserSleepTime    The browser sleep time setting (in seconds)
     * @param browserType         The browser type setting (e.g., "Firefox", "Chrome")
     * @param defaultKeywordPages The default number of pages for keyword search
     * @param oldItemDisplayCount The number of old items to display
     * @return true if the save was successful, false otherwise
     * @deprecated Use SettingsRepository.saveSettings() instead
     */
    @Deprecated
    public static boolean saveSettings(int soldDate, int browserLoadTimeout, int browserSleepTime,
                                       String browserType, int defaultKeywordPages, int oldItemDisplayCount) {
        return SettingsRepository.saveSettings(soldDate, browserLoadTimeout, browserSleepTime, SettingsConstants.DEFAULT_BROWSER_INIT_SLEEP_TIME, browserType, defaultKeywordPages, oldItemDisplayCount, SettingsConstants.DEFAULT_THEME, SettingsConstants.DEFAULT_HEADLESS_MODE, SettingsConstants.DEFAULT_SALES_AGGREGATION_DAYS, "MEDIUM");
    }

    /**
     * Save settings to the database (backward compatibility method).
     * If settings already exist, they will be updated.
     *
     * @param soldDate            The sold date setting (in days)
     * @param browserLoadTimeout  The browser load timeout setting (in seconds)
     * @param browserSleepTime    The browser sleep time setting (in seconds)
     * @param browserType         The browser type setting (e.g., "Firefox", "Chrome")
     * @param defaultKeywordPages The default number of pages for keyword search
     * @return true if the save was successful, false otherwise
     * @deprecated Use SettingsRepository.saveSettings() instead
     */
    @Deprecated
    public static boolean saveSettings(int soldDate, int browserLoadTimeout, int browserSleepTime,
                                       String browserType, int defaultKeywordPages) {
        return SettingsRepository.saveSettings(soldDate, browserLoadTimeout, browserSleepTime, SettingsConstants.DEFAULT_BROWSER_INIT_SLEEP_TIME, browserType, defaultKeywordPages, SettingsConstants.DEFAULT_OLD_ITEM_DISPLAY_COUNT, SettingsConstants.DEFAULT_THEME, SettingsConstants.DEFAULT_HEADLESS_MODE, SettingsConstants.DEFAULT_SALES_AGGREGATION_DAYS, "MEDIUM");
    }

    /**
     * Save login information to the database.
     * If login information already exists, it will be updated.
     *
     * @param email    The email address
     * @param password The password (will be Base64 encoded before saving)
     * @return true if the save was successful, false otherwise
     */
    public static boolean saveLoginInfo(String email, String password) {
        try (Connection conn = DatabaseConnectionManager.getConnection()) {
            // Check if login info already exists
            boolean hasLoginInfo = false;
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM login_info")) {
                if (rs.next()) {
                    hasLoginInfo = rs.getInt(1) > 0;
                }
            }

            // Encode the password using Base64
            String encodedPassword = Base64.getEncoder().encodeToString(password.getBytes(java.nio.charset.StandardCharsets.UTF_8));

            String sql;
            if (hasLoginInfo) {
                // Update existing login info
                sql = "UPDATE login_info SET email = ?, password_hash = ?";
            } else {
                // Insert new login info
                sql = "INSERT INTO login_info (email, password_hash) VALUES (?, ?)";
            }

            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, email);
                pstmt.setString(2, encodedPassword);

                int affectedRows = pstmt.executeUpdate();
                return affectedRows > 0;
            }
        } catch (SQLException e) {
            logger.error("Error saving login info", e);
            return false;
        }
    }

    /**
     * Load login information from the database.
     *
     * @return An array containing [email, passwordHash] or null if no login info exists
     */
    public static String[] loadLoginInfo() {
        try (Connection conn = DatabaseConnectionManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT * FROM login_info LIMIT 1")) {

            if (rs.next()) {
                String email = rs.getString("email");
                String passwordHash = rs.getString("password_hash");

                return new String[]{email, passwordHash};
            }
        } catch (SQLException e) {
            logger.error("Error loading login info", e);
        }

        return null;
    }

    /**
     * Verify if the provided password matches the stored Base64-encoded password.
     *
     * @param password    The password to verify
     * @param storedValue The stored Base64-encoded password to compare against
     * @return true if the password matches, false otherwise
     */
    public static boolean verifyPassword(String password, String storedValue) {
        try {
            // Try to decode the stored value using Base64
            byte[] decodedBytes = Base64.getDecoder().decode(storedValue);
            String decodedPassword = new String(decodedBytes, java.nio.charset.StandardCharsets.UTF_8);

            // Compare with the provided password
            return password.equals(decodedPassword);
        } catch (Exception e) {
            // If Base64 decoding fails, fall back to hash comparison for backward compatibility
            return hashPassword(password).equals(storedValue);
        }
    }

    /**
     * Hash a password for storage.
     *
     * @param password The password to hash
     * @return The hashed password
     */
    private static String hashPassword(String password) {
        // In a real application, you would use a proper password hashing library
        // For simplicity, we're using a basic hash function here
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(password.getBytes(java.nio.charset.StandardCharsets.UTF_8));

            // Convert byte array to hex string
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (java.security.NoSuchAlgorithmException e) {
            logger.error("Error hashing password", e);
            // Fallback to a very basic hash if SHA-256 is not available
            return String.valueOf(password.hashCode());
        }
    }


    /**
     * Load settings from the database.
     *
     * @return An array of settings values in the order: [soldDate, browserLoadTimeout, browserSleepTime, browserType, defaultKeywordPages, oldItemDisplayCount, themeType]
     * or null if settings could not be loaded
     */
    public static Object[] loadSettings() {
        return SettingsRepository.loadSettings();
    }

    /**
     * Get the total number of keyword search results.
     *
     * @return The total number of records
     */
    public static int getTotalKeywordSearchResults() {
        String sql = "SELECT COUNT(*) FROM keyword_search_results";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            if (rs.next()) {
                return rs.getInt(1);
            }

        } catch (SQLException e) {
            logger.error("Error getting total keyword search results", e);
        }

        return 0;
    }

    /**
     * Delete a keyword search result from the database.
     *
     * @param id The ID of the record to delete
     * @return true if the deletion was successful, false otherwise
     */
    public static boolean deleteKeywordSearchResult(int id) {
        String sql = "DELETE FROM keyword_search_results WHERE id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, id);

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;

        } catch (SQLException e) {
            logger.error("Error deleting keyword search result", e);
            return false;
        }
    }

    /**
     * Parse a date string in the format "yyyy-MM-dd HH:mm:ss".
     *
     * @param dateStr The date string to parse
     * @return The parsed Date object, or null if parsing failed
     */
    private static java.util.Date parseDate(String dateStr) {
        try {
            return DATE_FORMAT.parse(dateStr);
        } catch (Exception e) {
            logger.error("Error parsing date", e);
            return null;
        }
    }

    /**
     * Save a favorite seller to the database.
     *
     * @param model The favorite seller model to save
     * @return The ID of the saved record, or -1 if an error occurred
     */
    public static int saveFavoriteSeller(FavoriteSellerModel model) {
        String sql = "INSERT INTO favorite_sellers (seller_name, seller_id, added_date, updated_date, research_status, tags, group_name) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?)";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

            pstmt.setString(1, model.getSellerName());
            pstmt.setString(2, model.getSellerId());
            pstmt.setString(3, DATE_FORMAT.format(new java.util.Date())); // Added date is now
            pstmt.setString(4, DATE_FORMAT.format(model.getLastUpdated()));
            pstmt.setString(5, model.getResearchStatus());
            pstmt.setString(6, model.getTags());
            pstmt.setString(7, model.getGroupName());

            int affectedRows = pstmt.executeUpdate();

            if (affectedRows > 0) {
                try (ResultSet rs = pstmt.getGeneratedKeys()) {
                    if (rs.next()) {
                        return rs.getInt(1);
                    }
                }
            }

        } catch (SQLException e) {
            logger.error("Error saving favorite seller", e);
        }

        return -1;
    }

    /**
     * Update an existing favorite seller in the database.
     *
     * @param model The favorite seller model to update
     * @return true if the update was successful, false otherwise
     */
    public static boolean updateFavoriteSeller(FavoriteSellerModel model) {
        String sql = "UPDATE favorite_sellers SET seller_name = ?, seller_id = ?, " +
                "updated_date = ?, research_status = ?, tags = ?, group_name = ? WHERE id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, model.getSellerName());
            pstmt.setString(2, model.getSellerId());
            pstmt.setString(3, DATE_FORMAT.format(model.getLastUpdated()));
            pstmt.setString(4, model.getResearchStatus());
            pstmt.setString(5, model.getTags());
            pstmt.setString(6, model.getGroupName());
            pstmt.setInt(7, model.getId());

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;

        } catch (SQLException e) {
            logger.error("Error updating favorite seller", e);
            return false;
        }
    }

    /**
     * Get a list of favorite sellers with pagination.
     *
     * @param page     The page number (1-based)
     * @param pageSize The number of records per page
     * @return A list of favorite seller models
     */
    public static List<FavoriteSellerModel> getFavoriteSellers(int page, int pageSize) {
        List<FavoriteSellerModel> results = new ArrayList<>();

        String sql = "SELECT * FROM favorite_sellers ORDER BY updated_date DESC LIMIT ? OFFSET ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, pageSize);
            pstmt.setInt(2, (page - 1) * pageSize);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    FavoriteSellerModel model = new FavoriteSellerModel(
                            rs.getInt("id"),
                            rs.getString("seller_name"),
                            rs.getString("seller_id"),
                            parseDate(rs.getString("updated_date")),
                            rs.getString("research_status"),
                            rs.getString("tags"),
                            rs.getString("group_name")
                    );

                    results.add(model);
                }
            }

        } catch (SQLException e) {
            logger.error("Error getting favorite sellers", e);
        }

        return results;
    }

    /**
     * Get a list of favorite sellers by group name.
     *
     * @param groupName The group name to filter by
     * @return A list of favorite seller models
     */
    public static List<FavoriteSellerModel> getFavoriteSellersByGroup(String groupName) {
        List<FavoriteSellerModel> results = new ArrayList<>();

        String sql = "SELECT * FROM favorite_sellers WHERE group_name = ? ORDER BY updated_date DESC";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, groupName);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    FavoriteSellerModel model = new FavoriteSellerModel(
                            rs.getInt("id"),
                            rs.getString("seller_name"),
                            rs.getString("seller_id"),
                            parseDate(rs.getString("updated_date")),
                            rs.getString("research_status"),
                            rs.getString("tags"),
                            rs.getString("group_name")
                    );

                    results.add(model);
                }
            }

        } catch (SQLException e) {
            logger.error("Error getting favorite sellers by group", e);
        }

        return results;
    }

    /**
     * Get a list of favorite sellers by tag.
     *
     * @param tag The tag to filter by
     * @return A list of favorite seller models
     */
    public static List<FavoriteSellerModel> getFavoriteSellersByTag(String tag) {
        List<FavoriteSellerModel> results = new ArrayList<>();

        String sql = "SELECT * FROM favorite_sellers WHERE tags LIKE ? ORDER BY updated_date DESC";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, "%" + tag + "%");

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    FavoriteSellerModel model = new FavoriteSellerModel(
                            rs.getInt("id"),
                            rs.getString("seller_name"),
                            rs.getString("seller_id"),
                            parseDate(rs.getString("updated_date")),
                            rs.getString("research_status"),
                            rs.getString("tags"),
                            rs.getString("group_name")
                    );

                    results.add(model);
                }
            }

        } catch (SQLException e) {
            logger.error("Error getting favorite sellers by tag", e);
        }

        return results;
    }

    /**
     * Get all unique group names.
     *
     * @return A list of group names
     */
    public static List<String> getAllGroups() {
        List<String> groups = new ArrayList<>();

        String sql = "SELECT DISTINCT group_name FROM favorite_sellers WHERE group_name IS NOT NULL AND group_name != ''";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                groups.add(rs.getString("group_name"));
            }

        } catch (SQLException e) {
            logger.error("Error getting all groups", e);
        }

        return groups;
    }

    /**
     * Get all unique tags.
     *
     * @return A list of tags
     */
    public static List<String> getAllTags() {
        List<String> allTags = new ArrayList<>();

        String sql = "SELECT tags FROM favorite_sellers WHERE tags IS NOT NULL AND tags != ''";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                String tags = rs.getString("tags");
                if (tags != null && !tags.isEmpty()) {
                    String[] tagArray = tags.split(",");
                    for (String tag : tagArray) {
                        tag = tag.trim();
                        if (!tag.isEmpty() && !allTags.contains(tag)) {
                            allTags.add(tag);
                        }
                    }
                }
            }

        } catch (SQLException e) {
            logger.error("Error getting all tags", e);
        }

        return allTags;
    }

    /**
     * Delete a favorite seller from the database.
     *
     * @param sellerId The seller ID to delete
     * @return true if the deletion was successful, false otherwise
     */
    public static boolean deleteFavoriteSeller(String sellerId) {
        String sql = "DELETE FROM favorite_sellers WHERE seller_id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, sellerId);

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;

        } catch (SQLException e) {
            logger.error("Error deleting favorite seller", e);
            return false;
        }
    }

    /**
     * Check if a seller is already in favorites.
     *
     * @param sellerId The seller ID to check
     * @return true if the seller is in favorites, false otherwise
     */
    public static boolean isFavoriteSeller(String sellerId) {
        String sql = "SELECT COUNT(*) FROM favorite_sellers WHERE seller_id = ?";
        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, sellerId);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        } catch (SQLException e) {
            logger.error("Error checking if seller is favorite", e);
        }
        return false;
    }

    /**
     * Get a favorite seller by their seller ID.
     *
     * @param sellerId The seller ID
     * @return The favorite seller model, or null if not found
     */
    public static FavoriteSellerModel getFavoriteSellerBySellerId(String sellerId) {
        String sql = "SELECT * FROM favorite_sellers WHERE seller_id = ?";
        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, sellerId);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return new FavoriteSellerModel(
                            rs.getInt("id"),
                            rs.getString("seller_name"),
                            rs.getString("seller_id"),
                            parseDate(rs.getString("updated_date")),
                            rs.getString("research_status"),
                            rs.getString("tags"),
                            rs.getString("group_name")
                    );
                }
            }
        } catch (SQLException e) {
            logger.error("Error getting favorite seller by seller id", e);
        }
        return null;
    }


    /**
     * Get a list of sellers with pagination.
     * Now uses the sellers table through SellerRepository.
     *
     * @param page     The page number (1-based)
     * @param pageSize The number of records per page
     * @return A list of seller models
     * @deprecated Use SellerRepository.getAllSellers() instead
     */
    @Deprecated
    public static List<SellerModel> getSellerResearchResults(int page, int pageSize) {
        return SellerRepository.getAllSellers(page, pageSize);
    }

    /**
     * Get the total number of sellers.
     * Now uses the sellers table through SellerRepository.
     *
     * @return The total number of records
     * @deprecated Use SellerRepository.getTotalSellerCount() instead
     */
    @Deprecated
    public static int getTotalSellerResearchResults() {
        return SellerRepository.getTotalSellerCount();
    }


    public static String getPassword() {
        try (Connection conn = DatabaseConnectionManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT password_hash FROM login_info LIMIT 1")) {

            if (rs.next()) {
                String passwordHash = rs.getString("password_hash");

                // Try to decode using Base64
                try {
                    byte[] decodedBytes = Base64.getDecoder().decode(passwordHash);
                    return new String(decodedBytes, java.nio.charset.StandardCharsets.UTF_8);
                } catch (Exception e) {
                    // If Base64 decoding fails, fall back to hex conversion for backward compatibility
                }

                // Fall back to hex conversion if both Base64 and AES fail
                try {
                    StringBuilder result = new StringBuilder();
                    for (int i = 0; i < passwordHash.length(); i += 2) {
                        String str = passwordHash.substring(i, i + 2);
                        result.append((char) Integer.parseInt(str, 16));
                    }
                    return result.toString();
                } catch (Exception e) {
                    // If hex conversion fails, return the hash as is
                    return passwordHash;
                }
            }
        } catch (SQLException e) {
            logger.error("Error getting password", e);
        }
        return null;
    }

    /**
     * Update the research status of a keyword search result by ID.
     *
     * @param id             The ID of the record to update
     * @param researchStatus The new research status
     * @return true if the update was successful, false otherwise
     */
    public static boolean updateKeywordSearchResultStatus(int id, String researchStatus) {
        String sql = "UPDATE keyword_search_results SET research_status = ?, updated_date = ? WHERE id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, researchStatus);
            pstmt.setString(2, DATE_FORMAT.format(new java.util.Date()));
            pstmt.setInt(3, id);

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;

        } catch (SQLException e) {
            logger.error("Error updating keyword search result status", e);
            return false;
        }
    }

    /**
     * Update seller information by seller ID.
     * Now uses the sellers table through SellerManagementService.
     *
     * @param sellerId   The seller ID to update
     * @param sellerName The new seller name
     * @return true if the update was successful, false otherwise
     * @deprecated Use SellerManagementService.updateSellerName() instead
     */
    @Deprecated
    public static boolean updateSellerResearchResultStatus(String sellerId, String sellerName) {
        return com.mrcresearch.service.SellerManagementService.updateSellerName(sellerId, sellerName);
    }

    /**
     * Update the seller name by seller ID.
     * Now uses the sellers table through SellerManagementService.
     *
     * @param sellerId   The seller ID to update
     * @param sellerName The new seller name
     * @return true if the update was successful, false otherwise
     * @deprecated Use SellerManagementService.updateSellerName() instead
     */
    @Deprecated
    public static boolean updateSellerResearchResultName(String sellerId, String sellerName) {
        return com.mrcresearch.service.SellerManagementService.updateSellerName(sellerId, sellerName);
    }

    /**
     * Update the seller name by seller ID.
     * Now uses the sellers table through SellerManagementService.
     *
     * @param sellerId   The seller ID to update
     * @param sellerName The new seller name
     * @return true if the update was successful, false otherwise
     * @deprecated Use SellerManagementService.updateSellerName() instead
     */
    @Deprecated
    public static boolean updateSellerResearchResultNameBySellerId(String sellerId, String sellerName) {
        return com.mrcresearch.service.SellerManagementService.updateSellerName(sellerId, sellerName);
    }


    /**
     * Get a keyword search result by ID.
     *
     * @param id The ID of the record to retrieve
     * @return The keyword search result model, or null if not found
     */
    public static KeywordSearchResultModel getKeywordSearchResultById(int id) {
        String sql = "SELECT * FROM keyword_search_results WHERE id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, id);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    KeywordSearchResultModel model = new KeywordSearchResultModel();
                    model.setId(rs.getInt("id"));
                    model.setKeyword(rs.getString("keyword"));
                    model.setCategories(rs.getString("categories"));
                    model.setAddedDate(parseDate(rs.getString("added_date")));
                    model.setUpdatedDate(parseDate(rs.getString("updated_date")));
                    model.setStatus(rs.getString("status"));
                    model.setCategoryId(rs.getString("category_id"));

                    // Handle research_status (may be null in existing records)
                    String researchStatus = rs.getString("research_status");
                    if (researchStatus == null) {
                        model.setResearchStatus(rs.getString("status"));
                    } else {
                        model.setResearchStatus(researchStatus);
                    }

                    // Get the search parameters
                    try {
                        model.setMinPrice(rs.getInt("min_price"));
                        model.setMaxPrice(rs.getInt("max_price"));
                        model.setPages(rs.getInt("pages"));
                        model.setSortByNew(rs.getInt("sort_by_new") == 1);
                        model.setIgnoreShops(rs.getInt("ignore_shops") == 1);
                        model.setSalesStatus(rs.getString("sales_status"));
                        model.setItemConditionIds(rs.getString("item_condition_ids"));
                        model.setColorIds(rs.getString("color_ids"));
                        model.setExcludeKeyword(rs.getString("exclude_keyword"));
                    } catch (SQLException e) {
                        // If columns don't exist yet, use default values
                        model.setMinPrice(300);
                        model.setMaxPrice(9999999);
                        model.setPages(1);
                        model.setSortByNew(false);
                        model.setIgnoreShops(false);
                        model.setSalesStatus("すべて");
                    }

                    return model;
                }
            }

        } catch (SQLException e) {
            logger.error("Error retrieving keyword search result by ID " + id + ": " + e.getMessage(), e);
        }

        return null;
    }

    /**
     * 過去5日間の売り切れ・取引中アイテムの集計データを取得する（ページネーション対応）
     *
     * @param page       ページ番号（1ベース）
     * @param pageSize   1ページあたりの件数
     * @param nameFilter 商品名フィルタ（null または空文字列の場合はフィルタなし）
     * @return 集計結果のリスト
     */
    public static List<SalesAggregationModel> getSalesAggregationData(int page, int pageSize, String nameFilter) {
        List<SalesAggregationModel> results = new ArrayList<>();

        // 過去5日間の日付を計算
        LocalDate today = LocalDate.now();
        List<String> dateList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        for (int i = 4; i >= 0; i--) {
            LocalDate date = today.minusDays(i);
            dateList.add(date.format(formatter));
        }

        String startDate = dateList.get(0);
        String endDate = dateList.get(dateList.size() - 1);

        logger.debug("売上集計の日付範囲: " + startDate + " から " + endDate);

        // 売り切れ・取引中アイテムを取得するSQLーデータベースレベルで集計して総販売数降順でソート）
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("WITH sold_items AS (")
                .append("    SELECT ")
                .append("        item_id, ")
                .append("        product_name as name, ")
                .append("        seller_id, ")
                .append("        price, ")
                .append("        category, ")
                .append("        status, ")
                .append("        DATE(updated_date) as sold_date, ")
                .append("        ROW_NUMBER() OVER (PARTITION BY item_id, product_name ORDER BY updated_date DESC) as rn ")
                .append("    FROM search_items ")
                .append("    WHERE status NOT IN (1) ")  // Exclude ON_SALE status (ID = 1)
                .append("    AND DATE(updated_date) BETWEEN ? AND ? ");

        // 商品名フィルタを追加
        if (nameFilter != null && !nameFilter.trim().isEmpty()) {
            sqlBuilder.append("    AND LOWER(name) LIKE LOWER(?) ");
        }

        sqlBuilder.append("), ")
                .append("aggregated_sales AS (")
                .append("    SELECT ")
                .append("        seller_id, ")
                .append("        name, ")
                .append("        category, ")
                .append("        price, ")
                .append("        SUM(CASE WHEN sold_date = ? THEN 1 ELSE 0 END) as day1_count, ")
                .append("        SUM(CASE WHEN sold_date = ? THEN 1 ELSE 0 END) as day2_count, ")
                .append("        SUM(CASE WHEN sold_date = ? THEN 1 ELSE 0 END) as day3_count, ")
                .append("        SUM(CASE WHEN sold_date = ? THEN 1 ELSE 0 END) as day4_count, ")
                .append("        SUM(CASE WHEN sold_date = ? THEN 1 ELSE 0 END) as day5_count, ")
                .append("        COUNT(*) as total_sales_count ")
                .append("    FROM sold_items ")
                .append("    WHERE rn = 1 ")
                .append("    GROUP BY seller_id, name")
                .append(") ")
                .append("SELECT ")
                .append("    seller_id, ")
                .append("    name, ")
                .append("    category, ")
                .append("    price, ")
                .append("    day1_count, ")
                .append("    day2_count, ")
                .append("    day3_count, ")
                .append("    day4_count, ")
                .append("    day5_count, ")
                .append("    total_sales_count ")
                .append("FROM aggregated_sales ")
                .append("ORDER BY total_sales_count DESC, seller_id, name ");

        // ページネーション用のLIMIT/OFFSETを追加
        sqlBuilder.append("LIMIT ? OFFSET ?");

        String sql = sqlBuilder.toString();
        logger.debug("SQL: " + sql);

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            int paramIndex = 1;
            pstmt.setString(paramIndex++, startDate);
            pstmt.setString(paramIndex++, endDate);

            // 商品名フィルタのパラメータを設定
            if (nameFilter != null && !nameFilter.trim().isEmpty()) {
                pstmt.setString(paramIndex++, "%" + nameFilter.trim() + "%");
            }

            // 5日間E日付パラメータを設定
            for (String date : dateList) {
                pstmt.setString(paramIndex++, date);
            }

            // ページネーションのパラメータを設定            pstmt.setInt(paramIndex++, pageSize);
            pstmt.setInt(paramIndex, (page - 1) * pageSize);

            logger.debug("売上集計クエリを以下のパラメータで実行中: " + startDate + ", " + endDate +
                    ", 名前フィルター: " + nameFilter + ", ページ: " + page + ", ページサイズ: " + pageSize);

            try (ResultSet rs = pstmt.executeQuery()) {
                int rowCount = 0;

                while (rs.next()) {
                    String sellerId = rs.getString("seller_id");
                    String itemName = rs.getString("name");
                    String category = rs.getString("category");
                    int price = rs.getInt("price");
                    int day1Count = rs.getInt("day1_count");
                    int day2Count = rs.getInt("day2_count");
                    int day3Count = rs.getInt("day3_count");
                    int day4Count = rs.getInt("day4_count");
                    int day5Count = rs.getInt("day5_count");
                    int totalSalesCount = rs.getInt("total_sales_count");

                    rowCount++;
                    if (rowCount <= 5) { // デバッグ用に最初の5行をログ出力
                        logger.debug("行 " + rowCount + " - セラー: " + sellerId +
                                ", アイテム: " + itemName + ", 総売上: " + totalSalesCount);
                    }

                    // SalesAggregationModelを作成
                    SalesAggregationModel model = new SalesAggregationModel();
                    model.setSellerId(sellerId);
                    model.setItemName(itemName);
                    model.setCategoryName(getCategoryNameById(category));
                    model.setPrice(price);
                    model.setTotalSalesCount(totalSalesCount);

                    // 日別販売数を設定
                    Map<String, Integer> dailySalesCount = new LinkedHashMap<>();
                    dailySalesCount.put(dateList.get(0), day1Count);
                    dailySalesCount.put(dateList.get(1), day2Count);
                    dailySalesCount.put(dateList.get(2), day3Count);
                    dailySalesCount.put(dateList.get(3), day4Count);
                    dailySalesCount.put(dateList.get(4), day5Count);
                    model.setDailySalesCount(dailySalesCount);

                    results.add(model);
                }

                logger.debug("処理された総行数: " + rowCount);
                logger.debug("集計された総アイテム数: " + results.size());
                logger.debug("結果はデータベースレベルで総売上数の降順に既にソートされています");
            }

        } catch (SQLException e) {
            logger.error("Error retrieving sales aggregation data: " + e.getMessage(), e);
        }

        logger.debug(results.size() + " 件の売上集計結果を返します");
        return results;
    }

    /**
     * 過去5日間の売り切れ・取引中アイテムの集計データを取得する（後方互換性のため）     *
     *
     * @return 集計結果のリスト
     */
    public static List<SalesAggregationModel> getSalesAggregationData() {
        return getSalesAggregationData(1, Integer.MAX_VALUE, null);
    }

    /**
     * 売り切れ・取引中アイテム集計データの総件数を取得する     *
     *
     * @param nameFilter 商品名フィルタEEull またE空文字列の場合Eフィルタなし！E     * @return 総件数
     */
    public static int getSalesAggregationDataCount(String nameFilter) {
        // 過去5日間の日付を計算
        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String startDate = today.minusDays(4).format(formatter);
        String endDate = today.format(formatter);

        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("WITH sold_items AS (")
                .append("    SELECT ")
                .append("        item_id, ")
                .append("        product_name as name, ")
                .append("        seller_id, ")
                .append("        price, ")
                .append("        category, ")
                .append("        status, ")
                .append("        DATE(updated_date) as sold_date, ")
                .append("        ROW_NUMBER() OVER (PARTITION BY item_id, product_name ORDER BY updated_date DESC) as rn ")
                .append("    FROM search_items ")
                .append("    WHERE status NOT IN (1) ")  // Exclude ON_SALE status (ID = 1)
                .append("    AND DATE(updated_date) BETWEEN ? AND ? ");

        // 商品名フィルタを追加
        if (nameFilter != null && !nameFilter.trim().isEmpty()) {
            sqlBuilder.append("    AND LOWER(name) LIKE LOWER(?) ");
        }

        sqlBuilder.append(") ")
                .append("SELECT COUNT(DISTINCT (seller_id || '|' || name || '|' || category || '|' || price)) as total_count ")
                .append("FROM sold_items ")
                .append("WHERE rn = 1");

        String sql = sqlBuilder.toString();

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            int paramIndex = 1;
            pstmt.setString(paramIndex++, startDate);
            pstmt.setString(paramIndex++, endDate);

            // 商品名フィルタのパラメータを設定
            if (nameFilter != null && !nameFilter.trim().isEmpty()) {
                pstmt.setString(paramIndex, "%" + nameFilter.trim() + "%");
            }

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("total_count");
                }
            }

        } catch (SQLException e) {
            logger.error("Error retrieving sales aggregation data count: " + e.getMessage(), e);
        }

        return 0;
    }

    /**
     * カテゴリーIDからカテゴリー名を取得する     * Categoriesクラスを使用してCSVファイルから正確なカテゴリー名を取得     *
     *
     * @param categoryId カテゴリーIDE文字EEE     * @return カテゴリー名（見つからなし合E"カチEリID: [ID]"形式でフォールバック表示EE
     */
    public static String getCategoryNameById(String categoryId) {
        if (categoryId == null || categoryId.trim().isEmpty()) {
            return "カテゴリなし";
        }

        // Categoriesクラスのインスタンスを作成して初期化
        Categories categories =
                new Categories();
        categories.init();

        // カテゴリー名を取得
        String categoryName = categories.getCategoryNameById(categoryId.trim());
        if (categoryName != null) {
            return categoryName;
        } else {
            // カテゴリー名が見つからなし合Eフォールバック表示
            return "カチEリID: " + categoryId.trim();
        }
    }

    /**
     * カテゴリー情報をカテゴリー名に変換する
     * Categoriesクラスを使用してCSVファイルから正確なカテゴリー名を取得     *
     *
     * @param categoryInfo カテゴリー情報EIDまたは名前）     * @return カテゴリー名（見つからなし合E元の情報をフォールバック表示EE
     */
    public static String convertToDisplayCategoryName(String categoryInfo) {
        if (categoryInfo == null || categoryInfo.trim().isEmpty()) {
            return "";
        }

        // Categoriesクラスのインスタンスを作成して初期化
        Categories categories = new Categories();
        categories.init();

        // 既にカテゴリー名の場合はそのまま返す
        if (categories.isCategoryName(categoryInfo)) {
            return categoryInfo;
        }

        // カテゴリーIDの場合の名前に変換
        return categories.convertCategoryIdsToNames(categoryInfo);
    }

    /**
     * Get enhanced group information with seller counts and last updated timestamps.
     *
     * @return A list of group information models
     */
    public static List<GroupTagInfoModel> getGroupInfoWithCounts() {
        return FavoriteSellerRepository.getGroupInfoWithCounts();
    }

    /**
     * Get enhanced tag information with seller counts and last updated timestamps.
     *
     * @return A list of tag information models
     */
    public static List<GroupTagInfoModel> getTagInfoWithCounts() {
        return FavoriteSellerRepository.getTagInfoWithCounts();
    }

    /**
     * Update group research timestamps.
     *
     * @param groupName         The group name
     * @param researchStartedAt The research start timestamp
     * @param lastUpdated       The last updated timestamp
     * @return true if the update was successful, false otherwise
     */
    public static boolean updateGroupResearchTimestamps(String groupName, java.util.Date researchStartedAt, java.util.Date lastUpdated) {
        return FavoriteGroupRepository.updateGroupResearchTimestamps(groupName, researchStartedAt, lastUpdated);
    }

    /**
     * Update tag research timestamps.
     *
     * @param tagName           The tag name
     * @param researchStartedAt The research start timestamp
     * @param lastUpdated       The last updated timestamp
     * @return true if the update was successful, false otherwise
     */
    public static boolean updateTagResearchTimestamps(String tagName, java.util.Date researchStartedAt, java.util.Date lastUpdated) {
        return FavoriteTagRepository.updateTagResearchTimestamps(tagName, researchStartedAt, lastUpdated);
    }
}