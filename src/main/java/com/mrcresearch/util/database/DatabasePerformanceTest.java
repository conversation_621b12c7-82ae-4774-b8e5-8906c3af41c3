package com.mrcresearch.util.database;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.logging.Logger;

/**
 * データベースパフォーマンス改善機能の統合テストクラス
 * <p>
 * テスト対象:
 * - QueryPerformanceMonitor（クエリ実行時間計測）
 * - DatabaseIndexManager（インデックス最適化）
 * - DatabaseConnectionPool（コネクションプーリング）
 * - TransactionManager（トランザクション管理）
 */
public class DatabasePerformanceTest {
    private static final Logger logger = Logger.getLogger(DatabasePerformanceTest.class.getName());

    /**
     * メインテストメソッド
     *
     * @param args コマンドライン引数
     */
    public static void main(String[] args) {
        logger.info("=== データベースパフォーマンス改善機能 統合テスト開始 ===");

        try {
            // 1. コネクションプールの初期化テスト
            testConnectionPool();

            // 2. インデックス最適化テスト
            testIndexOptimization();

            // 3. クエリパフォーマンス監視テスト
            testQueryPerformanceMonitoring();

            // 4. トランザクション管理テスト
            testTransactionManager();

            // 5. 統合パフォーマンステスト
            testIntegratedPerformance();

            logger.info("=== 全てのテストが正常に完了しました ===");

        } catch (Exception e) {
            logger.severe("テスト実行中にエラーが発生しました: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // クリーンアップ
            cleanup();
        }
    }

    /**
     * コネクションプールの初期化と動作をテストする
     */
    private static void testConnectionPool() throws Exception {
        logger.info("--- コネクションプールテスト開始 ---");

        // プール初期化
        DatabaseConnectionManager.initializeConnectionPool();

        // プール状態確認
        String status = DatabaseConnectionManager.getConnectionStatus();
        logger.info("プール状態: " + status);

        // 複数の接続取得テスト
        List<Connection> connections = new ArrayList<>();
        try {
            for (int i = 0; i < 5; i++) {
                Connection conn = DatabaseConnectionManager.getConnection();
                connections.add(conn);
                logger.info("接続 " + (i + 1) + " 取得成功");
            }

            // プール状態確認
            logger.info("接続取得後のプール状態: " + DatabaseConnectionManager.getConnectionStatus());

        } finally {
            // 接続を閉じる
            for (Connection conn : connections) {
                if (conn != null && !conn.isClosed()) {
                    conn.close();
                }
            }
        }

        logger.info("コネクションプールテスト完了");
    }

    /**
     * インデックス最適化をテストする
     */
    private static void testIndexOptimization() throws Exception {
        logger.info("--- インデックス最適化テスト開始 ---");

        // 既存インデックスの確認
        DatabaseIndexManager.reportExistingIndexes();

        // 最適化インデックスの作成
        DatabaseIndexManager.createOptimizedIndexes();

        // よく使用されるクエリの実行計画分析
        DatabaseIndexManager.analyzeCommonQueries();

        // 具体的なクエリのパフォーマンステスト
        testSpecificQueryPerformance();

        logger.info("インデックス最適化テスト完了");
    }

    /**
     * 特定のクエリのパフォーマンスをテストする
     */
    private static void testSpecificQueryPerformance() throws Exception {
        logger.info("特定クエリパフォーマンステスト開始");

        String[] testQueries = {
                "SELECT COUNT(*) FROM search_items WHERE status = 3",
                "SELECT * FROM favorite_sellers ORDER BY updated_date DESC LIMIT 10",
                "SELECT seller_id, COUNT(*) FROM search_items GROUP BY seller_id LIMIT 10"
        };

        for (String query : testQueries) {
            long startTime = System.currentTimeMillis();

            try (Connection conn = DatabaseConnectionManager.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(query);
                 ResultSet rs = stmt.executeQuery()) {

                int count = 0;
                while (rs.next()) {
                    count++;
                }

                long executionTime = System.currentTimeMillis() - startTime;
                logger.info(String.format("クエリ実行時間: %dms, 結果数: %d, SQL: %s",
                        executionTime, count, query.substring(0, Math.min(query.length(), 50)) + "..."));

            } catch (SQLException e) {
                logger.warning("クエリ実行エラー（テーブルが存在しない可能性があります）: " + e.getMessage());
            }
        }
    }

    /**
     * クエリパフォーマンス監視機能をテストする
     */
    private static void testQueryPerformanceMonitoring() throws Exception {
        logger.info("--- クエリパフォーマンス監視テスト開始 ---");

        // 統計情報をクリア
        QueryPerformanceMonitor.clearStatistics();

        // テストクエリを複数回実行
        for (int i = 0; i < 10; i++) {
            try (Connection conn = DatabaseConnectionManager.getConnection();
                 PreparedStatement stmt = conn.prepareStatement("SELECT 1 as test_value")) {

                QueryPerformanceMonitor.executeQueryWithMonitoring(
                        stmt, "SELECT", "統合テスト用シンプルクエリ"
                );

            } catch (SQLException e) {
                logger.warning("テストクエリ実行エラー: " + e.getMessage());
            }

            // 一部のクエリで遅延を発生させてスロークエリをテスト
            if (i % 3 == 0) {
                Thread.sleep(150); // 100ms以上でスロークエリとして検出される
            }
        }

        // 統計レポート出力
        QueryPerformanceMonitor.printPerformanceReport();

        // 統計サマリー取得
        String summary = QueryPerformanceMonitor.getStatisticsSummary();
        logger.info("パフォーマンス統計サマリー: " + summary);

        logger.info("クエリパフォーマンス監視テスト完了");
    }

    /**
     * トランザクション管理をテストする
     */
    private static void testTransactionManager() throws Exception {
        logger.info("--- トランザクション管理テスト開始 ---");

        // 基本的なトランザクションテスト
        testBasicTransaction();

        // ネストしたトランザクションテスト
        testNestedTransaction();

        // セーブポイントテスト
        testSavepointTransaction();

        // ロールバックテスト
        testRollbackTransaction();

        // 読み込み専用トランザクションテスト
        testReadOnlyTransaction();

        logger.info("トランザクション管理テスト完了");
    }

    /**
     * 基本的なトランザクションをテストする
     */
    private static void testBasicTransaction() throws Exception {
        logger.info("基本トランザクションテスト開始");

        String result = TransactionManager.executeInTransaction(() -> {
            logger.info("トランザクション内処理実行中...");

            // トランザクション状態確認
            String status = TransactionManager.getTransactionStatus();
            logger.info("トランザクション状態: " + status);

            // 簡単なデータベース操作（テーブルが存在する場合）
            try (Connection conn = TransactionManager.getConnection();
                 PreparedStatement stmt = conn.prepareStatement("SELECT COUNT(*) as count FROM sqlite_master WHERE type='table'")) {

                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        int tableCount = rs.getInt("count");
                        logger.info("データベース内のテーブル数: " + tableCount);
                        return "テーブル数: " + tableCount;
                    }
                }
            }

            return "基本トランザクション成功";
        });

        logger.info("基本トランザクション結果: " + result);
    }

    /**
     * ネストしたトランザクションをテストする
     */
    private static void testNestedTransaction() throws Exception {
        logger.info("ネストトランザクションテスト開始");

        String result = TransactionManager.executeInTransaction(() -> {
            logger.info("外側のトランザクション開始");

            // ネストしたトランザクション
            String nestedResult = TransactionManager.executeInTransaction(() -> {
                logger.info("内側のトランザクション実行中");

                String status = TransactionManager.getTransactionStatus();
                logger.info("ネスト状態: " + status);

                return "ネストしたトランザクション成功";
            });

            logger.info("ネスト結果: " + nestedResult);
            return "外側のトランザクション成功";
        });

        logger.info("ネストトランザクション結果: " + result);
    }

    /**
     * セーブポイント機能をテストする
     */
    private static void testSavepointTransaction() throws Exception {
        logger.info("セーブポイントテスト開始");

        TransactionManager.executeInTransaction(() -> {
            logger.info("トランザクション開始");

            // セーブポイント処理
            try {
                String result = TransactionManager.executeWithSavepoint(() -> {
                    logger.info("セーブポイント処理実行中");
                    return "セーブポイント処理成功";
                }, "test_savepoint");

                logger.info("セーブポイント結果: " + result);

            } catch (Exception e) {
                logger.info("セーブポイント処理でエラー: " + e.getMessage());
            }

            return null;
        });

        logger.info("セーブポイントテスト完了");
    }

    /**
     * ロールバック機能をテストする
     */
    private static void testRollbackTransaction() throws Exception {
        logger.info("ロールバックテスト開始");

        try {
            TransactionManager.executeInTransaction(() -> {
                logger.info("エラーを発生させてロールバックをテスト");

                // 意図的に例外を発生させる
                throw new RuntimeException("テスト用例外");
            });

        } catch (Exception e) {
            logger.info("期待通りロールバックが実行されました: " + e.getMessage());
        }

        logger.info("ロールバックテスト完了");
    }

    /**
     * 読み込み専用トランザクションをテストする
     */
    private static void testReadOnlyTransaction() throws Exception {
        logger.info("読み込み専用トランザクションテスト開始");

        String result = TransactionManager.executeReadOnlyTransaction(() -> {
            logger.info("読み込み専用トランザクション実行中");

            try (Connection conn = TransactionManager.getConnection();
                 PreparedStatement stmt = conn.prepareStatement("SELECT 'readonly_test' as test")) {

                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        return rs.getString("test");
                    }
                }
            }

            return "読み込み専用テスト完了";
        });

        logger.info("読み込み専用トランザクション結果: " + result);
    }

    /**
     * 統合パフォーマンステストを実行する
     */
    private static void testIntegratedPerformance() throws Exception {
        logger.info("--- 統合パフォーマンステスト開始 ---");

        // 全機能を組み合わせたテスト
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < 50; i++) {
            final int iteration = i;

            TransactionManager.executeInTransaction(() -> {
                // パフォーマンス監視付きでクエリ実行
                try (Connection conn = TransactionManager.getConnection();
                     PreparedStatement stmt = conn.prepareStatement("SELECT ? as iteration, datetime('now') as current_time")) {

                    stmt.setInt(1, iteration);

                    QueryPerformanceMonitor.executeQueryWithMonitoring(
                            stmt, "SELECT", "統合パフォーマンステスト_反復_" + iteration
                    );

                } catch (SQLException e) {
                    logger.warning("統合テストクエリエラー: " + e.getMessage());
                }

                return null;
            });
        }

        long totalTime = System.currentTimeMillis() - startTime;
        logger.info("統合パフォーマンステスト完了: 50回実行、総時間: " + totalTime + "ms");

        // 最終統計レポート
        QueryPerformanceMonitor.printPerformanceReport();

        // プール状態確認
        logger.info("最終プール状態: " + DatabaseConnectionManager.getConnectionStatus());

        // 詳細メトリクス出力
        if (DatabaseConnectionPool.isInitialized()) {
            logger.info(DatabaseConnectionPool.getDetailedMetrics());
        }
    }

    /**
     * テスト後のクリーンアップ
     */
    private static void cleanup() {
        logger.info("--- クリーンアップ開始 ---");

        try {
            // 統計情報をクリア
            QueryPerformanceMonitor.clearStatistics();

            // コネクションプールのシャットダウン
            DatabaseConnectionManager.shutdownConnectionPool();

            logger.info("クリーンアップ完了");

        } catch (Exception e) {
            logger.warning("クリーンアップ中にエラーが発生しました: " + e.getMessage());
        }
    }
}