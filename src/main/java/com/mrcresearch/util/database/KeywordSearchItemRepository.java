package com.mrcresearch.util.database;

import com.mrcresearch.screen.model.KeywordSearchItemModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * Repository class for keyword search items reference/mapping table data access.
 * This table now only contains item_id and status as a reference/mapping table.
 */
public class KeywordSearchItemRepository {

    private static final Logger logger = LoggerFactory.getLogger(KeywordSearchItemRepository.class);

    /**
     * Private constructor to prevent instantiation.
     * This is a utility class with static methods only.
     */
    private KeywordSearchItemRepository() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * Save keyword search item references to the database.
     *
     * @param itemIds List of item IDs to save as references
     * @param status  The status ID for these items
     * @param keywordSearchResultId The ID of the keyword search result
     * @return The number of items successfully saved
     */
    public static int saveKeywordSearchItemReferences(List<String> itemIds, int status, int keywordSearchResultId) {
        if (itemIds == null || itemIds.isEmpty()) {
            logger.info("keyword_search_items テーブルに保存するアイテムIDがありません");
            return 0;
        }

        String sql = "INSERT INTO keyword_search_items (item_id, status, keyword_search_result_id) VALUES (?, ?, ?)";
        int savedCount = 0;

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            for (String itemId : itemIds) {
                try {
                    pstmt.setString(1, itemId);
                    pstmt.setInt(2, status);
                    pstmt.setInt(3, keywordSearchResultId);

                    int affectedRows = pstmt.executeUpdate();
                    if (affectedRows > 0) {
                        savedCount++;
                    }

                } catch (SQLException e) {
                    logger.error("Error saving keyword search item reference {}: {}", itemId, e.getMessage(), e);
                }
            }

        } catch (SQLException e) {
            logger.error("Error saving keyword search item references: {}", e.getMessage(), e);
        }

        logger.info("keyword_search_items テーブルに {} 件のアイテム参照を正常に保存しました", savedCount);
        return savedCount;
    }

    /**
     * Get keyword search items with pagination.
     *
     * @param page     The page number (1-based)
     * @param pageSize The number of records per page
     * @return A list of keyword search item models
     */
    public static List<KeywordSearchItemModel> getKeywordSearchItems(int page, int pageSize) {
        List<KeywordSearchItemModel> items = new ArrayList<>();

        String sql = "SELECT * FROM keyword_search_items " +
                "ORDER BY id DESC LIMIT ? OFFSET ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, pageSize);
            pstmt.setInt(2, (page - 1) * pageSize);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    KeywordSearchItemModel item = new KeywordSearchItemModel();
                    item.setId(rs.getInt("id"));
                    item.setItemId(rs.getString("item_id"));
                    item.setStatus(rs.getInt("status"));
                    items.add(item);
                }
            }

        } catch (SQLException e) {
            logger.error("Error retrieving keyword search items: {}", e.getMessage(), e);
        }

        return items;
    }

    /**
     * Get the total count of keyword search items.
     *
     * @return The total count of items
     */
    public static int getTotalKeywordSearchItemsCount() {
        String sql = "SELECT COUNT(*) FROM keyword_search_items";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }

        } catch (SQLException e) {
            logger.error("Error getting total keyword search items count: {}", e.getMessage(), e);
        }

        return 0;
    }

    /**
     * Get keyword search items by status.
     *
     * @param status The status ID to filter by
     * @return A list of keyword search item models
     */
    public static List<KeywordSearchItemModel> getKeywordSearchItemsByStatus(int status) {
        List<KeywordSearchItemModel> items = new ArrayList<>();

        String sql = "SELECT * FROM keyword_search_items WHERE status = ? ORDER BY id DESC";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, status);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    KeywordSearchItemModel item = new KeywordSearchItemModel();
                    item.setId(rs.getInt("id"));
                    item.setItemId(rs.getString("item_id"));
                    item.setStatus(rs.getInt("status"));
                    items.add(item);
                }
            }

        } catch (SQLException e) {
            logger.error("Error getting keyword search items by status: {}", e.getMessage(), e);
        }

        return items;
    }

    /**
     * Delete a keyword search item reference by item ID.
     *
     * @param itemId The item ID to delete
     * @return true if the deletion was successful, false otherwise
     */
    public static boolean deleteKeywordSearchItem(String itemId) {
        String sql = "DELETE FROM keyword_search_items WHERE item_id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, itemId);

            int affectedRows = pstmt.executeUpdate();
            logger.debug("アイテムID: {} のキーワード検索アイテム参照を {} 件削除しました", affectedRows, itemId);
            return affectedRows > 0;

        } catch (SQLException e) {
            logger.error("Error deleting keyword search item: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Update the status of a keyword search item reference.
     *
     * @param itemId The item ID to update
     * @param status The new status ID
     * @return true if the update was successful, false otherwise
     */
    public static boolean updateKeywordSearchItemStatus(String itemId, int status) {
        String sql = "UPDATE keyword_search_items SET status = ? WHERE item_id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, status);
            pstmt.setString(2, itemId);

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;

        } catch (SQLException e) {
            logger.error("Error updating keyword search item status: {}", e.getMessage(), e);
            return false;
        }
    }
}