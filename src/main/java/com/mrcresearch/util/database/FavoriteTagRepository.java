package com.mrcresearch.util.database;

import com.mrcresearch.screen.model.FavoriteTagModel;
import com.mrcresearch.screen.model.GroupTagInfoModel;

import java.sql.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Repository class for favorite tag related database operations.
 */
public class FavoriteTagRepository {

    /**
     * Private constructor to prevent instantiation.
     */
    private FavoriteTagRepository() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * Save or update a favorite tag.
     *
     * @param tagName The tag name
     * @return The ID of the saved/updated record, or -1 if an error occurred
     */
    public static int saveOrUpdateFavoriteTag(String tagName) {
        // First try to get existing record
        FavoriteTagModel existing = getFavoriteTagByName(tagName);

        if (existing != null) {
            return existing.getId(); // Already exists, return existing ID
        }

        // Create new record
        String sql = "INSERT INTO favorite_tags (tag_name) VALUES (?)";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

            pstmt.setString(1, tagName);

            int affectedRows = pstmt.executeUpdate();

            if (affectedRows > 0) {
                try (ResultSet rs = pstmt.getGeneratedKeys()) {
                    if (rs.next()) {
                        return rs.getInt(1);
                    }
                }
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return -1;
    }

    /**
     * Update research timestamps for a tag.
     *
     * @param tagName           The tag name
     * @param researchStartedAt The research start timestamp (null to keep current value)
     * @param lastUpdated       The last updated timestamp (null to keep current value)
     * @return true if the update was successful, false otherwise
     */
    public static boolean updateTagResearchTimestamps(String tagName, Date researchStartedAt, Date lastUpdated) {
        // Ensure the tag exists
        int tagId = saveOrUpdateFavoriteTag(tagName);
        if (tagId == -1) {
            return false;
        }

        // Build SQL dynamically based on which fields need to be updated
        StringBuilder sqlBuilder = new StringBuilder("UPDATE favorite_tags SET ");
        List<String> updates = new ArrayList<>();
        List<Object> parameters = new ArrayList<>();

        if (researchStartedAt != null) {
            updates.add("research_started_at = ?");
            parameters.add(DatabaseDateTimeUtil.formatDate(researchStartedAt));
        }

        if (lastUpdated != null) {
            updates.add("last_updated = ?");
            parameters.add(DatabaseDateTimeUtil.formatDate(lastUpdated));
        }

        if (updates.isEmpty()) {
            return true; // Nothing to update
        }

        sqlBuilder.append(String.join(", ", updates));
        sqlBuilder.append(" WHERE tag_name = ?");
        parameters.add(tagName);

        String sql = sqlBuilder.toString();

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            for (int i = 0; i < parameters.size(); i++) {
                pstmt.setString(i + 1, (String) parameters.get(i));
            }

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;

        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Get a favorite tag by name.
     *
     * @param tagName The tag name
     * @return The favorite tag model, or null if not found
     */
    public static FavoriteTagModel getFavoriteTagByName(String tagName) {
        String sql = "SELECT * FROM favorite_tags WHERE tag_name = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, tagName);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    FavoriteTagModel model = new FavoriteTagModel();
                    model.setId(rs.getInt("id"));
                    model.setTagName(rs.getString("tag_name"));

                    String lastUpdatedStr = rs.getString("last_updated");
                    if (lastUpdatedStr != null && !lastUpdatedStr.isEmpty()) {
                        model.setLastUpdated(DatabaseDateTimeUtil.parseDate(lastUpdatedStr));
                    }

                    String researchStartedStr = rs.getString("research_started_at");
                    if (researchStartedStr != null && !researchStartedStr.isEmpty()) {
                        model.setResearchStartedAt(DatabaseDateTimeUtil.parseDate(researchStartedStr));
                    }

                    return model;
                }
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return null;
    }

    /**
     * Get enhanced tag information with seller counts and timestamps from favorite_tags table.
     *
     * @return A list of tag information models
     */
    public static List<GroupTagInfoModel> getTagInfoWithCounts() {
        List<GroupTagInfoModel> results = new ArrayList<>();

        // First get all unique tags from favorite_sellers
        List<String> allTags = FavoriteSellerRepository.getAllTags();

        for (String tag : allTags) {
            String sql = "SELECT " +
                    "    COUNT(fs.seller_id) as seller_count, " +
                    "    ft.last_updated, " +
                    "    ft.research_started_at " +
                    "FROM favorite_sellers fs " +
                    "LEFT JOIN favorite_tags ft ON ? = ft.tag_name " +
                    "WHERE fs.tags LIKE ?";

            try (Connection conn = DatabaseConnectionManager.getConnection();
                 PreparedStatement pstmt = conn.prepareStatement(sql)) {

                pstmt.setString(1, tag);
                pstmt.setString(2, "%" + tag + "%");

                try (ResultSet rs = pstmt.executeQuery()) {
                    if (rs.next()) {
                        int sellerCount = rs.getInt("seller_count");
                        String lastUpdatedStr = rs.getString("last_updated");
                        String researchStartedStr = rs.getString("research_started_at");

                        Date lastUpdated = null;
                        Date researchStartedAt = null;
                        boolean hasResearch = false;

                        if (lastUpdatedStr != null && !lastUpdatedStr.isEmpty()) {
                            lastUpdated = DatabaseDateTimeUtil.parseDate(lastUpdatedStr);
                            hasResearch = true;
                        }

                        if (researchStartedStr != null && !researchStartedStr.isEmpty()) {
                            researchStartedAt = DatabaseDateTimeUtil.parseDate(researchStartedStr);
                        }

                        GroupTagInfoModel info = new GroupTagInfoModel(tag, sellerCount, lastUpdated, researchStartedAt, hasResearch);
                        results.add(info);
                    }
                }

            } catch (SQLException e) {
                e.printStackTrace();
            }
        }

        return results;
    }
}
