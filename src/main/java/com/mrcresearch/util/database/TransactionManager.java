package com.mrcresearch.util.database;

import java.sql.*;
import java.util.concurrent.Callable;
import java.util.logging.Logger;

/**
 * データベーストランザクションを管理するユーティリティクラス
 * <p>
 * 主な機能:
 * - 宣言的トランザクション管理
 * - 自動コミット/ロールバック制御
 * - ネストしたトランザクションのサポート（セーブポイント使用）
 * - ACIDプロパティの保証
 * - 例外処理とエラーログ出力
 */
public class TransactionManager {
    private static final Logger logger = Logger.getLogger(TransactionManager.class.getName());

    // ThreadLocalを使用して各スレッドのトランザクション状態を管理
    private static final ThreadLocal<TransactionContext> transactionContext = new ThreadLocal<>();

    /**
     * プライベートコンストラクタ（ユーティリティクラスのため）
     */
    private TransactionManager() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * トランザクションコンテキストを格納するクラス
     */
    private static class TransactionContext {
        Connection connection;
        boolean autoCommit;
        int depth;

        TransactionContext(Connection connection, boolean autoCommit) throws SQLException {
            this.connection = connection;
            this.autoCommit = autoCommit;
            this.depth = 1;
        }

        void incrementDepth() {
            depth++;
        }

        void decrementDepth() {
            depth--;
        }

        boolean isRootTransaction() {
            return depth == 1;
        }

        void close() throws SQLException {
            if (connection != null && !connection.isClosed()) {
                connection.setAutoCommit(autoCommit);
                connection.close();
            }
        }
    }

    /**
     * トランザクション内で処理を実行し、結果を返す
     *
     * @param <T>       戻り値の型
     * @param operation 実行する処理
     * @return 処理結果
     * @throws Exception 処理中に発生した例外
     */
    public static <T> T executeInTransaction(Callable<T> operation) throws Exception {
        boolean isNewTransaction = !isInTransaction();
        TransactionContext context = null;

        try {
            if (isNewTransaction) {
                // 新しいトランザクションを開始
                context = beginTransaction();
                logger.fine("新しいトランザクションを開始しました");
            } else {
                // 既存のトランザクションを継続（ネストしたトランザクション）
                context = transactionContext.get();
                context.incrementDepth();
                logger.fine("ネストしたトランザクションを開始しました（深度: " + context.depth + "）");
            }

            // 処理を実行
            T result = operation.call();

            if (isNewTransaction) {
                // ルートトランザクションの場合のみコミット
                commitTransaction();
                logger.fine("トランザクションをコミットしました");
            } else {
                // ネストしたトランザクションの深度を戻す
                context.decrementDepth();
                logger.fine("ネストしたトランザクションが正常終了しました（深度: " + context.depth + "）");
            }

            return result;

        } catch (Exception e) {
            logger.severe("トランザクション実行中にエラーが発生しました: " + e.getMessage());

            if (isNewTransaction) {
                // ルートトランザクションの場合はロールバック
                rollbackTransaction();
                logger.warning("トランザクションをロールバックしました");
            } else {
                // ネストしたトランザクションの場合は例外を上位に伝播
                context.decrementDepth();
                logger.warning("ネストしたトランザクションでエラーが発生しました。上位でロールバックされます。");
            }

            throw e;

        } finally {
            if (isNewTransaction && context != null) {
                // リソースのクリーンアップ
                cleanupTransaction();
            }
        }
    }

    /**
     * トランザクション内で処理を実行する（戻り値なし）
     *
     * @param operation 実行する処理
     * @throws Exception 処理中に発生した例外
     */
    public static void executeInTransaction(Runnable operation) throws Exception {
        executeInTransaction(() -> {
            operation.run();
            return null;
        });
    }

    /**
     * セーブポイントを使用してトランザクション内で安全に処理を実行する
     * エラーが発生した場合、セーブポイントまでロールバックする
     *
     * @param <T>           戻り値の型
     * @param operation     実行する処理
     * @param savepointName セーブポイント名
     * @return 処理結果
     * @throws Exception 処理中に発生した例外
     */
    public static <T> T executeWithSavepoint(Callable<T> operation, String savepointName) throws Exception {
        if (!isInTransaction()) {
            throw new IllegalStateException("セーブポイントはトランザクション内でのみ使用できます");
        }

        TransactionContext context = transactionContext.get();
        Connection conn = context.connection;
        Savepoint savepoint = null;

        try {
            // セーブポイントを作成
            savepoint = conn.setSavepoint(savepointName);
            logger.fine("セーブポイントを作成しました: " + savepointName);

            // 処理を実行
            T result = operation.call();

            logger.fine("セーブポイント処理が正常完了しました: " + savepointName);
            return result;

        } catch (Exception e) {
            logger.warning("セーブポイント処理でエラーが発生しました: " + savepointName + " - " + e.getMessage());

            if (savepoint != null) {
                try {
                    // セーブポイントまでロールバック
                    conn.rollback(savepoint);
                    logger.info("セーブポイントまでロールバックしました: " + savepointName);
                } catch (SQLException rollbackEx) {
                    logger.severe("セーブポイントロールバック中にエラーが発生しました: " + rollbackEx.getMessage());
                    // 元の例外を優先する
                    e.addSuppressed(rollbackEx);
                }
            }

            throw e;

        } finally {
            if (savepoint != null) {
                try {
                    // セーブポイントを解放
                    conn.releaseSavepoint(savepoint);
                    logger.fine("セーブポイントを解放しました: " + savepointName);
                } catch (SQLException e) {
                    logger.warning("セーブポイント解放中にエラーが発生しました: " + e.getMessage());
                    // セーブポイント解放エラーは無視（重要度が低い）
                }
            }
        }
    }

    /**
     * 手動でトランザクションを開始する
     *
     * @return トランザクションコンテキスト
     * @throws SQLException データベースエラー
     */
    public static TransactionContext beginTransaction() throws SQLException {
        if (isInTransaction()) {
            throw new IllegalStateException("既にトランザクションが開始されています");
        }

        try {
            Connection conn = DatabaseConnectionManager.getConnection();
            boolean originalAutoCommit = conn.getAutoCommit();

            // オートコミットを無効化
            conn.setAutoCommit(false);

            // SQLite用のトランザクション設定
            try (Statement stmt = conn.createStatement()) {
                // WALモードでの読み込み一貫性向上
                stmt.execute("BEGIN IMMEDIATE");
            }

            TransactionContext context = new TransactionContext(conn, originalAutoCommit);
            transactionContext.set(context);

            logger.fine("手動トランザクションを開始しました");
            return context;

        } catch (SQLException e) {
            logger.severe("トランザクション開始に失敗しました: " + e.getMessage());
            throw e;
        }
    }

    /**
     * トランザクションをコミットする
     *
     * @throws SQLException データベースエラー
     */
    public static void commitTransaction() throws SQLException {
        TransactionContext context = transactionContext.get();
        if (context == null) {
            throw new IllegalStateException("アクティブなトランザクションがありません");
        }

        if (!context.isRootTransaction()) {
            throw new IllegalStateException("ネストしたトランザクション内からはコミットできません");
        }

        try {
            Connection conn = context.connection;
            conn.commit();
            logger.fine("トランザクションをコミットしました");

        } catch (SQLException e) {
            logger.severe("コミット中にエラーが発生しました: " + e.getMessage());
            throw e;
        }
    }

    /**
     * トランザクションをロールバックする
     *
     * @throws SQLException データベースエラー
     */
    public static void rollbackTransaction() throws SQLException {
        TransactionContext context = transactionContext.get();
        if (context == null) {
            logger.warning("ロールバック要求がありましたが、アクティブなトランザクションがありません");
            return;
        }

        if (!context.isRootTransaction()) {
            throw new IllegalStateException("ネストしたトランザクション内からはロールバックできません");
        }

        try {
            Connection conn = context.connection;
            conn.rollback();
            logger.info("トランザクションをロールバックしました");

        } catch (SQLException e) {
            logger.severe("ロールバック中にエラーが発生しました: " + e.getMessage());
            throw e;
        }
    }

    /**
     * トランザクションが現在アクティブかチェックする
     *
     * @return トランザクション中の場合true
     */
    public static boolean isInTransaction() {
        return transactionContext.get() != null;
    }

    /**
     * 現在のトランザクション接続を取得する
     * トランザクション外から呼び出された場合は新しい接続を返す
     *
     * @return データベース接続
     * @throws SQLException データベースエラー
     */
    public static Connection getConnection() throws SQLException {
        TransactionContext context = transactionContext.get();
        if (context != null) {
            return context.connection;
        } else {
            // トランザクション外の場合は通常の接続を返す
            return DatabaseConnectionManager.getConnection();
        }
    }

    /**
     * トランザクションリソースをクリーンアップする
     */
    private static void cleanupTransaction() {
        TransactionContext context = transactionContext.get();
        if (context != null) {
            try {
                context.close();
                logger.fine("トランザクションリソースをクリーンアップしました");
            } catch (SQLException e) {
                logger.warning("トランザクションクリーンアップ中にエラーが発生しました: " + e.getMessage());
            } finally {
                transactionContext.remove();
            }
        }
    }

    /**
     * 現在のトランザクション状態情報を取得する
     *
     * @return トランザクション状態の文字列
     */
    public static String getTransactionStatus() {
        TransactionContext context = transactionContext.get();
        if (context == null) {
            return "トランザクション無効";
        }

        try {
            Connection conn = context.connection;
            boolean isValid = conn != null && !conn.isClosed() && conn.isValid(1);

            return String.format(
                    "トランザクション有効 - 深度: %d, 接続状態: %s, オートコミット: %s",
                    context.depth,
                    isValid ? "有効" : "無効",
                    conn.getAutoCommit() ? "有効" : "無効"
            );

        } catch (SQLException e) {
            return "トランザクション状態取得エラー: " + e.getMessage();
        }
    }

    /**
     * SQLiteのWALモードでの読み込み専用トランザクションを開始する
     * 大量のデータ読み込み処理に最適
     *
     * @param <T>       戻り値の型
     * @param operation 実行する読み込み処理
     * @return 処理結果
     * @throws Exception 処理中に発生した例外
     */
    public static <T> T executeReadOnlyTransaction(Callable<T> operation) throws Exception {
        return executeInTransaction(() -> {
            // 読み込み専用の最適化設定
            try (Connection conn = getConnection();
                 Statement stmt = conn.createStatement()) {

                // 読み込み専用設定（SQLite）
                stmt.execute("PRAGMA query_only = ON");
                logger.fine("読み込み専用トランザクションを設定しました");

                T result = operation.call();

                // 設定を戻す
                stmt.execute("PRAGMA query_only = OFF");

                return result;

            } catch (SQLException e) {
                logger.warning("読み込み専用トランザクション設定中にエラーが発生しました: " + e.getMessage());
                // 設定エラーは無視して通常の処理を続行
                return operation.call();
            }
        });
    }
}