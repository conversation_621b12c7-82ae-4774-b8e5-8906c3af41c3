package com.mrcresearch.util.database;

import com.mrcresearch.screen.model.SellerSearchModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Repository class for managing seller search data in the database.
 * Handles CRUD operations for the seller_searches table.
 */
public class SellerSearchRepository {

    private static final Logger logger = LoggerFactory.getLogger(SellerSearchRepository.class);

    /**
     * Private constructor to prevent instantiation.
     * This is a utility class with static methods only.
     */
    private SellerSearchRepository() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * Save a seller search to the database.
     *
     * @param model The seller search model to save
     * @return The ID of the saved record, or -1 if an error occurred
     */
    public static int saveSellerSearch(SellerSearchModel model) {
        String sql = "INSERT INTO seller_searches (seller_id, seller_name, seller_name_changed, " +
                "last_researched_at, added_date, updated_date) VALUES (?, ?, ?, ?, ?, ?)";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

            pstmt.setString(1, model.getSellerId());
            pstmt.setString(2, model.getSellerName());
            pstmt.setInt(3, model.isSellerNameChanged() ? 1 : 0);
            pstmt.setString(4, DatabaseDateTimeUtil.getDateFormat().format(model.getLastResearchedAt()));
            pstmt.setString(5, DatabaseDateTimeUtil.getDateFormat().format(model.getAddedDate()));
            pstmt.setString(6, DatabaseDateTimeUtil.getDateFormat().format(model.getUpdatedDate()));

            int affectedRows = pstmt.executeUpdate();

            if (affectedRows > 0) {
                try (ResultSet rs = pstmt.getGeneratedKeys()) {
                    if (rs.next()) {
                        int generatedId = rs.getInt(1);
                        logger.info("セラー検索をID: {} で正常に保存しました", generatedId);
                        return generatedId;
                    }
                }
            }

        } catch (SQLException e) {
            logger.error("Error saving seller search: {}", e.getMessage(), e);
        }

        return -1;
    }

    /**
     * Upsert a seller search to the database.
     * If a record with the same seller ID exists, update only the updated_date field.
     * If no record exists, create a new record with all information.
     *
     * @param model The seller search model to upsert
     * @return The ID of the saved/updated record, or -1 if an error occurred
     */
    public static int upsertSellerSearch(SellerSearchModel model) {
        // First, check if a record with this seller ID already exists
        SellerSearchModel existingRecord = getSellerSearchBySellerId(model.getSellerId());

        if (existingRecord != null) {
            // Record exists, update only the updated_date field
            String updateSql = "UPDATE seller_searches SET updated_date = ? WHERE seller_id = ?";

            try (Connection conn = DatabaseConnectionManager.getConnection();
                 PreparedStatement pstmt = conn.prepareStatement(updateSql)) {

                pstmt.setString(1, DatabaseDateTimeUtil.getDateFormat().format(model.getUpdatedDate()));
                pstmt.setString(2, model.getSellerId());

                int affectedRows = pstmt.executeUpdate();
                if (affectedRows > 0) {
                    logger.info("セラーID: {} のセラー検索の更新日時を正常に更新しました", model.getSellerId());
                    return existingRecord.getId();
                }

            } catch (SQLException e) {
                logger.error("Error updating seller search: {}", e.getMessage(), e);
            }
        } else {
            // Record doesn't exist, create a new one
            return saveSellerSearch(model);
        }

        return -1;
    }

    /**
     * Update an existing seller search in the database.
     *
     * @param model The seller search model to update
     * @return true if the update was successful, false otherwise
     */
    public static boolean updateSellerSearch(SellerSearchModel model) {
        String sql = "UPDATE seller_searches SET seller_name = ?, seller_name_changed = ?, " +
                "last_researched_at = ?, updated_date = ? WHERE id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, model.getSellerName());
            pstmt.setInt(2, model.isSellerNameChanged() ? 1 : 0);
            pstmt.setString(3, DatabaseDateTimeUtil.getDateFormat().format(model.getLastResearchedAt()));
            pstmt.setString(4, DatabaseDateTimeUtil.getDateFormat().format(model.getUpdatedDate()));
            pstmt.setInt(5, model.getId());

            int affectedRows = pstmt.executeUpdate();
            logger.info("{} 件のセラー検索レコードを更新しました", affectedRows);
            return affectedRows > 0;

        } catch (SQLException e) {
            logger.error("Error updating seller search: {}", e.getMessage(), e);
        }

        return false;
    }

    /**
     * Get a seller search by ID.
     *
     * @param id The ID of the seller search
     * @return The seller search model, or null if not found
     */
    public static SellerSearchModel getSellerSearchById(int id) {
        String sql = "SELECT * FROM seller_searches WHERE id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, id);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return createSellerSearchFromResultSet(rs);
                }
            }

        } catch (SQLException e) {
            logger.error("Error retrieving seller search by ID: {}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * Get a seller search by seller ID.
     *
     * @param sellerId The seller ID
     * @return The seller search model, or null if not found
     */
    public static SellerSearchModel getSellerSearchBySellerId(String sellerId) {
        String sql = "SELECT * FROM seller_searches WHERE seller_id = ? ORDER BY last_researched_at DESC LIMIT 1";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, sellerId);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return createSellerSearchFromResultSet(rs);
                }
            }

        } catch (SQLException e) {
            logger.error("Error retrieving seller search by seller ID: {}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * Get a list of seller searches with pagination.
     *
     * @param page     The page number (1-based)
     * @param pageSize The number of records per page
     * @return A list of seller search models
     */
    public static List<SellerSearchModel> getSellerSearches(int page, int pageSize) {
        List<SellerSearchModel> searches = new ArrayList<>();

        String sql = "SELECT * FROM seller_searches ORDER BY last_researched_at DESC LIMIT ? OFFSET ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, pageSize);
            pstmt.setInt(2, (page - 1) * pageSize);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    searches.add(createSellerSearchFromResultSet(rs));
                }
            }

        } catch (SQLException e) {
            logger.error("Error retrieving seller searches: {}", e.getMessage(), e);
        }

        return searches;
    }

    /**
     * Get the total count of seller searches.
     *
     * @return The total count of seller searches
     */
    public static int getTotalSellerSearchCount() {
        String sql = "SELECT COUNT(*) FROM seller_searches";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            if (rs.next()) {
                return rs.getInt(1);
            }

        } catch (SQLException e) {
            logger.error("Error getting seller search count: {}", e.getMessage(), e);
        }

        return 0;
    }

    /**
     * Update seller name by seller ID.
     *
     * @param sellerId   The seller ID
     * @param sellerName The new seller name
     * @return true if the update was successful, false otherwise
     */
    public static boolean updateSellerNameBySellerId(String sellerId, String sellerName) {
        String sql = "UPDATE seller_searches SET seller_name = ?, seller_name_changed = 1, " +
                "updated_date = ? WHERE seller_id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, sellerName);
            pstmt.setString(2, DatabaseDateTimeUtil.formatJapaneseTimezone(new Date()));
            pstmt.setString(3, sellerId);

            int affectedRows = pstmt.executeUpdate();
            logger.info("セラーID: {} のセラー検索レコード {} 件のセラー名を更新しました", sellerId, affectedRows);
            return affectedRows > 0;

        } catch (SQLException e) {
            logger.error("Error updating seller name by seller ID: {}", e.getMessage(), e);
        }

        return false;
    }

    /**
     * Delete a seller search by ID.
     *
     * @param id The ID of the seller search to delete
     * @return true if the deletion was successful, false otherwise
     */
    public static boolean deleteSellerSearch(int id) {
        String sql = "DELETE FROM seller_searches WHERE id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, id);

            int affectedRows = pstmt.executeUpdate();
            logger.info("{} 件のセラー検索レコードを削除しました", affectedRows);
            return affectedRows > 0;

        } catch (SQLException e) {
            logger.error("Error deleting seller search: {}", e.getMessage(), e);
        }

        return false;
    }

    /**
     * Create a SellerSearchModel from a ResultSet.
     *
     * @param rs The ResultSet
     * @return The SellerSearchModel
     * @throws SQLException If there's an error reading from the ResultSet
     */
    private static SellerSearchModel createSellerSearchFromResultSet(ResultSet rs) throws SQLException {
        SellerSearchModel model = new SellerSearchModel();
        model.setId(rs.getInt("id"));
        model.setSellerId(rs.getString("seller_id"));

        String sellerName = rs.getString("seller_name");
        // Use the seller name directly from the database to avoid circular reference
        // The SellerManagementService will handle name resolution at a higher level
        model.setSellerName(sellerName);

        model.setSellerNameChanged(rs.getInt("seller_name_changed") == 1);

        try {
            String lastResearchedAtStr = rs.getString("last_researched_at");
            if (lastResearchedAtStr != null && !lastResearchedAtStr.trim().isEmpty()) {
                model.setLastResearchedAt(parseDateFromDatabase(lastResearchedAtStr));
            }

            String addedDateStr = rs.getString("added_date");
            if (addedDateStr != null && !addedDateStr.trim().isEmpty()) {
                model.setAddedDate(parseDateFromDatabase(addedDateStr));
            }

            String updatedDateStr = rs.getString("updated_date");
            if (updatedDateStr != null && !updatedDateStr.trim().isEmpty()) {
                model.setUpdatedDate(parseDateFromDatabase(updatedDateStr));
            }
        } catch (Exception e) {
            logger.error("Error parsing dates in seller search: {}", e.getMessage(), e);
            // Set current date as fallback
            Date now = new Date();
            if (model.getLastResearchedAt() == null) model.setLastResearchedAt(now);
            if (model.getAddedDate() == null) model.setAddedDate(now);
            if (model.getUpdatedDate() == null) model.setUpdatedDate(now);
        }

        return model;
    }

    /**
     * Format a Date object for database storage with null safety.
     * Uses detailed format with milliseconds for precise timestamp storage.
     *
     * @param date The Date object to format
     * @return Formatted detailed date string, or current timestamp if date is null
     */
    private static String formatDateForDatabase(Date date) {
        if (date == null) {
            date = new Date(); // Use current time if null
        }

        try {
            // Use detailed Japanese timezone formatting with milliseconds
            return DatabaseDateTimeUtil.formatDetailedJapaneseTimezone(date);
        } catch (Exception e) {
            logger.error("Error formatting detailed date for database: {}", e.getMessage());
            try {
                // Fallback to standard Japanese timezone format
                return DatabaseDateTimeUtil.formatJapaneseTimezone(date);
            } catch (Exception fallbackError) {
                logger.error("Fallback formatting also failed: {}", fallbackError.getMessage());
                // Final fallback to simple format
                return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(date);
            }
        }
    }

    /**
     * Parse a date string from database with improved error handling.
     * Supports multiple date formats including detailed format with milliseconds.
     *
     * @param dateStr The date string from database
     * @return Parsed Date object, or current date if parsing fails
     */
    private static Date parseDateFromDatabase(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return new Date();
        }

        try {
            // Try parsing with automatic format detection first
            Date parsed = DatabaseDateTimeUtil.parseAnyDateFormat(dateStr);
            if (parsed != null) {
                return parsed;
            }

            // Fallback to detailed format parsing
            parsed = DatabaseDateTimeUtil.parseDetailedDate(dateStr);
            if (parsed != null) {
                return parsed;
            }

            // Fallback to standard format parsing
            parsed = DatabaseDateTimeUtil.parseDate(dateStr);
            if (parsed != null) {
                return parsed;
            }

            // Final fallback to simple parsing
            return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").parse(dateStr);
        } catch (Exception e) {
            logger.error("Error parsing date from database: {} - {}", dateStr, e.getMessage());
            return new Date(); // Return current date as fallback
        }
    }
}