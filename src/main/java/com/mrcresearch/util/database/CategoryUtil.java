package com.mrcresearch.util.database;

/**
 * Utility class for category name resolution and conversion.
 * Handles category ID to name conversion and display formatting.
 */
public class CategoryUtil {

    /**
     * Private constructor to prevent instantiation.
     * This is a utility class with static methods only.
     */
    private CategoryUtil() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * カテゴリーIDからカテゴリー名を取得する
     * Categoriesクラスを使用してCSVファイルから正確なカテゴリー名を取得
     *
     * @param categoryId カテゴリーID（文字列）
     * @return カテゴリー名（見つからない場合は"カテゴリID: [ID]"形式でフォールバック表示）
     */
    public static String getCategoryNameById(String categoryId) {
        if (categoryId == null || categoryId.trim().isEmpty() || categoryId.equals("0")) {
            return "カテゴリなし";
        }

        // Categoriesクラスのインスタンスを作成して初期化
        com.mrcresearch.screen.component.category.Categories categories =
                new com.mrcresearch.screen.component.category.Categories();
        categories.init();

        // カテゴリー名を取得
        String categoryName = categories.getCategoryNameById(categoryId.trim());
        if (categoryName != null) {
            return categoryName;
        } else {
            // カテゴリー名が見つからない場合はフォールバック表示
            return "カテゴリなし";
        }
    }

    /**
     * カテゴリー情報をカテゴリー名に変換する
     * Categoriesクラスを使用してCSVファイルから正確なカテゴリー名を取得
     *
     * @param categoryInfo カテゴリー情報（IDまたは名前）
     * @return カテゴリー名（見つからない場合は元の情報をフォールバック表示）
     */
    public static String convertToDisplayCategoryName(String categoryInfo) {
        if (categoryInfo == null || categoryInfo.trim().isEmpty()) {
            return "";
        }

        // Categoriesクラスのインスタンスを作成して初期化
        com.mrcresearch.screen.component.category.Categories categories =
                new com.mrcresearch.screen.component.category.Categories();
        categories.init();

        // 既にカテゴリー名の場合はそのまま返す
        if (categories.isCategoryName(categoryInfo)) {
            return categoryInfo;
        }

        // カテゴリーIDの場合は名前に変換
        return categories.convertCategoryIdsToNames(categoryInfo);
    }

    /**
     * カテゴリー名からカテゴリーIDを取得する
     *
     * @param categoryName カテゴリー名
     * @return カテゴリーID（見つからない場合は-1）
     */
    public static String getCategoryIdByName(String categoryName) {
        // カテゴリー1>カテゴリー2>カテゴリー3,カテゴリー4の場合、カテゴリー3,カテゴリー4を返却する
        if (categoryName == null || categoryName.trim().isEmpty()) {
            return "-1";
        }

        // Categoriesクラスのインスタンスを作成して初期化
        com.mrcresearch.screen.component.category.Categories categories =
                new com.mrcresearch.screen.component.category.Categories();
        categories.init();

        String processedCategoryName = categoryName.trim();
        String parentId = "0"; // ルートカテゴリーのID

        // ">" で区切られている場合、階層を順番に辿って親IDを特定する
        if (processedCategoryName.contains(">")) {
            String[] parts = processedCategoryName.split(">");

            // 各階層を順番に辿る
            for (int i = 0; i < parts.length - 1; i++) {
                String parentName = parts[i].trim();
                if (!parentName.isEmpty()) {
                    String foundParentId = getCategoryIdByNameWithParent(categories, parentName, parentId);
                    if (foundParentId.equals("-1")) {
                        return "-1"; // 親カテゴリーが見つからない場合
                    }
                    parentId = foundParentId;
                }
            }

            // 最後の部分を取得
            if (parts.length > 0) {
                processedCategoryName = parts[parts.length - 1].trim();
            }
        }

        // カンマ区切りの場合は複数のカテゴリー名をIDに変換
        if (processedCategoryName.contains(",")) {
            String[] categoryNames = processedCategoryName.split(",");
            java.util.List<String> categoryIds = new java.util.ArrayList<>();

            for (String name : categoryNames) {
                String trimmedName = name.trim();
                if (!trimmedName.isEmpty()) {
                    String categoryId = getCategoryIdByNameWithParent(categories, trimmedName, parentId);
                    if (!categoryId.equals("-1")) {
                        categoryIds.add(categoryId);
                    }
                }
            }

            return categoryIds.isEmpty() ? "-1" : String.join(",", categoryIds);
        } else {
            // 単一のカテゴリー名の場合
            return getCategoryIdByNameWithParent(categories, processedCategoryName, parentId);
        }
    }

    /**
     * 指定された親IDを持つカテゴリー名からカテゴリーIDを取得するヘルパーメソッド
     *
     * @param categories   Categories インスタンス
     * @param categoryName カテゴリー名
     * @param parentId     親カテゴリーID
     * @return カテゴリーID（見つからない場合は-1）
     */
    private static String getCategoryIdByNameWithParent(com.mrcresearch.screen.component.category.Categories categories, String categoryName, String parentId) {
        if (categoryName == null || categoryName.trim().isEmpty()) {
            return "-1";
        }

        // Categoriesクラスのcategoryリストから名前と親IDでIDを検索
        try {
            java.lang.reflect.Field categoryListField = categories.getClass().getDeclaredField("categoryList");
            categoryListField.setAccessible(true);
            @SuppressWarnings("unchecked")
            java.util.List<Object> categoryList = (java.util.List<Object>) categoryListField.get(categories);

            for (Object category : categoryList) {
                java.lang.reflect.Method getNameMethod = category.getClass().getMethod("getName");
                java.lang.reflect.Method getIdMethod = category.getClass().getMethod("getId");
                java.lang.reflect.Method getParentIdMethod = category.getClass().getMethod("getParentId");

                String name = (String) getNameMethod.invoke(category);
                String id = (String) getIdMethod.invoke(category);
                String categoryParentId = (String) getParentIdMethod.invoke(category);

                // 名前と親IDが一致するカテゴリーを探す
                // parentIdがnullの場合は名前のみで検索（従来の動作）
                if (categoryName.equals(name) && (parentId == null || parentId.equals(categoryParentId))) {
                    return id;
                }
            }
        } catch (Exception e) {
            // リフレクションに失敗した場合は-1を返す
            return "-1";
        }

        return "-1";
    }

    /**
     * 単一のカテゴリー名からカテゴリーIDを取得するヘルパーメソッド（互換性のために残す）
     *
     * @param categories   Categories インスタンス
     * @param categoryName カテゴリー名
     * @return カテゴリーID（見つからない場合は-1）
     */
    private static String getCategoryIdByNameSingle(com.mrcresearch.screen.component.category.Categories categories, String categoryName) {
        // 親IDを指定せずに最初に見つかったものを返す（従来の動作）
        return getCategoryIdByNameWithParent(categories, categoryName, null);
    }
}
