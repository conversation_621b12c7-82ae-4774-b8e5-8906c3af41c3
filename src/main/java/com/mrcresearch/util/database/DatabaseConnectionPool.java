package com.mrcresearch.util.database;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;

import javax.sql.DataSource;
import java.io.File;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;

/**
 * HikariCPを使用したデータベースコネクションプールの管理クラス
 * <p>
 * 主な機能:
 * - 高性能なコネクションプールの提供
 * - 自動的なコネクション管理とリーク検出
 * - プール状態の監視とメトリクス収集
 * - SQLite特有の設定最適化
 */
public class DatabaseConnectionPool {
    private static final Logger logger = Logger.getLogger(DatabaseConnectionPool.class.getName());

    private static HikariDataSource dataSource;
    private static volatile boolean initialized = false;
    private static final Object initLock = new Object();

    // プール設定パラメータ
    private static final int MAXIMUM_POOL_SIZE = 10;
    private static final int MINIMUM_IDLE = 2;
    private static final long CONNECTION_TIMEOUT_MS = 30000; // 30秒
    private static final long IDLE_TIMEOUT_MS = 600000; // 10分
    private static final long MAX_LIFETIME_MS = 1800000; // 30分
    private static final long LEAK_DETECTION_THRESHOLD_MS = 60000; // 60秒

    /**
     * プライベートコンストラクタ（ユーティリティクラスのため）
     */
    private DatabaseConnectionPool() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * コネクションプールを初期化する
     *
     * @param databasePath データベースファイルのパス
     */
    public static void initialize(String databasePath) {
        if (initialized) {
            logger.info("コネクションプールは既に初期化済みです");
            return;
        }

        synchronized (initLock) {
            if (initialized) {
                return;
            }

            try {
                logger.info("データベースコネクションプールを初期化中...");

                // HikariCP設定
                HikariConfig config = new HikariConfig();

                // データベース接続設定
                config.setJdbcUrl("jdbc:sqlite:" + databasePath);
                config.setDriverClassName("org.sqlite.JDBC");

                // プール設定
                config.setMaximumPoolSize(MAXIMUM_POOL_SIZE);
                config.setMinimumIdle(MINIMUM_IDLE);
                config.setConnectionTimeout(CONNECTION_TIMEOUT_MS);
                config.setIdleTimeout(IDLE_TIMEOUT_MS);
                config.setMaxLifetime(MAX_LIFETIME_MS);
                config.setLeakDetectionThreshold(LEAK_DETECTION_THRESHOLD_MS);

                // プール名の設定（監視用）
                config.setPoolName("MercariResearchDB-Pool");

                // SQLite特有の設定
                config.addDataSourceProperty("cachePrepStmts", "true");
                config.addDataSourceProperty("prepStmtCacheSize", "250");
                config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");

                // SQLite WALモードの設定（パフォーマンス向上）
                config.setConnectionInitSql("PRAGMA journal_mode=WAL; PRAGMA synchronous=NORMAL; PRAGMA temp_store=MEMORY; PRAGMA mmap_size=268435456;");

                // 接続テスト設定
                config.setConnectionTestQuery("SELECT 1");
                config.setValidationTimeout(5000);

                // データソースの作成
                dataSource = new HikariDataSource(config);

                // 初期化完了
                initialized = true;

                logger.info(String.format(
                        "データベースコネクションプール初期化完了 - プール名: %s, 最大接続数: %d, 最小アイドル: %d",
                        config.getPoolName(), MAXIMUM_POOL_SIZE, MINIMUM_IDLE
                ));

                // 初期接続テスト
                testInitialConnection();

                // JVM終了時のクリーンアップ
                Runtime.getRuntime().addShutdownHook(new Thread(DatabaseConnectionPool::shutdown));

            } catch (Exception e) {
                logger.severe("コネクションプールの初期化に失敗しました: " + e.getMessage());
                e.printStackTrace();
                throw new RuntimeException("Database connection pool initialization failed", e);
            }
        }
    }

    /**
     * デフォルトのデータベースパスでコネクションプールを初期化する
     */
    public static void initializeDefault() {
        String userHome = System.getProperty("user.home");
        String separator = System.getProperty("file.separator");
        String dbDir = userHome + separator + ".mrcresearch";
        String dbPath = dbDir + separator + "research.db";

        // ディレクトリが存在しない場合は作成
        File directory = new File(dbDir);
        if (!directory.exists()) {
            directory.mkdirs();
        }

        initialize(dbPath);
    }

    /**
     * 初期接続テストを実行する
     */
    private static void testInitialConnection() {
        try (Connection conn = getConnection()) {
            if (conn != null && !conn.isClosed()) {
                logger.info("初期データベース接続テスト成功");
            } else {
                throw new SQLException("接続が無効です");
            }
        } catch (SQLException e) {
            logger.severe("初期接続テストに失敗しました: " + e.getMessage());
            throw new RuntimeException("Initial connection test failed", e);
        }
    }

    /**
     * データベース接続を取得する
     *
     * @return データベース接続
     * @throws SQLException 接続取得に失敗した場合
     */
    public static Connection getConnection() throws SQLException {
        if (!initialized) {
            throw new SQLException("Connection pool is not initialized. Call initializeDefault() first.");
        }

        return dataSource.getConnection();
    }

    /**
     * DataSourceを取得する
     *
     * @return DataSource
     */
    public static DataSource getDataSource() {
        if (!initialized) {
            throw new IllegalStateException("Connection pool is not initialized. Call initializeDefault() first.");
        }

        return dataSource;
    }

    /**
     * コネクションプールの状態情報を取得する
     *
     * @return プール状態の文字列表現
     */
    public static String getPoolStatus() {
        if (!initialized || dataSource == null) {
            return "プールは初期化されていません";
        }

        HikariPoolMXBean poolBean = dataSource.getHikariPoolMXBean();

        return String.format(
                "プール状態 - アクティブ接続: %d, アイドル接続: %d, 総接続数: %d, 待機中スレッド: %d",
                poolBean.getActiveConnections(),
                poolBean.getIdleConnections(),
                poolBean.getTotalConnections(),
                poolBean.getThreadsAwaitingConnection()
        );
    }

    /**
     * 詳細なプールメトリクスを取得する
     *
     * @return プールメトリクス情報
     */
    public static String getDetailedMetrics() {
        if (!initialized || dataSource == null) {
            return "プールは初期化されていません";
        }

        HikariPoolMXBean poolBean = dataSource.getHikariPoolMXBean();

        StringBuilder metrics = new StringBuilder();
        metrics.append("=== HikariCP プールメトリクス ===\n");
        metrics.append(String.format("プール名: %s\n", dataSource.getPoolName()));
        metrics.append(String.format("アクティブ接続数: %d\n", poolBean.getActiveConnections()));
        metrics.append(String.format("アイドル接続数: %d\n", poolBean.getIdleConnections()));
        metrics.append(String.format("総接続数: %d\n", poolBean.getTotalConnections()));
        metrics.append(String.format("待機中スレッド数: %d\n", poolBean.getThreadsAwaitingConnection()));
        metrics.append(String.format("最大プールサイズ: %d\n", MAXIMUM_POOL_SIZE));
        metrics.append(String.format("最小アイドル数: %d\n", MINIMUM_IDLE));

        return metrics.toString();
    }

    /**
     * プール状態を監視し、警告が必要な場合はログに出力する
     */
    public static void monitorPoolHealth() {
        if (!initialized || dataSource == null) {
            return;
        }

        HikariPoolMXBean poolBean = dataSource.getHikariPoolMXBean();

        int activeConnections = poolBean.getActiveConnections();
        int totalConnections = poolBean.getTotalConnections();
        int threadsWaiting = poolBean.getThreadsAwaitingConnection();

        // プール枯渇の警告
        if (activeConnections >= MAXIMUM_POOL_SIZE * 0.9) {
            logger.warning(String.format(
                    "コネクションプール使用率が高くなっています: %d/%d (%.1f%%)",
                    activeConnections, MAXIMUM_POOL_SIZE,
                    (double) activeConnections / MAXIMUM_POOL_SIZE * 100
            ));
        }

        // 待機スレッドの警告
        if (threadsWaiting > 0) {
            logger.warning(String.format(
                    "接続待機中のスレッドがあります: %d threads - プールサイズの調整を検討してください",
                    threadsWaiting
            ));
        }

        // 定期的な状態ログ出力（デバッグ用）
        logger.fine(getPoolStatus());
    }

    /**
     * コネクションプールをシャットダウンする
     */
    public static void shutdown() {
        if (initialized && dataSource != null) {
            logger.info("データベースコネクションプールをシャットダウン中...");

            try {
                // グレースフルシャットダウン
                dataSource.close();
                logger.info("コネクションプールのシャットダウンが完了しました");
            } catch (Exception e) {
                logger.severe("コネクションプールのシャットダウン中にエラーが発生しました: " + e.getMessage());
                e.printStackTrace();
            } finally {
                dataSource = null;
                initialized = false;
            }
        }
    }

    /**
     * プールが初期化されているかチェックする
     *
     * @return 初期化済みの場合true
     */
    public static boolean isInitialized() {
        return initialized;
    }

    /**
     * プール設定情報を取得する
     *
     * @return 設定情報の文字列
     */
    public static String getConfigurationInfo() {
        StringBuilder config = new StringBuilder();
        config.append("=== HikariCP プール設定 ===\n");
        config.append(String.format("最大プールサイズ: %d\n", MAXIMUM_POOL_SIZE));
        config.append(String.format("最小アイドル数: %d\n", MINIMUM_IDLE));
        config.append(String.format("接続タイムアウト: %d ms\n", CONNECTION_TIMEOUT_MS));
        config.append(String.format("アイドルタイムアウト: %d ms\n", IDLE_TIMEOUT_MS));
        config.append(String.format("最大生存時間: %d ms\n", MAX_LIFETIME_MS));
        config.append(String.format("リーク検出閾値: %d ms\n", LEAK_DETECTION_THRESHOLD_MS));

        return config.toString();
    }
}