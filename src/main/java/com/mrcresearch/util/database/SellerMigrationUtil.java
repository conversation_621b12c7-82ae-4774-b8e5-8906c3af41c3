package com.mrcresearch.util.database;

import com.mrcresearch.service.SellerManagementService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Utility class for migrating seller data to the new sellers table
 * This class provides methods to migrate existing seller information from various tables
 * to the new unified sellers master table
 */
public class SellerMigrationUtil {

    private static final Logger logger = LoggerFactory.getLogger(SellerMigrationUtil.class);

    /**
     * Migrate all seller data from existing tables to the new sellers table
     * This method should be called once during application startup to ensure data consistency
     *
     * @return The total number of sellers migrated
     */
    public static int migrateAllSellerData() {
        logger.info("セラーデータ移行を開始します...");

        int totalMigrated = 0;

        try {
            // Migrate from seller_searches table
            int fromSellerSearches = SellerManagementService.migrateFromSellerSearches(100);
            totalMigrated += fromSellerSearches;
            logger.info("seller_searches テーブルから {} 件のセラーを移行しました", fromSellerSearches);

            // Synchronize any remaining data
            int sync = SellerManagementService.synchronizeSellerInformation();
            logger.info("{} 件のセラーレコードを同期しました", sync);

            logger.info("セラーデータ移行が完了しました。総移行数: {}", totalMigrated);

        } catch (Exception e) {
            logger.error("Error during seller data migration: {}", e.getMessage(), e);
        }

        return totalMigrated;
    }

    /**
     * Check if migration is needed
     * Returns true if the sellers table is empty or has significantly fewer records than expected
     *
     * @return true if migration is needed, false otherwise
     */
    public static boolean isMigrationNeeded() {
        try {
            int sellersCount = SellerRepository.getTotalSellerCount();
            int sellerSearchesCount = getTotalSellerSearchesCount();

            // If sellers table is empty or has less than 50% of seller_searches records, migration is needed
            return sellersCount == 0 || (sellerSearchesCount > 0 && sellersCount < sellerSearchesCount * 0.5);

        } catch (Exception e) {
            logger.error("Error checking migration status: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Get the total count of records in seller_searches table
     *
     * @return The total number of seller_searches records
     */
    private static int getTotalSellerSearchesCount() {
        try {
            return SellerSearchRepository.getTotalSellerSearchCount();
        } catch (Exception e) {
            logger.error("Error getting seller_searches count: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * Perform a quick migration check and migrate if needed
     * This method is designed to be called during application startup
     *
     * @return true if migration was performed, false if not needed
     */
    public static boolean checkAndMigrateIfNeeded() {
        if (isMigrationNeeded()) {
            logger.info("セラーデータ移行が必要です。移行を開始します...");
            int migrated = migrateAllSellerData();
            logger.info("移行が完了しました。{} 件のセラーが移行されました。", migrated);
            return true;
        } else {
            logger.info("セラーデータ移行は不要です。");
            return false;
        }
    }

    /**
     * Force a complete re-migration of all seller data
     * This method clears the sellers table and re-migrates all data
     * Use with caution as it will overwrite existing data in the sellers table
     *
     * @return The number of sellers migrated
     */
    public static int forceMigration() {
        logger.info("強制セラーデータ移行を開始します...");

        try {
            // Clear existing sellers table
            clearSellersTable();

            // Perform migration
            return migrateAllSellerData();

        } catch (Exception e) {
            logger.error("Error during forced migration: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * Clear all data from the sellers table
     * Use with caution as this will delete all seller master data
     */
    private static void clearSellersTable() {
        try {
            java.sql.Connection conn = DatabaseConnectionManager.getConnection();
            java.sql.Statement stmt = conn.createStatement();

            stmt.execute("DELETE FROM sellers");

            stmt.close();
            conn.close();

            logger.info("セラーテーブルをクリアしました。");

        } catch (Exception e) {
            logger.error("Error clearing sellers table: {}", e.getMessage(), e);
        }
    }

    /**
     * Get migration statistics
     *
     * @return A string containing migration statistics
     */
    public static String getMigrationStatistics() {
        try {
            int sellersCount = SellerRepository.getTotalSellerCount();
            int sellerSearchesCount = getTotalSellerSearchesCount();
            int sellersWithNameChanges = SellerRepository.getSellersWithNameChanges(1, Integer.MAX_VALUE).size();

            String stats = "Seller Migration Statistics:\n" +
                    "- Sellers table records: " + sellersCount + "\n" +
                    "- Seller searches records: " + sellerSearchesCount + "\n" +
                    "- Sellers with name changes: " + sellersWithNameChanges + "\n" +
                    "- Migration coverage: " +
                    (sellerSearchesCount > 0 ? String.format("%.1f%%", (sellersCount * 100.0 / sellerSearchesCount)) : "N/A") +
                    "\n";

            return stats;

        } catch (Exception e) {
            return "Error getting migration statistics: " + e.getMessage();
        }
    }

    /**
     * Validate the integrity of migrated data
     *
     * @return true if data integrity is good, false if issues are found
     */
    public static boolean validateMigrationIntegrity() {
        try {
            // Check for sellers with null or empty seller_id
            int invalidSellers = countInvalidSellers();

            if (invalidSellers > 0) {
                logger.error("Found {} sellers with invalid seller_id", invalidSellers);
                return false;
            }

            // Check for duplicate seller_ids (should not happen due to PRIMARY KEY constraint)
            // This is more of a sanity check

            logger.info("移行整合性検証に合格しました。");
            return true;

        } catch (Exception e) {
            logger.error("Error validating migration integrity: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Count sellers with invalid data
     *
     * @return The number of sellers with invalid data
     */
    private static int countInvalidSellers() {
        try {
            java.sql.Connection conn = DatabaseConnectionManager.getConnection();
            java.sql.PreparedStatement pstmt = conn.prepareStatement(
                    "SELECT COUNT(*) FROM sellers WHERE seller_id IS NULL OR seller_id = ''"
            );

            java.sql.ResultSet rs = pstmt.executeQuery();
            int count = 0;
            if (rs.next()) {
                count = rs.getInt(1);
            }

            rs.close();
            pstmt.close();
            conn.close();

            return count;

        } catch (Exception e) {
            logger.error("Error counting invalid sellers: {}", e.getMessage(), e);
            return -1;
        }
    }
}
