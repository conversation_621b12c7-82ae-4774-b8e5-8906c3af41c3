package com.mrcresearch.util.database;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Base64;

public class LoginInfoRepository {

    private static final String DB_URL = DatabaseConnectionManager.getDbUrl();

    public static boolean saveLoginInfo(String email, String password) {
        try (Connection conn = DatabaseConnectionManager.getConnection()) {
            // Check if login info already exists
            boolean hasLoginInfo = false;
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM login_info")) {
                if (rs.next()) {
                    hasLoginInfo = rs.getInt(1) > 0;
                }
            }

            // Encode the password using Base64
            String encodedPassword = Base64.getEncoder().encodeToString(password.getBytes(java.nio.charset.StandardCharsets.UTF_8));

            String sql;
            if (hasLoginInfo) {
                // Update existing login info
                sql = "UPDATE login_info SET email = ?, password_hash = ?";
            } else {
                // Insert new login info
                sql = "INSERT INTO login_info (email, password_hash) VALUES (?, ?)";
            }

            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, email);
                pstmt.setString(2, encodedPassword);

                int affectedRows = pstmt.executeUpdate();
                return affectedRows > 0;
            }
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    public static String[] loadLoginInfo() {
        try (Connection conn = DatabaseConnectionManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT * FROM login_info LIMIT 1")) {

            if (rs.next()) {
                String email = rs.getString("email");
                String passwordHash = rs.getString("password_hash");

                return new String[]{email, passwordHash};
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return null;
    }

    public static boolean verifyPassword(String password, String storedValue) {
        try {
            // Try to decode the stored value using Base64
            byte[] decodedBytes = Base64.getDecoder().decode(storedValue);
            String decodedPassword = new String(decodedBytes, java.nio.charset.StandardCharsets.UTF_8);

            // Compare with the provided password
            return password.equals(decodedPassword);
        } catch (Exception e) {
            // If Base64 decoding fails, fall back to hash comparison for backward compatibility
            return hashPassword(password).equals(storedValue);
        }
    }

    private static String hashPassword(String password) {
        // In a real application, you would use a proper password hashing library
        // For simplicity, we're using a basic hash function here
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(password.getBytes(java.nio.charset.StandardCharsets.UTF_8));

            // Convert byte array to hex string
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (java.security.NoSuchAlgorithmException e) {
            e.printStackTrace();
            // Fallback to a very basic hash if SHA-256 is not available
            return String.valueOf(password.hashCode());
        }
    }

    public static String getPassword() {
        try (Connection conn = DatabaseConnectionManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT password_hash FROM login_info LIMIT 1")) {

            if (rs.next()) {
                String passwordHash = rs.getString("password_hash");

                // Try to decode using Base64
                try {
                    byte[] decodedBytes = Base64.getDecoder().decode(passwordHash);
                    return new String(decodedBytes, java.nio.charset.StandardCharsets.UTF_8);
                } catch (Exception e) {
                    // If Base64 decoding fails, fall back to hex conversion for backward compatibility
                }

                // Fall back to hex conversion if both Base64 and AES fail
                try {
                    StringBuilder result = new StringBuilder();
                    for (int i = 0; i < passwordHash.length(); i += 2) {
                        String str = passwordHash.substring(i, i + 2);
                        result.append((char) Integer.parseInt(str, 16));
                    }
                    return result.toString();
                } catch (Exception e) {
                    // If hex conversion fails, return the hash as is
                    return passwordHash;
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }
}
