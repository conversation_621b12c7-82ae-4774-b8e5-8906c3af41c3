package com.mrcresearch.util.database;

import java.sql.*;

import static com.mrcresearch.util.database.SettingsConstants.*;

/**
 * Repository class for application settings related database operations.
 * Handles saving and loading of application configuration settings.
 */
public class SettingsRepository {

    /**
     * Private constructor to prevent instantiation.
     * This is a utility class with static methods only.
     */
    private SettingsRepository() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * Save settings to the database.
     * If settings already exist, they will be updated.
     *
     * @param soldDate                    The sold date setting (in days)
     * @param browserLoadTimeout          The browser load timeout setting (in seconds)
     * @param browserSleepTime            The browser sleep time setting (in seconds)
     * @param browserType                 The browser type setting (e.g., "Firefox", "Chrome")
     * @param defaultKeywordPages         The default number of pages for keyword search
     * @param oldItemDisplayCount         The number of old items to display
     * @param themeType                   The theme type setting ("light" or "dark")
     * @param headlessMode                The headless mode setting (true for headless, false for normal)
     * @param salesAggregationDefaultDays The default number of days for sales aggregation
     * @return true if the save was successful, false otherwise
     */
    public static boolean saveSettings(int soldDate, int browserLoadTimeout, int browserSleepTime, int browserInitSleepTime,
                                       String browserType, int defaultKeywordPages, int oldItemDisplayCount, String themeType, boolean headlessMode, int salesAggregationDefaultDays, String fontSize) {
        try (Connection conn = DatabaseConnectionManager.getConnection()) {
            // Check if settings already exist
            boolean hasSettings = false;
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM settings")) {
                if (rs.next()) {
                    hasSettings = rs.getInt(1) > 0;
                }
            }

            String sql;
            if (hasSettings) {
                // Update existing settings
                sql = "UPDATE settings SET sold_date = ?, browser_load_timeout = ?, " +
                        "browser_sleep_time = ?, browser_init_sleep_time = ?, browser_type = ?, default_keyword_pages = ?, old_item_display_count = ?, theme_type = ?, headless_mode = ?, sales_aggregation_default_days = ?, font_size = ?";
            } else {
                // Insert new settings
                sql = "INSERT INTO settings (sold_date, browser_load_timeout, browser_sleep_time, browser_init_sleep_time, " +
                        "browser_type, default_keyword_pages, old_item_display_count, theme_type, headless_mode, sales_aggregation_default_days, font_size) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            }

            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setInt(1, soldDate);
                pstmt.setInt(2, browserLoadTimeout);
                pstmt.setInt(3, browserSleepTime);
                pstmt.setInt(4, browserInitSleepTime);
                pstmt.setString(5, browserType);
                pstmt.setInt(6, defaultKeywordPages);
                pstmt.setInt(7, oldItemDisplayCount);
                pstmt.setString(8, themeType != null ? themeType : DEFAULT_THEME);
                pstmt.setBoolean(9, headlessMode);
                pstmt.setInt(10, salesAggregationDefaultDays);
                pstmt.setString(11, fontSize != null ? fontSize : "MEDIUM");

                int affectedRows = pstmt.executeUpdate();
                return affectedRows > 0;
            }
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Load settings from the database.
     *
     * @return An array of settings values in the order: [soldDate, browserLoadTimeout, browserSleepTime, browserInitSleepTime, browserType, defaultKeywordPages, oldItemDisplayCount, themeType, headlessMode, salesAggregationDefaultDays]
     * or null if settings could not be loaded
     */
    public static Object[] loadSettings() {
        try (Connection conn = DatabaseConnectionManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT * FROM settings LIMIT 1")) {

            if (rs.next()) {
                int soldDate = rs.getInt("sold_date");
                int browserLoadTimeout = rs.getInt("browser_load_timeout");
                int browserSleepTime = rs.getInt("browser_sleep_time");
                int browserInitSleepTime = rs.getInt("browser_init_sleep_time");
                String browserType = rs.getString("browser_type");
                int defaultKeywordPages = rs.getInt("default_keyword_pages");
                int oldItemDisplayCount = rs.getInt("old_item_display_count");
                String themeType = rs.getString("theme_type");
                if (themeType == null || themeType.isEmpty()) {
                    themeType = DEFAULT_THEME; // Default to light theme
                }
                boolean headlessMode = rs.getBoolean("headless_mode");
                int salesAggregationDefaultDays = rs.getInt("sales_aggregation_default_days");
                if (salesAggregationDefaultDays == 0) {
                    salesAggregationDefaultDays = DEFAULT_SALES_AGGREGATION_DAYS; // Default value
                }
                String fontSize = rs.getString("font_size");
                if (fontSize == null || fontSize.isEmpty()) {
                    fontSize = "MEDIUM";
                }

                return new Object[]{soldDate, browserLoadTimeout, browserSleepTime, browserInitSleepTime, browserType, defaultKeywordPages, oldItemDisplayCount, themeType, headlessMode, salesAggregationDefaultDays, fontSize};
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return null;
    }

    /**
     * Get the sold date setting from the database.
     *
     * @return The sold date setting (in days), or 30 as default if not found
     */
    public static int getSoldDate() {
        Object[] settings = loadSettings();
        if (settings != null && settings.length > 0) {
            return (Integer) settings[0];
        }
        return DEFAULT_SOLD_DATE; // Default value
    }

    /**
     * Get the browser load timeout setting from the database.
     *
     * @return The browser load timeout setting (in seconds), or 30 as default if not found
     */
    public static int getBrowserLoadTimeout() {
        Object[] settings = loadSettings();
        if (settings != null && settings.length > 1) {
            return (Integer) settings[1];
        }
        return DEFAULT_BROWSER_LOAD_TIMEOUT; // Default value
    }

    /**
     * Get the browser sleep time setting from the database.
     *
     * @return The browser sleep time setting (in seconds), or 3 as default if not found
     */
    public static int getBrowserSleepTime() {
        Object[] settings = loadSettings();
        if (settings != null && settings.length > 2) {
            return (Integer) settings[2];
        }
        return DEFAULT_BROWSER_SLEEP_TIME; // Default value
    }

    /**
     * Get the browser init sleep time setting from the database.
     *
     * @return The browser init sleep time setting (in seconds), or 2 as default if not found
     */
    public static int getBrowserInitSleepTime() {
        Object[] settings = loadSettings();
        if (settings != null && settings.length > 3) {
            return (Integer) settings[3];
        }
        return DEFAULT_BROWSER_INIT_SLEEP_TIME; // Default value
    }

    /**
     * Get the browser type setting from the database.
     *
     * @return The browser type setting (e.g., "Firefox", "Chrome"), or "Firefox" as default if not found
     */
    public static String getBrowserType() {
        Object[] settings = loadSettings();
        if (settings != null && settings.length > 4) {
            String browserType = (String) settings[4];
            return browserType != null ? browserType : DEFAULT_BROWSER_TYPE;
        }
        return DEFAULT_BROWSER_TYPE; // Default value
    }

    /**
     * Get the default keyword pages setting from the database.
     *
     * @return The default number of pages for keyword search, or DEFAULT_KEYWORD_PAGES as default if not found
     */
    public static int getDefaultKeywordPages() {
        Object[] settings = loadSettings();
        if (settings != null && settings.length > 5) {
            return (Integer) settings[5];
        }
        return DEFAULT_KEYWORD_PAGES; // Default value
    }

    /**
     * Get the old item display count setting from the database.
     *
     * @return The number of old items to display, or 10 as default if not found
     */
    public static int getOldItemDisplayCount() {
        Object[] settings = loadSettings();
        if (settings != null && settings.length > 6) {
            return (Integer) settings[6];
        }
        return DEFAULT_OLD_ITEM_DISPLAY_COUNT; // Default value
    }

    /**
     * Get the theme type setting from the database.
     *
     * @return The theme type setting ("light" or "dark"), or "light" as default if not found
     */
    public static String getThemeType() {
        Object[] settings = loadSettings();
        if (settings != null && settings.length > 7) {
            String themeType = (String) settings[7];
            return themeType != null && !themeType.isEmpty() ? themeType : DEFAULT_THEME;
        }
        return DEFAULT_THEME; // Default value
    }

    /**
     * Get the headless mode setting from the database.
     *
     * @return The headless mode setting (true for headless, false for normal), or false as default if not found
     */
    public static boolean getHeadlessMode() {
        Object[] settings = loadSettings();
        if (settings != null && settings.length > 8) {
            return (Boolean) settings[8];
        }
        return DEFAULT_HEADLESS_MODE; // Default value
    }

    /**
     * Get the sales aggregation default days setting from the database.
     *
     * @return The default number of days for sales aggregation, or 7 as default if not found
     */
    public static int getSalesAggregationDefaultDays() {
        Object[] settings = loadSettings();
        if (settings != null && settings.length > 9) {
            return (Integer) settings[9];
        }
        return DEFAULT_SALES_AGGREGATION_DAYS; // Default value
    }

    /**
     * Get the font size setting from the database.
     *
     * @return The font size setting (e.g., "SMALL", "MEDIUM", "LARGE"), or "MEDIUM" as default if not found
     */
    public static String getFontSize() {
        Object[] settings = loadSettings();
        if (settings != null && settings.length > 10) {
            String fontSize = (String) settings[10];
            return fontSize != null && !fontSize.isEmpty() ? fontSize : "MEDIUM";
        }
        return "MEDIUM"; // Default value
    }

    /**
     * Save only the font size setting to the database.
     *
     * @param fontSize The font size setting to save
     * @return true if the save was successful, false otherwise
     */
    public static boolean saveFontSize(String fontSize) {
        try (Connection conn = DatabaseConnectionManager.getConnection()) {
            String sql = "UPDATE settings SET font_size = ?";
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, fontSize != null ? fontSize : "MEDIUM");
                int affectedRows = pstmt.executeUpdate();
                return affectedRows > 0;
            }
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
}