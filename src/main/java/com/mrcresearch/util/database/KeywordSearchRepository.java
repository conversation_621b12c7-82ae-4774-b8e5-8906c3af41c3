package com.mrcresearch.util.database;

import com.mrcresearch.screen.model.KeywordSearchResultModel;
import com.mrcresearch.service.enums.ItemStatusEnum;
import com.mrcresearch.service.models.item.Item;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Repository class for keyword search related database operations.
 * Handles CRUD operations for keyword search results and related data.
 */
public class KeywordSearchRepository {

    private static final Logger logger = LoggerFactory.getLogger(KeywordSearchRepository.class);

    /**
     * Private constructor to prevent instantiation.
     * This is a utility class with static methods only.
     */
    private KeywordSearchRepository() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * Save a keyword search result to the database.
     *
     * @param model The keyword search result model to save
     * @return The ID of the saved record, or -1 if an error occurred
     */
    public static int saveKeywordSearchResult(KeywordSearchResultModel model) {
        String sql = "INSERT INTO keyword_search_results (keyword, categories, added_date, updated_date, status, research_status, min_price, max_price, pages, sort_by_new, ignore_shops, sales_status, item_condition_ids, exclude_keyword, color_ids, category_id) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

            pstmt.setString(1, model.getKeyword());
            pstmt.setString(2, model.getCategories());
            pstmt.setString(3, DatabaseDateTimeUtil.getDateFormat().format(model.getAddedDate()));
            pstmt.setString(4, DatabaseDateTimeUtil.getDateFormat().format(model.getUpdatedDate()));
            pstmt.setString(5, model.getStatus());
            pstmt.setString(6, model.getResearchStatus());
            pstmt.setInt(7, model.getMinPrice());
            pstmt.setInt(8, model.getMaxPrice());
            pstmt.setInt(9, model.getPages());
            pstmt.setInt(10, model.isSortByNew() ? 1 : 0);
            pstmt.setInt(11, model.isIgnoreShops() ? 1 : 0);
            pstmt.setString(12, model.getSalesStatus());
            pstmt.setString(13, model.getItemConditionIds());
            pstmt.setString(14, model.getExcludeKeyword());
            pstmt.setString(15, model.getColorIds());
            pstmt.setString(16, model.getCategoryId());

            int affectedRows = pstmt.executeUpdate();

            if (affectedRows > 0) {
                try (ResultSet rs = pstmt.getGeneratedKeys()) {
                    if (rs.next()) {
                        return rs.getInt(1);
                    }
                }
            }

        } catch (SQLException e) {
            final String errorMessage = "データベースへの保存に失敗しました。\n" +
                    "エラー: " + e.getMessage() + "\n" +
                    "SQLステート: " + e.getSQLState();
            logger.error(errorMessage, e);
            SwingUtilities.invokeLater(() ->
                    JOptionPane.showMessageDialog(null, errorMessage, "データベースエラー", JOptionPane.ERROR_MESSAGE)
            );
        }

        return -1;
    }

    /**
     * Save keyword search result items to both search_items and keyword_search_items tables.
     * Uses upsert functionality for search_items and saves references in keyword_search_items.
     *
     * @param keywordSearchResultId The ID of the keyword search result (can be null for standalone items)
     * @param items                 The list of items to save
     * @return The number of items successfully saved
     */
    public static int saveKeywordSearchItems(int keywordSearchResultId, List<Item> items) {
        if (items == null || items.isEmpty()) {
            logger.info("キーワード検索結果ID: {} に保存するアイテムがありません", keywordSearchResultId);
            return 0;
        }

        // Save items to search_items table with upsert functionality
        int searchItemsSavedCount = SearchItemRepository.saveSearchItems(items);

        // Save references to keyword_search_items table
        List<String> itemIds = new ArrayList<>();
        int status = ItemStatusEnum.ON_SALE.getId(); // Default status

        for (Item item : items) {
            itemIds.add(item.getId());
            // Use the actual status from the item if available
            if (item.getStatus() != null) {
                status = ItemStatusEnum.getIdFromStatus(item.getStatus());
            }
        }

        int keywordSearchItemsSavedCount = KeywordSearchItemRepository.saveKeywordSearchItemReferences(itemIds, status, keywordSearchResultId);

        logger.info("キーワード検索結果ID: {} の search_items テーブルに {} 件のアイテム、keyword_search_items テーブルに {} 件の参照を正常に保存しました", keywordSearchResultId, searchItemsSavedCount, keywordSearchItemsSavedCount);

        return Math.min(searchItemsSavedCount, keywordSearchItemsSavedCount);
    }

    /**
     * Update an existing keyword search result in the database.
     *
     * @param model The keyword search result model to update
     * @return true if the update was successful, false otherwise
     */
    public static boolean updateKeywordSearchResult(KeywordSearchResultModel model) {
        String sql = "UPDATE keyword_search_results SET keyword = ?, categories = ?, " +
                "updated_date = ?, status = ?, research_status = ?, min_price = ?, max_price = ?, pages = ?, " +
                "sort_by_new = ?, ignore_shops = ?, sales_status = ?, item_condition_ids = ?, exclude_keyword = ?, color_ids = ? WHERE id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, model.getKeyword());
            pstmt.setString(2, model.getCategories());
            pstmt.setString(3, DatabaseDateTimeUtil.getDateFormat().format(model.getUpdatedDate()));
            pstmt.setString(4, model.getStatus());
            pstmt.setString(5, model.getResearchStatus());
            pstmt.setInt(6, model.getMinPrice());
            pstmt.setInt(7, model.getMaxPrice());
            pstmt.setInt(8, model.getPages());
            pstmt.setInt(9, model.isSortByNew() ? 1 : 0);
            pstmt.setInt(10, model.isIgnoreShops() ? 1 : 0);
            pstmt.setString(11, model.getSalesStatus());
            pstmt.setString(12, model.getItemConditionIds());
            pstmt.setString(13, model.getExcludeKeyword());
            pstmt.setString(14, model.getColorIds());
            pstmt.setInt(15, model.getId());

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;

        } catch (SQLException e) {
            logger.error("Error updating keyword search result", e);
            return false;
        }
    }

    /**
     * Get a list of keyword search results with pagination.
     *
     * @param page     The page number (1-based)
     * @param pageSize The number of records per page
     * @return A list of keyword search result models
     */
    public static List<KeywordSearchResultModel> getKeywordSearchResults(int page, int pageSize) {
        List<KeywordSearchResultModel> results = new ArrayList<>();

        String sql = "SELECT * FROM keyword_search_results ORDER BY updated_date DESC LIMIT ? OFFSET ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, pageSize);
            pstmt.setInt(2, (page - 1) * pageSize);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    KeywordSearchResultModel model = new KeywordSearchResultModel();
                    model.setId(rs.getInt("id"));
                    model.setKeyword(rs.getString("keyword"));
                    model.setCategories(rs.getString("categories"));
                    model.setAddedDate(DatabaseDateTimeUtil.parseDate(rs.getString("added_date")));
                    model.setUpdatedDate(DatabaseDateTimeUtil.parseDate(rs.getString("updated_date")));
                    model.setStatus(rs.getString("status"));
                    // Handle research_status (may be null in existing records)
                    String researchStatus = rs.getString("research_status");
                    if (researchStatus == null) {
                        // If research_status is null, use status as a fallback
                        model.setResearchStatus(rs.getString("status"));
                    } else {
                        model.setResearchStatus(researchStatus);
                    }

                    // Get the new fields (may be null in existing records)
                    try {
                        model.setMinPrice(rs.getInt("min_price"));
                        model.setMaxPrice(rs.getInt("max_price"));
                        model.setPages(rs.getInt("pages"));
                    } catch (SQLException e) {
                        // If columns don't exist yet, use default values
                        model.setMinPrice(0);
                        model.setMaxPrice(0);
                        model.setPages(0);
                    }

                    // Handle new search parameters (may be null in existing records)
                    try {
                        model.setSortByNew(rs.getInt("sort_by_new") == 1);
                    } catch (SQLException e) {
                        model.setSortByNew(false); // Default value for backward compatibility
                    }
                    try {
                        model.setIgnoreShops(rs.getInt("ignore_shops") == 1);
                    } catch (SQLException e) {
                        model.setIgnoreShops(false); // Default value for backward compatibility
                    }
                    try {
                        String salesStatus = rs.getString("sales_status");
                        model.setSalesStatus(salesStatus != null ? salesStatus : "すべて");
                    } catch (SQLException e) {
                        model.setSalesStatus("すべて"); // Default value for backward compatibility
                    }

                    // Handle detailed search parameters (may be null in existing records)
                    try {
                        model.setItemConditionIds(rs.getString("item_condition_ids"));
                        model.setExcludeKeyword(rs.getString("exclude_keyword"));
                        model.setColorIds(rs.getString("color_ids"));
                    } catch (SQLException e) {
                        // If columns don't exist yet, use null values
                        model.setItemConditionIds(null);
                        model.setExcludeKeyword(null);
                        model.setColorIds(null);
                    }

                    results.add(model);
                }
            }

        } catch (SQLException e) {
            logger.error("Error getting keyword search results", e);
        }

        return results;
    }

    /**
     * Get the total number of keyword search results.
     *
     * @return The total number of records
     */
    public static int getTotalKeywordSearchResults() {
        String sql = "SELECT COUNT(*) FROM keyword_search_results";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            if (rs.next()) {
                return rs.getInt(1);
            }

        } catch (SQLException e) {
            logger.error("Error getting total keyword search results", e);
        }

        return 0;
    }

    /**
     * Delete a keyword search result from the database.
     *
     * @param id The ID of the record to delete
     * @return true if the deletion was successful, false otherwise
     */
    public static boolean deleteKeywordSearchResult(int id) {
        String sql = "DELETE FROM keyword_search_results WHERE id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, id);

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;

        } catch (SQLException e) {
            logger.error("Error deleting keyword search result", e);
            return false;
        }
    }

    /**
     * Update the research status of a keyword search result by ID.
     *
     * @param id             The ID of the record to update
     * @param researchStatus The new research status
     * @return true if the update was successful, false otherwise
     */
    public static boolean updateKeywordSearchResultStatus(int id, String researchStatus) {
        String sql = "UPDATE keyword_search_results SET research_status = ?, updated_date = ? WHERE id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, researchStatus);
            pstmt.setString(2, DatabaseDateTimeUtil.getDateFormat().format(new java.util.Date()));
            pstmt.setInt(3, id);

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;

        } catch (SQLException e) {
            logger.error("Error updating keyword search result status", e);
            return false;
        }
    }

    /**
     * Get a keyword search result by ID.
     *
     * @param id The ID of the record to retrieve
     * @return The keyword search result model, or null if not found
     */
    public static KeywordSearchResultModel getKeywordSearchResultById(int id) {
        String sql = "SELECT * FROM keyword_search_results WHERE id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, id);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    KeywordSearchResultModel model = new KeywordSearchResultModel();
                    model.setId(rs.getInt("id"));
                    model.setKeyword(rs.getString("keyword"));
                    model.setCategories(rs.getString("categories"));
                    model.setAddedDate(DatabaseDateTimeUtil.parseDate(rs.getString("added_date")));
                    model.setUpdatedDate(DatabaseDateTimeUtil.parseDate(rs.getString("updated_date")));
                    model.setStatus(rs.getString("status"));

                    // Handle research_status (may be null in existing records)
                    String researchStatus = rs.getString("research_status");
                    if (researchStatus == null) {
                        model.setResearchStatus(rs.getString("status"));
                    } else {
                        model.setResearchStatus(researchStatus);
                    }

                    // Get the search parameters
                    try {
                        model.setMinPrice(rs.getInt("min_price"));
                        model.setMaxPrice(rs.getInt("max_price"));
                        model.setPages(rs.getInt("pages"));
                        model.setSortByNew(rs.getInt("sort_by_new") == 1);
                        model.setIgnoreShops(rs.getInt("ignore_shops") == 1);
                        model.setSalesStatus(rs.getString("sales_status"));
                    } catch (SQLException e) {
                        // If columns don't exist yet, use default values
                        model.setMinPrice(300);
                        model.setMaxPrice(9999999);
                        model.setPages(1);
                        model.setSortByNew(false);
                        model.setIgnoreShops(false);
                        model.setSalesStatus("すべて");
                    }

                    // Get detailed search parameters
                    try {
                        model.setItemConditionIds(rs.getString("item_condition_ids"));
                        model.setExcludeKeyword(rs.getString("exclude_keyword"));
                        model.setColorIds(rs.getString("color_ids"));
                        model.setCategoryId(rs.getString("category_id"));
                    } catch (SQLException e) {
                        // If columns don't exist yet, use null values
                        model.setItemConditionIds(null);
                        model.setExcludeKeyword(null);
                        model.setColorIds(null);
                        model.setCategoryId(null);
                    }

                    return model;
                }
            }

        } catch (SQLException e) {
            logger.error("Error retrieving keyword search result by ID {}: {}", id, e.getMessage(), e);
        }

        return null;
    }

    /**
     * Remove duplicate items from the database based on item_id.
     * Keeps only the latest item based on saved_date.
     *
     * @param items The list of items to check for duplicates
     */
    private static void removeDuplicateItems(List<Item> items) {
        if (items == null || items.isEmpty()) {
            return;
        }

        // Extract item IDs from the new items
        Set<String> itemIds = items.stream()
                .map(Item::getId)
                .collect(Collectors.toSet());

        if (itemIds.isEmpty()) {
            return;
        }

        // Create placeholders for the IN clause
        String placeholders = itemIds.stream()
                .map(id -> "?")
                .collect(Collectors.joining(","));

        String deleteSql = "DELETE FROM keyword_search_items WHERE item_id IN (" + placeholders + ")";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(deleteSql)) {

            int paramIndex = 1;
            for (String itemId : itemIds) {
                pstmt.setString(paramIndex++, itemId);
            }

            int deletedCount = pstmt.executeUpdate();
            if (deletedCount > 0) {
                logger.info("データベースから重複するアイテムを {} 件削除しました", deletedCount);
            }

        } catch (SQLException e) {
            logger.error("Error removing duplicate items: {}", e.getMessage(), e);
        }
    }
}