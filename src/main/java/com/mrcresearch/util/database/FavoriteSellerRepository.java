package com.mrcresearch.util.database;

import com.mrcresearch.screen.model.FavoriteSellerModel;
import com.mrcresearch.screen.model.FavoriteSellerStatsModel;
import com.mrcresearch.screen.model.GroupTagInfoModel;

import java.sql.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Repository class for favorite seller related database operations.
 * Handles CRUD operations for favorite sellers, groups, and tags.
 */
public class FavoriteSellerRepository {

    /**
     * Private constructor to prevent instantiation.
     * This is a utility class with static methods only.
     */
    private FavoriteSellerRepository() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * Save a favorite seller to the database.
     * Uses UPSERT (INSERT OR REPLACE) to prevent duplicates based on seller_id.
     *
     * @param model The favorite seller model to save
     * @return The ID of the saved record, or -1 if an error occurred
     */
    public static int saveFavoriteSeller(FavoriteSellerModel model) {
        // Use INSERT OR REPLACE to handle duplicates
        String sql = "INSERT OR REPLACE INTO favorite_sellers (seller_name, seller_id, added_date, updated_date, research_status, tags, group_name) " +
                "VALUES (?, ?, " +
                "COALESCE((SELECT added_date FROM favorite_sellers WHERE seller_id = ?), ?), " +  // Keep original added_date if exists
                "?, ?, ?, ?)";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

            String currentDate = DatabaseDateTimeUtil.getDateFormat().format(new Date());

            pstmt.setString(1, model.getSellerName());
            pstmt.setString(2, model.getSellerId());
            pstmt.setString(3, model.getSellerId()); // For COALESCE check
            pstmt.setString(4, currentDate); // Default added_date for new records
            pstmt.setString(5, DatabaseDateTimeUtil.getDateFormat().format(model.getLastUpdated()));
            pstmt.setString(6, model.getResearchStatus());
            pstmt.setString(7, model.getTags());
            pstmt.setString(8, model.getGroupName());

            int affectedRows = pstmt.executeUpdate();

            if (affectedRows > 0) {
                // Get the ID of the inserted/updated record
                String getIdSql = "SELECT id FROM favorite_sellers WHERE seller_id = ?";
                try (PreparedStatement getIdStmt = conn.prepareStatement(getIdSql)) {
                    getIdStmt.setString(1, model.getSellerId());
                    try (ResultSet rs = getIdStmt.executeQuery()) {
                        if (rs.next()) {
                            return rs.getInt(1);
                        }
                    }
                }
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return -1;
    }

    /**
     * Update an existing favorite seller in the database.
     *
     * @param model The favorite seller model to update
     * @return true if the update was successful, false otherwise
     */
    public static boolean updateFavoriteSeller(FavoriteSellerModel model) {
        String sql = "UPDATE favorite_sellers SET seller_name = ?, seller_id = ?, " +
                "updated_date = ?, research_status = ?, tags = ?, group_name = ? WHERE id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, model.getSellerName());
            pstmt.setString(2, model.getSellerId());
            pstmt.setString(3, DatabaseDateTimeUtil.getDateFormat().format(model.getLastUpdated()));
            pstmt.setString(4, model.getResearchStatus());
            pstmt.setString(5, model.getTags());
            pstmt.setString(6, model.getGroupName());
            pstmt.setInt(7, model.getId());

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;

        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Get a list of favorite sellers with pagination.
     *
     * @param page     The page number (1-based)
     * @param pageSize The number of records per page
     * @return A list of favorite seller models
     */
    public static List<FavoriteSellerModel> getFavoriteSellers(int page, int pageSize) {
        List<FavoriteSellerModel> results = new ArrayList<>();

        String sql = "SELECT * FROM favorite_sellers ORDER BY updated_date DESC LIMIT ? OFFSET ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, pageSize);
            pstmt.setInt(2, (page - 1) * pageSize);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    FavoriteSellerModel model = new FavoriteSellerModel(
                            rs.getInt("id"),
                            rs.getString("seller_name"),
                            rs.getString("seller_id"),
                            DatabaseDateTimeUtil.parseDate(rs.getString("updated_date")),
                            rs.getString("research_status"),
                            rs.getString("tags"),
                            rs.getString("group_name")
                    );

                    results.add(model);
                }
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return results;
    }

    /**
     * Get a list of favorite sellers by group name.
     *
     * @param groupName The group name to filter by
     * @return A list of favorite seller models
     */
    public static List<FavoriteSellerModel> getFavoriteSellersByGroup(String groupName) {
        List<FavoriteSellerModel> results = new ArrayList<>();

        String sql = "SELECT * FROM favorite_sellers WHERE group_name = ? ORDER BY updated_date DESC";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, groupName);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    FavoriteSellerModel model = new FavoriteSellerModel(
                            rs.getString("seller_name"),
                            rs.getString("seller_id"),
                            DatabaseDateTimeUtil.parseDate(rs.getString("updated_date")),
                            rs.getString("research_status"),
                            rs.getString("tags"),
                            rs.getString("group_name")
                    );

                    results.add(model);
                }
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return results;
    }

    /**
     * Get a list of favorite sellers by tag.
     *
     * @param tag The tag to filter by
     * @return A list of favorite seller models
     */
    public static List<FavoriteSellerModel> getFavoriteSellersByTag(String tag) {
        List<FavoriteSellerModel> results = new ArrayList<>();

        String sql = "SELECT * FROM favorite_sellers WHERE tags LIKE ? ORDER BY updated_date DESC";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, "%" + tag + "%");

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    FavoriteSellerModel model = new FavoriteSellerModel(
                            rs.getString("seller_name"),
                            rs.getString("seller_id"),
                            DatabaseDateTimeUtil.parseDate(rs.getString("updated_date")),
                            rs.getString("research_status"),
                            rs.getString("tags"),
                            rs.getString("group_name")
                    );

                    results.add(model);
                }
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return results;
    }

    /**
     * Get all unique group names.
     *
     * @return A list of group names
     */
    public static List<String> getAllGroups() {
        List<String> groups = new ArrayList<>();

        String sql = "SELECT DISTINCT group_name FROM favorite_sellers WHERE group_name IS NOT NULL AND group_name != ''";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                groups.add(rs.getString("group_name"));
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return groups;
    }

    /**
     * Get all unique tags.
     *
     * @return A list of tags
     */
    public static List<String> getAllTags() {
        List<String> allTags = new ArrayList<>();

        String sql = "SELECT tags FROM favorite_sellers WHERE tags IS NOT NULL AND tags != ''";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                String tags = rs.getString("tags");
                if (tags != null && !tags.isEmpty()) {
                    String[] tagArray = tags.split(",");
                    for (String tag : tagArray) {
                        tag = tag.trim();
                        if (!tag.isEmpty() && !allTags.contains(tag)) {
                            allTags.add(tag);
                        }
                    }
                }
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return allTags;
    }

    /**
     * Delete a favorite seller from the database.
     *
     * @param sellerId The seller ID to delete
     * @return true if the deletion was successful, false otherwise
     */
    public static boolean deleteFavoriteSeller(String sellerId) {
        String sql = "DELETE FROM favorite_sellers WHERE seller_id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, sellerId);

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;

        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Get enhanced group information with seller counts and last updated timestamps.
     *
     * @return A list of group information models
     */
    public static List<GroupTagInfoModel> getGroupInfoWithCounts() {
        return FavoriteGroupRepository.getGroupInfoWithCounts();
    }

    /**
     * Get enhanced tag information with seller counts and last updated timestamps.
     *
     * @return A list of tag information models
     */
    public static List<GroupTagInfoModel> getTagInfoWithCounts() {
        return FavoriteTagRepository.getTagInfoWithCounts();
    }

    /**
     * Get favorite sellers with statistics for display.
     *
     * @param page      The page number (1-based)
     * @param pageSize  The number of records per page
     * @param groupName Filter by group name (null for all)
     * @param tagName   Filter by tag name (null for all)
     * @return A list of favorite seller statistics models
     */
    public static List<FavoriteSellerStatsModel> getFavoriteSellersWithStats(int page, int pageSize, String groupName, String tagName) {
        List<FavoriteSellerStatsModel> results = new ArrayList<>();

        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT ")
                .append("    fs.id, ")
                .append("    fs.seller_id, ")
                .append("    COALESCE(s.seller_name, fs.seller_name) as seller_name, ")
                .append("    fs.group_name, ")
                .append("    fs.tags, ")
                .append("    fs.updated_date, ")
                .append("    fs.research_status, ")
                .append("    latest_ss.last_researched_at ")
                .append("FROM favorite_sellers fs ")
                .append("LEFT JOIN sellers s ON fs.seller_id = s.seller_id ")
                .append("LEFT JOIN (")
                .append("    SELECT seller_id, last_researched_at ")
                .append("    FROM seller_searches ss1 ")
                .append("    WHERE ss1.last_researched_at = (")
                .append("        SELECT MAX(ss2.last_researched_at) ")
                .append("        FROM seller_searches ss2 ")
                .append("        WHERE ss2.seller_id = ss1.seller_id")
                .append("    )")
                .append(") latest_ss ON fs.seller_id = latest_ss.seller_id ");

        List<String> conditions = new ArrayList<>();
        List<Object> parameters = new ArrayList<>();

        if (groupName != null && !groupName.trim().isEmpty()) {
            conditions.add("fs.group_name = ?");
            parameters.add(groupName);
        }

        if (tagName != null && !tagName.trim().isEmpty()) {
            conditions.add("fs.tags LIKE ?");
            parameters.add("%" + tagName + "%");
        }

        if (!conditions.isEmpty()) {
            sqlBuilder.append("WHERE ").append(String.join(" AND ", conditions)).append(" ");
        }

        sqlBuilder.append("ORDER BY fs.updated_date DESC LIMIT ? OFFSET ?");
        parameters.add(pageSize);
        parameters.add((page - 1) * pageSize);

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sqlBuilder.toString())) {

            for (int i = 0; i < parameters.size(); i++) {
                pstmt.setObject(i + 1, parameters.get(i));
            }

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    int id = rs.getInt("id");
                    String sellerId = rs.getString("seller_id");
                    String sellerName = rs.getString("seller_name");
                    String group = rs.getString("group_name");
                    String tags = rs.getString("tags");
                    Date lastUpdated = DatabaseDateTimeUtil.parseDate(rs.getString("updated_date"));
                    String researchStatus = rs.getString("research_status");
                    Date lastResearchDate = null;
                    String lastResearchedAtStr = rs.getString("last_researched_at");
                    if (lastResearchedAtStr != null) {
                        lastResearchDate = DatabaseDateTimeUtil.parseDate(lastResearchedAtStr);
                    }

                    FavoriteSellerStatsModel statsModel = new FavoriteSellerStatsModel(
                            id, sellerId, sellerName, group, tags, lastUpdated, researchStatus);
                    statsModel.setLastResearchDate(lastResearchDate);

                    // Calculate statistics
                    calculateSellerStatistics(statsModel);

                    results.add(statsModel);
                }
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return results;
    }

    /**
     * Calculate statistics for a seller.
     *
     * @param statsModel The stats model to populate
     */
    private static void calculateSellerStatistics(FavoriteSellerStatsModel statsModel) {
        String sellerId = statsModel.getSellerId();

        // Calculate average daily sales for past 30 days
        double avgDailySales = calculateAverageDailySales30Days(sellerId);
        statsModel.setAverageDailySales30Days(avgDailySales);

        // Calculate total sales for past 7 days
        int totalSales7Days = calculateTotalSalesLast7Days(sellerId);
        statsModel.setTotalSalesLast7Days(totalSales7Days);

        // Calculate daily sales for past 7 days
        List<Integer> dailySales7Days = calculateDailySalesLast7Days(sellerId);
        statsModel.setDailySalesLast7Days(dailySales7Days);

        // Calculate total items researched
        int totalItems = calculateTotalItemsResearched(sellerId);
        statsModel.setTotalItemsResearched(totalItems);
    }

    /**
     * Calculate average daily sales for past 30 days.
     *
     * @param sellerId The seller ID
     * @return Average daily sales (rounded to 1 decimal place)
     */
    private static double calculateAverageDailySales30Days(String sellerId) {
        String sql = "SELECT COUNT(*) as total_sales " +
                "FROM search_items " +
                "WHERE seller_id = ? " +
                "AND status IN (2, 3) " +  // 取引中, 取引完了
                "AND DATE(updated_date) >= DATE('now', '-30 days')";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, sellerId);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    int totalSales = rs.getInt("total_sales");
                    double average = totalSales / 30.0;
                    return Math.round(average * 10.0) / 10.0; // Round to 1 decimal place
                }
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return 0.0;
    }

    /**
     * Calculate total sales for past 7 days.
     *
     * @param sellerId The seller ID
     * @return Total sales count
     */
    private static int calculateTotalSalesLast7Days(String sellerId) {
        String sql = "SELECT COUNT(*) as total_sales " +
                "FROM search_items " +
                "WHERE seller_id = ? " +
                "AND status IN (2, 3) " +  // 取引中, 取引完了
                "AND DATE(updated_date) >= DATE('now', '-7 days')";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, sellerId);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("total_sales");
                }
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return 0;
    }

    /**
     * Calculate daily sales for past 7 days.
     *
     * @param sellerId The seller ID
     * @return List of daily sales counts (index 0 = today, index 6 = 7 days ago)
     */
    private static List<Integer> calculateDailySalesLast7Days(String sellerId) {
        List<Integer> dailySales = new ArrayList<>();

        // Initialize with 0 for each day
        for (int i = 0; i < 7; i++) {
            dailySales.add(0);
        }

        String sql = "SELECT DATE(updated_date) as sale_date, COUNT(*) as daily_count " +
                "FROM search_items " +
                "WHERE seller_id = ? " +
                "AND status IN (2, 3) " +  // 取引中, 取引完了
                "AND DATE(updated_date) >= DATE('now', '-7 days') " +
                "GROUP BY DATE(updated_date) " +
                "ORDER BY sale_date DESC";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, sellerId);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    String saleDateStr = rs.getString("sale_date");
                    int dailyCount = rs.getInt("daily_count");

                    // Calculate days difference from today
                    String todayStr = "SELECT DATE('now') as today";
                    try (PreparedStatement todayStmt = conn.prepareStatement(todayStr);
                         ResultSet todayRs = todayStmt.executeQuery()) {
                        if (todayRs.next()) {
                            String today = todayRs.getString("today");

                            // Calculate difference in days
                            String diffSql = "SELECT julianday(?) - julianday(?) as day_diff";
                            try (PreparedStatement diffStmt = conn.prepareStatement(diffSql)) {
                                diffStmt.setString(1, today);
                                diffStmt.setString(2, saleDateStr);
                                try (ResultSet diffRs = diffStmt.executeQuery()) {
                                    if (diffRs.next()) {
                                        int dayDiff = (int) diffRs.getDouble("day_diff");
                                        if (dayDiff >= 0 && dayDiff < 7) {
                                            dailySales.set(dayDiff, dailyCount);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return dailySales;
    }

    /**
     * Calculate total items researched for a seller.
     *
     * @param sellerId The seller ID
     * @return Total items count
     */
    private static int calculateTotalItemsResearched(String sellerId) {
        String sql = "SELECT COUNT(DISTINCT item_id) as total_items " +
                "FROM search_items " +
                "WHERE seller_id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, sellerId);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("total_items");
                }
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return 0;
    }

    /**
     * Get total count of favorite sellers with optional filtering.
     *
     * @param groupName Filter by group name (null for all)
     * @param tagName   Filter by tag name (null for all)
     * @return Total count
     */
    public static int getFavoriteSellersCount(String groupName, String tagName) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT COUNT(*) as total_count FROM favorite_sellers fs ");

        List<String> conditions = new ArrayList<>();
        List<Object> parameters = new ArrayList<>();

        if (groupName != null && !groupName.trim().isEmpty()) {
            conditions.add("fs.group_name = ?");
            parameters.add(groupName);
        }

        if (tagName != null && !tagName.trim().isEmpty()) {
            conditions.add("fs.tags LIKE ?");
            parameters.add("%" + tagName + "%");
        }

        if (!conditions.isEmpty()) {
            sqlBuilder.append("WHERE ").append(String.join(" AND ", conditions));
        }

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sqlBuilder.toString())) {

            for (int i = 0; i < parameters.size(); i++) {
                pstmt.setObject(i + 1, parameters.get(i));
            }

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("total_count");
                }
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return 0;
    }
}
