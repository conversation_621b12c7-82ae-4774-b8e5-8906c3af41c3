package com.mrcresearch.util.database;

import com.mrcresearch.screen.model.SellerModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Repository class for seller master information management
 * Provides CRUD operations for the sellers table
 */
public class SellerRepository {

    private static final Logger logger = LoggerFactory.getLogger(SellerRepository.class);

    /**
     * Save or update a seller in the database (upsert operation)
     * If the seller exists, updates the information; otherwise, creates a new record
     *
     * @param seller The seller model to save
     * @return true if the operation was successful, false otherwise
     */
    public static boolean saveOrUpdateSeller(SellerModel seller) {
        if (seller == null || seller.getSellerId() == null || seller.getSellerId().trim().isEmpty()) {
            logger.error("Invalid seller data: seller or seller ID is null/empty");
            return false;
        }

        // Check if seller already exists
        SellerModel existingSeller = getSellerById(seller.getSellerId());

        if (existingSeller != null) {
            return updateSeller(seller);
        } else {
            return insertSeller(seller);
        }
    }

    /**
     * Insert a new seller into the database
     *
     * @param seller The seller model to insert
     * @return true if the insertion was successful, false otherwise
     */
    private static boolean insertSeller(SellerModel seller) {
        String sql = "INSERT INTO sellers (seller_id, seller_name, seller_name_changed, created_at, updated_at, research_status) " +
                "VALUES (?, ?, ?, ?, ?, ?)";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            Date now = new Date();
            if (seller.getCreatedAt() == null) {
                seller.setCreatedAt(now);
            }
            if (seller.getUpdatedAt() == null) {
                seller.setUpdatedAt(now);
            }
            if (seller.getResearchStatus() == null) {
                seller.setResearchStatus("待機中");
            }

            pstmt.setString(1, seller.getSellerId());
            pstmt.setString(2, seller.getSellerName());
            pstmt.setInt(3, seller.isSellerNameChanged() ? 1 : 0);
            pstmt.setString(4, DatabaseDateTimeUtil.getDateFormat().format(seller.getCreatedAt()));
            pstmt.setString(5, DatabaseDateTimeUtil.getDateFormat().format(seller.getUpdatedAt()));
            pstmt.setString(6, seller.getResearchStatus());

            int affectedRows = pstmt.executeUpdate();
            boolean success = affectedRows > 0;

            if (success) {
                logger.info("セラーを正常に挿入しました: {}", seller.getSellerId());
            }

            return success;

        } catch (SQLException e) {
            logger.error("Error inserting seller: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Update an existing seller in the database
     *
     * @param seller The seller model to update
     * @return true if the update was successful, false otherwise
     */
    private static boolean updateSeller(SellerModel seller) {
        String sql = "UPDATE sellers SET seller_name = ?, seller_name_changed = ?, updated_at = ?, research_status = ? " +
                "WHERE seller_id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            seller.setUpdatedAt(new Date());

            pstmt.setString(1, seller.getSellerName());
            pstmt.setInt(2, seller.isSellerNameChanged() ? 1 : 0);
            pstmt.setString(3, DatabaseDateTimeUtil.getDateFormat().format(seller.getUpdatedAt()));
            pstmt.setString(4, seller.getResearchStatus() != null ? seller.getResearchStatus() : "待機中");
            pstmt.setString(5, seller.getSellerId());

            int affectedRows = pstmt.executeUpdate();
            boolean success = affectedRows > 0;

            if (success) {
                logger.info("セラーを正常に更新しました: {}", seller.getSellerId());
            }

            return success;

        } catch (SQLException e) {
            logger.error("Error updating seller: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Get a seller by seller ID
     *
     * @param sellerId The seller ID
     * @return The seller model, or null if not found
     */
    public static SellerModel getSellerById(String sellerId) {
        if (sellerId == null || sellerId.trim().isEmpty()) {
            return null;
        }

        String sql = "SELECT * FROM sellers WHERE seller_id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, sellerId);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return createSellerFromResultSet(rs);
                }
            }

        } catch (SQLException e) {
            logger.error("Error retrieving seller by ID: {}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * Get all sellers with pagination
     *
     * @param page     The page number (1-based)
     * @param pageSize The number of records per page
     * @return A list of seller models
     */
    public static List<SellerModel> getAllSellers(int page, int pageSize) {
        List<SellerModel> sellers = new ArrayList<>();

        String sql = "SELECT * FROM sellers ORDER BY updated_at DESC LIMIT ? OFFSET ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, pageSize);
            pstmt.setInt(2, (page - 1) * pageSize);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    sellers.add(createSellerFromResultSet(rs));
                }
            }

        } catch (SQLException e) {
            logger.error("Error retrieving sellers: {}", e.getMessage(), e);
        }

        return sellers;
    }

    /**
     * Search sellers by name (partial match)
     *
     * @param namePattern The name pattern to search for
     * @param page        The page number (1-based)
     * @param pageSize    The number of records per page
     * @return A list of matching seller models
     */
    public static List<SellerModel> searchSellersByName(String namePattern, int page, int pageSize) {
        List<SellerModel> sellers = new ArrayList<>();

        if (namePattern == null || namePattern.trim().isEmpty()) {
            return sellers;
        }

        String sql = "SELECT * FROM sellers WHERE seller_name LIKE ? ORDER BY updated_at DESC LIMIT ? OFFSET ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, "%" + namePattern + "%");
            pstmt.setInt(2, pageSize);
            pstmt.setInt(3, (page - 1) * pageSize);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    sellers.add(createSellerFromResultSet(rs));
                }
            }

        } catch (SQLException e) {
            logger.error("Error searching sellers by name: {}", e.getMessage(), e);
        }

        return sellers;
    }

    /**
     * Get sellers with name changes
     *
     * @param page     The page number (1-based)
     * @param pageSize The number of records per page
     * @return A list of sellers with name changes
     */
    public static List<SellerModel> getSellersWithNameChanges(int page, int pageSize) {
        List<SellerModel> sellers = new ArrayList<>();

        String sql = "SELECT * FROM sellers WHERE seller_name_changed = 1 ORDER BY updated_at DESC LIMIT ? OFFSET ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, pageSize);
            pstmt.setInt(2, (page - 1) * pageSize);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    sellers.add(createSellerFromResultSet(rs));
                }
            }

        } catch (SQLException e) {
            logger.error("Error retrieving sellers with name changes: {}", e.getMessage(), e);
        }

        return sellers;
    }

    /**
     * Delete a seller by seller ID
     *
     * @param sellerId The seller ID
     * @return true if the deletion was successful, false otherwise
     */
    public static boolean deleteSeller(String sellerId) {
        if (sellerId == null || sellerId.trim().isEmpty()) {
            return false;
        }

        String sql = "DELETE FROM sellers WHERE seller_id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, sellerId);

            int affectedRows = pstmt.executeUpdate();
            boolean success = affectedRows > 0;

            if (success) {
                logger.info("セラーを正常に削除しました: {}", sellerId);
            }

            return success;

        } catch (SQLException e) {
            logger.error("Error deleting seller: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Get the total count of sellers
     *
     * @return The total number of sellers
     */
    public static int getTotalSellerCount() {
        String sql = "SELECT COUNT(*) FROM sellers";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {

            if (rs.next()) {
                return rs.getInt(1);
            }

        } catch (SQLException e) {
            logger.error("Error getting total seller count: {}", e.getMessage(), e);
        }

        return 0;
    }

    /**
     * Update the research status of a seller
     *
     * @param sellerId       The seller ID
     * @param researchStatus The new research status
     * @return true if the update was successful, false otherwise
     */
    public static boolean updateSellerResearchStatus(String sellerId, String researchStatus) {
        if (sellerId == null || sellerId.trim().isEmpty()) {
            return false;
        }

        String sql = "UPDATE sellers SET research_status = ?, updated_at = ? WHERE seller_id = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, researchStatus);
            pstmt.setString(2, DatabaseDateTimeUtil.getDateFormat().format(new Date()));
            pstmt.setString(3, sellerId);

            int affectedRows = pstmt.executeUpdate();
            boolean success = affectedRows > 0;

            if (success) {
                logger.info("セラー: {} のリサーチステータスを {} に正常に更新しました", sellerId, researchStatus);
            }

            return success;

        } catch (SQLException e) {
            logger.error("Error updating seller research status: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Create a SellerModel from a ResultSet
     *
     * @param rs The ResultSet
     * @return The SellerModel
     * @throws SQLException If there's an error reading from the ResultSet
     */
    private static SellerModel createSellerFromResultSet(ResultSet rs) throws SQLException {
        SellerModel seller = new SellerModel();
        seller.setSellerId(rs.getString("seller_id"));
        seller.setSellerName(rs.getString("seller_name"));
        seller.setSellerNameChanged(rs.getInt("seller_name_changed") == 1);
        seller.setCreatedAt(DatabaseDateTimeUtil.parseDate(rs.getString("created_at")));
        seller.setUpdatedAt(DatabaseDateTimeUtil.parseDate(rs.getString("updated_at")));

        // Handle research_status column (may not exist in older database versions)
        try {
            String researchStatus = rs.getString("research_status");
            seller.setResearchStatus(researchStatus != null ? researchStatus : "待機中");
        } catch (SQLException e) {
            // Column doesn't exist, set default value
            seller.setResearchStatus("待機中");
        }

        return seller;
    }
}