package com.mrcresearch.util.database;

import com.mrcresearch.screen.model.ProductResearchResultModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Repository class for product research related database operations.
 * Handles CRUD operations for product research results with pagination and filtering.
 */
public class ProductResearchRepository {

    private static final Logger logger = LoggerFactory.getLogger(ProductResearchRepository.class);

    /**
     * Private constructor to prevent instantiation.
     * This is a utility class with static methods only.
     */
    private ProductResearchRepository() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * Get a list of product research results with pagination and optional name filter.
     * Results are sorted by total sales count (descending) and include sales aggregation data.
     *
     * @param page       The page number (1-based)
     * @param pageSize   The number of records per page
     * @param nameFilter Optional product name filter (null or empty for no filter)
     * @return A list of product research result models sorted by total sales count
     */
    public static List<ProductResearchResultModel> getProductResearchResults(int page, int pageSize, String nameFilter) {
        logger.debug("getProductResearchResults が呼び出されました - ページ: {}, ページサイズ: {}, 名前フィルター: {}", page, pageSize, nameFilter);
        List<ProductResearchResultModel> results = new ArrayList<>();

        // 過去5日間の日付を計算
        java.time.LocalDate today = java.time.LocalDate.now();
        java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String startDate = today.minusDays(4).format(formatter);
        String endDate = today.format(formatter);

        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("WITH sales_aggregation AS (")
                .append("    SELECT ")
                .append("        item_id, ")
                .append("        product_name as name, ")
                .append("        seller_id, ")
                .append("        price, ")
                .append("        category, ")
                .append("        COUNT(*) as total_sales_count ")
                .append("    FROM search_items ")
                .append("    WHERE status NOT IN (1) ")  // Exclude ON_SALE status (ID = 1)
                .append("    AND DATE(updated_date) BETWEEN ? AND ? ")
                .append("    GROUP BY item_id, product_name, seller_id, price, category ")
                .append(") ")
                .append("SELECT ")
                .append("    si.*, ")
                .append("    '' as keyword, ")  // No keyword join for now, can be added later if needed
                .append("    COALESCE(sa.total_sales_count, 0) as total_sales_count ")
                .append("FROM search_items si ")
                .append("LEFT JOIN sales_aggregation sa ON (")
                .append("    si.item_id = sa.item_id AND ")
                .append("    si.product_name = sa.name AND ")
                .append("    si.seller_id = sa.seller_id AND ")
                .append("    si.price = sa.price AND ")
                .append("    si.category = sa.category")
                .append(") ");

        if (nameFilter != null && !nameFilter.trim().isEmpty()) {
            sqlBuilder.append("WHERE LOWER(si.product_name) LIKE LOWER(?) ");
        }

        sqlBuilder.append("ORDER BY COALESCE(sa.total_sales_count, 0) DESC, si.updated_date DESC ");
        sqlBuilder.append("LIMIT ? OFFSET ?");

        logger.debug("SQLクエリ: {}", sqlBuilder);

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sqlBuilder.toString())) {

            int paramIndex = 1;
            pstmt.setString(paramIndex++, startDate);
            pstmt.setString(paramIndex++, endDate);

            if (nameFilter != null && !nameFilter.trim().isEmpty()) {
                pstmt.setString(paramIndex++, "%" + nameFilter.trim() + "%");
            }
            pstmt.setInt(paramIndex++, pageSize);
            pstmt.setInt(paramIndex, (page - 1) * pageSize);

            logger.debug("日付範囲でクエリを実行中: {} から {}", startDate, endDate);

            try (ResultSet rs = pstmt.executeQuery()) {
                int rowCount = 0;
                while (rs.next()) {
                    rowCount++;
                    ProductResearchResultModel model = new ProductResearchResultModel();
                    // Note: Using item_id as id since search_items doesn't have a separate id field
                    model.setId(rowCount);  // Use row count as ID
                    model.setKeywordSearchResultId(0);  // No longer available in new structure
                    model.setItemId(rs.getString("item_id"));
                    model.setSellerId(rs.getString("seller_id"));

                    // Convert status ID to display name
                    int statusId = rs.getInt("status");
                    String statusDisplay = com.mrcresearch.service.enums.ItemStatusEnum.fromId(statusId).getDisplayName();
                    model.setStatus(statusDisplay);

                    model.setName(rs.getString("product_name"));
                    model.setPrice(rs.getInt("price"));
                    model.setCreatedDate(DatabaseDateTimeUtil.parseDate(rs.getString("listing_date")));
                    model.setUpdatedDate(DatabaseDateTimeUtil.parseDate(rs.getString("updated_date")));
                    model.setThumbnail(rs.getString("thumbnail"));

                    // Convert item type ID to display name
                    int itemTypeId = rs.getInt("item_type");
                    String itemTypeDisplay = com.mrcresearch.service.enums.ItemTypeEnum.fromId(itemTypeId).getDisplayName();
                    model.setItemType(itemTypeDisplay);

                    model.setItemConditionId(0);  // Convert from string if needed
                    model.setShippingMethodId(0);  // No longer available

                    // Parse category from string to int if possible
                    String categoryStr = rs.getString("category");
                    int categoryId = 0;
                    try {
                        categoryId = Integer.parseInt(categoryStr);
                    } catch (NumberFormatException e) {
                        // Keep as 0 if not a number
                    }
                    model.setCategoryId(categoryId);

                    model.setSavedDate(DatabaseDateTimeUtil.parseDate(rs.getString("listing_date")));  // Use listing date
                    model.setKeyword(rs.getString("keyword"));
                    model.setTotalSalesCount(rs.getInt("total_sales_count"));

                    results.add(model);

                    // Log first few rows for debugging
                    if (rowCount <= 3) {
                        logger.debug("行 {} - ID: {}, 名前: {}, セラー: {}, 総売上: {}", rowCount, model.getItemId(), model.getName(), model.getSellerId(), model.getTotalSalesCount());
                    }
                }
                logger.debug("処理された総行数: {}", rowCount);
                logger.debug("結果はデータベースレベルで総売上数の降順にソートされています");
            }

        } catch (SQLException e) {
            logger.error("Error retrieving product research results: {}", e.getMessage(), e);
        }

        return results;
    }

    /**
     * Get the total count of product research results with optional name filter.
     *
     * @param nameFilter Optional product name filter (null or empty for no filter)
     * @return The total count of matching records
     */
    public static int getProductResearchResultsCount(String nameFilter) {
        logger.debug("getProductResearchResultsCount が名前フィルター: {} で呼び出されました", nameFilter);

        // First, check if the table exists and has any data
        try (Connection conn = DatabaseConnectionManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM search_items")) {

            if (rs.next()) {
                int totalCount = rs.getInt(1);
                logger.debug("search_items テーブル内の総アイテム数: {}", totalCount);
            }
        } catch (SQLException e) {
            logger.error("DEBUG: Error checking search_items table: {}", e.getMessage());
        }

        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT COUNT(*) FROM search_items si ");

        if (nameFilter != null && !nameFilter.trim().isEmpty()) {
            sqlBuilder.append("WHERE LOWER(si.product_name) LIKE LOWER(?) ");
        }

        logger.debug("カウントSQLクエリ: {}", sqlBuilder);

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sqlBuilder.toString())) {

            if (nameFilter != null && !nameFilter.trim().isEmpty()) {
                pstmt.setString(1, "%" + nameFilter.trim() + "%");
            }

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    int count = rs.getInt(1);
                    logger.debug("商品リサーチ結果数: {}", count);
                    return count;
                }
            }

        } catch (SQLException e) {
            logger.error("Error getting product research results count: {}", e.getMessage(), e);
        }

        logger.debug("商品リサーチ結果数として0を返します");
        return 0;
    }
}