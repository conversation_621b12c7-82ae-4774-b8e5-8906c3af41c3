package com.mrcresearch.util.database;

/**
 * Constants class for application settings default values.
 * This class centralizes all default values used in the application.
 */
public class SettingsConstants {

    /**
     * Private constructor to prevent instantiation.
     * This is a utility class with constants only.
     */
    private SettingsConstants() {
        throw new UnsupportedOperationException("Constants class cannot be instantiated");
    }

    // Default setting values
    public static final int DEFAULT_SOLD_DATE = 30;
    public static final int DEFAULT_BROWSER_LOAD_TIMEOUT = 30;
    public static final int DEFAULT_BROWSER_SLEEP_TIME = 3;
    public static final int DEFAULT_BROWSER_INIT_SLEEP_TIME = 30;
    public static final String DEFAULT_BROWSER_TYPE = "Firefox";
    public static final int DEFAULT_KEYWORD_PAGES = 10; // Using value from Settings.java
    public static final int DEFAULT_OLD_ITEM_DISPLAY_COUNT = 10;
    public static final String DEFAULT_THEME = "light";
    public static final boolean DEFAULT_HEADLESS_MODE = false;
    public static final int DEFAULT_SALES_AGGREGATION_DAYS = 7;
}