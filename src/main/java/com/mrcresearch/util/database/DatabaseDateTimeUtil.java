package com.mrcresearch.util.database;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * Utility class for date and time operations in database context.
 * Handles timezone conversions and date formatting for database storage.
 * Thread-safe implementation using ThreadLocal for SimpleDateFormat.
 */
public class DatabaseDateTimeUtil {
    // Thread-safe SimpleDateFormat using ThreadLocal for standard format
    private static final ThreadLocal<SimpleDateFormat> DATE_FORMAT = ThreadLocal.withInitial(
            () -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
    );

    // Thread-safe SimpleDateFormat for detailed format with milliseconds
    private static final ThreadLocal<SimpleDateFormat> DETAILED_DATE_FORMAT = ThreadLocal.withInitial(
            () -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
    );

    // Thread-safe SimpleDateFormat for human-readable format
    private static final ThreadLocal<SimpleDateFormat> READABLE_DATE_FORMAT = ThreadLocal.withInitial(
            () -> new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss")
    );

    // Japanese timezone for timestamp conversion
    private static final ZoneId JAPAN_TIMEZONE = ZoneId.of("Asia/Tokyo");

    /**
     * Private constructor to prevent instantiation.
     * This is a utility class with static methods only.
     */
    private DatabaseDateTimeUtil() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * Convert timestamp string to Japanese timezone Date object.
     * Handles various timestamp formats that might come from the Mercari API.
     *
     * @param timestampStr The timestamp string to convert (e.g., "1234567890", "2023-12-01T10:30:00Z")
     * @return Date object in Japanese timezone, or null if conversion fails
     */
    public static Date convertToJapaneseTimezone(String timestampStr) {
        if (timestampStr == null || timestampStr.trim().isEmpty()) {
            return null;
        }

        try {
            // Handle Unix timestamp (numeric string)
            if (timestampStr.matches("\\d+")) {
                long timestamp = Long.parseLong(timestampStr);
                Instant instant = Instant.ofEpochSecond(timestamp);
                ZonedDateTime japanTime = instant.atZone(JAPAN_TIMEZONE);
                return Date.from(japanTime.toInstant());
            }

            // Handle ISO 8601 format with Z suffix (UTC)
            if (timestampStr.endsWith("Z")) {
                LocalDateTime localDateTime = LocalDateTime.parse(timestampStr.replace("Z", ""));
                ZonedDateTime utcTime = localDateTime.atZone(ZoneId.of("UTC"));
                ZonedDateTime japanTime = utcTime.withZoneSameInstant(JAPAN_TIMEZONE);
                return Date.from(japanTime.toInstant());
            }

            // Handle ISO 8601 format without timezone (assume UTC)
            if (timestampStr.matches("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}")) {
                LocalDateTime localDateTime = LocalDateTime.parse(timestampStr);
                ZonedDateTime utcTime = localDateTime.atZone(ZoneId.of("UTC"));
                ZonedDateTime japanTime = utcTime.withZoneSameInstant(JAPAN_TIMEZONE);
                return Date.from(japanTime.toInstant());
            }

            // Handle other date formats - try to parse as LocalDateTime and assume UTC
            try {
                LocalDateTime localDateTime = LocalDateTime.parse(timestampStr);
                ZonedDateTime utcTime = localDateTime.atZone(ZoneId.of("UTC"));
                ZonedDateTime japanTime = utcTime.withZoneSameInstant(JAPAN_TIMEZONE);
                return Date.from(japanTime.toInstant());
            } catch (Exception e) {
                System.err.println("Failed to parse timestamp: " + timestampStr + " - " + e.getMessage());
                return null;
            }

        } catch (Exception e) {
            System.err.println("Error converting timestamp to Japanese timezone: " + timestampStr + " - " + e.getMessage());
            return null;
        }
    }

    /**
     * Format a Date object to Japanese timezone string for database storage.
     *
     * @param date The Date object to format
     * @return Formatted date string in Japanese timezone, or null if date is null
     */
    public static String formatJapaneseTimezone(Date date) {
        if (date == null) {
            return null;
        }

        try {
            ZonedDateTime japanTime = date.toInstant().atZone(JAPAN_TIMEZONE);
            return japanTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            System.err.println("Error formatting date to Japanese timezone: " + date + " - " + e.getMessage());
            return DATE_FORMAT.get().format(date); // Fallback to original format
        }
    }

    /**
     * Parse a date string in the format "yyyy-MM-dd HH:mm:ss".
     * Thread-safe implementation.
     *
     * @param dateStr The date string to parse
     * @return The parsed Date object, or null if parsing failed
     */
    public static Date parseDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }

        try {
            return DATE_FORMAT.get().parse(dateStr.trim());
        } catch (Exception e) {
            System.err.println("Error parsing date string: " + dateStr + " - " + e.getMessage());
            return null;
        }
    }

    /**
     * Parse a detailed date string with milliseconds in the format "yyyy-MM-dd HH:mm:ss.SSS".
     * Thread-safe implementation with fallback to standard format.
     *
     * @param dateStr The detailed date string to parse
     * @return The parsed Date object, or null if parsing failed
     */
    public static Date parseDetailedDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }

        String trimmedStr = dateStr.trim();

        try {
            // Try detailed format first
            if (trimmedStr.contains(".") && trimmedStr.length() >= 23) {
                return DETAILED_DATE_FORMAT.get().parse(trimmedStr);
            }

            // Fallback to standard format
            return DATE_FORMAT.get().parse(trimmedStr);
        } catch (Exception e) {
            System.err.println("Error parsing detailed date string: " + dateStr + " - " + e.getMessage());
            return null;
        }
    }

    /**
     * Parse a date string with automatic format detection.
     * Supports multiple formats including detailed and standard formats.
     *
     * @param dateStr The date string to parse
     * @return The parsed Date object, or null if parsing failed
     */
    public static Date parseAnyDateFormat(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }

        String trimmedStr = dateStr.trim();

        // Try Unix timestamp first (numeric string)
        if (trimmedStr.matches("\\d+")) {
            try {
                long timestamp = Long.parseLong(trimmedStr);
                // Check if it's in seconds or milliseconds
                if (timestamp < 10000000000L) {
                    // Likely in seconds, convert to milliseconds
                    timestamp = timestamp * 1000;
                }
                return new Date(timestamp);
            } catch (NumberFormatException e) {
                System.err.println("Error parsing timestamp: " + trimmedStr + " - " + e.getMessage());
            }
        }

        // Try detailed format (with milliseconds)
        Date result = parseDetailedDate(trimmedStr);
        if (result != null) {
            return result;
        }

        // Try standard format
        result = parseDate(trimmedStr);
        if (result != null) {
            return result;
        }

        // Try readable format
        try {
            return READABLE_DATE_FORMAT.get().parse(trimmedStr);
        } catch (Exception e) {
            System.err.println("Error parsing any date format: " + dateStr + " - " + e.getMessage());
        }

        return null;
    }

    /**
     * Get the standard date format used for database operations.
     * Thread-safe implementation.
     *
     * @return The SimpleDateFormat instance for current thread
     */
    public static SimpleDateFormat getDateFormat() {
        return DATE_FORMAT.get();
    }

    /**
     * Format a Date object to database-compatible string format.
     * Thread-safe implementation with null safety.
     *
     * @param date The Date object to format
     * @return Formatted date string, or null if date is null
     */
    public static String formatDate(Date date) {
        if (date == null) {
            return null;
        }

        try {
            return DATE_FORMAT.get().format(date);
        } catch (Exception e) {
            System.err.println("Error formatting date: " + date + " - " + e.getMessage());
            return null;
        }
    }

    /**
     * Format a Date object to detailed string format with milliseconds.
     * Thread-safe implementation with null safety.
     *
     * @param date The Date object to format
     * @return Formatted detailed date string (yyyy-MM-dd HH:mm:ss.SSS), or null if date is null
     */
    public static String formatDetailedDate(Date date) {
        if (date == null) {
            return null;
        }

        try {
            return DETAILED_DATE_FORMAT.get().format(date);
        } catch (Exception e) {
            System.err.println("Error formatting detailed date: " + date + " - " + e.getMessage());
            return formatDate(date); // Fallback to standard format
        }
    }

    /**
     * Format a Date object to human-readable Japanese format.
     * Thread-safe implementation with null safety.
     *
     * @param date The Date object to format
     * @return Formatted readable date string (yyyy年MM月dd日 HH:mm:ss), or null if date is null
     */
    public static String formatReadableDate(Date date) {
        if (date == null) {
            return null;
        }

        try {
            return READABLE_DATE_FORMAT.get().format(date);
        } catch (Exception e) {
            System.err.println("Error formatting readable date: " + date + " - " + e.getMessage());
            return formatDate(date); // Fallback to standard format
        }
    }

    /**
     * Format a Date object to detailed Japanese timezone string with milliseconds.
     *
     * @param date The Date object to format
     * @return Formatted detailed date string in Japanese timezone, or null if date is null
     */
    public static String formatDetailedJapaneseTimezone(Date date) {
        if (date == null) {
            return null;
        }

        try {
            ZonedDateTime japanTime = date.toInstant().atZone(JAPAN_TIMEZONE);
            return japanTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
        } catch (Exception e) {
            System.err.println("Error formatting detailed Japanese timezone date: " + date + " - " + e.getMessage());
            return formatDetailedDate(date); // Fallback to detailed format
        }
    }

    /**
     * Get the Japan timezone.
     *
     * @return The Japan timezone ZoneId
     */
    public static ZoneId getJapanTimezone() {
        return JAPAN_TIMEZONE;
    }
}
