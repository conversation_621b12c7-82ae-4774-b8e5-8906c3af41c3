package com.mrcresearch.util.database;

import com.mrcresearch.screen.model.SearchItemModel;
import com.mrcresearch.service.enums.ItemStatusEnum;
import com.mrcresearch.service.enums.ItemTypeEnum;
import com.mrcresearch.service.models.item.Item;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.InvalidPathException;
import java.nio.file.Path;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * search_itemsテーブルの検索アイテムを管理するためのリポジトリクラス。
 * CRUD操作とデータアクセス機能を提供します。
 */
public class SearchItemRepository {

    private static final Logger log = LoggerFactory.getLogger(SearchItemRepository.class);

    private static final String COLUMNS = "item_id, seller_id, status, product_name, price, " +
            "listing_date, updated_date, thumbnail, item_type, item_condition, category";
    private static final String PLACEHOLDERS = "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?";

    // SQLiteの "UPSERT" 機能を使用します。同じ複合主キー（item_id, updated_date）を持つ行が存在する場合は置換され、
    // 存在しない場合は新しい行が挿入されます。
    private static final String UPSERT_SQL = "INSERT OR REPLACE INTO search_items (" + COLUMNS + ") VALUES (" + PLACEHOLDERS + ")";

    /**
     * インスタンス化を防ぐためのプライベートコンストラクタ。
     * このクラスは静的メソッドのみを持つユーティリティクラスです。
     */
    private SearchItemRepository() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * upsert機能を使用して検索アイテムをデータベースに保存します。
     * 複合主キー（item_id, updated_date）に基づいて、既存のアイテムを更新するか、新しいアイテムを挿入します。
     *
     * @param items 保存するアイテムのリスト。
     * @return 正常に保存または更新されたアイテムの数。
     */
    public static int saveSearchItems(List<Item> items) {
        if (items == null || items.isEmpty()) {
            log.info("No items to save to search_items table.");
            return 0;
        }
        log.info("Attempting to save/update {} items.", items.size());
        int savedCount = saveItemsInBatch(items, UPSERT_SQL);
        log.info("Successfully saved/updated {} items to search_items table.", savedCount);
        return savedCount;
    }

    /**
     * ページネーション付きで検索アイテムを取得します。
     *
     * @param page     ページ番号（1ベース）。
     * @param pageSize 1ページあたりのレコード数。
     * @return 検索アイテムモデルのリスト。
     */
    public static List<SearchItemModel> getSearchItems(int page, int pageSize, Integer minPrice, Integer maxPrice) {
        StringBuilder sql = new StringBuilder("SELECT * FROM search_items ");
        List<Object> params = new ArrayList<>();

        if (minPrice != null || maxPrice != null) {
            sql.append("WHERE ");
            if (minPrice != null) {
                sql.append("price >= ? ");
                params.add(minPrice);
            }
            if (maxPrice != null) {
                if (minPrice != null) {
                    sql.append("AND ");
                }
                sql.append("price <= ? ");
                params.add(maxPrice);
            }
        }

        sql.append("ORDER BY updated_date DESC LIMIT ? OFFSET ?");
        params.add(pageSize);
        params.add((page - 1) * pageSize);

        return executeQueryForList(sql.toString(), params.toArray());
    }

    /**
     * 検索アイテムの総数を取得します。
     *
     * @return 検索アイテムの総数。
     */
    public static int getTotalSearchItemsCount(Integer minPrice, Integer maxPrice) {
        StringBuilder sql = new StringBuilder("SELECT COUNT(*) FROM search_items ");
        List<Object> params = new ArrayList<>();

        if (minPrice != null || maxPrice != null) {
            sql.append("WHERE ");
            if (minPrice != null) {
                sql.append("price >= ? ");
                params.add(minPrice);
            }
            if (maxPrice != null) {
                if (minPrice != null) {
                    sql.append("AND ");
                }
                sql.append("price <= ? ");
                params.add(maxPrice);
            }
        }

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql.toString())) {

            for (int i = 0; i < params.size(); i++) {
                pstmt.setObject(i + 1, params.get(i));
            }

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        } catch (SQLException e) {
            log.error("Error getting total search items count.", e);
        }
        return 0;
    }

    /**
     * ステータスとページネーションを指定して検索アイテムを取得します。
     *
     * @param status   フィルタリングするステータスID。
     * @param page     ページ番号（1ベース）。
     * @param pageSize 1ページあたりのレコード数。
     * @return 検索アイテムモデルのリスト。
     */
    public static List<SearchItemModel> getSearchItemsByStatus(int status, int page, int pageSize) {
        String sql = "SELECT * FROM search_items WHERE status = ? ORDER BY updated_date DESC LIMIT ? OFFSET ?";
        return executeQueryForList(sql, status, pageSize, (page - 1) * pageSize);
    }

    /**
     * アイテムIDで検索アイテムを削除します。
     *
     * @param itemId 削除するアイテムID。
     * @return 削除が成功した場合はtrue、それ以外はfalse。
     */
    public static boolean deleteSearchItem(String itemId) {
        String sql = "DELETE FROM search_items WHERE item_id = ?";
        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, itemId);
            int affectedRows = pstmt.executeUpdate();
            log.debug("Deleted {} search item(s) for item ID: {}", affectedRows, itemId);
            return affectedRows > 0;
        } catch (SQLException e) {
            log.error("Error deleting search item with ID: {}", itemId, e);
            return false;
        }
    }

    /**
     * バッチ挿入/更新操作を実行します。
     *
     * @param items 処理するアイテムのリスト。
     * @param sql   各アイテムに対して実行するSQLステートメント。
     * @return 影響を受けた総行数。
     */
    private static int saveItemsInBatch(List<Item> items, String sql) {
        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            conn.setAutoCommit(false); // パフォーマンスのため

            for (Item item : items) {
                setItemParameters(pstmt, item);
                pstmt.addBatch();
            }

            int[] updateCounts = pstmt.executeBatch();
            conn.commit();

            // 影響を受けたすべての行の合計を返します。
            return Arrays.stream(updateCounts).sum();

        } catch (SQLException e) {
            log.error("Error during batch save of search items.", e);
            // 問題を引き起こした可能性のあるアイテムの詳細をログに記録します
            String itemIds = items.stream().map(Item::getId).limit(10).collect(Collectors.joining(", "));
            log.error("First 10 item IDs in failed batch: {}", itemIds);
            return 0;
        }
    }

    /**
     * SELECTクエリを実行し、結果をSearchItemModelのリストにマッピングします。
     *
     * @param sql    実行するSQLクエリ。
     * @param params クエリパラメータ。
     * @return 検索アイテムモデルのリスト。
     */
    private static List<SearchItemModel> executeQueryForList(String sql, Object... params) {
        List<SearchItemModel> items = new ArrayList<>();
        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    items.add(mapRowToSearchItemModel(rs));
                }
            }
        } catch (SQLException e) {
            log.error("Error executing query for list: {}", sql, e);
        }
        return items;
    }

    /**
     * ItemオブジェクトからPreparedStatementにパラメータを設定します。
     */
    private static void setItemParameters(PreparedStatement pstmt, Item item) throws SQLException {
        pstmt.setString(1, item.getId());
        pstmt.setString(2, item.getSellerId());
        pstmt.setInt(3, ItemStatusEnum.getIdFromStatus(item.getStatus()));
        pstmt.setString(4, item.getName());
        pstmt.setInt(5, item.getPrice());
        pstmt.setString(6, item.getCreated());
        pstmt.setString(7, item.getUpdated());
        pstmt.setString(8, getThumbnailFilename(item));
        pstmt.setInt(9, determineItemTypeId(item));
        pstmt.setString(10, String.valueOf(item.getItemConditionId()));
        pstmt.setString(11, String.valueOf(item.getCategoryId()));
    }

    /**
     * ResultSetの単一行をSearchItemModelオブジェクトにマッピングします。
     */
    private static SearchItemModel mapRowToSearchItemModel(ResultSet rs) throws SQLException {
        SearchItemModel item = new SearchItemModel();
        item.setItemId(rs.getString("item_id"));
        item.setSellerId(rs.getString("seller_id"));
        item.setStatus(rs.getInt("status"));
        item.setProductName(rs.getString("product_name"));
        item.setPrice(rs.getInt("price"));
        item.setListingDate(rs.getString("listing_date"));
        item.setUpdatedDate(rs.getString("updated_date"));
        item.setThumbnail(rs.getString("thumbnail"));
        item.setItemType(rs.getInt("item_type"));
        item.setItemCondition(rs.getString("item_condition"));
        item.setCategory(rs.getString("category"));
        return item;
    }

    /**
     * 完全または部分的なパス文字列からファイル名を安全に抽出します。
     *
     * @return ファイル名。利用できない場合はnull。
     */
    private static String getThumbnailFilename(Item item) {
        if (item.getThumbnails() == null || item.getThumbnails().isEmpty()) {
            return null;
        }
        String thumbnailPath = item.getThumbnails().get(0);
        if (thumbnailPath == null || thumbnailPath.trim().isEmpty()) {
            return null;
        }
        try {
            // Path.getFileName()は、/と\の両方のセパレータを処理する堅牢な方法です。
            return Path.of(thumbnailPath).getFileName().toString();
        } catch (InvalidPathException e) {
            log.warn("Could not parse thumbnail path: '{}'. Using substring fallback.", thumbnailPath);
            // Path APIが拒否する可能性のある不正な形式のパスのフォールバック
            int lastSlash = thumbnailPath.lastIndexOf('/');
            int lastBackslash = thumbnailPath.lastIndexOf('\\');
            int lastSeparator = Math.max(lastSlash, lastBackslash);
            return thumbnailPath.substring(lastSeparator + 1);
        }
    }

    /**
     * アイテムのプロパティに基づいてアイテムタイプIDを決定します。
     */
    private static int determineItemTypeId(Item item) {
        // ショップ名が存在する場合、SHOPSタイプが優先されます。
        if (item.getShopName() != null && !item.getShopName().trim().isEmpty()) {
            return ItemTypeEnum.SHOPS.getId();
        }
        return ItemTypeEnum.getIdFromType(item.getItemType());
    }
}
