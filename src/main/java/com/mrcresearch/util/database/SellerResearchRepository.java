package com.mrcresearch.util.database;

import com.mrcresearch.screen.model.SellerModel;
import com.mrcresearch.service.SellerManagementService;

import java.util.List;

/**
 * Repository class for seller research related database operations.
 * Now uses the unified sellers table instead of seller_research_results.
 * Handles CRUD operations for seller information.
 */
public class SellerResearchRepository {

    /**
     * Private constructor to prevent instantiation.
     * This is a utility class with static methods only.
     */
    private SellerResearchRepository() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * Save a seller research result to the database.
     * Now uses the sellers table through SellerManagementService.
     *
     * @param sellerId   The seller ID
     * @param sellerName The seller name
     * @return true if the operation was successful, false otherwise
     */
    public static boolean saveSellerResearchResult(String sellerId, String sellerName) {
        try {
            // Use SellerManagementService to create or update seller
            SellerModel seller = SellerManagementService.getOrCreateSeller(sellerId);

            // Update seller name if provided and valid
            if (sellerName != null && !sellerName.trim().isEmpty() &&
                    !sellerName.equals(sellerId) && !sellerName.startsWith("セラー_")) {
                SellerManagementService.updateSellerName(sellerId, sellerName);
            }

            return true;
        } catch (Exception e) {
            System.err.println("Error in saveSellerResearchResult: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Update an existing seller in the database.
     * Now uses the sellers table through SellerManagementService.
     *
     * @param sellerId   The seller ID
     * @param sellerName The new seller name
     * @return true if the update was successful, false otherwise
     */
    public static boolean updateSellerResearchResult(String sellerId, String sellerName) {
        try {
            return SellerManagementService.updateSellerName(sellerId, sellerName);
        } catch (Exception e) {
            System.err.println("Error updating seller: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Get a list of sellers with pagination.
     * Now uses the sellers table through SellerRepository.
     *
     * @param page     The page number (1-based)
     * @param pageSize The number of records per page
     * @return A list of seller models
     */
    public static List<SellerModel> getSellerResearchResults(int page, int pageSize) {
        // Delegate to SellerRepository for unified seller management
        return SellerRepository.getAllSellers(page, pageSize);
    }

    /**
     * Get the total number of sellers.
     * Now uses the sellers table through SellerRepository.
     *
     * @return The total number of records
     */
    public static int getTotalSellerResearchResults() {
        return SellerRepository.getTotalSellerCount();
    }

    /**
     * Update seller name by seller ID.
     * Now uses the sellers table through SellerManagementService.
     *
     * @param sellerId   The seller ID
     * @param sellerName The new seller name
     * @return true if the update was successful, false otherwise
     */
    public static boolean updateSellerResearchResultName(String sellerId, String sellerName) {
        try {
            return SellerManagementService.updateSellerName(sellerId, sellerName);
        } catch (Exception e) {
            System.err.println("Error updating seller name: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
}
