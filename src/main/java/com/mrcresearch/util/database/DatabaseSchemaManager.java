package com.mrcresearch.util.database;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.FileSystems;
import java.sql.*;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * Manages database schema creation, migrations, and versioning.
 * Handles table creation, column additions, and database version management.
 */
public class DatabaseSchemaManager {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseSchemaManager.class);
    private static final int CURRENT_DB_VERSION = 18;

    /**
     * Private constructor to prevent instantiation.
     * This is a utility class with static methods only.
     */
    private DatabaseSchemaManager() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * Initialize the database by creating tables if they don't exist.
     */
    public static void initDatabase() {
        try (Connection conn = DatabaseConnectionManager.getConnection();
             Statement stmt = conn.createStatement()) {

            // Create db_version table if it doesn't exist
            String sql = "CREATE TABLE IF NOT EXISTS db_version (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "version INTEGER NOT NULL," +
                    "updated_date TEXT NOT NULL" +
                    ")";
            stmt.execute(sql);

            // Check and apply migrations before creating other tables
            // This ensures any schema changes are applied first
            checkAndMigrate();

            // Create keyword_search_results table if it doesn't exist
            sql = "CREATE TABLE IF NOT EXISTS keyword_search_results (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "keyword TEXT NOT NULL," +
                    "categories TEXT," +
                    "added_date TEXT NOT NULL," +
                    "updated_date TEXT NOT NULL," +
                    "status TEXT," +
                    "research_status TEXT," +
                    "min_price INTEGER," +
                    "max_price INTEGER," +
                    "pages INTEGER," +
                    "sort_by_new INTEGER DEFAULT 0," +
                    "ignore_shops INTEGER DEFAULT 0," +
                    "sales_status TEXT DEFAULT 'すべて'," +
                    "item_condition_ids TEXT," +
                    "exclude_keyword TEXT," +
                    "color_ids TEXT," +
                    "category_id TEXT" +
                    ")";
            stmt.execute(sql);

            // Create favorite_sellers table if it doesn't exist
            sql = "CREATE TABLE IF NOT EXISTS favorite_sellers (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "seller_name TEXT," +
                    "seller_id TEXT NOT NULL," +
                    "added_date TEXT NOT NULL," +
                    "updated_date TEXT NOT NULL," +
                    "research_status TEXT," +
                    "tags TEXT," +
                    "group_name TEXT" +
                    ")";
            stmt.execute(sql);

            // Create favorite_groups table if it doesn't exist
            sql = "CREATE TABLE IF NOT EXISTS favorite_groups (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "group_name TEXT NOT NULL UNIQUE," +
                    "last_updated TEXT," +
                    "research_started_at TEXT" +
                    ")";
            stmt.execute(sql);

            // Create favorite_tags table if it doesn't exist
            sql = "CREATE TABLE IF NOT EXISTS favorite_tags (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "tag_name TEXT NOT NULL UNIQUE," +
                    "last_updated TEXT," +
                    "research_started_at TEXT" +
                    ")";
            stmt.execute(sql);

            // Note: seller_research_results table has been deprecated and replaced with sellers table

            // Create settings table if it doesn't exist
            sql = "CREATE TABLE IF NOT EXISTS settings (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "sold_date INTEGER," +
                    "browser_load_timeout INTEGER," +
                    "browser_sleep_time INTEGER," +
                    "browser_init_sleep_time INTEGER," +
                    "browser_type TEXT," +
                    "default_keyword_pages INTEGER," +
                    "old_item_display_count INTEGER" +
                    ")";
            stmt.execute(sql);

            // Create login_info table if it doesn't exist
            sql = "CREATE TABLE IF NOT EXISTS login_info (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "email TEXT NOT NULL," +
                    "password_hash TEXT NOT NULL" +
                    ")";
            stmt.execute(sql);

            // Create keyword_search_items table if it doesn't exist (reference/mapping table)
            sql = "CREATE TABLE IF NOT EXISTS keyword_search_items (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "item_id TEXT NOT NULL," +
                    "status INTEGER" +  // Status ID mapping
                    ")";
            stmt.execute(sql);

            // Create search_items table if it doesn't exist (main data table)
            sql = "CREATE TABLE IF NOT EXISTS search_items (" +
                    "item_id TEXT NOT NULL," +
                    "seller_id TEXT," +
                    "status INTEGER," +  // 1:販売中, 2:取引中, 3:取引完了
                    "product_name TEXT," +
                    "price INTEGER," +
                    "listing_date TEXT," +
                    "updated_date TEXT NOT NULL," +
                    "thumbnail TEXT," +  // ファイル名のみ (例: image123.jpg)
                    "item_type INTEGER," +  // 1:通常出品, 2:SHOPSの商品
                    "item_condition TEXT," +
                    "category TEXT," +
                    "PRIMARY KEY (item_id, updated_date)" +
                    ")";
            stmt.execute(sql);

            // Create seller_searches table if it doesn't exist
            sql = "CREATE TABLE IF NOT EXISTS seller_searches (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "seller_id TEXT NOT NULL," +
                    "seller_name TEXT," +
                    "seller_name_changed INTEGER DEFAULT 0," +  // boolean field (0=false, 1=true)
                    "last_researched_at TEXT NOT NULL," +
                    "added_date TEXT NOT NULL," +
                    "updated_date TEXT NOT NULL" +
                    ")";
            stmt.execute(sql);

            // Create seller_search_items table if it doesn't exist (mapping table)
            sql = "CREATE TABLE IF NOT EXISTS seller_search_items (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "seller_search_id INTEGER," +
                    "item_id TEXT NOT NULL," +
                    "status INTEGER" +  // Status ID mapping
                    ")";
            stmt.execute(sql);

            // Create sellers table if it doesn't exist (master seller information table)
            sql = "CREATE TABLE IF NOT EXISTS sellers (" +
                    "seller_id TEXT PRIMARY KEY," +
                    "seller_name TEXT," +
                    "seller_name_changed INTEGER DEFAULT 0," +  // boolean field (0=false, 1=true)
                    "created_at TEXT NOT NULL," +
                    "updated_at TEXT NOT NULL," +
                    "research_status TEXT DEFAULT '待機中'" +  // Research status: 待機中, リサーチ中, 完了, エラー
                    ")";
            stmt.execute(sql);

            // Create index on sellers table for better performance
            sql = "CREATE INDEX IF NOT EXISTS idx_sellers_updated_at ON sellers(updated_at)";
            stmt.execute(sql);

            sql = "CREATE INDEX IF NOT EXISTS idx_sellers_name_changed ON sellers(seller_name_changed)";
            stmt.execute(sql);

        } catch (SQLException e) {
            logger.error("Failed to initialize database.", e);
        }
    }

    /**
     * Add columns to favorite_groups and favorite_tags tables if they don't exist
     */
    private static void addFavoriteGroupTagColumns(Connection conn) throws SQLException {
        try (Statement stmt = conn.createStatement()) {
            // Check and add columns to favorite_groups table
            try {
                stmt.execute("ALTER TABLE favorite_groups ADD COLUMN last_updated TEXT");
                logger.info("favorite_groups テーブルに last_updated カラムを追加しました");
            } catch (SQLException e) {
                // Column already exists, ignore
            }

            try {
                stmt.execute("ALTER TABLE favorite_groups ADD COLUMN research_started_at TEXT");
                logger.info("favorite_groups テーブルに research_started_at カラムを追加しました");
            } catch (SQLException e) {
                // Column already exists, ignore
            }

            // Check and add columns to favorite_tags table
            try {
                stmt.execute("ALTER TABLE favorite_tags ADD COLUMN last_updated TEXT");
                logger.info("favorite_tags テーブルに last_updated カラムを追加しました");
            } catch (SQLException e) {
                // Column already exists, ignore
            }

            try {
                stmt.execute("ALTER TABLE favorite_tags ADD COLUMN research_started_at TEXT");
                logger.info("favorite_tags テーブルに research_started_at カラムを追加しました");
            } catch (SQLException e) {
                // Column already exists, ignore
            }
        }
    }

    /**
     * Check the current database version and apply migrations if needed.
     */
    public static void checkAndMigrate() {
        try (Connection conn = DatabaseConnectionManager.getConnection()) {
            int currentVersion = getDatabaseVersion(conn);

            // If this is a new database, set the initial version
            if (currentVersion == -1) {
                setDatabaseVersion(conn);
                return;
            }

            // If the database is already at the current version, no migration needed
            if (currentVersion >= CURRENT_DB_VERSION) {
                // Even if the version is current, check for missing columns
                checkAndAddMissingColumns(conn);
                return;
            }

            // Apply migrations based on the current version
            applyMigrations(conn, currentVersion);

            // Update the database version
            setDatabaseVersion(conn);

        } catch (SQLException e) {
            logger.error("Failed to initialize database.", e);
        }
    }

    /**
     * Get the current database version from the db_version table.
     *
     * @param conn The database connection
     * @return The current database version, or -1 if not found
     */
    private static int getDatabaseVersion(Connection conn) throws SQLException {
        // Check if the db_version table exists
        try {
            String sql = "SELECT version FROM db_version ORDER BY id DESC LIMIT 1";
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                if (rs.next()) {
                    return rs.getInt("version");
                }
            }
        } catch (SQLException e) {
            // Table might not exist yet
            return -1;
        }
        return -1;
    }

    /**
     * Set the database version in the db_version table.
     *
     * @param conn The database connection
     */
    private static void setDatabaseVersion(Connection conn) throws SQLException {
        String sql = "INSERT INTO db_version (version, updated_date) VALUES (?, ?)";
        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setInt(1, DatabaseSchemaManager.CURRENT_DB_VERSION);
            pstmt.setString(2, DatabaseDateTimeUtil.getDateFormat().format(new java.util.Date()));
            pstmt.executeUpdate();
        }
    }

    /**
     * Apply database migrations based on the current version.
     *
     * @param conn           The database connection
     * @param currentVersion The current database version
     */
    private static void applyMigrations(Connection conn, int currentVersion) throws SQLException {
        // Apply migrations sequentially
        if (currentVersion < 1) {
            // Migration to version 1
            migrateToVersion1(conn);
        }

        if (currentVersion < 2) {
            // Migration to version 2 - Add keyword_search_items table
            migrateToVersion2(conn);
        }

        if (currentVersion < 3) {
            // Migration to version 3 - Convert thumbnail paths to filenames only
            migrateToVersion3(conn);
        }

        if (currentVersion < 4) {
            // Migration to version 4 - Make keyword_search_result_id nullable
            migrateToVersion4(conn);
        }

        if (currentVersion < 5) {
            // Migration to version 5 - Add theme_type column to settings table
            migrateToVersion5(conn);
        }

        if (currentVersion < 6) {
            // Migration to version 6 - Convert status and item_type to INTEGER IDs
            migrateToVersion6(conn);
        }

        if (currentVersion < 7) {
            // Migration to version 7 - Restructure keyword_search_items and create search_items table
            migrateToVersion7(conn);
        }

        if (currentVersion < 8) {
            // Migration to version 8 - Add headless_mode column to settings table
            migrateToVersion8(conn);
        }

        if (currentVersion < 9) {
            // Migration to version 9 - Add seller search persistence tables
            migrateToVersion9(conn);
        }

        if (currentVersion < 10) {
            // Migration to version 10 - Add unique constraint to favorite_sellers.seller_id
            migrateToVersion10(conn);
        }

        if (currentVersion < 11) {
            // Migration to version 11 - Change search_items primary key to composite key (item_id, updated_date)
            migrateToVersion11(conn);
        }

        if (currentVersion < 12) {
            // Migration to version 12 - Add sales_aggregation_default_days column to settings table
            migrateToVersion12(conn);
        }

        if (currentVersion < 13) {
            // Migration to version 13 - Add keyword_search_result_id column to search_items table
            migrateToVersion13(conn);
        }

        if (currentVersion < 14) {
            // Migration to version 14 - Add keyword_search_result_id column to keyword_search_items table
            migrateToVersion14(conn);
        }

        if (currentVersion < 15) {
            // Migration to version 15 - Remove keyword_search_result_id column from search_items table
            migrateToVersion15(conn);
        }

        if (currentVersion < 16) {
            // Migration to version 16 - Add font_size column to settings table
            migrateToVersion16(conn);
        }

        if (currentVersion < 17) {
            // Migration to version 17 - Add category_id column to keyword_search_results table
            migrateToVersion17(conn);
        }

        if (currentVersion < 18) {
            // Migration to version 18 - Add missing columns to keyword_search_results table
            migrateToVersion18(conn);
        }

        // Always check for missing columns in favorite_groups and favorite_tags tables
        addFavoriteGroupTagColumns(conn);
    }

    /**
     * Migrate the database to version 1.
     *
     * @param conn The database connection
     */
    private static void migrateToVersion1(Connection conn) throws SQLException {
        // Use the generic method to check and add all missing columns
        checkAndAddMissingColumns(conn);

        // Special handling for research_status column in keyword_search_results
        // to set it equal to status for backward compatibility
        if (columnExists(conn, "keyword_search_results", "research_status")) {
            try (Statement stmt = conn.createStatement()) {
                // Update existing records to set research_status equal to status where it's null
                stmt.execute("UPDATE keyword_search_results SET research_status = status WHERE research_status IS NULL");
            }
        }

        // Add any other version 1 specific migrations here
    }

    /**
     * Migrate the database to version 2.
     * Adds the keyword_search_items table for storing search result items.
     *
     * @param conn The database connection
     */
    private static void migrateToVersion2(Connection conn) throws SQLException {
        // Create keyword_search_items table if it doesn't exist
        String sql = "CREATE TABLE IF NOT EXISTS keyword_search_items (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "keyword_search_result_id INTEGER NOT NULL," +
                "item_id TEXT NOT NULL," +
                "seller_id TEXT," +
                "status TEXT," +
                "name TEXT," +
                "price INTEGER," +
                "created_date TEXT," +
                "updated_date TEXT," +
                "thumbnail TEXT," +
                "item_type TEXT," +
                "item_condition_id INTEGER," +
                "shipping_method_id INTEGER," +
                "category_id INTEGER," +
                "saved_date TEXT NOT NULL," +
                "FOREIGN KEY (keyword_search_result_id) REFERENCES keyword_search_results(id)" +
                ")";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            logger.info("バージョン2移行のために keyword_search_items テーブルを作成しました");
        }
    }

    /**
     * Migrate the database to version 3.
     * Converts thumbnail paths from full paths to filenames only.
     *
     * @param conn The database connection
     */
    private static void migrateToVersion3(Connection conn) throws SQLException {
        logger.info("バージョン3への移行を開始します: サムネイルパスをファイル名のみに変換中");

        // Update keyword_search_items table
        String selectSql = "SELECT id, thumbnail FROM keyword_search_items WHERE thumbnail IS NOT NULL AND thumbnail != ''";
        String updateSql = "UPDATE keyword_search_items SET thumbnail = ? WHERE id = ?";

        int updatedCount = 0;

        try (Statement selectStmt = conn.createStatement();
             PreparedStatement updateStmt = conn.prepareStatement(updateSql);
             ResultSet rs = selectStmt.executeQuery(selectSql)) {

            while (rs.next()) {
                int id = rs.getInt("id");
                String thumbnailPath = rs.getString("thumbnail");

                if (thumbnailPath != null && !thumbnailPath.trim().isEmpty()) {
                    // Extract filename from full path
                    String fileName = extractFileName(thumbnailPath);

                    if (fileName != null && !fileName.equals(thumbnailPath)) {
                        updateStmt.setString(1, fileName);
                        updateStmt.setInt(2, id);
                        updateStmt.executeUpdate();
                        updatedCount++;
                    }
                }
            }
        }

        logger.info("バージョン3への移行が完了しました: " + updatedCount + " 件のサムネイルパスを更新しました");
    }

    /**
     * Extract filename from a full path.
     *
     * @param fullPath The full path
     * @return The filename only, or null if extraction fails
     */
    private static String extractFileName(String fullPath) {
        if (fullPath == null || fullPath.trim().isEmpty()) {
            return null;
        }

        // Handle both Windows and Unix path separators
        String fileName = fullPath;
        int lastSeparatorIndex = Math.max(
                fileName.lastIndexOf(FileSystems.getDefault().getSeparator()),
                Math.max(fileName.lastIndexOf("/"), fileName.lastIndexOf("\\"))
        );

        if (lastSeparatorIndex >= 0 && lastSeparatorIndex < fileName.length() - 1) {
            fileName = fileName.substring(lastSeparatorIndex + 1);
        }

        return fileName;
    }

    /**
     * Migrate the database to version 4.
     * Makes keyword_search_result_id nullable in keyword_search_items table.
     *
     * @param conn The database connection
     */
    private static void migrateToVersion4(Connection conn) throws SQLException {
        logger.info("バージョン4への移行を開始します: keyword_search_result_id を NULL 許容に設定中");

        try {
            // Check if the table exists
            boolean tableExists = false;
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery("SELECT name FROM sqlite_master WHERE type='table' AND name='keyword_search_items'")) {
                tableExists = rs.next();
            }

            if (!tableExists) {
                logger.info("keyword_search_items テーブルが存在しないため、移行をスキップします");
                return;
            }

            // Create a new table with the updated schema (keyword_search_result_id nullable)
            String createNewTableSql = "CREATE TABLE keyword_search_items_new (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "keyword_search_result_id INTEGER," +  // Removed NOT NULL constraint
                    "item_id TEXT NOT NULL," +
                    "seller_id TEXT," +
                    "status TEXT," +
                    "name TEXT," +
                    "price INTEGER," +
                    "created_date TEXT," +
                    "updated_date TEXT," +
                    "thumbnail TEXT," +
                    "item_type TEXT," +
                    "item_condition_id INTEGER," +
                    "shipping_method_id INTEGER," +
                    "category_id INTEGER," +
                    "saved_date TEXT NOT NULL" +
                    ")";

            try (Statement stmt = conn.createStatement()) {
                // Create the new table
                stmt.execute(createNewTableSql);

                // Copy data from old table to new table
                String copyDataSql = "INSERT INTO keyword_search_items_new " +
                        "(id, keyword_search_result_id, item_id, seller_id, status, name, price, " +
                        "created_date, updated_date, thumbnail, item_type, item_condition_id, " +
                        "shipping_method_id, category_id, saved_date) " +
                        "SELECT id, keyword_search_result_id, item_id, seller_id, status, name, price, " +
                        "created_date, updated_date, thumbnail, item_type, item_condition_id, " +
                        "shipping_method_id, category_id, saved_date FROM keyword_search_items";
                stmt.execute(copyDataSql);

                // Drop the old table
                stmt.execute("DROP TABLE keyword_search_items");

                // Rename the new table to the original name
                stmt.execute("ALTER TABLE keyword_search_items_new RENAME TO keyword_search_items");

                logger.info("keyword_search_items テーブルを keyword_search_result_id が NULL 許容になるように正常に移行しました");
            }

        } catch (SQLException e) {
            logger.error("Error during migration to version 4: " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Migrate the database to version 5.
     * Adds theme_type column to settings table.
     *
     * @param conn The database connection
     */
    private static void migrateToVersion5(Connection conn) throws SQLException {
        logger.info("バージョン5への移行を開始します: settings テーブルに theme_type カラムを追加中");

        try {
            // Add theme_type column to settings table if it doesn't exist
            boolean columnAdded = addColumnIfNotExists(conn, "settings", "theme_type", "TEXT", "'light'");

            if (columnAdded) {
                logger.info("settings テーブルに theme_type カラムを正常に追加しました");
            } else {
                logger.info("settings テーブルに theme_type カラムは既に存在します");
            }

        } catch (SQLException e) {
            logger.error("Error during migration to version 5: " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Migrate the database to version 6.
     * Converts status and item_type columns from TEXT to INTEGER IDs.
     *
     * @param conn The database connection
     */
    private static void migrateToVersion6(Connection conn) throws SQLException {
        logger.info("バージョン6への移行を開始します: status と item_type を INTEGER ID に変換中");

        try {
            // Check if the table exists
            boolean tableExists = false;
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery("SELECT name FROM sqlite_master WHERE type='table' AND name='keyword_search_items'")) {
                tableExists = rs.next();
            }

            if (!tableExists) {
                logger.info("keyword_search_items テーブルが存在しないため、移行をスキップします");
                return;
            }

            // Create a new table with INTEGER status and item_type columns
            String createNewTableSql = "CREATE TABLE keyword_search_items_new (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "keyword_search_result_id INTEGER," +
                    "item_id TEXT NOT NULL," +
                    "seller_id TEXT," +
                    "status INTEGER," +  // Changed from TEXT to INTEGER
                    "name TEXT," +
                    "price INTEGER," +
                    "created_date TEXT," +
                    "updated_date TEXT," +
                    "thumbnail TEXT," +
                    "item_type INTEGER," +  // Changed from TEXT to INTEGER
                    "item_condition_id INTEGER," +
                    "shipping_method_id INTEGER," +
                    "category_id INTEGER," +
                    "saved_date TEXT NOT NULL" +
                    ")";

            try (Statement stmt = conn.createStatement()) {
                // Create the new table
                stmt.execute(createNewTableSql);

                // Copy data from old table to new table with status and item_type conversion
                String copyDataSql = "INSERT INTO keyword_search_items_new " +
                        "(id, keyword_search_result_id, item_id, seller_id, status, name, price, " +
                        "created_date, updated_date, thumbnail, item_type, item_condition_id, " +
                        "shipping_method_id, category_id, saved_date) " +
                        "SELECT id, keyword_search_result_id, item_id, seller_id, " +
                        "CASE " +
                        "  WHEN status = 'ITEM_STATUS_ON_SALE' OR status = 'on_sale' THEN 1 " +
                        "  WHEN status = 'ITEM_STATUS_TRADING' OR status = 'trading' THEN 2 " +
                        "  WHEN status = 'ITEM_STATUS_SOLD_OUT' OR status = 'sold_out' THEN 3 " +
                        "  ELSE 1 " +  // Default to ON_SALE
                        "END as status, " +
                        "name, price, created_date, updated_date, thumbnail, " +
                        "CASE " +
                        "  WHEN item_type LIKE '%SHOPS%' OR item_type LIKE '%SHOP%' THEN 2 " +
                        "  ELSE 1 " +  // Default to NORMAL
                        "END as item_type, " +
                        "item_condition_id, shipping_method_id, category_id, saved_date " +
                        "FROM keyword_search_items";
                stmt.execute(copyDataSql);

                // Drop the old table
                stmt.execute("DROP TABLE keyword_search_items");

                // Rename the new table to the original name
                stmt.execute("ALTER TABLE keyword_search_items_new RENAME TO keyword_search_items");

                logger.info("keyword_search_items テーブルを INTEGER 型の status と item_type を使用するように正常に移行しました");
            }

        } catch (SQLException e) {
            logger.error("Error during migration to version 6: " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Migrate the database to version 7.
     * Restructures keyword_search_items table and creates search_items table.
     *
     * @param conn The database connection
     */
    private static void migrateToVersion7(Connection conn) throws SQLException {
        logger.info("バージョン7への移行を開始します: keyword_search_items の再構築と search_items テーブルの作成");

        try {
            // Check if the keyword_search_items table exists
            boolean tableExists = false;
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery("SELECT name FROM sqlite_master WHERE type='table' AND name='keyword_search_items'")) {
                tableExists = rs.next();
            }

            if (!tableExists) {
                logger.info("keyword_search_items テーブルが存在しないため、新しいテーブルを作成します");
                createNewTablesForVersion7(conn);
                return;
            }

            // Create the new search_items table
            String createSearchItemsTableSql = "CREATE TABLE IF NOT EXISTS search_items (" +
                    "item_id TEXT PRIMARY KEY," +
                    "seller_id TEXT," +
                    "status INTEGER," +
                    "product_name TEXT," +
                    "price INTEGER," +
                    "listing_date TEXT," +
                    "updated_date TEXT," +
                    "thumbnail TEXT," +
                    "item_type INTEGER," +
                    "item_condition TEXT," +
                    "category TEXT" +
                    ")";

            try (Statement stmt = conn.createStatement()) {
                // Create the search_items table
                stmt.execute(createSearchItemsTableSql);

                // Migrate data from keyword_search_items to search_items with upsert logic
                String migrateDataSql = "INSERT OR REPLACE INTO search_items " +
                        "(item_id, seller_id, status, product_name, price, listing_date, updated_date, thumbnail, item_type, item_condition, category) " +
                        "SELECT item_id, seller_id, status, name, price, created_date, updated_date, thumbnail, item_type, " +
                        "CAST(item_condition_id AS TEXT), CAST(category_id AS TEXT) " +
                        "FROM keyword_search_items " +
                        "WHERE item_id IS NOT NULL";
                stmt.execute(migrateDataSql);

                // Create new keyword_search_items table (reference/mapping table)
                String createNewKeywordSearchItemsTableSql = "CREATE TABLE keyword_search_items_new (" +
                        "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                        "item_id TEXT NOT NULL," +
                        "status INTEGER" +
                        ")";
                stmt.execute(createNewKeywordSearchItemsTableSql);

                // Migrate reference data to new keyword_search_items table
                String migrateReferenceDataSql = "INSERT INTO keyword_search_items_new (item_id, status) " +
                        "SELECT DISTINCT item_id, status FROM keyword_search_items WHERE item_id IS NOT NULL";
                stmt.execute(migrateReferenceDataSql);

                // Drop the old keyword_search_items table
                stmt.execute("DROP TABLE keyword_search_items");

                // Rename the new table to the original name
                stmt.execute("ALTER TABLE keyword_search_items_new RENAME TO keyword_search_items");

                logger.info("バージョン7への移行が完了しました: search_items テーブルを作成し、keyword_search_items を再構築しました");
            }

        } catch (SQLException e) {
            logger.error("Error during migration to version 7: " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Create new tables for version 7 when keyword_search_items doesn't exist.
     *
     * @param conn The database connection
     */
    private static void createNewTablesForVersion7(Connection conn) throws SQLException {
        try (Statement stmt = conn.createStatement()) {
            // Create keyword_search_items table (reference/mapping table)
            String createKeywordSearchItemsTableSql = "CREATE TABLE IF NOT EXISTS keyword_search_items (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "item_id TEXT NOT NULL," +
                    "status INTEGER" +
                    ")";
            stmt.execute(createKeywordSearchItemsTableSql);

            // Create search_items table (main data table)
            String createSearchItemsTableSql = "CREATE TABLE IF NOT EXISTS search_items (" +
                    "item_id TEXT PRIMARY KEY," +
                    "seller_id TEXT," +
                    "status INTEGER," +
                    "product_name TEXT," +
                    "price INTEGER," +
                    "listing_date TEXT," +
                    "updated_date TEXT," +
                    "thumbnail TEXT," +
                    "item_type INTEGER," +
                    "item_condition TEXT," +
                    "category TEXT" +
                    ")";
            stmt.execute(createSearchItemsTableSql);

            logger.info("バージョン7用の新しいテーブルを作成しました");
        }
    }

    /**
     * Migrate the database to version 8.
     * Adds headless_mode column to settings table.
     *
     * @param conn The database connection
     */
    private static void migrateToVersion8(Connection conn) throws SQLException {
        logger.info("バージョン8への移行を開始します: settings テーブルに headless_mode カラムを追加中");

        try {
            // Add headless_mode column to settings table if it doesn't exist
            boolean columnAdded = addColumnIfNotExists(conn, "settings", "headless_mode", "INTEGER", "0");

            if (columnAdded) {
                logger.info("settings テーブルに headless_mode カラムを正常に追加しました");
            } else {
                logger.info("settings テーブルに headless_mode カラムは既に存在します");
            }

        } catch (SQLException e) {
            logger.error("Error during migration to version 8: " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Check for missing columns in all tables and add them if needed.
     * This method ensures that all tables have the expected columns,
     * even if the database schema was created with an older version.
     *
     * @param conn The database connection
     */
    private static void checkAndAddMissingColumns(Connection conn) throws SQLException {
        // Define the expected columns for each table
        // Format: tableName -> Map of columnName -> columnType
        Map<String, Map<String, String>> tableColumns = new HashMap<>();

        // Define expected columns for keyword_search_results
        Map<String, String> keywordSearchResultsColumns = new HashMap<>();
        keywordSearchResultsColumns.put("id", "INTEGER PRIMARY KEY AUTOINCREMENT");
        keywordSearchResultsColumns.put("keyword", "TEXT NOT NULL");
        keywordSearchResultsColumns.put("categories", "TEXT");
        keywordSearchResultsColumns.put("added_date", "TEXT NOT NULL");
        keywordSearchResultsColumns.put("updated_date", "TEXT NOT NULL");
        keywordSearchResultsColumns.put("status", "TEXT");
        keywordSearchResultsColumns.put("research_status", "TEXT");
        keywordSearchResultsColumns.put("min_price", "INTEGER");
        keywordSearchResultsColumns.put("max_price", "INTEGER");
        keywordSearchResultsColumns.put("pages", "INTEGER");
        keywordSearchResultsColumns.put("sort_by_new", "INTEGER DEFAULT 0");
        keywordSearchResultsColumns.put("ignore_shops", "INTEGER DEFAULT 0");
        keywordSearchResultsColumns.put("sales_status", "TEXT DEFAULT 'すべて'");
        // Detailed search parameters
        keywordSearchResultsColumns.put("item_condition_ids", "TEXT");
        keywordSearchResultsColumns.put("exclude_keyword", "TEXT");
        keywordSearchResultsColumns.put("color_ids", "TEXT");
        tableColumns.put("keyword_search_results", keywordSearchResultsColumns);

        // Define expected columns for favorite_sellers
        Map<String, String> favoriteSellersColumns = new HashMap<>();
        favoriteSellersColumns.put("id", "INTEGER PRIMARY KEY AUTOINCREMENT");
        favoriteSellersColumns.put("seller_name", "TEXT");
        favoriteSellersColumns.put("seller_id", "TEXT NOT NULL");
        favoriteSellersColumns.put("added_date", "TEXT NOT NULL");
        favoriteSellersColumns.put("updated_date", "TEXT NOT NULL");
        favoriteSellersColumns.put("research_status", "TEXT");
        favoriteSellersColumns.put("tags", "TEXT");
        favoriteSellersColumns.put("group_name", "TEXT");
        tableColumns.put("favorite_sellers", favoriteSellersColumns);

        // Note: seller_research_results table has been deprecated and replaced with sellers table

        // Define expected columns for sellers
        Map<String, String> sellersColumns = new HashMap<>();
        sellersColumns.put("seller_id", "TEXT PRIMARY KEY");
        sellersColumns.put("seller_name", "TEXT");
        sellersColumns.put("seller_name_changed", "INTEGER DEFAULT 0");
        sellersColumns.put("created_at", "TEXT NOT NULL");
        sellersColumns.put("updated_at", "TEXT NOT NULL");
        sellersColumns.put("research_status", "TEXT DEFAULT '待機中'");
        tableColumns.put("sellers", sellersColumns);

        // Define expected columns for settings
        Map<String, String> settingsColumns = new HashMap<>();
        settingsColumns.put("id", "INTEGER PRIMARY KEY AUTOINCREMENT");
        settingsColumns.put("sold_date", "INTEGER");
        settingsColumns.put("browser_load_timeout", "INTEGER");
        settingsColumns.put("browser_init_sleep_time", "INTEGER");
        settingsColumns.put("browser_sleep_time", "INTEGER");
        settingsColumns.put("browser_type", "TEXT");
        settingsColumns.put("default_keyword_pages", "INTEGER");
        settingsColumns.put("old_item_display_count", "INTEGER");
        settingsColumns.put("theme_type", "TEXT");
        settingsColumns.put("headless_mode", "INTEGER");
        settingsColumns.put("sales_aggregation_default_days", "INTEGER");
        settingsColumns.put("font_size", "TEXT");
        tableColumns.put("settings", settingsColumns);

        // Define expected columns for login_info
        Map<String, String> loginInfoColumns = new HashMap<>();
        loginInfoColumns.put("id", "INTEGER PRIMARY KEY AUTOINCREMENT");
        loginInfoColumns.put("email", "TEXT NOT NULL");
        loginInfoColumns.put("password_hash", "TEXT NOT NULL");
        tableColumns.put("login_info", loginInfoColumns);

        // Check each table and add missing columns
        for (String tableName : tableColumns.keySet()) {
            Map<String, String> expectedColumns = tableColumns.get(tableName);

            // Get existing columns
            Set<String> existingColumns = new HashSet<>();
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery("PRAGMA table_info(" + tableName + ")")) {
                while (rs.next()) {
                    existingColumns.add(rs.getString("name"));
                }
            } catch (SQLException e) {
                // Table might not exist yet
                continue;
            }

            // Add missing columns
            for (String columnName : expectedColumns.keySet()) {
                if (!existingColumns.contains(columnName)) {
                    // Skip primary key columns as they can't be added later
                    if (expectedColumns.get(columnName).contains("PRIMARY KEY")) {
                        continue;
                    }

                    String columnType = expectedColumns.get(columnName);

                    // For NOT NULL columns, we need to provide a default value
                    String defaultValue = null;
                    if (columnType.contains("NOT NULL")) {
                        if (columnType.startsWith("TEXT")) {
                            defaultValue = "''";
                        } else if (columnType.startsWith("INTEGER")) {
                            defaultValue = "0";
                        } else if (columnType.startsWith("REAL")) {
                            defaultValue = "0.0";
                        }
                    }

                    // Add the column
                    try (Statement stmt = conn.createStatement()) {
                        String sql = "ALTER TABLE " + tableName + " ADD COLUMN " + columnName + " " + columnType;
                        stmt.execute(sql);

                        // Set default value if needed
                        if (defaultValue != null) {
                            sql = "UPDATE " + tableName + " SET " + columnName + " = " + defaultValue +
                                    " WHERE " + columnName + " IS NULL";
                            stmt.execute(sql);
                        }

                        logger.info("テーブル " + tableName + " にカラム " + columnName + " を追加しました");
                    }
                }
            }
        }
    }

    /**
     * Check if a column exists in a table.
     *
     * @param conn       The database connection
     * @param tableName  The name of the table
     * @param columnName The name of the column
     * @return true if the column exists, false otherwise
     */
    public static boolean columnExists(Connection conn, String tableName, String columnName) throws SQLException {
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("PRAGMA table_info(" + tableName + ")")) {
            while (rs.next()) {
                if (columnName.equals(rs.getString("name"))) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Add a column to a table if it doesn't exist.
     *
     * @param conn         The database connection
     * @param tableName    The name of the table
     * @param columnName   The name of the column
     * @param columnType   The SQL type of the column (e.g., "TEXT", "INTEGER")
     * @param defaultValue The default value for existing rows (can be null)
     * @return true if the column was added, false if it already existed
     */
    public static boolean addColumnIfNotExists(Connection conn, String tableName, String columnName,
                                               String columnType, String defaultValue) throws SQLException {
        if (!columnExists(conn, tableName, columnName)) {
            try (Statement stmt = conn.createStatement()) {
                // Add the column
                String sql = "ALTER TABLE " + tableName + " ADD COLUMN " + columnName + " " + columnType;
                stmt.execute(sql);

                // Set default value if provided
                if (defaultValue != null) {
                    sql = "UPDATE " + tableName + " SET " + columnName + " = " + defaultValue +
                            " WHERE " + columnName + " IS NULL";
                    stmt.execute(sql);
                }
            }
            return true;
        }
        return false;
    }

    /**
     * Migrate the database to version 9.
     * Adds seller search persistence tables.
     *
     * @param conn The database connection
     */
    private static void migrateToVersion9(Connection conn) throws SQLException {
        logger.info("バージョン9への移行を開始します: セラー検索永続化テーブルを追加中");

        try (Statement stmt = conn.createStatement()) {
            // Create seller_searches table if it doesn't exist
            String createSellerSearchesTableSql = "CREATE TABLE IF NOT EXISTS seller_searches (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "seller_id TEXT NOT NULL," +
                    "seller_name TEXT," +
                    "seller_name_changed INTEGER DEFAULT 0," +  // boolean field (0=false, 1=true)
                    "last_researched_at TEXT NOT NULL," +
                    "added_date TEXT NOT NULL," +
                    "updated_date TEXT NOT NULL" +
                    ")";
            stmt.execute(createSellerSearchesTableSql);

            // Create seller_search_items table if it doesn't exist (mapping table)
            String createSellerSearchItemsTableSql = "CREATE TABLE IF NOT EXISTS seller_search_items (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "seller_search_id INTEGER," +
                    "item_id TEXT NOT NULL," +
                    "status INTEGER" +  // Status ID mapping
                    ")";
            stmt.execute(createSellerSearchItemsTableSql);

            logger.info("バージョン9用のセラー検索永続化テーブルを正常に作成しました");
        } catch (SQLException e) {
            logger.error("Error during migration to version 9: " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Migrate the database to version 10.
     * Adds unique constraint to favorite_sellers.seller_id to prevent duplicates.
     *
     * @param conn The database connection
     */
    private static void migrateToVersion10(Connection conn) throws SQLException {
        logger.info("バージョン10への移行を開始します: favorite_sellers.seller_id にユニーク制約を追加中");

        try {
            // Check if favorite_sellers table exists
            boolean tableExists = false;
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery("SELECT name FROM sqlite_master WHERE type='table' AND name='favorite_sellers'")) {
                tableExists = rs.next();
            }

            if (!tableExists) {
                logger.info("favorite_sellers テーブルが存在しないため、移行をスキップします");
                return;
            }

            // First, remove any duplicate records based on seller_id
            // Keep only the most recent record for each seller_id
            String removeDuplicatesSql = "DELETE FROM favorite_sellers " +
                    "WHERE id NOT IN (" +
                    "    SELECT MIN(id) " +
                    "    FROM favorite_sellers " +
                    "    GROUP BY seller_id" +
                    ")";

            try (Statement stmt = conn.createStatement()) {
                int deletedRows = stmt.executeUpdate(removeDuplicatesSql);
                if (deletedRows > 0) {
                    logger.info("favorite_sellers テーブルから重複するレコードを " + deletedRows + " 件削除しました");
                }
            }

            // Create a new table with unique constraint
            String createNewTableSql = "CREATE TABLE favorite_sellers_new (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "seller_name TEXT," +
                    "seller_id TEXT NOT NULL UNIQUE," +  // Add UNIQUE constraint
                    "added_date TEXT NOT NULL," +
                    "updated_date TEXT NOT NULL," +
                    "research_status TEXT," +
                    "tags TEXT," +
                    "group_name TEXT" +
                    ")";

            try (Statement stmt = conn.createStatement()) {
                stmt.execute(createNewTableSql);
                logger.info("ユニーク制約付きの新しい favorite_sellers テーブルを作成しました");
            }

            // Copy data from old table to new table
            String copyDataSql = "INSERT INTO favorite_sellers_new " +
                    "(id, seller_name, seller_id, added_date, updated_date, research_status, tags, group_name) " +
                    "SELECT id, seller_name, seller_id, added_date, updated_date, research_status, tags, group_name " +
                    "FROM favorite_sellers";

            try (Statement stmt = conn.createStatement()) {
                stmt.execute(copyDataSql);
                logger.info("新しい favorite_sellers テーブルにデータをコピーしました");
            }

            // Drop the old table
            try (Statement stmt = conn.createStatement()) {
                stmt.execute("DROP TABLE favorite_sellers");
                logger.info("古い favorite_sellers テーブルを削除しました");
            }

            // Rename the new table to the original name
            try (Statement stmt = conn.createStatement()) {
                stmt.execute("ALTER TABLE favorite_sellers_new RENAME TO favorite_sellers");
                logger.info("新しいテーブルの名前を favorite_sellers に変更しました");
            }

            logger.info("バージョン10への移行が正常に完了しました");

        } catch (SQLException e) {
            logger.error("Error during migration to version 10: " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Migrate the database to version 11.
     * Changes the search_items table primary key from item_id to a composite key (item_id, updated_date).
     * This allows storing multiple versions of the same item with different update dates.
     *
     * @param conn The database connection
     */
    private static void migrateToVersion11(Connection conn) throws SQLException {
        logger.info("バージョン11への移行を開始します: search_items の主キーを複合キーに変更中");

        try {
            // Check if search_items table exists
            boolean tableExists = false;
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery("SELECT name FROM sqlite_master WHERE type='table' AND name='search_items'")) {
                tableExists = rs.next();
            }

            if (!tableExists) {
                logger.info("search_items テーブルが存在しないため、移行をスキップします");
                return;
            }

            // Create a new table with composite primary key
            String createNewTableSql = "CREATE TABLE search_items_new (" +
                    "item_id TEXT NOT NULL," +
                    "seller_id TEXT," +
                    "status INTEGER," +  // 1:販売中, 2:取引中, 3:取引完了
                    "product_name TEXT," +
                    "price INTEGER," +
                    "listing_date TEXT," +
                    "updated_date TEXT NOT NULL," +
                    "thumbnail TEXT," +  // ファイル名のみ (例: image123.jpg)
                    "item_type INTEGER," +  // 1:通常出品, 2:SHOPSの商品
                    "item_condition TEXT," +
                    "category TEXT," +
                    "PRIMARY KEY (item_id, updated_date)" +
                    ")";

            try (Statement stmt = conn.createStatement()) {
                stmt.execute(createNewTableSql);
                logger.info("複合主キーを持つ新しい search_items テーブルを作成しました");
            }

            // Copy data from old table to new table
            String copyDataSql = "INSERT INTO search_items_new " +
                    "(item_id, seller_id, status, product_name, price, listing_date, updated_date, thumbnail, item_type, item_condition, category) " +
                    "SELECT item_id, seller_id, status, product_name, price, listing_date, updated_date, thumbnail, item_type, item_condition, category " +
                    "FROM search_items";

            try (Statement stmt = conn.createStatement()) {
                stmt.execute(copyDataSql);
                logger.info("新しい search_items テーブルにデータをコピーしました");
            }

            // Drop the old table
            try (Statement stmt = conn.createStatement()) {
                stmt.execute("DROP TABLE search_items");
                logger.info("古い search_items テーブルを削除しました");
            }

            // Rename the new table to the original name
            try (Statement stmt = conn.createStatement()) {
                stmt.execute("ALTER TABLE search_items_new RENAME TO search_items");
                logger.info("新しいテーブルの名前を search_items に変更しました");
            }

            logger.info("バージョン11への移行が正常に完了しました");

        } catch (SQLException e) {
            logger.error("Error during migration to version 11: " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Migrate the database to version 12.
     * Adds sales_aggregation_default_days column to settings table.
     *
     * @param conn The database connection
     */
    private static void migrateToVersion12(Connection conn) throws SQLException {
        logger.info("バージョン12への移行を開始します: settings テーブルに sales_aggregation_default_days カラムを追加中");

        try {
            // Add sales_aggregation_default_days column to settings table if it doesn't exist
            boolean columnAdded = addColumnIfNotExists(conn, "settings", "sales_aggregation_default_days", "INTEGER", "7");

            if (columnAdded) {
                logger.info("settings テーブルに sales_aggregation_default_days カラムを正常に追加しました");
            } else {
                logger.info("settings テーブルに sales_aggregation_default_days カラムは既に存在します");
            }

            logger.info("バージョン12への移行が正常に完了しました");

        } catch (SQLException e) {
            logger.error("Error during migration to version 12: " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Migrate the database to version 13.
     * Adds keyword_search_result_id column to search_items table.
     *
     * @param conn The database connection
     */
    private static void migrateToVersion13(Connection conn) throws SQLException {
        logger.info("バージョン13への移行を開始します: search_items テーブルに keyword_search_result_id カラムを追加中");

        try {
            // Add keyword_search_result_id column to search_items table if it doesn't exist
            boolean columnAdded = addColumnIfNotExists(conn, "search_items", "keyword_search_result_id", "INTEGER", null);

            if (columnAdded) {
                logger.info("search_items テーブルに keyword_search_result_id カラムを正常に追加しました");
            } else {
                logger.info("search_items テーブルに keyword_search_result_id カラムは既に存在します");
            }

            logger.info("バージョン13への移行が正常に完了しました");

        } catch (SQLException e) {
            logger.error("Error during migration to version 13: " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Migrate the database to version 14.
     * Adds keyword_search_result_id column to keyword_search_items table.
     *
     * @param conn The database connection
     */
    private static void migrateToVersion14(Connection conn) throws SQLException {
        logger.info("バージョン14への移行を開始します: keyword_search_items テーブルに keyword_search_result_id カラムを追加中");

        try {
            // Add keyword_search_result_id column to keyword_search_items table if it doesn't exist
            boolean columnAdded = addColumnIfNotExists(conn, "keyword_search_items", "keyword_search_result_id", "INTEGER", null);

            if (columnAdded) {
                logger.info("keyword_search_items テーブルに keyword_search_result_id カラムを正常に追加しました");
            } else {
                logger.info("keyword_search_items テーブルに keyword_search_result_id カラムは既に存在します");
            }

            logger.info("バージョン14への移行が正常に完了しました");

        } catch (SQLException e) {
            logger.error("Error during migration to version 14: " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Migrate the database to version 15.
     * Removes keyword_search_result_id column from search_items table.
     *
     * @param conn The database connection
     */
    private static void migrateToVersion15(Connection conn) throws SQLException {
        logger.info("バージョン15への移行を開始します: search_items テーブルから keyword_search_result_id カラムを削除中");

        try {
            // Check if search_items table exists and has the column
            boolean tableExists = false;
            boolean columnExists = false;
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery("PRAGMA table_info(search_items)")) {
                while (rs.next()) {
                    if ("search_items".equals(rs.getString("name"))) {
                        tableExists = true;
                    }
                    if ("keyword_search_result_id".equals(rs.getString("name"))) {
                        columnExists = true;
                    }
                }
            }

            if (!tableExists || !columnExists) {
                logger.info("search_items テーブルまたは keyword_search_result_id カラムが存在しないため、移行をスキップします");
                return;
            }

            // Rename the old table
            try (Statement stmt = conn.createStatement()) {
                stmt.execute("ALTER TABLE search_items RENAME TO search_items_old");
            }

            // Create the new table without the keyword_search_result_id column
            String createNewTableSql = "CREATE TABLE search_items (" +
                    "item_id TEXT NOT NULL," +
                    "seller_id TEXT," +
                    "status INTEGER," +
                    "product_name TEXT," +
                    "price INTEGER," +
                    "listing_date TEXT," +
                    "updated_date TEXT NOT NULL," +
                    "thumbnail TEXT," +
                    "item_type INTEGER," +
                    "item_condition TEXT," +
                    "category TEXT," +
                    "PRIMARY KEY (item_id, updated_date)" +
                    ")";
            try (Statement stmt = conn.createStatement()) {
                stmt.execute(createNewTableSql);
            }

            // Copy data from the old table to the new table, excluding the removed column
            String copyDataSql = "INSERT INTO search_items (" +
                    "item_id, seller_id, status, product_name, price, listing_date, updated_date, thumbnail, item_type, item_condition, category) " +
                    "SELECT item_id, seller_id, status, product_name, price, listing_date, updated_date, thumbnail, item_type, item_condition, category " +
                    "FROM search_items_old";
            try (Statement stmt = conn.createStatement()) {
                stmt.execute(copyDataSql);
            }

            // Drop the old table
            try (Statement stmt = conn.createStatement()) {
                stmt.execute("DROP TABLE search_items_old");
            }

            logger.info("search_items テーブルから keyword_search_result_id カラムを正常に削除しました");

        } catch (SQLException e) {
            logger.error("Error during migration to version 15: " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Migrate the database to version 16.
     * Adds font_size column to settings table.
     *
     * @param conn The database connection
     */
    private static void migrateToVersion16(Connection conn) throws SQLException {
        logger.info("バージョン16への移行を開始します: settings テーブルに font_size カラムを追加中");

        try {
            boolean columnAdded = addColumnIfNotExists(conn, "settings", "font_size", "TEXT", "'MEDIUM'");

            if (columnAdded) {
                logger.info("settings テーブルに font_size カラムを正常に追加しました");
            } else {
                logger.info("settings テーブルに font_size カラムは既に存在します");
            }

            logger.info("バージョン16への移行が正常に完了しました");

        } catch (SQLException e) {
            logger.error("Error during migration to version 16: " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Migrate the database to version 17.
     * Adds category_id column to keyword_search_results table.
     *
     * @param conn The database connection
     */
    private static void migrateToVersion17(Connection conn) throws SQLException {
        logger.info("バージョン17への移行を開始します: keyword_search_results テーブルに category_id カラムを追加中");

        try {
            boolean columnAdded = addColumnIfNotExists(conn, "keyword_search_results", "category_id", "TEXT", null);

            if (columnAdded) {
                logger.info("keyword_search_results テーブルに category_id カラムを正常に追加しました");
            } else {
                logger.info("keyword_search_results テーブルに category_id カラムは既に存在します");
            }

            logger.info("バージョン17への移行が正常に完了しました");

        } catch (SQLException e) {
            logger.error("Error during migration to version 17: " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Migrate the database to version 18.
     * Adds missing columns to keyword_search_results table.
     *
     * @param conn The database connection
     */
    private static void migrateToVersion18(Connection conn) throws SQLException {
        logger.info("バージョン18への移行を開始します: keyword_search_results テーブルに不足しているカラムを追加中");

        try {
            // Add item_condition_ids column
            boolean itemConditionAdded = addColumnIfNotExists(conn, "keyword_search_results", "item_condition_ids", "TEXT", null);
            if (itemConditionAdded) {
                logger.info("keyword_search_results テーブルに item_condition_ids カラムを正常に追加しました");
            }

            // Add exclude_keyword column
            boolean excludeKeywordAdded = addColumnIfNotExists(conn, "keyword_search_results", "exclude_keyword", "TEXT", null);
            if (excludeKeywordAdded) {
                logger.info("keyword_search_results テーブルに exclude_keyword カラムを正常に追加しました");
            }

            // Add color_ids column
            boolean colorIdsAdded = addColumnIfNotExists(conn, "keyword_search_results", "color_ids", "TEXT", null);
            if (colorIdsAdded) {
                logger.info("keyword_search_results テーブルに color_ids カラムを正常に追加しました");
            }

            // Add category_id column
            boolean categoryIdAdded = addColumnIfNotExists(conn, "keyword_search_results", "category_id", "TEXT", null);
            if (categoryIdAdded) {
                logger.info("keyword_search_results テーブルに category_id カラムを正常に追加しました");
            }

            logger.info("バージョン18への移行が正常に完了しました");

        } catch (SQLException e) {
            logger.error("Error during migration to version 18: " + e.getMessage(), e);
            throw e;
        }
    }
}