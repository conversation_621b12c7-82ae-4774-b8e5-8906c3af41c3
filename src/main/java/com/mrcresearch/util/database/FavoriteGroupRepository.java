package com.mrcresearch.util.database;

import com.mrcresearch.screen.model.FavoriteGroupModel;
import com.mrcresearch.screen.model.GroupTagInfoModel;

import java.sql.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Repository class for favorite group related database operations.
 */
public class FavoriteGroupRepository {

    /**
     * Private constructor to prevent instantiation.
     */
    private FavoriteGroupRepository() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * Save or update a favorite group.
     *
     * @param groupName The group name
     * @return The ID of the saved/updated record, or -1 if an error occurred
     */
    public static int saveOrUpdateFavoriteGroup(String groupName) {
        // First try to get existing record
        FavoriteGroupModel existing = getFavoriteGroupByName(groupName);

        if (existing != null) {
            return existing.getId(); // Already exists, return existing ID
        }

        // Create new record
        String sql = "INSERT INTO favorite_groups (group_name) VALUES (?)";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

            pstmt.setString(1, groupName);

            int affectedRows = pstmt.executeUpdate();

            if (affectedRows > 0) {
                try (ResultSet rs = pstmt.getGeneratedKeys()) {
                    if (rs.next()) {
                        return rs.getInt(1);
                    }
                }
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return -1;
    }

    /**
     * Update research timestamps for a group.
     *
     * @param groupName         The group name
     * @param researchStartedAt The research start timestamp (null to keep current value)
     * @param lastUpdated       The last updated timestamp (null to keep current value)
     * @return true if the update was successful, false otherwise
     */
    public static boolean updateGroupResearchTimestamps(String groupName, Date researchStartedAt, Date lastUpdated) {
        // Ensure the group exists
        int groupId = saveOrUpdateFavoriteGroup(groupName);
        if (groupId == -1) {
            return false;
        }

        // Build SQL dynamically based on which fields need to be updated
        StringBuilder sqlBuilder = new StringBuilder("UPDATE favorite_groups SET ");
        List<String> updates = new ArrayList<>();
        List<Object> parameters = new ArrayList<>();

        if (researchStartedAt != null) {
            updates.add("research_started_at = ?");
            parameters.add(DatabaseDateTimeUtil.formatDate(researchStartedAt));
        }

        if (lastUpdated != null) {
            updates.add("last_updated = ?");
            parameters.add(DatabaseDateTimeUtil.formatDate(lastUpdated));
        }

        if (updates.isEmpty()) {
            return true; // Nothing to update
        }

        sqlBuilder.append(String.join(", ", updates));
        sqlBuilder.append(" WHERE group_name = ?");
        parameters.add(groupName);

        String sql = sqlBuilder.toString();

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            for (int i = 0; i < parameters.size(); i++) {
                pstmt.setString(i + 1, (String) parameters.get(i));
            }

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;

        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Get a favorite group by name.
     *
     * @param groupName The group name
     * @return The favorite group model, or null if not found
     */
    public static FavoriteGroupModel getFavoriteGroupByName(String groupName) {
        String sql = "SELECT * FROM favorite_groups WHERE group_name = ?";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, groupName);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    FavoriteGroupModel model = new FavoriteGroupModel();
                    model.setId(rs.getInt("id"));
                    model.setGroupName(rs.getString("group_name"));

                    String lastUpdatedStr = rs.getString("last_updated");
                    if (lastUpdatedStr != null && !lastUpdatedStr.isEmpty()) {
                        model.setLastUpdated(DatabaseDateTimeUtil.parseDate(lastUpdatedStr));
                    }

                    String researchStartedStr = rs.getString("research_started_at");
                    if (researchStartedStr != null && !researchStartedStr.isEmpty()) {
                        model.setResearchStartedAt(DatabaseDateTimeUtil.parseDate(researchStartedStr));
                    }

                    return model;
                }
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return null;
    }

    /**
     * Get enhanced group information with seller counts and timestamps from favorite_groups table.
     *
     * @return A list of group information models
     */
    public static List<GroupTagInfoModel> getGroupInfoWithCounts() {
        List<GroupTagInfoModel> results = new ArrayList<>();

        String sql = "SELECT " +
                "    fs.group_name, " +
                "    COUNT(fs.seller_id) as seller_count, " +
                "    fg.last_updated, " +
                "    fg.research_started_at " +
                "FROM favorite_sellers fs " +
                "LEFT JOIN favorite_groups fg ON fs.group_name = fg.group_name " +
                "WHERE fs.group_name IS NOT NULL AND fs.group_name != '' " +
                "GROUP BY fs.group_name, fg.last_updated, fg.research_started_at " +
                "ORDER BY fs.group_name";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                String groupName = rs.getString("group_name");
                int sellerCount = rs.getInt("seller_count");
                String lastUpdatedStr = rs.getString("last_updated");
                String researchStartedStr = rs.getString("research_started_at");

                Date lastUpdated = null;
                Date researchStartedAt = null;
                boolean hasResearch = false;

                if (lastUpdatedStr != null && !lastUpdatedStr.isEmpty()) {
                    lastUpdated = DatabaseDateTimeUtil.parseDate(lastUpdatedStr);
                    hasResearch = true;
                }

                if (researchStartedStr != null && !researchStartedStr.isEmpty()) {
                    researchStartedAt = DatabaseDateTimeUtil.parseDate(researchStartedStr);
                }

                GroupTagInfoModel info = new GroupTagInfoModel(groupName, sellerCount, lastUpdated, researchStartedAt, hasResearch);
                results.add(info);
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return results;
    }
}
