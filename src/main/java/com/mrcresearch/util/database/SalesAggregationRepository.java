package com.mrcresearch.util.database;

import com.mrcresearch.screen.model.SalesAggregationFilterModel;
import com.mrcresearch.screen.model.SalesAggregationModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Repository class for sales aggregation related database operations.
 * Handles complex sales data aggregation queries with pagination and filtering.
 */
public class SalesAggregationRepository {

    private static final Logger logger = LoggerFactory.getLogger(SalesAggregationRepository.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final int MAX_DATE_RANGE_DAYS = 14;

    /**
     * Private constructor to prevent instantiation.
     * This is a utility class with static methods only.
     */
    private SalesAggregationRepository() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * Retrieves aggregated sales data for sold or in-transaction items from the last 7 days, with pagination and an optional product name filter.
     *
     * @param page       The page number (1-based).
     * @param pageSize   The number of items per page.
     * @param nameFilter An optional filter for the product name. Can be null or empty.
     * @return A list of aggregated sales data.
     */
    public static List<SalesAggregationModel> getSalesAggregationDataWithNameFilter(int page, int pageSize, String nameFilter) {
        SalesAggregationFilterModel filter = new SalesAggregationFilterModel();
        if (nameFilter != null && !nameFilter.trim().isEmpty()) {
            filter.setNameFilter(nameFilter);
        }
        return getSalesAggregationDataWithFilter(page, pageSize, filter);
    }

    /**
     * Retrieves aggregated sales data for sold or in-transaction items from the last 7 days.
     * This method is for backward compatibility.
     *
     * @return A list of aggregated sales data.
     */
    public static List<SalesAggregationModel> getSalesAggregationData() {
        return getSalesAggregationDataWithNameFilter(1, Integer.MAX_VALUE, null);
    }

    /**
     * Retrieves the total count of aggregated sales data for sold or in-transaction items, with an optional product name filter.
     *
     * @param nameFilter An optional filter for the product name. Can be null or empty.
     * @return The total count of items.
     */
    public static int getSalesAggregationDataCountWithNameFilter(String nameFilter) {
        SalesAggregationFilterModel filter = new SalesAggregationFilterModel();
        if (nameFilter != null && !nameFilter.trim().isEmpty()) {
            filter.setNameFilter(nameFilter);
        }
        return getSalesAggregationDataCountWithFilter(filter);
    }

    /**
     * Retrieves aggregated sales data for sold or in-transaction items with advanced filtering.
     *
     * @param page     The page number (1-based).
     * @param pageSize The number of items per page.
     * @param filter   The filter criteria.
     * @return A list of aggregated sales data.
     */
    public static List<SalesAggregationModel> getSalesAggregationDataWithFilter(int page, int pageSize, SalesAggregationFilterModel filter) {
        List<SalesAggregationModel> results = new ArrayList<>();
        List<String> dateList;
        LocalDate startDate, endDate;

        if (filter != null && filter.hasDateFilter()) {
            startDate = filter.getStartDate();
            endDate = filter.getEndDate();
            long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate) + 1;
            if (daysBetween > MAX_DATE_RANGE_DAYS) {
                throw new IllegalArgumentException("Date range cannot exceed " + MAX_DATE_RANGE_DAYS + " days.");
            }
            dateList = buildDateList(startDate, endDate);
        } else {
            int defaultDays = com.mrcresearch.util.database.SettingsRepository.getSalesAggregationDefaultDays();
            dateList = buildDateList(defaultDays, 0);
            startDate = LocalDate.parse(dateList.get(0), DATE_FORMATTER);
            endDate = LocalDate.parse(dateList.get(dateList.size() - 1), DATE_FORMATTER);
        }

        List<Object> parameters = new ArrayList<>();
        parameters.add(startDate.format(DATE_FORMATTER));
        parameters.add(endDate.format(DATE_FORMATTER));

        String sql = buildAdvancedQuery(filter, parameters, dateList, true);

        parameters.add(pageSize);
        parameters.add((page - 1) * pageSize);

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            for (int i = 0; i < parameters.size(); i++) {
                pstmt.setObject(i + 1, parameters.get(i));
            }

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    results.add(mapResultSetToModel(rs, dateList));
                }
            }
        } catch (SQLException e) {
            logger.error("Error retrieving sales aggregation data with filters: {}", e.getMessage());
        }
        return results;
    }

    /**
     * Retrieves the total count of aggregated sales data with advanced filtering.
     *
     * @param filter The filter criteria.
     * @return The total count of items.
     */
    public static int getSalesAggregationDataCountWithFilter(SalesAggregationFilterModel filter) {
        LocalDate startDate, endDate;

        if (filter != null && filter.hasDateFilter()) {
            startDate = filter.getStartDate();
            endDate = filter.getEndDate();
            long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate) + 1;
            if (daysBetween > MAX_DATE_RANGE_DAYS) {
                throw new IllegalArgumentException("Date range cannot exceed " + MAX_DATE_RANGE_DAYS + " days.");
            }
        } else {
            int defaultDays = com.mrcresearch.util.database.SettingsRepository.getSalesAggregationDefaultDays();
            endDate = LocalDate.now();
            startDate = endDate.minusDays(defaultDays - 1);
        }

        List<Object> parameters = new ArrayList<>();
        parameters.add(startDate.format(DATE_FORMATTER));
        parameters.add(endDate.format(DATE_FORMATTER));

        String sql = buildAdvancedCountQuery(filter, parameters);

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            for (int i = 0; i < parameters.size(); i++) {
                pstmt.setObject(i + 1, parameters.get(i));
            }

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("total_count");
                }
            }
        } catch (SQLException e) {
            logger.error("Error retrieving sales aggregation count with filters: {}", e.getMessage());
        }
        return 0;
    }

    /**
     * Retrieves aggregated sales data for a specific date range.
     *
     * @param page     The page number (1-based).
     * @param pageSize The number of items per page.
     * @param filter   The filter criteria, which must include a date range.
     * @return A list of aggregated sales data.
     */
    public static List<SalesAggregationModel> getSalesAggregationDataWithDateRange(int page, int pageSize, SalesAggregationFilterModel filter) {
        return getSalesAggregationDataWithFilter(page, pageSize, filter);
    }

    /**
     * Retrieves the total count of aggregated sales data for a specific date range.
     *
     * @param filter The filter criteria, which must include a date range.
     * @return The total count of items.
     */
    public static int getSalesAggregationDataCountWithDateRange(SalesAggregationFilterModel filter) {
        return getSalesAggregationDataCountWithFilter(filter);
    }

    // --- Private Helper Methods ---

    private static String buildAdvancedQuery(SalesAggregationFilterModel filter, List<Object> parameters, List<String> dateList, boolean paginate) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("""
                WITH sold_items AS (
                    SELECT item_id, product_name as name, seller_id, price, category, thumbnail, status, DATE(updated_date) as sold_date
                    FROM search_items
                    WHERE (status NOT IN (1)) AND DATE(updated_date) BETWEEN ? AND ?
                """);

        addFilterConditions(sqlBuilder, filter, parameters);

        sqlBuilder.append("), item_aggregation AS ( SELECT seller_id, name, category, price, thumbnail, item_id, ");
        for (int i = 0; i < dateList.size(); i++) {
            sqlBuilder.append("SUM(CASE WHEN sold_date = ? THEN 1 ELSE 0 END) as day").append(i + 1).append("_count, ");
        }
        parameters.addAll(dateList);
        sqlBuilder.append("COUNT(*) as total_sales_count FROM sold_items GROUP BY seller_id, name ) ");
        sqlBuilder.append("SELECT ia.seller_id, ia.item_id, ia.name, ia.category, ia.price, ia.thumbnail, ");
        for (int i = 0; i < dateList.size(); i++) {
            sqlBuilder.append("ia.day").append(i + 1).append("_count, ");
        }
        sqlBuilder.append("""
                    ia.total_sales_count, s.seller_name, latest_research.latest_research_date, s.research_status
                    FROM item_aggregation ia
                    LEFT JOIN sellers s ON ia.seller_id = s.seller_id
                    LEFT JOIN ( 
                        SELECT seller_id, MAX(updated_date) as latest_research_date
                        FROM search_items
                        GROUP BY seller_id
                    ) latest_research ON ia.seller_id = latest_research.seller_id
                """);

        addSalesCountFilter(sqlBuilder, filter, parameters);
        sqlBuilder.append("GROUP BY ia.seller_id, ia.name ORDER BY ia.total_sales_count DESC, ia.seller_id, ia.name ");
        if (paginate) {
            sqlBuilder.append("LIMIT ? OFFSET ?");
        }
        return sqlBuilder.toString();
    }

    private static String buildAdvancedCountQuery(SalesAggregationFilterModel filter, List<Object> parameters) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("""
                WITH sold_items AS (
                    SELECT item_id, product_name as name, seller_id, price, category, status, DATE(updated_date) as sold_date
                    FROM search_items
                    WHERE (status NOT IN (1)) AND DATE(updated_date) BETWEEN ? AND ?
                """);
        addFilterConditions(sqlBuilder, filter, parameters);
        sqlBuilder.append("""
                ), item_aggregation AS (
                    SELECT seller_id, name, category, price, COUNT(*) as total_sales_count
                    FROM sold_items
                    GROUP BY seller_id, name, category, price
                )
                SELECT COUNT(*) as total_count
                FROM item_aggregation ia
                """);
        addSalesCountFilter(sqlBuilder, filter, parameters);
        return sqlBuilder.toString();
    }

    private static void addFilterConditions(StringBuilder sqlBuilder, SalesAggregationFilterModel filter, List<Object> parameters) {
        if (filter == null) return;

        logger.debug("フィルタ条件を構築中...");

        List<String> itemIds = getItemIdsFromSearchFilters(filter);
        if (!itemIds.isEmpty()) {
            logger.debug("アイテムIDでフィルタリング中: {} アイテム", itemIds.size());
            String placeholders = String.join(",", Collections.nCopies(itemIds.size(), "?"));
            sqlBuilder.append(" AND item_id IN (").append(placeholders).append(") ");
            parameters.addAll(itemIds);
        }

        if (filter.hasNameFilter()) {
            List<String> keywords = filter.getNameKeywords();
            if (!keywords.isEmpty()) {
                sqlBuilder.append(" AND (");
                for (int i = 0; i < keywords.size(); i++) {
                    if (i > 0) sqlBuilder.append(" AND ");
                    sqlBuilder.append("LOWER(product_name) LIKE LOWER(?)");
                    parameters.add("%" + keywords.get(i) + "%");
                }
                sqlBuilder.append(") ");
            }
        }
        if (filter.hasSellerIdFilter()) {
            sqlBuilder.append(" AND seller_id = ? ");
            parameters.add(filter.getSellerIdFilter());
        }
        if (filter.hasCategoryFilter()) {
            List<String> categoryIds = filter.getCategoryIds();
            if (!categoryIds.isEmpty()) {
                sqlBuilder.append(" AND category IN (");
                String placeholders = String.join(",", Collections.nCopies(categoryIds.size(), "?"));
                sqlBuilder.append(placeholders).append(") ");
                parameters.addAll(categoryIds);
            }
        }
        if (filter.hasPriceFilter()) {
            if (filter.getMinPrice() != null) {
                sqlBuilder.append(" AND price >= ? ");
                parameters.add(filter.getMinPrice());
            }
            if (filter.getMaxPrice() != null) {
                sqlBuilder.append(" AND price <= ? ");
                parameters.add(filter.getMaxPrice());
            }
        }
    }

    private static List<String> getItemIdsFromSearchFilters(SalesAggregationFilterModel filter) {
        List<String> itemIds = new ArrayList<>();
        if (filter.getKeywordSearchId() != null && !filter.getKeywordSearchId().trim().isEmpty()) {
            itemIds.addAll(getItemIdsByKeywordSearchId(filter.getKeywordSearchId()));
        }
        if (filter.getSellerSearchId() != null && !filter.getSellerSearchId().trim().isEmpty()) {
            List<String> sellerItemIds = getItemIdsBySellerSearchId(filter.getSellerSearchId());
            if (itemIds.isEmpty()) {
                itemIds.addAll(sellerItemIds);
            } else {
                itemIds.retainAll(sellerItemIds);
            }
        }
        return itemIds;
    }

    private static List<String> getItemIdsByKeywordSearchId(String keywordSearchId) {
        String sql = "SELECT item_id FROM keyword_search_items WHERE keyword_search_result_id = ?";
        List<String> itemIds = new ArrayList<>();
        logger.debug("キーワード検索ID: {} のアイテムIDを取得中", keywordSearchId);
        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, keywordSearchId);
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    itemIds.add(rs.getString("item_id"));
                }
            }
        } catch (SQLException e) {
            logger.error("Error retrieving item IDs by keyword search ID: {}", e.getMessage());
        }
        logger.debug("キーワード検索ID: {} のアイテムIDを {} 件見つけました", itemIds.size(), keywordSearchId);
        return itemIds;
    }

    private static List<String> getItemIdsBySellerSearchId(String sellerSearchId) {
        String sql = "SELECT item_id FROM seller_search_items WHERE seller_search_id = ?";
        List<String> itemIds = new ArrayList<>();
        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, sellerSearchId);
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    itemIds.add(rs.getString("item_id"));
                }
            }
        } catch (SQLException e) {
            logger.error("Error retrieving item IDs by seller search ID: {}", e.getMessage());
        }
        return itemIds;
    }

    private static void addSalesCountFilter(StringBuilder sqlBuilder, SalesAggregationFilterModel filter, List<Object> parameters) {
        if (filter == null || !filter.hasSalesCountFilter()) return;

        sqlBuilder.append("WHERE ");
        boolean hasCondition = false;
        if (filter.getMinSalesCount() != null) {
            sqlBuilder.append("ia.total_sales_count >= ? ");
            parameters.add(filter.getMinSalesCount());
            hasCondition = true;
        }
        if (filter.getMaxSalesCount() != null) {
            if (hasCondition) sqlBuilder.append("AND ");
            sqlBuilder.append("ia.total_sales_count <= ? ");
            parameters.add(filter.getMaxSalesCount());
        }
    }

    private static List<String> buildDateList(int days, int offset) {
        LocalDate today = LocalDate.now();
        List<String> dateList = new ArrayList<>();
        for (int i = offset; i < days + offset; i++) {
            dateList.add(today.minusDays(i).format(DATE_FORMATTER));
        }
        Collections.reverse(dateList); // Newest first
        return dateList;
    }

    private static List<String> buildDateList(LocalDate startDate, LocalDate endDate) {
        List<String> dateList = new ArrayList<>();
        for (LocalDate date = endDate; !date.isBefore(startDate); date = date.minusDays(1)) {
            dateList.add(date.format(DATE_FORMATTER));
        }
        return dateList;
    }

    private static SalesAggregationModel mapResultSetToModel(ResultSet rs, List<String> dateList) throws SQLException {
        SalesAggregationModel model = new SalesAggregationModel();
        model.setSellerId(rs.getString("seller_id"));
        model.setItemId(rs.getString("item_id"));
        model.setItemName(rs.getString("name"));
        model.setCategoryName(CategoryUtil.getCategoryNameById(rs.getString("category")));
        model.setPrice(rs.getInt("price"));
        model.setThumbnailPath(rs.getString("thumbnail"));
        model.setTotalSalesCount(rs.getInt("total_sales_count"));
        model.setSellerName(rs.getString("seller_name"));
        model.setLastUpdatedAt(rs.getString("latest_research_date"));
        model.setResearchStatus(rs.getString("research_status"));

        Map<String, Integer> dailySalesCount = new LinkedHashMap<>();
        for (int i = 0; i < dateList.size(); i++) {
            dailySalesCount.put(dateList.get(i), rs.getInt("day" + (i + 1) + "_count"));
        }
        model.setDailySalesCount(dailySalesCount);
        return model;
    }
}
