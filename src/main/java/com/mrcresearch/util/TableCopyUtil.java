package com.mrcresearch.util;

import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.KeyEvent;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * Utility class for adding copy functionality to JTable components
 */
public class TableCopyUtil {

    /**
     * Enable copy functionality for a JTable with keyboard shortcuts and context menu
     *
     * @param table The JTable to enable copy functionality for
     */
    public static void enableTableCopy(JTable table) {
        if (table == null) {
            return;
        }

        // Enable cell selection
        table.setCellSelectionEnabled(true);
        table.setRowSelectionAllowed(true);
        table.setColumnSelectionAllowed(true);

        // Add keyboard shortcuts
        addKeyboardShortcuts(table);

        // Add right-click context menu
        addContextMenu(table);
    }

    /**
     * Enable copy functionality for a JTable with seller-specific options
     *
     * @param table              The JTable to enable copy functionality for
     * @param sellerNameProvider Function to get seller name from row index
     * @param favoriteAction     Action to add seller to favorites (sellerId, sellerName)
     * @param researchAction     Action to start seller research (sellerId, sellerName)
     */
    public static void enableTableCopyWithSellerOptions(JTable table,
                                                        Function<Integer, String> sellerNameProvider,
                                                        BiConsumer<String, String> favoriteAction,
                                                        BiConsumer<String, String> researchAction) {
        if (table == null) {
            return;
        }

        // Enable cell selection
        table.setCellSelectionEnabled(true);
        table.setRowSelectionAllowed(true);
        table.setColumnSelectionAllowed(true);

        // Add keyboard shortcuts
        addKeyboardShortcuts(table);

        // Add right-click context menu with seller options
        addContextMenuWithSellerOptions(table, sellerNameProvider, favoriteAction, researchAction);
    }

    /**
     * Add keyboard shortcuts for copy operations
     *
     * @param table The JTable to add shortcuts to
     */
    private static void addKeyboardShortcuts(JTable table) {
        // Ctrl+C for copying selected cells
        table.getInputMap(JComponent.WHEN_FOCUSED).put(
                KeyStroke.getKeyStroke(KeyEvent.VK_C, KeyEvent.CTRL_DOWN_MASK), "copy");
        table.getActionMap().put("copy", new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                ClipboardUtil.copyTableSelection(table);
            }
        });

        // Ctrl+A for selecting all
        table.getInputMap(JComponent.WHEN_FOCUSED).put(
                KeyStroke.getKeyStroke(KeyEvent.VK_A, KeyEvent.CTRL_DOWN_MASK), "selectAll");
        table.getActionMap().put("selectAll", new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                table.selectAll();
            }
        });
    }

    /**
     * Add right-click context menu for copy operations
     *
     * @param table The JTable to add context menu to
     */
    private static void addContextMenu(JTable table) {
        JPopupMenu contextMenu = new JPopupMenu();

        // Copy selected cells
        JMenuItem copyItem = new JMenuItem("コピー (Ctrl+C)");
        copyItem.addActionListener(e -> ClipboardUtil.copyTableSelection(table));
        contextMenu.add(copyItem);

        // Copy all data
        JMenuItem copyAllItem = new JMenuItem("すべてコピー");
        copyAllItem.addActionListener(e -> ClipboardUtil.copyAllTableData(table));
        contextMenu.add(copyAllItem);

        contextMenu.addSeparator();

        // Select all
        JMenuItem selectAllItem = new JMenuItem("すべて選択 (Ctrl+A)");
        selectAllItem.addActionListener(e -> table.selectAll());
        contextMenu.add(selectAllItem);

        // Add mouse listener for right-click
        table.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showContextMenu(e);
                }
            }

            @Override
            public void mouseReleased(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showContextMenu(e);
                }
            }

            private void showContextMenu(MouseEvent e) {
                // Update menu item states based on selection
                boolean hasSelection = table.getSelectedRowCount() > 0 && table.getSelectedColumnCount() > 0;
                copyItem.setEnabled(hasSelection);

                boolean hasData = table.getRowCount() > 0;
                copyAllItem.setEnabled(hasData);
                selectAllItem.setEnabled(hasData);

                // Show context menu
                contextMenu.show(table, e.getX(), e.getY());
            }
        });
    }

    /**
     * Add right-click context menu with seller-specific options
     *
     * @param table              The JTable to add context menu to
     * @param sellerNameProvider Function to get seller name from row index
     * @param favoriteAction     Action to add seller to favorites
     * @param researchAction     Action to start seller research
     */
    private static void addContextMenuWithSellerOptions(JTable table,
                                                        Function<Integer, String> sellerNameProvider,
                                                        BiConsumer<String, String> favoriteAction,
                                                        BiConsumer<String, String> researchAction) {

        table.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showContextMenuWithSellerOptions(e);
                }
            }

            @Override
            public void mouseReleased(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showContextMenuWithSellerOptions(e);
                }
            }

            private void showContextMenuWithSellerOptions(MouseEvent e) {
                int row = table.rowAtPoint(e.getPoint());
                int column = table.columnAtPoint(e.getPoint());

                if (row >= 0) {
                    // 行を選択
                    table.setRowSelectionInterval(row, row);
                    if (column >= 0) {
                        table.setColumnSelectionInterval(column, column);
                    }

                    // コンテキストメニューを作成
                    JPopupMenu contextMenu = createContextMenuWithSellerOptions(
                            table, row, column, sellerNameProvider, favoriteAction, researchAction);

                    // メニューアイテムの状態を更新
                    updateMenuItemStates(contextMenu, table);

                    // コンテキストメニューを表示
                    contextMenu.show(table, e.getX(), e.getY());
                }
            }
        });
    }

    /**
     * Create context menu with seller-specific options
     */
    private static JPopupMenu createContextMenuWithSellerOptions(JTable table, int row, int column,
                                                                 Function<Integer, String> sellerNameProvider,
                                                                 BiConsumer<String, String> favoriteAction,
                                                                 BiConsumer<String, String> researchAction) {
        JPopupMenu contextMenu = new JPopupMenu();

        // 基本のコピー機能
        JMenuItem copyItem = new JMenuItem("コピー (Ctrl+C)");
        copyItem.setName("copy");
        copyItem.addActionListener(e -> ClipboardUtil.copyTableSelection(table));
        contextMenu.add(copyItem);

        // 行をコピー
        JMenuItem copyRowItem = new JMenuItem("行をコピー");
        copyRowItem.addActionListener(e -> copySelectedRow(table, row));
        contextMenu.add(copyRowItem);

        // すべてコピー
        JMenuItem copyAllItem = new JMenuItem("すべてコピー");
        copyAllItem.setName("copyAll");
        copyAllItem.addActionListener(e -> ClipboardUtil.copyAllTableData(table));
        contextMenu.add(copyAllItem);

        // セラーID列（0列目）の場合のみ追加オプションを表示
        if (column == 0) {
            contextMenu.addSeparator();

            // セラー情報を取得
            String sellerId = (String) table.getValueAt(row, 0);
            String sellerName = sellerNameProvider != null ? sellerNameProvider.apply(row) : null;

            // お気に入り登録
            JMenuItem favoriteItem = new JMenuItem("お気に入り登録");
            favoriteItem.addActionListener(e -> {
                if (favoriteAction != null) {
                    favoriteAction.accept(sellerId, sellerName);
                }
            });
            contextMenu.add(favoriteItem);

            // セラーをリサーチ
            JMenuItem researchItem = new JMenuItem("セラーをリサーチ");
            researchItem.addActionListener(e -> {
                if (researchAction != null) {
                    researchAction.accept(sellerId, sellerName);
                }
            });
            contextMenu.add(researchItem);
        }

        contextMenu.addSeparator();

        // すべて選択
        JMenuItem selectAllItem = new JMenuItem("すべて選択 (Ctrl+A)");
        selectAllItem.setName("selectAll");
        selectAllItem.addActionListener(e -> table.selectAll());
        contextMenu.add(selectAllItem);

        return contextMenu;
    }

    /**
     * Copy a specific row to clipboard
     */
    private static void copySelectedRow(JTable table, int row) {
        if (row < 0 || row >= table.getRowCount()) {
            return;
        }

        StringBuilder rowData = new StringBuilder();
        int columnCount = table.getColumnCount();

        for (int col = 0; col < columnCount; col++) {
            Object value = table.getValueAt(row, col);
            String cellValue = "";

            if (value != null) {
                // ImageIconの場合は画像パスまたは代替テキストを使用
                if (value instanceof ImageIcon) {
                    cellValue = "[画像]";
                } else {
                    cellValue = value.toString();
                }

                // タブと改行を除去
                cellValue = cellValue.replace("\t", " ").replace("\n", " ").replace("\r", " ");
            }

            rowData.append(cellValue);

            if (col < columnCount - 1) {
                rowData.append("\t");
            }
        }

        ClipboardUtil.copyToClipboard(rowData.toString());
    }

    /**
     * Update menu item states based on table state
     */
    private static void updateMenuItemStates(JPopupMenu contextMenu, JTable table) {
        boolean hasSelection = table.getSelectedRowCount() > 0 && table.getSelectedColumnCount() > 0;
        boolean hasData = table.getRowCount() > 0;

        // 名前でメニューアイテムを検索して状態を更新
        for (int i = 0; i < contextMenu.getComponentCount(); i++) {
            java.awt.Component component = contextMenu.getComponent(i);
            if (component instanceof JMenuItem menuItem) {
                String name = menuItem.getName();

                if ("copy".equals(name)) {
                    menuItem.setEnabled(hasSelection);
                } else if ("copyAll".equals(name)) {
                    menuItem.setEnabled(hasData);
                } else if ("selectAll".equals(name)) {
                    menuItem.setEnabled(hasData);
                }
            }
        }
    }

}
