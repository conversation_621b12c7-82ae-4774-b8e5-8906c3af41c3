package com.mrcresearch.util;

import javax.swing.*;
import javax.swing.table.DefaultTableCellRenderer;
import java.awt.*;

/**
 * マルチライン表示対応のテーブルヘッダーレンダラー
 * ヘッダーテキストを2行で表示する（1行目：基本名、2行目：統計情報）
 */
public class MultiLineHeaderRenderer extends DefaultTableCellRenderer {
    private static final Color HEADER_BORDER = new Color(184, 201, 199);

    @Override
    public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected,
                                                   boolean hasFocus, int row, int column) {

        // ヘッダー用の設定
        setHorizontalAlignment(SwingConstants.CENTER);
        setVerticalAlignment(SwingConstants.CENTER);
        setBorder(BorderFactory.createMatteBorder(1, 1, 1, 1, HEADER_BORDER));
        setFont(table.getTableHeader().getFont());

        if (value == null) {
            setText("");
            return this;
        }

        String headerText = value.toString();

        // マルチライン表示用のHTMLを作成
        String htmlText = createMultiLineHeaderHtml(headerText);
        setText(htmlText);

        return this;
    }

    /**
     * マルチライン表示用のHTMLを作成する
     * 統計情報が含まれている場合は2行で表示、そうでなければ1行で表示
     */
    private String createMultiLineHeaderHtml(String headerText) {
        if (headerText == null || headerText.isEmpty() || isWhitespace(headerText)) {
            return "<html><div style='text-align: center; padding: 5px;'>&nbsp;</div></html>";
        }

        // 既にHTMLタグが含まれている場合はそのまま返す
        if (headerText.startsWith("<html>") && headerText.endsWith("</html>")) {
            return headerText;
        }

        // 統計情報が含まれているかチェック（括弧で囲まれた部分）
        if (headerText.contains("(") && headerText.contains(")")) {
            // 統計情報を分離
            int startIndex = headerText.indexOf("(");
            String baseName = headerText.substring(0, startIndex).trim();
            String statisticsInfo = headerText.substring(startIndex).trim();

            // 2行で表示 - String.format()を使わずに文字列連結を使用
            return "<html><div style='text-align: center; padding: 3px; line-height: 1.1;'>" +
                    "<div style='font-weight: bold; margin-bottom: 2px;'>" + escapeHtml(baseName) + "</div>" +
                    "<div style='font-size: 90%; color: #666666;'>" + escapeHtml(statisticsInfo) + "</div>" +
                    "</div></html>";
        } else {
            // 統計情報がない場合は1行で表示
            return "<html><div style='text-align: center; padding: 5px; font-weight: bold;'>" +
                    escapeHtml(headerText) +
                    "</div></html>";
        }
    }

    /**
     * HTMLエスケープ処理
     */
    private String escapeHtml(String text) {
        if (text == null) {
            return "";
        }
        return text.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&#39;");
    }

    /**
     * 文字列が空白文字のみで構成されているかチェックする
     * trim()を使わずに実装することでStackOverflowErrorを回避
     */
    private boolean isWhitespace(String str) {
        if (str == null || str.isEmpty()) {
            return true;
        }
        for (int i = 0; i < str.length(); i++) {
            if (!Character.isWhitespace(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }
}
