package com.mrcresearch.util;

import com.formdev.flatlaf.FlatDarkLaf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.formdev.flatlaf.FlatLaf;
import org.kordamp.ikonli.Ikon;
import org.kordamp.ikonli.antdesignicons.AntDesignIconsOutlined;
import org.kordamp.ikonli.swing.FontIcon;

import javax.swing.*;
import java.awt.*;

/**
 * Comprehensive utility class for creating theme-aware FontIcons using FontIcon.of()
 * Automatically adjusts icon colors based on the current theme (light/dark)
 */
public class ThemeIconUtil {

    private static final Logger logger = LoggerFactory.getLogger(ThemeIconUtil.class);

    // ========== BASIC THEME ICON CREATION ==========

    /**
     * Create a FontIcon with appropriate color based on current theme
     *
     * @param icon The icon to create
     * @param size The size of the icon
     * @return FontIcon with theme-appropriate color
     */
    public static FontIcon of(Ikon icon, int size) {
        Color iconColor = getThemeIconColor();
        return FontIcon.of(icon, size, iconColor);
    }

    /**
     * Create a FontIcon with default size (16px) and theme-appropriate color
     *
     * @param icon The icon to create
     * @return FontIcon with theme-appropriate color
     */
    public static FontIcon of(Ikon icon) {
        return of(icon, 16);
    }

    // ========== SPECIALIZED ICON CREATION ==========

    /**
     * Create a button icon (typically white on colored backgrounds)
     *
     * @param icon The icon to create
     * @param size The size of the icon
     * @return FontIcon optimized for buttons
     */
    public static FontIcon ofButton(Ikon icon, int size) {
        return FontIcon.of(icon, size, getThemeIconColor());
    }

    /**
     * Create a button icon with default size (16px)
     *
     * @param icon The icon to create
     * @return FontIcon optimized for buttons
     */
    public static FontIcon ofButton(Ikon icon) {
        return ofButton(icon, 16);
    }

    /**
     * Create a header button icon (always white for colored backgrounds)
     *
     * @param icon The icon to create
     * @param size The size of the icon
     * @return FontIcon with white color for header buttons
     */
    public static FontIcon ofHeaderButton(Ikon icon, int size) {
        return FontIcon.of(icon, size, Color.WHITE);
    }

    /**
     * Create a header button icon with default size (20px)
     *
     * @param icon The icon to create
     * @return FontIcon with white color for header buttons
     */
    public static FontIcon ofHeaderButton(Ikon icon) {
        return ofHeaderButton(icon, 20);
    }

    /**
     * Create a menu icon with theme-appropriate color
     *
     * @param icon The icon to create
     * @param size The size of the icon
     * @return FontIcon optimized for menus
     */
    public static FontIcon ofMenu(Ikon icon, int size) {
        return FontIcon.of(icon, size, getMenuIconColor());
    }

    /**
     * Create a menu icon with default size (20px)
     *
     * @param icon The icon to create
     * @return FontIcon optimized for menus
     */
    public static FontIcon ofMenu(Ikon icon) {
        return ofMenu(icon, 20);
    }

    /**
     * Create a status icon with specific color (color preserved regardless of theme)
     *
     * @param icon        The icon to create
     * @param size        The size of the icon
     * @param statusColor The color representing the status
     * @return FontIcon with status color
     */
    public static FontIcon ofStatus(Ikon icon, int size, Color statusColor) {
        return FontIcon.of(icon, size, statusColor);
    }

    /**
     * Create a status icon with default size (16px)
     *
     * @param icon        The icon to create
     * @param statusColor The color representing the status
     * @return FontIcon with status color
     */
    public static FontIcon ofStatus(Ikon icon, Color statusColor) {
        return ofStatus(icon, 16, statusColor);
    }

    /**
     * Create a FontIcon with specific color for light theme and white for dark theme
     *
     * @param icon            The icon to create
     * @param size            The size of the icon
     * @param lightThemeColor The color to use in light theme
     * @return FontIcon with theme-appropriate color
     */
    public static FontIcon ofThemed(Ikon icon, int size, Color lightThemeColor) {
        Color iconColor = isDarkTheme() ? Color.WHITE : lightThemeColor;
        return FontIcon.of(icon, size, iconColor);
    }

    /**
     * Create a FontIcon with specific colors for both themes
     *
     * @param icon            The icon to create
     * @param size            The size of the icon
     * @param lightThemeColor The color to use in light theme
     * @param darkThemeColor  The color to use in dark theme
     * @return FontIcon with theme-appropriate color
     */
    public static FontIcon ofThemed(Ikon icon, int size, Color lightThemeColor, Color darkThemeColor) {
        Color iconColor = isDarkTheme() ? darkThemeColor : lightThemeColor;
        return FontIcon.of(icon, size, iconColor);
    }

    // ========== PREDEFINED STATUS ICONS ==========

    /**
     * Create a success/completed icon (green)
     *
     * @param size The size of the icon
     * @return Green check circle icon
     */
    public static FontIcon ofSuccess(int size) {
        return FontIcon.of(org.kordamp.ikonli.antdesignicons.AntDesignIconsOutlined.CHECK_CIRCLE, size, Color.GREEN);
    }

    /**
     * Create a success/completed icon with default size (16px)
     *
     * @return Green check circle icon
     */
    public static FontIcon ofSuccess() {
        return ofSuccess(16);
    }

    /**
     * Create an error icon (red)
     *
     * @param size The size of the icon
     * @return Red exclamation circle icon
     */
    public static FontIcon ofError(int size) {
        return FontIcon.of(org.kordamp.ikonli.antdesignicons.AntDesignIconsOutlined.EXCLAMATION_CIRCLE, size, Color.RED);
    }

    /**
     * Create an error icon with default size (16px)
     *
     * @return Red exclamation circle icon
     */
    public static FontIcon ofError() {
        return ofError(16);
    }

    /**
     * Create a warning icon (yellow/orange)
     *
     * @param size The size of the icon
     * @return Yellow warning icon
     */
    public static FontIcon ofWarning(int size) {
        return FontIcon.of(org.kordamp.ikonli.antdesignicons.AntDesignIconsOutlined.WARNING, size, Color.ORANGE);
    }

    /**
     * Create a warning icon with default size (16px)
     *
     * @return Yellow warning icon
     */
    public static FontIcon ofWarning() {
        return ofWarning(16);
    }

    /**
     * Create an info icon (blue)
     *
     * @param size The size of the icon
     * @return Blue info circle icon
     */
    public static FontIcon ofInfo(int size) {
        return FontIcon.of(AntDesignIconsOutlined.INFO_CIRCLE, size, Color.BLUE);
    }

    /**
     * Create an info icon with default size (16px)
     *
     * @return Blue info circle icon
     */
    public static FontIcon ofInfo() {
        return ofInfo(16);
    }

    /**
     * Create a loading/processing icon (white)
     *
     * @param size The size of the icon
     * @return White loading icon
     */
    public static FontIcon ofLoading(int size) {
        return FontIcon.of(AntDesignIconsOutlined.HOURGLASS, size, Color.WHITE);
    }

    /**
     * Create a loading/processing icon with default size (16px)
     *
     * @return White loading icon
     */
    public static FontIcon ofLoading() {
        return ofLoading(16);
    }

    /**
     * Create a waiting/clock icon (yellow)
     *
     * @param size The size of the icon
     * @return Yellow clock icon
     */
    public static FontIcon ofWaiting(int size) {
        return FontIcon.of(AntDesignIconsOutlined.CLOCK_CIRCLE, size, Color.YELLOW);
    }

    /**
     * Create a waiting/clock icon with default size (16px)
     *
     * @return Yellow clock icon
     */
    public static FontIcon ofWaiting() {
        return ofWaiting(16);
    }

    // ========== COLOR GETTERS ==========

    /**
     * Get the default icon color for the current theme
     *
     * @return White for dark theme, dark gray for light theme
     */
    public static Color getThemeIconColor() {
        return isDarkTheme() ? Color.WHITE : new Color(60, 60, 60);
    }

    /**
     * Get the button icon color for the current theme
     *
     * @return White for both themes (buttons typically have colored backgrounds)
     */
    public static Color getButtonIconColor() {
        return Color.WHITE; // Always white for buttons with colored backgrounds
    }

    /**
     * Get the menu icon color for the current theme
     *
     * @return White for dark theme, light gray for light theme
     */
    public static Color getMenuIconColor() {
        return isDarkTheme() ? Color.WHITE : new Color(220, 220, 220);
    }

    /**
     * Check if the current theme is a dark theme
     *
     * @return true if dark theme is active, false otherwise
     */
    public static boolean isDarkTheme() {
        LookAndFeel laf = UIManager.getLookAndFeel();
        boolean isDark = false;

        if (laf instanceof FlatLaf) {
            isDark = ((FlatLaf) laf).isDark();
        } else {
            // Fallback: check if it's specifically FlatDarkLaf
            isDark = laf instanceof FlatDarkLaf;
        }

        // Debug output
        logger.debug("Current LAF: {}, isDark: {}", laf.getClass().getSimpleName(), isDark);

        return isDark;
    }

    // ========== UTILITY METHODS ==========

    /**
     * Update an existing FontIcon with theme-appropriate color
     *
     * @param fontIcon        The FontIcon to update
     * @param lightThemeColor The color to use in light theme
     * @param darkThemeColor  The color to use in dark theme
     */
    public static void updateIconColor(FontIcon fontIcon, Color lightThemeColor, Color darkThemeColor) {
        Color newColor = isDarkTheme() ? darkThemeColor : lightThemeColor;
        fontIcon.setIconColor(newColor);
    }

    /**
     * Update an existing FontIcon with default theme-appropriate color
     *
     * @param fontIcon The FontIcon to update
     */
    public static void updateIconColor(FontIcon fontIcon) {
        fontIcon.setIconColor(getThemeIconColor());
    }

    /**
     * Update multiple FontIcons with theme-appropriate colors
     *
     * @param fontIcons Array of FontIcons to update
     */
    public static void updateIconColors(FontIcon... fontIcons) {
        Color themeColor = getThemeIconColor();
        for (FontIcon icon : fontIcons) {
            if (icon != null) {
                icon.setIconColor(themeColor);
            }
        }
    }

    // ========== BACKWARD COMPATIBILITY ==========

    /**
     * @deprecated Use ThemeIconUtil.of() instead
     */
    @Deprecated
    public static FontIcon createThemeIcon(Ikon icon, int size) {
        return of(icon, size);
    }

    /**
     * @deprecated Use ThemeIconUtil.ofThemed() instead
     */
    @Deprecated
    public static FontIcon createThemeIcon(Ikon icon, int size, Color lightThemeColor) {
        return ofThemed(icon, size, lightThemeColor);
    }

    /**
     * @deprecated Use ThemeIconUtil.ofThemed() instead
     */
    @Deprecated
    public static FontIcon createThemeIcon(Ikon icon, int size, Color lightThemeColor, Color darkThemeColor) {
        return ofThemed(icon, size, lightThemeColor, darkThemeColor);
    }

    /**
     * @deprecated Use ThemeIconUtil.ofStatus() instead
     */
    @Deprecated
    public static FontIcon createStatusIcon(Ikon icon, int size, Color statusColor) {
        return ofStatus(icon, size, statusColor);
    }
}
