package com.mrcresearch.util;

import javax.swing.*;
import java.awt.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * A utility class to show a simple calendar dialog for selecting a date.
 */
public final class DatePicker {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy/MM/dd");

    private DatePicker() {
    } // Prevent instantiation

    public static void showDatePickerDialog(Component parent, JTextField targetField, String title) {
        JDialog dialog = new JDialog((Frame) SwingUtilities.getWindowAncestor(parent), title, true);
        dialog.setResizable(false);

        LocalDate initialDate;
        try {
            initialDate = LocalDate.parse(targetField.getText().trim(), FORMATTER);
        } catch (DateTimeParseException e) {
            initialDate = LocalDate.now();
        }

        JPanel calendarPanel = createCalendarPanel(targetField, dialog, initialDate);
        dialog.add(calendarPanel);
        dialog.pack();
        dialog.setLocationRelativeTo(targetField);
        dialog.setVisible(true);
    }

    private static JPanel createCalendarPanel(JTextField targetField, JDialog dialog, LocalDate initialDate) {
        JPanel panel = new JPanel(new BorderLayout(5, 5));
        panel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));

        JPanel dateGridPanel = new JPanel(new GridLayout(0, 7, 2, 2));
        JComboBox<Integer> yearCombo = new JComboBox<>();
        JComboBox<MonthItem> monthCombo = new JComboBox<>();

        // Year/Month selection panel
        JPanel topPanel = createTopPanel(yearCombo, monthCombo, initialDate);

        // Day headers
        String[] dayHeaders = {"日", "月", "火", "水", "木", "金", "土"};
        for (String header : dayHeaders) {
            JLabel label = new JLabel(header, SwingConstants.CENTER);
            label.setFont(label.getFont().deriveFont(Font.BOLD));
            if ("日".equals(header)) label.setForeground(Color.RED);
            if ("土".equals(header)) label.setForeground(Color.BLUE);
            dateGridPanel.add(label);
        }

        Runnable updateGrid = () -> updateDateGrid(dateGridPanel, yearCombo, monthCombo, targetField, dialog);
        yearCombo.addActionListener(e -> updateGrid.run());
        monthCombo.addActionListener(e -> updateGrid.run());

        updateGrid.run(); // Initial population

        panel.add(topPanel, BorderLayout.NORTH);
        panel.add(dateGridPanel, BorderLayout.CENTER);
        panel.add(createBottomPanel(targetField, dialog), BorderLayout.SOUTH);

        return panel;
    }

    private static void updateDateGrid(JPanel gridPanel, JComboBox<Integer> yearCombo, JComboBox<MonthItem> monthCombo, JTextField targetField, JDialog dialog) {
        // Remove old day buttons
        while (gridPanel.getComponentCount() > 7) {
            gridPanel.remove(7);
        }

        int year = (int) yearCombo.getSelectedItem();
        int month = ((MonthItem) monthCombo.getSelectedItem()).getValue();
        LocalDate firstDayOfMonth = LocalDate.of(year, month, 1);
        int startDayOfWeek = firstDayOfMonth.getDayOfWeek().getValue() % 7; // Sun=0, Mon=1...

        for (int i = 0; i < startDayOfWeek; i++) {
            gridPanel.add(new JLabel(""));
        }

        for (int day = 1; day <= firstDayOfMonth.lengthOfMonth(); day++) {
            LocalDate date = LocalDate.of(year, month, day);
            JButton dayButton = new JButton(String.valueOf(day));
            dayButton.setFocusPainted(false);
            dayButton.setMargin(new Insets(1, 1, 1, 1));
            dayButton.addActionListener(e -> {
                targetField.setText(date.format(FORMATTER));
                dialog.dispose();
            });

            if (date.equals(LocalDate.now())) {
                dayButton.setBorder(BorderFactory.createLineBorder(Color.BLUE));
            }
            gridPanel.add(dayButton);
        }
        gridPanel.revalidate();
        gridPanel.repaint();
        dialog.pack();
    }

    private static JPanel createTopPanel(JComboBox<Integer> yearCombo, JComboBox<MonthItem> monthCombo, LocalDate initialDate) {
        int currentYear = initialDate.getYear();
        for (int i = currentYear - 5; i <= currentYear + 5; i++) {
            yearCombo.addItem(i);
        }
        yearCombo.setSelectedItem(initialDate.getYear());

        for (int i = 1; i <= 12; i++) {
            monthCombo.addItem(new MonthItem(i));
        }
        monthCombo.setSelectedItem(new MonthItem(initialDate.getMonthValue()));

        JPanel topPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        topPanel.add(yearCombo);
        topPanel.add(new JLabel("年"));
        topPanel.add(monthCombo);
        topPanel.add(new JLabel("月"));
        return topPanel;
    }

    private static JPanel createBottomPanel(JTextField targetField, JDialog dialog) {
        JPanel bottomPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        JButton todayButton = new JButton("今日");
        todayButton.addActionListener(e -> {
            targetField.setText(LocalDate.now().format(FORMATTER));
            dialog.dispose();
        });
        bottomPanel.add(todayButton);
        return bottomPanel;
    }

    private static class MonthItem {
        private final int value;

        public MonthItem(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @Override
        public boolean equals(Object obj) {
            return obj instanceof MonthItem && ((MonthItem) obj).value == this.value;
        }

        @Override
        public int hashCode() {
            return Integer.hashCode(value);
        }
    }
}