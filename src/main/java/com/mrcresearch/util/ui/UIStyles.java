package com.mrcresearch.util.ui;

import com.mrcresearch.service.common.FontSettings;

import javax.swing.*;
import java.awt.*;

/**
 * UIStyles provides small, reusable styling helpers to keep Swing UI consistent.
 * This intentionally stays minimal to avoid a large refactor.
 */
public final class UIStyles {
    private UIStyles() {
    }

    public static void applyTitleStyle(JLabel label) {
        if (label == null) return;
        label.setFont(FontSettings.getLabelFont());
    }

    public static void applyPrimaryButton(JButton button) {
        if (button == null) return;
        button.setFont(FontSettings.getButtonFont());
        button.setFocusPainted(false);
        button.setBorder(BorderFactory.createEmptyBorder(6, 12, 6, 12));
    }

    public static void setAccessibleName(JComponent comp, String name, String desc) {
        if (comp == null) return;
        if (comp.getAccessibleContext() != null) {
            comp.getAccessibleContext().setAccessibleName(name);
            if (desc != null) comp.getAccessibleContext().setAccessibleDescription(desc);
        }
        comp.setToolTipText(desc);
    }
}
