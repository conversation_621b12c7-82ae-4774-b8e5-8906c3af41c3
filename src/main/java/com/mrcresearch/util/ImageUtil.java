package com.mrcresearch.util;

import com.mrcresearch.service.common.FontSettings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 画像読み込み・表示用のユーティリティクラス
 */
public class ImageUtil {

    private static final Logger logger = LoggerFactory.getLogger(ImageUtil.class);

    private static final int THUMBNAIL_SIZE = 100; // サムネイルサイズ（100x100px）
    private static ImageIcon defaultImageIcon = null;
    private static boolean webpRegistered = false;

    // WebPデコーダーの登録（静的初期化ブロック）
    static {
        try {
            // WebPデコーダーを登録
            registerWebpDecoder();
        } catch (Exception e) {
            logger.error("Failed to register WebP decoder: {}", e.getMessage(), e);
        }
    }

    /**
     * WebPデコーダーを登録する
     */
    private static void registerWebpDecoder() {
        if (!webpRegistered) {
            try {
                // WebPデコーダーのクラスをロード
                Class.forName("com.luciad.imageio.webp.WebPImageReaderSpi");
                webpRegistered = true;
                logger.info("WebPデコーダが正常に登録されました");
            } catch (ClassNotFoundException e) {
                logger.warn("WebP decoder not available: {}", e.getMessage());
            }
        }
    }

    /**
     * サムネイルパスに基づいて画像を読み込み、サムネイルサイズのImageIconを返す
     *
     * @param thumbnailPath サムネイル画像のパス（フルパスまたはファイル名のみ）
     * @return サムネイルサイズのImageIcon（見つからない場合はデフォルト画像）
     */
    public static ImageIcon loadThumbnailImage(String thumbnailPath) {
        if (thumbnailPath == null || thumbnailPath.trim().isEmpty()) {
            return getDefaultImageIcon();
        }

        try {
            Path imageFile = null;

            // ファイル名のみの場合（パス区切り文字が含まれていない場合）、ユーザーフォルダのパスを構築
            if (!thumbnailPath.contains(System.getProperty("file.separator")) &&
                    !thumbnailPath.contains("/") && !thumbnailPath.contains("\\") &&
                    !thumbnailPath.contains("?")) {
                String userHome = System.getProperty("user.home");
                String separator = System.getProperty("file.separator");
                String imagePath = userHome + separator + ".mrcresearch" + separator + "img" + separator + thumbnailPath;
                imageFile = Paths.get(imagePath);

                // デバッグ情報を出力（集計データの画像表示問題のため、より頻繁にログ出力）
                if (Math.random() < 0.3) { // 30%の確率でログ出力
                    logger.debug("ImageUtil - Building path for filename: {}", thumbnailPath);
                    logger.debug("ImageUtil - Full path: {}", imagePath);
                    logger.debug("ImageUtil - File exists: {}", Files.exists(imageFile));
                }
            } else if (thumbnailPath.startsWith("http") || thumbnailPath.contains("?")) {
                // return null for HTTP URLs
                return null;
            } else {
                // フルパスの場合はそのまま使用（後方互換性）
                imageFile = Paths.get(thumbnailPath);

                // デバッグ情報を出力（フルパスの場合）
                if (Math.random() < 0.1) { // 10%の確率でログ出力
                    logger.debug("ImageUtil - Using full path: {}", thumbnailPath);
                }
            }

            // ファイルが存在するかチェック
            if (imageFile != null && Files.exists(imageFile)) {
                try {
                    File file = imageFile.toFile();
                    if (file.length() == 0) {
                        logger.warn("Image file exists but is empty: {}", thumbnailPath);
                        return getDefaultImageIcon();
                    }

                    // ファイルの内容を読み込む
                    byte[] fileData = Files.readAllBytes(imageFile);

                    // 画像形式を判定
                    BufferedImage originalImage = null;

                    // まずImageIOで通常の方法で読み込みを試みる
                    try {
                        originalImage = ImageIO.read(new ByteArrayInputStream(fileData));
                    } catch (Exception e) {
                        logger.warn("Standard image loading failed: {}", e.getMessage());
                    }

                    // 読み込みに失敗した場合、WebP形式として読み込みを試みる
                    if (originalImage == null && isWebPFormat(fileData)) {
                        try {
                            // WebPデコーダーが登録されていることを確認
                            registerWebpDecoder();
                            // 再度読み込みを試みる
                            originalImage = ImageIO.read(new ByteArrayInputStream(fileData));
                            logger.info("WebP画像を正常に読み込みました: {}", thumbnailPath);
                        } catch (Exception webpEx) {
                            logger.error("WebP loading failed: {}", webpEx.getMessage(), webpEx);
                        }
                    }

                    if (originalImage != null) {
                        // サムネイルサイズにリサイズ
                        Image scaledImage = originalImage.getScaledInstance(
                                THUMBNAIL_SIZE, THUMBNAIL_SIZE, Image.SCALE_SMOOTH);
                        return new ImageIcon(scaledImage);
                    } else {
                        logger.error("Failed to read image (null BufferedImage): {}", thumbnailPath);
                        // ファイル形式が不正な可能性があるため、ファイルを削除
                        try {
                            Files.delete(imageFile);
                            logger.warn("Deleted invalid image file: {}", thumbnailPath);
                        } catch (IOException deleteEx) {
                            logger.error("Failed to delete invalid image file: {}", deleteEx.getMessage(), deleteEx);
                        }
                    }
                } catch (OutOfMemoryError memoryError) {
                    System.err.println("Out of memory while loading image: " + thumbnailPath);
                    return getDefaultImageIcon();
                }
            } else {
                // デバッグ情報を出力（ファイルが見つからない場合）
                if (Math.random() < 0.2 && imageFile != null) { // 20%の確率でログ出力
                    logger.debug("ImageUtil - Image file not found: {}", imageFile);
                    logger.debug("ImageUtil - Original path: {}", thumbnailPath);

                    // ディレクトリの存在確認
                    Path parentDir = imageFile.getParent();
                    if (parentDir != null) {
                        logger.debug("ImageUtil - Parent directory exists: {}", Files.exists(parentDir));
                        if (Files.exists(parentDir)) {
                            try {
                                long fileCount = Files.list(parentDir).count();
                                logger.debug("ImageUtil - Files in directory: {}", fileCount);
                            } catch (IOException e) {
                                logger.debug("ImageUtil - Could not list directory contents");
                            }
                        }
                    }
                }
            }
        } catch (IOException e) {
            System.err.println("Error loading image from path: " + thumbnailPath + " - " + e.getMessage());
        }

        // 画像が見つからない場合はデフォルト画像を返す
        return getDefaultImageIcon();
    }

    /**
     * ItemIDに基づいて画像を読み込み、サムネイルサイズのImageIconを返す（後方互換性のため）
     *
     * @param itemId 商品ID
     * @return サムネイルサイズのImageIcon（見つからない場合はデフォルト画像）
     */
    public static ImageIcon loadThumbnailImageByItemId(String itemId) {
        if (itemId == null || itemId.trim().isEmpty()) {
            return getDefaultImageIcon();
        }

        try {
            // 画像ファイルのパスを構築
            String userHome = System.getProperty("user.home");
            String separator = System.getProperty("file.separator");
            String imagePath = userHome + separator + ".mrcresearch" + separator + "img" + separator + itemId + ".jpg";

            return loadThumbnailImage(imagePath);
        } catch (Exception e) {
            System.err.println("Error loading image for itemId: " + itemId + " - " + e.getMessage());
        }

        // 画像が見つからない場合はデフォルト画像を返す
        return getDefaultImageIcon();
    }

    /**
     * デフォルト画像のImageIconを取得する
     *
     * @return デフォルト画像のImageIcon
     */
    private static ImageIcon getDefaultImageIcon() {
        if (defaultImageIcon == null) {
            // デフォルト画像を作成（グレーの四角形）
            BufferedImage defaultImage = new BufferedImage(
                    THUMBNAIL_SIZE, THUMBNAIL_SIZE, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = defaultImage.createGraphics();

            // アンチエイリアシングを有効にする
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            // 背景を薄いグレーで塗りつぶし
            g2d.setColor(new Color(240, 240, 240));
            g2d.fillRect(0, 0, THUMBNAIL_SIZE, THUMBNAIL_SIZE);

            // 境界線を描画
            g2d.setColor(new Color(200, 200, 200));
            g2d.drawRect(0, 0, THUMBNAIL_SIZE - 1, THUMBNAIL_SIZE - 1);

            // "No Image"テキストを描画
            g2d.setColor(new Color(150, 150, 150));
            g2d.setFont(FontSettings.getSmallFont());
            FontMetrics fm = g2d.getFontMetrics();
            String text = "No Image";
            int textWidth = fm.stringWidth(text);
            int textHeight = fm.getHeight();
            int x = (THUMBNAIL_SIZE - textWidth) / 2;
            int y = (THUMBNAIL_SIZE - textHeight) / 2 + fm.getAscent();
            g2d.drawString(text, x, y);

            g2d.dispose();
            defaultImageIcon = new ImageIcon(defaultImage);
        }
        return defaultImageIcon;
    }

    /**
     * サムネイルサイズを取得する
     *
     * @return サムネイルサイズ（ピクセル）
     */
    public static int getThumbnailSize() {
        return THUMBNAIL_SIZE;
    }

    /**
     * データがWebP形式かどうかを判定する
     *
     * @param data 画像データ
     * @return WebP形式の場合はtrue
     */
    private static boolean isWebPFormat(byte[] data) {
        if (data.length < 12) {
            return false;
        }

        // WebPのマジックナンバーをチェック
        // "RIFF" + 4バイト + "WEBP"
        return data[0] == 'R' && data[1] == 'I' && data[2] == 'F' && data[3] == 'F'
                && data[8] == 'W' && data[9] == 'E' && data[10] == 'B' && data[11] == 'P';
    }
}
