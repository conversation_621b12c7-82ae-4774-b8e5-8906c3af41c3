package com.mrcresearch.util;

import javax.swing.*;
import javax.swing.text.JTextComponent;
import java.awt.*;
import java.awt.datatransfer.Clipboard;
import java.awt.datatransfer.DataFlavor;
import java.awt.datatransfer.Transferable;
import java.awt.event.*;

/**
 * Utility class for adding context menus and keyboard shortcuts to text components
 */
public class TextFieldContextMenuUtil {

    /**
     * Enable context menu and keyboard shortcuts for a text component
     *
     * @param textComponents The text components (JTextField, JTextArea, JPasswordField, etc.)
     */
    public static void enableTextFieldContextMenu(JTextComponent... textComponents) {
        if (textComponents == null) {
            return;
        }

        for (JTextComponent textComponent : textComponents) {
            enableTextFieldContextMenu(textComponent);
        }
    }

    /**
     * Enable context menu and keyboard shortcuts for a text component
     *
     * @param textComponent The text component (<PERSON><PERSON><PERSON>tField, JTextArea, JPasswordField, etc.)
     */
    public static void enableTextFieldContextMenu(JTextComponent textComponent) {
        if (textComponent == null) {
            return;
        }

        // Add keyboard shortcuts
        addKeyboardShortcuts(textComponent);

        // Add right-click context menu
        addContextMenu(textComponent);
    }

    /**
     * Add keyboard shortcuts for text operations
     *
     * @param textComponent The text component to add shortcuts to
     */
    private static void addKeyboardShortcuts(JTextComponent textComponent) {
        // Ctrl+C for copy
        textComponent.getInputMap(JComponent.WHEN_FOCUSED).put(
                KeyStroke.getKeyStroke(KeyEvent.VK_C, KeyEvent.CTRL_DOWN_MASK), "copy");
        textComponent.getActionMap().put("copy", new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                textComponent.copy();
            }
        });

        // Ctrl+V for paste
        textComponent.getInputMap(JComponent.WHEN_FOCUSED).put(
                KeyStroke.getKeyStroke(KeyEvent.VK_V, KeyEvent.CTRL_DOWN_MASK), "paste");
        textComponent.getActionMap().put("paste", new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                if (textComponent.isEditable()) {
                    safePaste(textComponent);
                }
            }
        });

        // Ctrl+X for cut
        textComponent.getInputMap(JComponent.WHEN_FOCUSED).put(
                KeyStroke.getKeyStroke(KeyEvent.VK_X, KeyEvent.CTRL_DOWN_MASK), "cut");
        textComponent.getActionMap().put("cut", new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                if (textComponent.isEditable()) {
                    textComponent.cut();
                }
            }
        });

        // Ctrl+A for select all
        textComponent.getInputMap(JComponent.WHEN_FOCUSED).put(
                KeyStroke.getKeyStroke(KeyEvent.VK_A, KeyEvent.CTRL_DOWN_MASK), "selectAll");
        textComponent.getActionMap().put("selectAll", new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                textComponent.selectAll();
            }
        });
    }

    /**
     * Add right-click context menu for text operations
     *
     * @param textComponent The text component to add context menu to
     */
    private static void addContextMenu(JTextComponent textComponent) {
        JPopupMenu contextMenu = new JPopupMenu();

        // Cut
        ActionListener contextMenuListener = e -> {
            String command = e.getActionCommand();
            switch (command) {
                case "cut":
                    if (textComponent.isEditable()) {
                        textComponent.cut();
                    }
                    break;
                case "copy":
                    textComponent.copy();
                    break;
                case "paste":
                    if (textComponent.isEditable()) {
                        safePaste(textComponent);
                    }
                    break;
                case "selectAll":
                    textComponent.selectAll();
                    break;
            }
        };

        JMenuItem cutItem = new JMenuItem("切り取り (Ctrl+X)");
        cutItem.setActionCommand("cut");
        cutItem.addActionListener(contextMenuListener);
        contextMenu.add(cutItem);

        // Copy
        JMenuItem copyItem = new JMenuItem("コピー (Ctrl+C)");
        copyItem.addActionListener(e -> textComponent.copy());
        contextMenu.add(copyItem);

        // Paste
        JMenuItem pasteItem = new JMenuItem("貼り付け (Ctrl+V)");
        pasteItem.addActionListener(e -> {
            if (textComponent.isEditable()) {
                safePaste(textComponent);
            }
        });
        contextMenu.add(pasteItem);

        contextMenu.addSeparator();

        // Select All
        JMenuItem selectAllItem = new JMenuItem("すべて選択 (Ctrl+A)");
        selectAllItem.addActionListener(e -> textComponent.selectAll());
        contextMenu.add(selectAllItem);

        // Add mouse listener for right-click
        textComponent.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showContextMenu(e);
                }
            }

            @Override
            public void mouseReleased(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showContextMenu(e);
                }
            }

            private void showContextMenu(MouseEvent e) {
                // Update menu item states based on current state
                updateMenuItemStates(cutItem, copyItem, pasteItem, selectAllItem, textComponent);

                // Show context menu
                contextMenu.show(textComponent, e.getX(), e.getY());
            }
        });
    }

    private static void updateMenuItemStates(JMenuItem cutItem, JMenuItem copyItem, JMenuItem pasteItem, JMenuItem selectAllItem, JTextComponent textComponent) {
        boolean hasSelection = textComponent.getSelectedText() != null && !textComponent.getSelectedText().isEmpty();
        boolean hasText = textComponent.getText() != null && !textComponent.getText().isEmpty();
        boolean isEditable = textComponent.isEditable();
        boolean hasClipboardContent = hasClipboardContent();

        cutItem.setEnabled(isEditable && hasSelection);
        copyItem.setEnabled(hasSelection);
        pasteItem.setEnabled(isEditable && hasClipboardContent);
        selectAllItem.setEnabled(hasText);
    }

    /**
     * Safely paste content from clipboard to text component with error handling
     *
     * @param textComponent The text component to paste into
     */
    private static void safePaste(JTextComponent textComponent) {
        if (textComponent == null || !textComponent.isEditable()) {
            return;
        }

        try {
            // Try to get clipboard content safely
            Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
            String clipboardText = (String) clipboard.getData(DataFlavor.stringFlavor);
            if (clipboardText != null && !clipboardText.isEmpty()) {
                // Use replaceSelection instead of paste() for better control
                textComponent.replaceSelection(clipboardText);
            }
        } catch (Exception e) {
            System.err.println("Error during paste operation: " + e.getMessage());
            // Fallback to standard paste method
            try {
                textComponent.paste();
            } catch (Exception fallbackError) {
                System.err.println("Fallback paste also failed: " + fallbackError.getMessage());
                // Show user-friendly error message
                JOptionPane.showMessageDialog(
                        SwingUtilities.getWindowAncestor(textComponent),
                        "貼り付けに失敗しました。クリップボードの内容を確認してください。",
                        "貼り付けエラー",
                        JOptionPane.WARNING_MESSAGE
                );
            }
        }
    }

    /**
     * Safely check if clipboard has content
     *
     * @return true if clipboard has text content, false otherwise
     */
    private static boolean hasClipboardContent() {
        try {
            Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
            Transferable contents = clipboard.getContents(null);
            return contents != null && contents.isDataFlavorSupported(DataFlavor.stringFlavor);
        } catch (Exception e) {
            System.err.println("Error checking clipboard content: " + e.getMessage());
            return false;
        }
    }
}
