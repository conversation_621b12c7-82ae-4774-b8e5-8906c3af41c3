package com.mrcresearch.util;

import com.mrcresearch.service.common.MercariApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.mrcresearch.service.enums.ItemTypeEnum;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.net.URI;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * Utility class for adding browser context menu functionality to JTable components
 * that display product data with item_id, seller_id, and item_type information.
 */
public class TableBrowserContextMenuUtil {

    private static final Logger logger = LoggerFactory.getLogger(TableBrowserContextMenuUtil.class);

    /**
     * Enable browser context menu functionality for a table with product data
     *
     * @param table                  The JTable to enable browser context menu for
     * @param itemIdColumnIndex      Column index for item ID (商品ID)
     * @param productNameColumnIndex Column index for product name (商品名)
     * @param sellerIdColumnIndex    Column index for seller ID (出品者ID)
     * @param itemTypeProvider       Function to get item type from row index (returns ItemTypeEnum or null)
     */
    public static void enableBrowserContextMenu(JTable table,
                                                int itemIdColumnIndex,
                                                int productNameColumnIndex,
                                                int sellerIdColumnIndex,
                                                Function<Integer, ItemTypeEnum> itemTypeProvider) {
        if (table == null) {
            return;
        }

        // First enable basic copy functionality
        TableCopyUtil.enableTableCopy(table);

        // Add browser-specific context menu
        addBrowserContextMenu(table, itemIdColumnIndex, productNameColumnIndex,
                sellerIdColumnIndex, itemTypeProvider);
    }

    /**
     * Enable browser context menu functionality with seller-specific options
     *
     * @param table                  The JTable to enable browser context menu for
     * @param itemIdColumnIndex      Column index for item ID (-1 if not in table, will use itemIdProvider)
     * @param productNameColumnIndex Column index for product name
     * @param sellerIdColumnIndex    Column index for seller ID
     * @param itemTypeProvider       Function to get item type from row index
     * @param sellerNameProvider     Function to get seller name from row index
     * @param favoriteAction         Action to add seller to favorites
     * @param researchAction         Action to start seller research
     */
    public static void enableBrowserContextMenuWithSellerOptions(JTable table,
                                                                 int itemIdColumnIndex,
                                                                 int productNameColumnIndex,
                                                                 int sellerIdColumnIndex,
                                                                 Function<Integer, ItemTypeEnum> itemTypeProvider,
                                                                 Function<Integer, String> sellerNameProvider,
                                                                 BiConsumer<String, String> favoriteAction,
                                                                 BiConsumer<String, String> researchAction) {
        if (table == null) {
            return;
        }

        // First enable copy functionality with seller options
        TableCopyUtil.enableTableCopyWithSellerOptions(table, sellerNameProvider, favoriteAction, researchAction);

        // Add browser-specific context menu
        addBrowserContextMenuWithSellerOptions(table, itemIdColumnIndex, productNameColumnIndex,
                sellerIdColumnIndex, itemTypeProvider, sellerNameProvider,
                favoriteAction, researchAction, null);
    }

    /**
     * Enable browser context menu functionality with seller-specific options and custom item ID provider
     *
     * @param table                  The JTable to enable browser context menu for
     * @param itemIdColumnIndex      Column index for item ID (-1 if not in table, will use itemIdProvider)
     * @param productNameColumnIndex Column index for product name
     * @param sellerIdColumnIndex    Column index for seller ID
     * @param itemTypeProvider       Function to get item type from row index
     * @param sellerNameProvider     Function to get seller name from row index
     * @param favoriteAction         Action to add seller to favorites
     * @param researchAction         Action to start seller research
     * @param itemIdProvider         Function to get item ID from row index (used when itemIdColumnIndex is -1)
     */
    public static void enableBrowserContextMenuWithSellerOptions(JTable table,
                                                                 int itemIdColumnIndex,
                                                                 int productNameColumnIndex,
                                                                 int sellerIdColumnIndex,
                                                                 Function<Integer, ItemTypeEnum> itemTypeProvider,
                                                                 Function<Integer, String> sellerNameProvider,
                                                                 BiConsumer<String, String> favoriteAction,
                                                                 BiConsumer<String, String> researchAction,
                                                                 Function<Integer, String> itemIdProvider) {
        if (table == null) {
            return;
        }

        // First enable copy functionality with seller options
        TableCopyUtil.enableTableCopyWithSellerOptions(table, sellerNameProvider, favoriteAction, researchAction);

        // Add browser-specific context menu
        addBrowserContextMenuWithSellerOptions(table, itemIdColumnIndex, productNameColumnIndex,
                sellerIdColumnIndex, itemTypeProvider, sellerNameProvider,
                favoriteAction, researchAction, itemIdProvider);
    }

    /**
     * Add browser context menu to table
     */
    private static void addBrowserContextMenu(JTable table,
                                              int itemIdColumnIndex,
                                              int productNameColumnIndex,
                                              int sellerIdColumnIndex,
                                              Function<Integer, ItemTypeEnum> itemTypeProvider) {

        // Remove existing mouse listeners to avoid conflicts
        removeExistingBrowserListeners(table);

        table.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showBrowserContextMenu(e, table, itemIdColumnIndex, productNameColumnIndex,
                            sellerIdColumnIndex, itemTypeProvider, null, null, null, null);
                }
            }

            @Override
            public void mouseReleased(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showBrowserContextMenu(e, table, itemIdColumnIndex, productNameColumnIndex,
                            sellerIdColumnIndex, itemTypeProvider, null, null, null, null);
                }
            }
        });
    }

    /**
     * Add browser context menu with seller options to table
     */
    private static void addBrowserContextMenuWithSellerOptions(JTable table,
                                                               int itemIdColumnIndex,
                                                               int productNameColumnIndex,
                                                               int sellerIdColumnIndex,
                                                               Function<Integer, ItemTypeEnum> itemTypeProvider,
                                                               Function<Integer, String> sellerNameProvider,
                                                               BiConsumer<String, String> favoriteAction,
                                                               BiConsumer<String, String> researchAction,
                                                               Function<Integer, String> itemIdProvider) {

        // Remove existing mouse listeners to avoid conflicts
        removeExistingBrowserListeners(table);

        table.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showBrowserContextMenu(e, table, itemIdColumnIndex, productNameColumnIndex,
                            sellerIdColumnIndex, itemTypeProvider, sellerNameProvider,
                            favoriteAction, researchAction, itemIdProvider);
                }
            }

            @Override
            public void mouseReleased(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showBrowserContextMenu(e, table, itemIdColumnIndex, productNameColumnIndex,
                            sellerIdColumnIndex, itemTypeProvider, sellerNameProvider,
                            favoriteAction, researchAction, itemIdProvider);
                }
            }
        });
    }

    /**
     * Remove existing browser-related mouse listeners to avoid conflicts
     */
    private static void removeExistingBrowserListeners(JTable table) {
        // This method can be enhanced if needed to remove specific listeners
        // For now, we'll rely on the fact that our new listeners will be added last
        // and will handle the popup trigger events
    }

    /**
     * Show browser context menu
     */
    private static void showBrowserContextMenu(MouseEvent e, JTable table,
                                               int itemIdColumnIndex,
                                               int productNameColumnIndex,
                                               int sellerIdColumnIndex,
                                               Function<Integer, ItemTypeEnum> itemTypeProvider,
                                               Function<Integer, String> sellerNameProvider,
                                               BiConsumer<String, String> favoriteAction,
                                               BiConsumer<String, String> researchAction,
                                               Function<Integer, String> itemIdProvider) {

        int row = table.rowAtPoint(e.getPoint());
        int column = table.columnAtPoint(e.getPoint());

        if (row >= 0) {
            // Select the clicked row
            table.setRowSelectionInterval(row, row);
            if (column >= 0) {
                table.setColumnSelectionInterval(column, column);
            }

            // Create context menu
            JPopupMenu contextMenu = createBrowserContextMenu(table, row, column,
                    itemIdColumnIndex, productNameColumnIndex,
                    sellerIdColumnIndex, itemTypeProvider,
                    sellerNameProvider, favoriteAction, researchAction,
                    itemIdProvider);

            // Show context menu
            contextMenu.show(table, e.getX(), e.getY());
        }
    }

    /**
     * Create browser context menu with appropriate options based on clicked column
     */
    private static JPopupMenu createBrowserContextMenu(JTable table, int row, int column,
                                                       int itemIdColumnIndex,
                                                       int productNameColumnIndex,
                                                       int sellerIdColumnIndex,
                                                       Function<Integer, ItemTypeEnum> itemTypeProvider,
                                                       Function<Integer, String> sellerNameProvider,
                                                       BiConsumer<String, String> favoriteAction,
                                                       BiConsumer<String, String> researchAction,
                                                       Function<Integer, String> itemIdProvider) {

        JPopupMenu contextMenu = new JPopupMenu();

        // Add basic copy functionality first
        addBasicCopyMenuItems(contextMenu, table, row);

        // Add separator before browser options
        contextMenu.addSeparator();

        // Get data from the row
        String itemId;
        if (itemIdColumnIndex >= 0) {
            itemId = getTableValue(table, row, itemIdColumnIndex);
        } else if (itemIdProvider != null) {
            itemId = itemIdProvider.apply(row);
        } else {
            itemId = "";
        }

        String sellerId = getTableValue(table, row, sellerIdColumnIndex);
        ItemTypeEnum itemType = itemTypeProvider != null ? itemTypeProvider.apply(row) : ItemTypeEnum.NORMAL;

        // Add browser menu items based on clicked column
        if (column == productNameColumnIndex) {
            addProductPageMenuItem(contextMenu, itemId, itemType);
        } else if (column == sellerIdColumnIndex) {
            addSellerPageMenuItem(contextMenu, sellerId, itemType);
        } else {
            // For other columns, show both options
            addProductPageMenuItem(contextMenu, itemId, itemType);
            addSellerPageMenuItem(contextMenu, sellerId, itemType);
        }

        // Add seller-specific options if available and clicked on seller column
        if (column == sellerIdColumnIndex && sellerNameProvider != null && favoriteAction != null && researchAction != null) {
            contextMenu.addSeparator();
            String sellerName = sellerNameProvider.apply(row);

            // お気に入り登録
            JMenuItem favoriteItem = new JMenuItem("お気に入り登録");
            favoriteItem.addActionListener(e -> favoriteAction.accept(sellerId, sellerName));
            contextMenu.add(favoriteItem);

            // セラーをリサーチ
            JMenuItem researchItem = new JMenuItem("セラーをリサーチ");
            researchItem.addActionListener(e -> researchAction.accept(sellerId, sellerName));
            contextMenu.add(researchItem);
        }

        return contextMenu;
    }

    /**
     * Add basic copy menu items
     */
    private static void addBasicCopyMenuItems(JPopupMenu contextMenu, JTable table, int row) {
        // Copy selected cells
        JMenuItem copyItem = new JMenuItem("コピー (Ctrl+C)");
        copyItem.addActionListener(e -> ClipboardUtil.copyTableSelection(table));
        contextMenu.add(copyItem);

        // Copy row
        JMenuItem copyRowItem = new JMenuItem("行をコピー");
        copyRowItem.addActionListener(e -> copySelectedRow(table, row));
        contextMenu.add(copyRowItem);

        // Copy all data
        JMenuItem copyAllItem = new JMenuItem("すべてコピー");
        copyAllItem.addActionListener(e -> ClipboardUtil.copyAllTableData(table));
        contextMenu.add(copyAllItem);
    }

    /**
     * Add product page menu item
     */
    private static void addProductPageMenuItem(JPopupMenu contextMenu, String itemId, ItemTypeEnum itemType) {
        JMenuItem productPageItem = new JMenuItem("ブラウザで商品ページを開く");

        if (itemId != null && !itemId.trim().isEmpty() && !itemId.equals("データがありません")) {
            productPageItem.addActionListener(e -> openProductPage(itemId, itemType));
        } else {
            productPageItem.setEnabled(false);
        }

        contextMenu.add(productPageItem);
    }

    /**
     * Add seller page menu item
     */
    private static void addSellerPageMenuItem(JPopupMenu contextMenu, String sellerId, ItemTypeEnum itemType) {
        JMenuItem sellerPageItem = new JMenuItem("ブラウザでセラーページを開く");

        // Disable for SHOPS items or if seller ID is invalid
        if (itemType == ItemTypeEnum.SHOPS || sellerId == null || sellerId.trim().isEmpty() || sellerId.equals("データがありません")) {
            sellerPageItem.setEnabled(false);
            sellerPageItem.setToolTipText("SHOPS商品にはセラーページがありません");
        } else {
            sellerPageItem.addActionListener(e -> openSellerPage(sellerId));
        }

        contextMenu.add(sellerPageItem);
    }

    /**
     * Get table value safely
     */
    private static String getTableValue(JTable table, int row, int column) {
        try {
            if (row >= 0 && row < table.getRowCount() && column >= 0 && column < table.getColumnCount()) {
                Object value = table.getValueAt(row, column);
                return value != null ? value.toString().trim() : "";
            }
        } catch (Exception e) {
            logger.error("Error getting table value at row " + row + ", column " + column + ": " + e.getMessage());
        }
        return "";
    }

    /**
     * Open product page in browser
     */
    private static void openProductPage(String itemId, ItemTypeEnum itemType) {
        try {
            String url;
            if (itemType == ItemTypeEnum.SHOPS) {
                url = MercariApi.PRODUCT_PAGE_BASE_URL + itemId;
            } else {
                url = MercariApi.ITEM_BASE_URL + itemId;
            }

            Desktop.getDesktop().browse(new URI(url));
            logger.info("商品ページを開いています: " + url);
        } catch (Exception e) {
            logger.error("Error opening product page for item " + itemId + ": " + e.getMessage());
            showErrorDialog("商品ページを開けませんでした。\n" + e.getMessage());
        }
    }

    /**
     * Open seller page in browser
     */
    private static void openSellerPage(String sellerId) {
        try {
            String url = MercariApi.USER_PROFILE + sellerId;
            Desktop.getDesktop().browse(new URI(url));
            logger.info("セラーページを開いています: " + url);
        } catch (Exception e) {
            logger.error("Error opening seller page for seller " + sellerId + ": " + e.getMessage());
            showErrorDialog("セラーページを開けませんでした。\n" + e.getMessage());
        }
    }

    /**
     * Copy selected row to clipboard
     */
    private static void copySelectedRow(JTable table, int row) {
        try {
            StringBuilder sb = new StringBuilder();

            for (int col = 0; col < table.getColumnCount(); col++) {
                Object value = table.getValueAt(row, col);
                String cellValue = (value != null) ? value.toString() : "";

                // Remove any tabs and newlines from cell content
                cellValue = cellValue.replace("\t", " ").replace("\n", " ").replace("\r", " ");

                sb.append(cellValue);

                if (col < table.getColumnCount() - 1) {
                    sb.append("\t"); // Tab separator between columns
                }
            }

            ClipboardUtil.copyToClipboard(sb.toString());
        } catch (Exception e) {
            logger.error("Error copying row: " + e.getMessage());
        }
    }

    /**
     * Show error dialog
     */
    private static void showErrorDialog(String message) {
        SwingUtilities.invokeLater(() -> {
            JOptionPane.showMessageDialog(
                    null,
                    message,
                    "エラー",
                    JOptionPane.ERROR_MESSAGE
            );
        });
    }
}
