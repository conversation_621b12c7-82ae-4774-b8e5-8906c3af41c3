package com.mrcresearch.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Nettyリソース管理ユーティリティクラス
 * ByteBufリソースリークの検出と対策を管理する
 */
public class NettyResourceManager {
    private static final Logger logger = LoggerFactory.getLogger(NettyResourceManager.class);
    
    private static boolean initialized = false;
    
    /**
     * Nettyリソース管理の初期化
     * アプリケーション起動時に一度だけ呼び出す
     */
    public static synchronized void initialize() {
        if (initialized) {
            return;
        }
        
        try {
            // 本番環境ではリソースリーク検出を無効化
            // 開発環境では SIMPLE レベルで検出
            String leakDetectionLevel = isProductionEnvironment() ? "DISABLED" : "SIMPLE";
            System.setProperty("io.netty.leakDetection.level", leakDetectionLevel);
            
            // Nettyのダイレクトメモリ使用量を制限
            System.setProperty("io.netty.maxDirectMemory", "128m");
            
            // Nettyのワーカースレッド数を制限
            System.setProperty("io.netty.eventLoopThreads", "4");
            
            // ByteBufアロケーターの設定
            System.setProperty("io.netty.allocator.type", "pooled");
            System.setProperty("io.netty.allocator.maxOrder", "9"); // 最大チャンクサイズを制限
            
            logger.info("Nettyリソース管理が初期化されました (リーク検出レベル: {})", leakDetectionLevel);
            initialized = true;
            
        } catch (Exception e) {
            logger.error("Nettyリソース管理の初期化に失敗しました: " + e.getMessage(), e);
        }
    }
    
    /**
     * 本番環境かどうかを判定
     * 
     * @return 本番環境の場合true
     */
    private static boolean isProductionEnvironment() {
        // 環境変数やシステムプロパティで判定
        String env = System.getProperty("app.environment", "development");
        return "production".equalsIgnoreCase(env) || "prod".equalsIgnoreCase(env);
    }
    
    /**
     * リソースクリーンアップの実行
     * 定期的に呼び出してメモリリークを軽減
     */
    public static void performCleanup() {
        try {
            // ガベージコレクションを促す
            System.gc();
            
            // 少し待ってから再度実行
            Thread.sleep(100);
            System.gc();
            
            logger.debug("Nettyリソースクリーンアップを実行しました");
            
        } catch (Exception e) {
            logger.warn("リソースクリーンアップ中にエラーが発生しました: " + e.getMessage());
        }
    }
    
    /**
     * アプリケーション終了時のクリーンアップ
     */
    public static void shutdown() {
        try {
            logger.info("Nettyリソース管理をシャットダウンしています...");
            
            // 最終的なクリーンアップ
            performCleanup();
            
            logger.info("Nettyリソース管理のシャットダウンが完了しました");
            
        } catch (Exception e) {
            logger.error("Nettyリソース管理のシャットダウン中にエラーが発生しました: " + e.getMessage(), e);
        }
    }
}
