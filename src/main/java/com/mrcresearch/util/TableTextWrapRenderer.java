package com.mrcresearch.util;

import javax.swing.*;
import javax.swing.table.DefaultTableCellRenderer;
import java.awt.*;

/**
 * Custom table cell renderer that provides text wrapping functionality for table cells.
 * This renderer automatically wraps long text content to multiple lines within the cell,
 * ensuring all content remains visible regardless of column width constraints.
 */
public class TableTextWrapRenderer extends DefaultTableCellRenderer {

    private static final int MIN_ROW_HEIGHT = 35; // Minimum row height to maintain consistency
    private static final int MAX_ROW_HEIGHT = 150; // Maximum row height to prevent excessive expansion
    private static final int PADDING = 12; // Padding for text within cells

    private Font customFont; // Allow custom font

    /**
     * Default constructor that sets up the renderer for text wrapping.
     */
    public TableTextWrapRenderer() {
        this(null);
    }

    /**
     * Constructor that allows specifying a custom font.
     *
     * @param customFont The font to use for rendering.
     */
    public TableTextWrapRenderer(Font customFont) {
        this.customFont = customFont;
        setVerticalAlignment(JLabel.TOP);
        setHorizontalAlignment(JLabel.LEFT);
    }

    /**
     * Constructor that allows customization of text alignment.
     *
     * @param horizontalAlignment The horizontal alignment (e.g., JLabel.LEFT, JLabel.CENTER)
     * @param verticalAlignment   The vertical alignment (e.g., JLabel.TOP, JLabel.CENTER)
     */
    public TableTextWrapRenderer(int horizontalAlignment, int verticalAlignment) {
        this(null, horizontalAlignment, verticalAlignment);
    }

    /**
     * Constructor that allows full customization of font and alignment.
     *
     * @param customFont          The font to use for rendering.
     * @param horizontalAlignment The horizontal alignment.
     * @param verticalAlignment   The vertical alignment.
     */
    public TableTextWrapRenderer(Font customFont, int horizontalAlignment, int verticalAlignment) {
        this.customFont = customFont;
        setHorizontalAlignment(horizontalAlignment);
        setVerticalAlignment(verticalAlignment);
    }

    @Override
    public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected,
                                                   boolean hasFocus, int row, int column) {

        // Get the default component first
        Component component = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

        if (value == null || value.toString().trim().isEmpty()) {
            return component;
        }

        String text = value.toString();

        // Use the custom font if provided, otherwise use the table's font
        Font font = (customFont != null) ? customFont : table.getFont();
        setFont(font);

        // Get the column width
        int columnWidth = table.getColumnModel().getColumn(column).getWidth();

        // If column width is too small, use a minimum width
        if (columnWidth < 50) {
            columnWidth = 150; // Default minimum width
        }

        // Calculate available width for text (subtract padding)
        int availableWidth = columnWidth - PADDING;

        // Use HTML to enable text wrapping
        String wrappedText = wrapTextToWidth(text, availableWidth, font);
        setText(wrappedText);

        // Calculate required height for the wrapped text
        int requiredHeight = calculateRequiredHeight(wrappedText, availableWidth, font);

        // Ensure the row height is appropriate for the content
        adjustRowHeight(table, row, requiredHeight);

        return this;
    }

    /**
     * Wraps text to fit within the specified width using HTML formatting.
     *
     * @param text  The original text to wrap
     * @param width The available width in pixels
     * @param font  The font used for rendering
     * @return HTML-formatted text with appropriate line breaks
     */
    private String wrapTextToWidth(String text, int width, Font font) {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }

        // Escape HTML characters to prevent rendering issues
        text = escapeHtml(text);

        // Calculate font-based adjustments
        int fontSize = font.getSize();
        String fontFamily = font.getFamily();
        String fontWeight = font.isBold() ? "bold" : "normal";
        String fontStyle = font.isItalic() ? "italic" : "normal";

        // Use HTML with enhanced styling for better font-aware wrapping
        return String.format("<html><div style='width: %dpx; word-wrap: break-word; word-break: break-word; " +
                        "font-family: %s; font-size: %dpt; font-weight: %s; font-style: %s; " +
                        "line-height: 1.2; overflow-wrap: break-word;'>%s</div></html>",
                width, fontFamily, fontSize, fontWeight, fontStyle, text);
    }

    /**
     * Calculates the required height for the wrapped text.
     *
     * @param htmlText The HTML-formatted text
     * @param width    The available width
     * @param font     The font used for rendering
     * @return The required height in pixels
     */
    private int calculateRequiredHeight(String htmlText, int width, Font font) {
        // Create a temporary JLabel to measure the text height
        JLabel tempLabel = new JLabel(htmlText);
        tempLabel.setFont(font);
        tempLabel.setSize(width, Integer.MAX_VALUE);
        tempLabel.setVerticalAlignment(JLabel.TOP);

        // Get the preferred size
        Dimension preferredSize = tempLabel.getPreferredSize();

        // Font-based height adjustment
        int fontSize = font.getSize();
        int fontBasedPadding = Math.max(PADDING, fontSize / 2);

        // Add font-aware padding and ensure it's within bounds
        int height = preferredSize.height + fontBasedPadding;
        return Math.max(MIN_ROW_HEIGHT, Math.min(MAX_ROW_HEIGHT, height));
    }

    /**
     * Adjusts the row height of the table to accommodate the wrapped text.
     *
     * @param table          The JTable instance
     * @param row            The row index
     * @param requiredHeight The required height for the row
     */
    private void adjustRowHeight(JTable table, int row, int requiredHeight) {
        // Only increase row height if necessary
        if (table.getRowHeight(row) < requiredHeight) {
            table.setRowHeight(row, requiredHeight);
        }
    }

    /**
     * Escapes HTML characters to prevent rendering issues.
     *
     * @param text The text to escape
     * @return The escaped text
     */
    private String escapeHtml(String text) {
        if (text == null) {
            return "";
        }

        return text.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&#39;");
    }

    /**
     * Creates a text wrap renderer with a specified font and alignment.
     *
     * @param font                The font to use.
     * @param horizontalAlignment The horizontal alignment.
     * @return A new TableTextWrapRenderer.
     */
    public static TableTextWrapRenderer create(Font font, int horizontalAlignment) {
        return new TableTextWrapRenderer(font, horizontalAlignment, JLabel.CENTER);
    }

    /**
     * Creates a text wrap renderer with center alignment.
     *
     * @return A new TableTextWrapRenderer with center alignment
     */
    public static TableTextWrapRenderer createCenterAligned() {
        return new TableTextWrapRenderer(null, JLabel.CENTER, JLabel.CENTER);
    }

    /**
     * Creates a text wrap renderer with left alignment (default).
     *
     * @return A new TableTextWrapRenderer with left alignment
     */
    public static TableTextWrapRenderer createLeftAligned() {
        return new TableTextWrapRenderer(null, JLabel.LEFT, JLabel.CENTER);
    }

    /**
     * Creates a text wrap renderer with right alignment.
     *
     * @return A new TableTextWrapRenderer with right alignment
     */
    public static TableTextWrapRenderer createRightAligned() {
        return new TableTextWrapRenderer(null, JLabel.RIGHT, JLabel.CENTER);
    }
}
