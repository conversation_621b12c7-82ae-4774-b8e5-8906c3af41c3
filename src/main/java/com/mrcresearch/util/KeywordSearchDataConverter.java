package com.mrcresearch.util;

import com.mrcresearch.service.enums.ItemStatusEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.mrcresearch.service.enums.ItemTypeEnum;
import com.mrcresearch.service.models.item.Item;
import com.mrcresearch.util.database.DatabaseDateTimeUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Utility class for converting keyword search data to database format.
 * Handles conversion of Item objects from keyword search results to proper database format.
 */
public class KeywordSearchDataConverter {

    private static final Logger logger = LoggerFactory.getLogger(KeywordSearchDataConverter.class);

    /**
     * Convert a list of Item objects from keyword search to proper database format.
     *
     * @param itemList The list of Item objects from keyword search
     * @return A list of Item objects ready for database storage
     */
    public static List<Item> convertKeywordSearchItems(List<Item> itemList) {
        if (itemList == null || itemList.isEmpty()) {
            return new ArrayList<>();
        }

        List<Item> convertedItems = new ArrayList<>();

        for (Item item : itemList) {
            try {
                Item convertedItem = convertKeywordSearchItem(item);
                if (convertedItem != null) {
                    convertedItems.add(convertedItem);
                }
            } catch (Exception e) {
                logger.error("Error converting keyword search Item for ID {}: {}", item.getId(), e.getMessage(), e);
            }
        }

        logger.info("データベース保存用に {} 件のキーワード検索アイテムオブジェクトを変換しました", convertedItems.size());
        return convertedItems;
    }

    /**
     * Convert a single Item object from keyword search to proper database format.
     *
     * @param item The Item object from keyword search
     * @return An Item object ready for database storage, or null if conversion fails
     */
    public static Item convertKeywordSearchItem(Item item) {
        if (item == null) {
            return null;
        }

        try {
            // Create a new Item object to avoid modifying the original
            Item convertedItem = new Item();

            // Copy basic item information
            convertedItem.setId(item.getId());
            convertedItem.setSellerId(item.getSellerId());
            convertedItem.setName(item.getName());
            convertedItem.setPrice(item.getPrice());

            // Handle status conversion
            if (item.getStatus() != null) {
                convertedItem.setStatus(item.getStatus());
            } else {
                // Default to ON_SALE if status is not set
                convertedItem.setStatus(ItemStatusEnum.ON_SALE.getApiStatus());
            }

            // Handle item type
            if (item.getItemType() != null) {
                convertedItem.setItemType(item.getItemType());
            } else {
                // Default to NORMAL if item type is not set
                convertedItem.setItemType(ItemTypeEnum.NORMAL.getApiType());
            }

            // Handle date conversion - this is the key fix
            String currentDate = DatabaseDateTimeUtil.formatJapaneseTimezone(new Date());

            // Convert Item's date fields to proper database format
            String createdDate = convertItemDateToDbFormat(item.getCreated());
            String updatedDate = convertItemDateToDbFormat(item.getUpdated());

            convertedItem.setCreated(createdDate != null ? createdDate : currentDate);
            convertedItem.setUpdated(updatedDate != null ? updatedDate : currentDate);

            // Copy other fields
            convertedItem.setThumbnails(item.getThumbnails());
            convertedItem.setItemConditionId(item.getItemConditionId() > 0 ? item.getItemConditionId() : 1);
            convertedItem.setCategoryId(item.getCategoryId() > 0 ? item.getCategoryId() : 1);
            convertedItem.setShippingMethodId(item.getShippingMethodId() > 0 ? item.getShippingMethodId() : 1);

            // Copy additional fields if present
            convertedItem.setBuyerId(item.getBuyerId());
            convertedItem.setShippingPayerId(item.getShippingPayerId());
            convertedItem.setItemBrand(item.getItemBrand());
            convertedItem.setShopName(item.getShopName());
            convertedItem.setItemSize(item.getItemSize());
            convertedItem.setIsNoPrice(item.getIsNoPrice());
            convertedItem.setTitle(item.getTitle());
            convertedItem.setIsLiked(item.getIsLiked());

            return convertedItem;

        } catch (Exception e) {
            logger.error("Error converting keyword search Item: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * Convert Item date string to database format.
     * Handles various date formats from Item objects and converts them to database-compatible format.
     *
     * @param dateStr The date string from Item (could be ISO format, timestamp, etc.)
     * @return Formatted date string for database storage, or null if conversion fails
     */
    private static String convertItemDateToDbFormat(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            logger.debug("Date string is null or empty");
            return null;
        }

        String trimmedDateStr = dateStr.trim();

        try {
            // Try to parse the date using DatabaseDateTimeUtil's automatic format detection
            Date parsedDate = DatabaseDateTimeUtil.parseAnyDateFormat(trimmedDateStr);
            if (parsedDate != null) {
                // Convert to Japanese timezone format for database storage
                String formattedDate = DatabaseDateTimeUtil.formatJapaneseTimezone(parsedDate);
                return formattedDate;
            }

            // If automatic parsing fails, try to handle Unix timestamp
            if (trimmedDateStr.matches("\\d+")) {
                long timestamp = Long.parseLong(trimmedDateStr);
                // Check if it's in seconds or milliseconds
                if (timestamp < 10000000000L) {
                    // Likely in seconds, convert to milliseconds
                    timestamp = timestamp * 1000;
                }
                Date date = new Date(timestamp);
                String formattedDate = DatabaseDateTimeUtil.formatJapaneseTimezone(date);
                return formattedDate;
            }

            // If all parsing attempts fail, log the issue
            logger.error("Failed to convert date format for keyword search: {}", trimmedDateStr);
            return null;

        } catch (Exception e) {
            logger.error("Error converting keyword search Item date to database format: {} - {}", trimmedDateStr, e.getMessage(), e);
            return null;
        }
    }
}
