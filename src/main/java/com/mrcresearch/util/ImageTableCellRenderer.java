package com.mrcresearch.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import javax.swing.table.DefaultTableCellRenderer;
import java.awt.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * テーブルセル内で画像を表示するためのカスタムレンダラー
 */
public class ImageTableCellRenderer extends DefaultTableCellRenderer {

    private static final Logger logger = LoggerFactory.getLogger(ImageTableCellRenderer.class);

    // 画像キャッシュ（パフォーマンス向上のため）
    private static final ConcurrentHashMap<String, ImageIcon> imageCache = new ConcurrentHashMap<>();

    public ImageTableCellRenderer() {
        setHorizontalAlignment(JLabel.CENTER);
        setVerticalAlignment(JLabel.CENTER);
    }

    @Override
    public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {

        // 基本的なレンダリング設定（テキストは空文字列で初期化）
        super.getTableCellRendererComponent(table, "", isSelected, hasFocus, row, column);

        // デバッグ情報（最初の数行のみ）
        if (row < 3 && Math.random() < 0.3) {
            logger.debug("ImageTableCellRenderer - 行: " + row + ", 値の型: " +
                    (value != null ? value.getClass().getName() : "null"));
        }

        // valueがImageIconの場合は直接使用
        if (value instanceof ImageIcon imageIcon) {
            setIcon(imageIcon);
            // テキストを確実にクリア
            setText("");
            if (row < 3 && Math.random() < 0.3) {
                logger.debug("ImageTableCellRenderer - 行 " + row + " に ImageIcon を直接設定しました");
            }
        } else {
            // 従来の処理（後方互換性のため）
            String thumbnailPath = (value != null) ? value.toString() : null;

            if (thumbnailPath != null && !thumbnailPath.trim().isEmpty()) {
                try {
                    // ローカルから画像を取得
                    ImageIcon imageIcon = ImageUtil.loadThumbnailImage(thumbnailPath);
                    setIcon(imageIcon);
                    setText(""); // テキストをクリア
                } catch (Exception e) {
                    // 例外が発生した場合はデフォルト画像
                    setIcon(ImageUtil.loadThumbnailImage(null));
                    setText(""); // テキストをクリア
                    logger.error("ERROR: ImageTableCellRenderer - Exception loading image: " + thumbnailPath + " - " + e.getMessage());
                }
            } else {
                // サムネイルパスがない場合はデフォルト画像
                setIcon(ImageUtil.loadThumbnailImage(null));
                setText(""); // テキストをクリア
            }
        }

        // セルの背景色設定
        if (isSelected) {
            setBackground(table.getSelectionBackground());
        } else {
            setBackground(table.getBackground());
        }

        return this;
    }

    /**
     * 画像キャッシュをクリアする
     */
    public static void clearImageCache() {
        imageCache.clear();
    }

    /**
     * 特定のサムネイルパスの画像をキャッシュから削除する
     *
     * @param thumbnailPath 削除するサムネイルパス
     */
    public static void removeFromCache(String thumbnailPath) {
        if (thumbnailPath != null) {
            imageCache.remove(thumbnailPath);
        }
    }
}