package com.mrcresearch.util;

import com.mrcresearch.service.analyze.model.GetItemModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.mrcresearch.service.enums.ItemStatusEnum;
import com.mrcresearch.service.enums.ItemTypeEnum;
import com.mrcresearch.service.models.item.Item;
import com.mrcresearch.util.database.DatabaseDateTimeUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Utility class for converting seller search data between different formats.
 * Handles conversion from GetItemModel to Item objects for database storage.
 */
public class SellerSearchDataConverter {

    private static final Logger logger = LoggerFactory.getLogger(SellerSearchDataConverter.class);

    /**
     * Private constructor to prevent instantiation.
     * This is a utility class with static methods only.
     */
    private SellerSearchDataConverter() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * Convert a list of GetItemModel objects to Item objects for database storage.
     *
     * @param getItemList The list of GetItemModel objects from seller search
     * @param sellerId    The seller ID for all items
     * @return A list of Item objects ready for database storage
     */
    public static List<Item> convertGetItemListToItems(List<GetItemModel> getItemList, String sellerId) {
        if (getItemList == null || getItemList.isEmpty()) {
            return new ArrayList<>();
        }

        List<Item> items = new ArrayList<>();

        for (GetItemModel getItem : getItemList) {
            try {
                Item item = convertGetItemToItem(getItem, sellerId);
                if (item != null) {
                    items.add(item);
                }
            } catch (Exception e) {
                logger.error("Error converting GetItemModel to Item for ID {}: {}", getItem.getId(), e.getMessage(), e);
            }
        }

        logger.info("{} 件の GetItemModel オブジェクトを Item オブジェクトに変換しました", items.size());
        return items;
    }

    /**
     * Convert a single GetItemModel to an Item object for database storage.
     *
     * @param getItem  The GetItemModel object
     * @param sellerId The seller ID
     * @return An Item object ready for database storage, or null if conversion fails
     */
    public static Item convertGetItemToItem(GetItemModel getItem, String sellerId) {
        if (getItem == null) {
            return null;
        }

        try {
            Item item = new Item();

            // Set basic item information
            item.setId(getItem.getId());
            item.setSellerId(sellerId != null ? sellerId : getItem.getSellerId());
            item.setName(getItem.getName());

            // Convert price from String to Integer
            if (getItem.getPrice() != null && !getItem.getPrice().trim().isEmpty()) {
                try {
                    // Remove any non-numeric characters except digits
                    String cleanPrice = getItem.getPrice().replaceAll("[^0-9]", "");
                    if (!cleanPrice.isEmpty()) {
                        item.setPrice(Integer.parseInt(cleanPrice));
                    }
                } catch (NumberFormatException e) {
                    logger.error("Error parsing price for item {}: {}", getItem.getId(), getItem.getPrice(), e);
                    item.setPrice(0);
                }
            } else {
                item.setPrice(0);
            }

            // Set status based on soldStatus
            item.setStatus(String.valueOf(getItem.getSoldStatus()));

            // Set item type (default to normal item)
            item.setItemType(ItemTypeEnum.NORMAL.getApiType());

            // Set dates - handle proper date conversion
            String currentDate = DatabaseDateTimeUtil.formatJapaneseTimezone(new Date());

            // Convert GetItemModel's date fields to proper database format
            String createdDate = convertGetItemDateToDbFormat(getItem.getCreated());
            String updatedDate = convertGetItemDateToDbFormat(getItem.getUpdated());

            item.setCreated(createdDate != null ? createdDate : currentDate);
            item.setUpdated(updatedDate != null ? updatedDate : currentDate);

            item.setCategoryId(Integer.parseInt(getItem.getCategory()));
            item.setShippingMethodId(getItem.getShippingMethodId());

            // Handle thumbnail
            if (getItem.getThumbnailUrl() != null && !getItem.getThumbnailUrl().trim().isEmpty()) {
                // Extract filename from thumbnail URL or path
                String thumbnailFileName = extractThumbnailFileName(getItem.getThumbnailUrl(), getItem.getId());
                ArrayList<String> thumbnails = new ArrayList<>();
                thumbnails.add(thumbnailFileName);
                item.setThumbnails(thumbnails);
            }

            // Set default values for other fields
            item.setItemConditionId(0); // 商品の状態は取得できない

            return item;

        } catch (Exception e) {
            logger.error("Error converting GetItemModel to Item: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * Extract thumbnail filename from URL or path, or generate one based on item ID.
     *
     * @param thumbnailUrl The thumbnail URL or path
     * @param itemId       The item ID
     * @return The thumbnail filename
     */
    private static String extractThumbnailFileName(String thumbnailUrl, String itemId) {
        if (thumbnailUrl == null || thumbnailUrl.trim().isEmpty()) {
            return "image" + itemId + ".jpg";
        }

        // If it's already a filename (no path separators), return as is
        if (!thumbnailUrl.contains("/") && !thumbnailUrl.contains("\\")) {
            return thumbnailUrl;
        }

        // Extract filename from URL or path
        String fileName = thumbnailUrl;
        int lastSlashIndex = Math.max(fileName.lastIndexOf("/"), fileName.lastIndexOf("\\"));
        if (lastSlashIndex >= 0 && lastSlashIndex < fileName.length() - 1) {
            fileName = fileName.substring(lastSlashIndex + 1);
        }

        // If no valid filename extracted, generate one
        if (fileName.trim().isEmpty() || fileName.equals(thumbnailUrl)) {
            fileName = "image" + itemId + ".jpg";
        }

        return fileName;
    }

    /**
     * Extract item IDs from a list of GetItemModel objects.
     *
     * @param getItemList The list of GetItemModel objects
     * @return A list of item IDs
     */
    public static List<String> extractItemIds(List<GetItemModel> getItemList) {
        if (getItemList == null || getItemList.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> itemIds = new ArrayList<>();
        for (GetItemModel getItem : getItemList) {
            if (getItem != null && getItem.getId() != null && !getItem.getId().trim().isEmpty()) {
                itemIds.add(getItem.getId());
            }
        }

        return itemIds;
    }

    /**
     * Convert GetItemModel date string to database format.
     * Handles various date formats from GetItemModel and converts them to database-compatible format.
     *
     * @param dateStr The date string from GetItemModel (could be ISO format, timestamp, etc.)
     * @return Formatted date string for database storage, or null if conversion fails
     */
    private static String convertGetItemDateToDbFormat(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            logger.debug("Date string is null or empty");
            return null;
        }

        String trimmedDateStr = dateStr.trim();

        try {
            // Try to parse the date using DatabaseDateTimeUtil's automatic format detection
            Date parsedDate = DatabaseDateTimeUtil.parseAnyDateFormat(trimmedDateStr);
            if (parsedDate != null) {
                // Convert to Japanese timezone format for database storage
                String formattedDate = DatabaseDateTimeUtil.formatJapaneseTimezone(parsedDate);
                return formattedDate;
            }

            // If automatic parsing fails, try to handle Unix timestamp
            if (trimmedDateStr.matches("\\d+")) {
                long timestamp = Long.parseLong(trimmedDateStr);
                Date date = new Date(timestamp * 1000); // Convert seconds to milliseconds
                String formattedDate = DatabaseDateTimeUtil.formatJapaneseTimezone(date);
                return formattedDate;
            }

            // If all parsing attempts fail, log the issue
            logger.error("Failed to convert date format for seller search: {}", trimmedDateStr);
            return null;

        } catch (Exception e) {
            logger.error("Error converting GetItemModel date to database format: {} - {}", trimmedDateStr, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Get the default status ID for seller search items.
     * This represents the status of items found during seller search.
     *
     * @return The default status ID
     */
    public static int getDefaultSellerSearchStatus() {
        // Default to sold out status for seller search items
        return ItemStatusEnum.SOLD_OUT.getId();
    }
}
