package com.mrcresearch.util;

import com.mrcresearch.util.database.DatabaseConnectionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * 画像ファイルのクリーンアップ用ユーティリティクラス
 */
public class ImageCleanupUtil {

    private static final Logger logger = LoggerFactory.getLogger(ImageCleanupUtil.class);

    /**
     * 画像保存ディレクトリのパスを取得
     *
     * @return 画像保存ディレクトリのパス
     */
    public static String getImageDirectoryPath() {
        String userHome = System.getProperty("user.home");
        String separator = System.getProperty("file.separator");
        return userHome + separator + ".mrcresearch" + separator + "img";
    }

    /**
     * 指定された商品IDの画像ファイルを削除
     *
     * @param itemId 商品ID
     * @return 削除成功の場合true
     */
    public static boolean deleteImageFile(String itemId) {
        if (itemId == null || itemId.trim().isEmpty()) {
            return false;
        }

        try {
            String imageDirPath = getImageDirectoryPath();
            String fileName = itemId + ".jpg";
            Path filePath = Paths.get(imageDirPath, fileName);

            if (Files.exists(filePath)) {
                Files.delete(filePath);
                logger.info("画像ファイルを削除しました: " + fileName);
                return true;
            } else {
                logger.info("画像ファイルが見つかりません: " + fileName);
                return false; // ファイルが存在しない場合はfalseを返す
            }

        } catch (Exception e) {
            logger.error("Error deleting image file for item " + itemId + ": " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * データベースに存在しない画像ファイルを削除（孤立画像のクリーンアップ）
     *
     * @return 削除された画像ファイル数
     */
    public static int cleanupOrphanedImages() {
        int deletedCount = 0;

        try {
            String imageDirPath = getImageDirectoryPath();
            File imageDir = new File(imageDirPath);

            if (!imageDir.exists() || !imageDir.isDirectory()) {
                logger.info("画像ディレクトリが存在しません: " + imageDirPath);
                return 0;
            }

            // 画像ディレクトリ内のすべての.jpgファイルを取得
            File[] imageFiles = imageDir.listFiles((dir, name) -> name.toLowerCase().endsWith(".jpg"));

            if (imageFiles == null || imageFiles.length == 0) {
                logger.info("ディレクトリに画像ファイルが見つかりません: " + imageDirPath);
                return 0;
            }

            logger.info(imageFiles.length + " 件の画像ファイルを確認します");

            // データベースに存在する商品IDのセットを取得
            List<String> existingItemIds = getExistingItemIds();
            logger.info("データベースに " + existingItemIds.size() + " 件のアイテムが見つかりました");

            // 各画像ファイルをチェック
            for (File imageFile : imageFiles) {
                String fileName = imageFile.getName();
                String itemId = fileName.substring(0, fileName.lastIndexOf('.'));

                // データベースに存在しない場合は削除
                if (!existingItemIds.contains(itemId)) {
                    try {
                        if (imageFile.delete()) {
                            logger.info("孤立した画像を削除しました: " + fileName);
                            deletedCount++;
                        } else {
                            logger.error("Failed to delete orphaned image: " + fileName);
                        }
                    } catch (Exception e) {
                        logger.error("Error deleting orphaned image " + fileName + ": " + e.getMessage());
                    }
                }
            }

        } catch (Exception e) {
            logger.error("Error during orphaned image cleanup: " + e.getMessage(), e);
        }

        return deletedCount;
    }

    /**
     * データベースに存在するすべての商品IDを取得
     *
     * @return 商品IDのリスト
     */
    private static List<String> getExistingItemIds() throws SQLException {
        List<String> itemIds = new ArrayList<>();

        String sql = "SELECT DISTINCT item_id FROM search_items WHERE item_id IS NOT NULL";

        try (Connection conn = DatabaseConnectionManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                String itemId = rs.getString("item_id");
                if (itemId != null && !itemId.trim().isEmpty()) {
                    itemIds.add(itemId);
                }
            }
        }

        return itemIds;
    }

    /**
     * 画像ディレクトリの統計情報を取得
     *
     * @return 統計情報の文字列
     */
    public static String getImageDirectoryStats() {
        StringBuilder stats = new StringBuilder();

        try {
            String imageDirPath = getImageDirectoryPath();
            File imageDir = new File(imageDirPath);

            stats.append("画像ディレクトリ: ").append(imageDirPath).append("\n");

            if (!imageDir.exists()) {
                stats.append("ディレクトリが存在しません\n");
                return stats.toString();
            }

            if (!imageDir.isDirectory()) {
                stats.append("指定されたパスはディレクトリではありません\n");
                return stats.toString();
            }

            // 画像ファイル数をカウント
            File[] imageFiles = imageDir.listFiles((dir, name) -> name.toLowerCase().endsWith(".jpg"));
            int imageFileCount = (imageFiles != null) ? imageFiles.length : 0;

            // 総ファイルサイズを計算
            long totalSize = 0;
            if (imageFiles != null) {
                for (File file : imageFiles) {
                    totalSize += file.length();
                }
            }

            stats.append("画像ファイル数: ").append(imageFileCount).append(" 件\n");
            stats.append("総ファイルサイズ: ").append(formatFileSize(totalSize)).append("\n");

            // データベース内の商品数と比較
            try {
                List<String> existingItemIds = getExistingItemIds();
                stats.append("データベース内商品数: ").append(existingItemIds.size()).append(" 件\n");

                if (imageFileCount > existingItemIds.size()) {
                    stats.append("孤立画像ファイル: ").append(imageFileCount - existingItemIds.size()).append(" 件（推定）\n");
                }
            } catch (SQLException e) {
                stats.append("データベース情報の取得に失敗: ").append(e.getMessage()).append("\n");
            }

        } catch (Exception e) {
            stats.append("統計情報の取得に失敗: ").append(e.getMessage()).append("\n");
        }

        return stats.toString();
    }

    /**
     * ファイルサイズを人間が読みやすい形式でフォーマット
     *
     * @param bytes バイト数
     * @return フォーマットされた文字列
     */
    private static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 画像ファイルが存在するかチェック
     *
     * @param itemId 商品ID
     * @return ファイルが存在する場合true
     */
    public static boolean imageFileExists(String itemId) {
        if (itemId == null || itemId.trim().isEmpty()) {
            return false;
        }

        try {
            String imageDirPath = getImageDirectoryPath();
            String fileName = itemId + ".jpg";
            Path filePath = Paths.get(imageDirPath, fileName);
            return Files.exists(filePath);
        } catch (Exception e) {
            logger.error("Error checking image file existence for item " + itemId + ": " + e.getMessage());
            return false;
        }
    }
}