package com.mrcresearch.util;

import javax.swing.*;
import java.awt.*;
import java.awt.datatransfer.Clipboard;
import java.awt.datatransfer.DataFlavor;
import java.awt.datatransfer.StringSelection;
import java.awt.datatransfer.UnsupportedFlavorException;
import java.io.IOException;

/**
 * Utility class for clipboard operations (copy/paste functionality)
 */
public class ClipboardUtil {

    /**
     * Copy text to system clipboard
     *
     * @param text The text to copy
     */
    public static void copyToClipboard(String text) {
        if (text == null) {
            text = "";
        }

        try {
            Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
            StringSelection selection = new StringSelection(text);
            clipboard.setContents(selection, null);
        } catch (Exception e) {
            System.err.println("Failed to copy to clipboard: " + e.getMessage());
        }
    }

    /**
     * Get text from system clipboard
     *
     * @return The text from clipboard, or empty string if unavailable
     */
    public static String getFromClipboard() {
        try {
            Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
            if (clipboard != null && clipboard.isDataFlavorAvailable(DataFlavor.stringFlavor)) {
                Object data = clipboard.getData(DataFlavor.stringFlavor);
                if (data instanceof String) {
                    return (String) data;
                }
            }
        } catch (UnsupportedFlavorException e) {
            System.err.println("Clipboard data flavor not supported: " + e.getMessage());
        } catch (IOException e) {
            System.err.println("IO error accessing clipboard: " + e.getMessage());
        } catch (IllegalStateException e) {
            System.err.println("Clipboard unavailable: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("Unexpected error accessing clipboard: " + e.getMessage());
        }
        return "";
    }

    /**
     * Safely check if clipboard has text content
     *
     * @return true if clipboard has text content, false otherwise
     */
    public static boolean hasTextContent() {
        try {
            Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
            return clipboard != null && clipboard.isDataFlavorAvailable(DataFlavor.stringFlavor);
        } catch (Exception e) {
            System.err.println("Error checking clipboard content: " + e.getMessage());
            return false;
        }
    }

    /**
     * Copy selected table cells to clipboard as tab-separated values
     *
     * @param table The JTable to copy from
     */
    public static void copyTableSelection(JTable table) {
        if (table == null) {
            return;
        }

        int[] selectedRows = table.getSelectedRows();
        int[] selectedColumns = table.getSelectedColumns();

        if (selectedRows.length == 0 || selectedColumns.length == 0) {
            return;
        }

        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < selectedRows.length; i++) {
            for (int j = 0; j < selectedColumns.length; j++) {
                Object value = table.getValueAt(selectedRows[i], selectedColumns[j]);
                String cellValue = (value != null) ? value.toString() : "";

                // Remove any tabs and newlines from cell content to avoid formatting issues
                cellValue = cellValue.replace("\t", " ").replace("\n", " ").replace("\r", " ");

                sb.append(cellValue);

                if (j < selectedColumns.length - 1) {
                    sb.append("\t"); // Tab separator between columns
                }
            }

            if (i < selectedRows.length - 1) {
                sb.append("\n"); // Newline separator between rows
            }
        }

        copyToClipboard(sb.toString());
    }

    /**
     * Copy all visible table data to clipboard as tab-separated values
     *
     * @param table The JTable to copy from
     */
    public static void copyAllTableData(JTable table) {
        if (table == null) {
            return;
        }

        StringBuilder sb = new StringBuilder();

        // Add header row
        for (int j = 0; j < table.getColumnCount(); j++) {
            String columnName = table.getColumnName(j);
            sb.append(columnName);

            if (j < table.getColumnCount() - 1) {
                sb.append("\t");
            }
        }
        sb.append("\n");

        // Add data rows
        for (int i = 0; i < table.getRowCount(); i++) {
            for (int j = 0; j < table.getColumnCount(); j++) {
                Object value = table.getValueAt(i, j);
                String cellValue = (value != null) ? value.toString() : "";

                // Remove any tabs and newlines from cell content
                cellValue = cellValue.replace("\t", " ").replace("\n", " ").replace("\r", " ");

                sb.append(cellValue);

                if (j < table.getColumnCount() - 1) {
                    sb.append("\t");
                }
            }

            if (i < table.getRowCount() - 1) {
                sb.append("\n");
            }
        }

        copyToClipboard(sb.toString());
    }
}
