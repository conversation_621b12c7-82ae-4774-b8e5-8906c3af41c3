package com.mrcresearch.util;

import java.util.regex.Pattern;

/**
 * Simple validation utilities for UI inputs.
 */
public final class ValidationUtil {
    private ValidationUtil() {
    }

    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}$", Pattern.CASE_INSENSITIVE);

    public static boolean isNonEmpty(String s) {
        return s != null && !s.trim().isEmpty();
    }

    public static boolean isValidEmail(String email) {
        if (!isNonEmpty(email)) return false;
        return EMAIL_PATTERN.matcher(email).matches();
    }
}
