package com.mrcresearch.security;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * AES-GCM utility for encrypting/decrypting short configuration secrets.
 * Format: Base64( IV(12) || CIPHERTEXT(+TAG) )
 */
public class CryptoUtil {
    private static final String TRANSFORMATION = "AES/GCM/NoPadding";
    private static final int GCM_TAG_LENGTH_BIT = 128;
    private static final int IV_LENGTH = 12; // 96-bit nonce recommended for GCM

    public static String encrypt(SecretKey key, String plainText) {
        if (plainText == null) return "";
        try {
            byte[] iv = new byte[IV_LENGTH];
            new SecureRandom().nextBytes(iv);

            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, key, new GCMParameterSpec(GCM_TAG_LENGTH_BIT, iv));
            byte[] cipherText = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));

            ByteBuffer bb = ByteBuffer.allocate(iv.length + cipherText.length);
            bb.put(iv);
            bb.put(cipherText);
            return Base64.getEncoder().encodeToString(bb.array());
        } catch (Exception e) {
            throw new RuntimeException("Encryption failed", e);
        }
    }

    public static String decrypt(SecretKey key, String base64IvAndCipher) {
        if (base64IvAndCipher == null || base64IvAndCipher.isEmpty()) return "";
        try {
            byte[] ivAndCipher = Base64.getDecoder().decode(base64IvAndCipher);
            if (ivAndCipher.length <= IV_LENGTH) {
                throw new IllegalArgumentException("Invalid ciphertext");
            }
            byte[] iv = new byte[IV_LENGTH];
            byte[] cipherText = new byte[ivAndCipher.length - IV_LENGTH];
            System.arraycopy(ivAndCipher, 0, iv, 0, IV_LENGTH);
            System.arraycopy(ivAndCipher, IV_LENGTH, cipherText, 0, cipherText.length);

            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, key, new GCMParameterSpec(GCM_TAG_LENGTH_BIT, iv));
            byte[] plain = cipher.doFinal(cipherText);
            return new String(plain, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("Decryption failed", e);
        }
    }
}
