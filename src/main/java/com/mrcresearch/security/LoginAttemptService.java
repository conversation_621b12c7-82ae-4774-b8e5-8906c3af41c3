package com.mrcresearch.security;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * ログイン試行を追跡し、ブルートフォース攻撃を防ぐためのサービス
 * 失敗したログイン試行を記録し、一定回数以上の失敗でアカウントを一時的にロックする
 */
public class LoginAttemptService {

    // 設定可能なパラメータ
    private static final int MAX_ATTEMPTS = 5; // 最大試行回数
    private static final int LOCKOUT_DURATION_MINUTES = 30; // ロックアウト時間（分）
    private static final int RESET_TIME_MINUTES = 60; // 試行回数リセット時間（分）

    // シングルトンインスタンス
    private static final LoginAttemptService INSTANCE = new LoginAttemptService();

    // ユーザーごとの試行情報を保存
    private final ConcurrentHashMap<String, LoginAttemptInfo> attemptCache = new ConcurrentHashMap<>();

    private LoginAttemptService() {
        // プライベートコンストラクタ（シングルトンパターン）
    }

    /**
     * シングルトンインスタンスを取得
     *
     * @return LoginAttemptServiceのインスタンス
     */
    public static LoginAttemptService getInstance() {
        return INSTANCE;
    }

    /**
     * ログイン成功時の処理
     *
     * @param username ユーザー名
     */
    public void loginSucceeded(String username) {
        if (username != null) {
            attemptCache.remove(username);
        }
    }

    /**
     * ログイン失敗時の処理
     *
     * @param username ユーザー名
     */
    public void loginFailed(String username) {
        if (username == null) return;

        LocalDateTime now = LocalDateTime.now();
        LoginAttemptInfo info = attemptCache.computeIfAbsent(username,
                k -> new LoginAttemptInfo());

        // 古い試行情報をリセット
        if (info.getLastAttemptTime() != null &&
                ChronoUnit.MINUTES.between(info.getLastAttemptTime(), now) > RESET_TIME_MINUTES) {
            info.reset();
        }

        info.incrementAttempts();
        info.setLastAttemptTime(now);

        // 最大試行回数に達した場合、ロックアウト時間を設定
        if (info.getAttemptCount() >= MAX_ATTEMPTS) {
            info.setLockoutTime(now.plusMinutes(LOCKOUT_DURATION_MINUTES));
        }
    }

    /**
     * アカウントがロックされているかチェック
     *
     * @param username ユーザー名
     * @return ロックされている場合true
     */
    public boolean isBlocked(String username) {
        if (username == null) return false;

        LoginAttemptInfo info = attemptCache.get(username);
        if (info == null) return false;

        // ロックアウト時間が設定されている場合
        if (info.getLockoutTime() != null) {
            if (LocalDateTime.now().isBefore(info.getLockoutTime())) {
                return true; // まだロックアウト中
            } else {
                // ロックアウト時間が過ぎた場合、情報をリセット
                info.reset();
                return false;
            }
        }

        return false;
    }

    /**
     * 残り試行回数を取得
     *
     * @param username ユーザー名
     * @return 残り試行回数
     */
    public int getRemainingAttempts(String username) {
        if (username == null || isBlocked(username)) return 0;

        LoginAttemptInfo info = attemptCache.get(username);
        if (info == null) return MAX_ATTEMPTS;

        return Math.max(0, MAX_ATTEMPTS - info.getAttemptCount());
    }

    /**
     * ロックアウト解除までの残り時間（分）を取得
     *
     * @param username ユーザー名
     * @return 残り時間（分）、ロックされていない場合は0
     */
    public long getRemainingLockoutMinutes(String username) {
        if (username == null) return 0;

        LoginAttemptInfo info = attemptCache.get(username);
        if (info == null || info.getLockoutTime() == null) return 0;

        LocalDateTime now = LocalDateTime.now();
        if (now.isBefore(info.getLockoutTime())) {
            return ChronoUnit.MINUTES.between(now, info.getLockoutTime());
        }

        return 0;
    }

    /**
     * 指定ユーザーの試行情報をクリア（管理者用）
     *
     * @param username ユーザー名
     */
    public void clearAttempts(String username) {
        if (username != null) {
            attemptCache.remove(username);
        }
    }

    /**
     * すべての試行情報をクリア（管理者用）
     */
    public void clearAllAttempts() {
        attemptCache.clear();
    }

    /**
     * 統計情報を取得
     *
     * @return ブロックされているユーザー数
     */
    public int getBlockedUsersCount() {
        return (int) attemptCache.entrySet().stream()
                .filter(entry -> isBlocked(entry.getKey()))
                .count();
    }

    /**
     * ログイン試行情報を保持する内部クラス
     */
    private static class LoginAttemptInfo {
        private final AtomicInteger attemptCount = new AtomicInteger(0);
        private volatile LocalDateTime lastAttemptTime;
        private volatile LocalDateTime lockoutTime;

        public int getAttemptCount() {
            return attemptCount.get();
        }

        public void incrementAttempts() {
            attemptCount.incrementAndGet();
        }

        public LocalDateTime getLastAttemptTime() {
            return lastAttemptTime;
        }

        public void setLastAttemptTime(LocalDateTime lastAttemptTime) {
            this.lastAttemptTime = lastAttemptTime;
        }

        public LocalDateTime getLockoutTime() {
            return lockoutTime;
        }

        public void setLockoutTime(LocalDateTime lockoutTime) {
            this.lockoutTime = lockoutTime;
        }

        public void reset() {
            attemptCount.set(0);
            lastAttemptTime = null;
            lockoutTime = null;
        }
    }
}