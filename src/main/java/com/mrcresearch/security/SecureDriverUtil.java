package com.mrcresearch.security;

import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.firefox.FirefoxOptions;
import org.openqa.selenium.firefox.FirefoxProfile;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * セキュアなWebDriverオプションを提供するユーティリティクラス
 * ブラウザ自動化における潜在的なセキュリティリスクを軽減するための設定を提供
 */
public class SecureDriverUtil {

    // セキュリティ強化のためのChrome引数
    private static final String[] SECURE_CHROME_ARGS = {
            "--incognito",                    // プライベートブラウジングモード
            "--no-sandbox",                   // サンドボックス無効（コンテナ環境用）
            "--disable-dev-shm-usage",        // /dev/shm使用無効（メモリ不足対策）
            "--disable-extensions",           // 拡張機能無効
            "--disable-plugins",              // プラグイン無効
            "--disable-gpu",                  // GPU使用無効
            "--disable-software-rasterizer",  // ソフトウェアラスタライザー無効
            "--disable-background-timer-throttling", // バックグラウンドタイマー調整無効
            "--disable-features=TranslateUI", // 翻訳UI無効
            "--disable-ipc-flooding-protection", // IPC洪水保護無効
            "--disable-default-apps",         // デフォルトアプリ無効
            "--disable-sync",                 // 同期機能無効
            "--disable-features=VizDisplayCompositor", // Vizディスプレイコンポジター無効
            "--disable-blink-features=AutomationControlled", // 自動化制御検出無効
            "--password-store=basic",         // パスワードストア基本モード
            "--use-mock-keychain",            // モックキーチェーン使用
            "--disable-component-update",     // コンポーネント更新無効
            "--disable-client-side-phishing-detection", // フィッシング検出無効
            "--disable-hang-monitor",         // ハングモニタ無効
            "--disable-popup-blocking",       // ポップアップブロック無効
            "--disable-prompt-on-repost",     // 再投稿プロンプト無効
            "--metrics-recording-only",       // メトリクス記録のみ
            "--no-default-browser-check",     // デフォルトブラウザチェック無効
            "--no-pings",                     // ping無効
            "--safebrowsing-disable-auto-update", // セーフブラウジング自動更新無効
    };

    // セキュリティ強化のためのFirefox設定
    private static final Map<String, Object> SECURE_FIREFOX_PREFS = new HashMap<>();

    static {
        // Firefox セキュリティ設定
        SECURE_FIREFOX_PREFS.put("privacy.trackingprotection.enabled", true);
        SECURE_FIREFOX_PREFS.put("privacy.donottrackheader.enabled", true);
        SECURE_FIREFOX_PREFS.put("privacy.clearOnShutdown.cookies", true);
        SECURE_FIREFOX_PREFS.put("privacy.clearOnShutdown.cache", true);
        SECURE_FIREFOX_PREFS.put("privacy.clearOnShutdown.history", true);
        SECURE_FIREFOX_PREFS.put("security.tls.version.min", 3); // TLS 1.2以上
        SECURE_FIREFOX_PREFS.put("security.ssl.require_safe_negotiation", true);
        SECURE_FIREFOX_PREFS.put("security.ssl3.rsa_des_ede3_sha", false);
        SECURE_FIREFOX_PREFS.put("security.mixed_content.block_active_content", true);
        SECURE_FIREFOX_PREFS.put("security.mixed_content.block_display_content", true);
        SECURE_FIREFOX_PREFS.put("dom.security.https_only_mode", true);
        SECURE_FIREFOX_PREFS.put("network.stricttransportsecurity.preloadlist", true);
        SECURE_FIREFOX_PREFS.put("network.http.referer.XOriginPolicy", 2);
        SECURE_FIREFOX_PREFS.put("network.cookie.cookieBehavior", 1); // サードパーティCookie拒否
        SECURE_FIREFOX_PREFS.put("dom.webnotifications.enabled", false);
        SECURE_FIREFOX_PREFS.put("geo.enabled", false); // 位置情報無効
        SECURE_FIREFOX_PREFS.put("media.peerconnection.enabled", false); // WebRTC無効
    }

    /**
     * セキュアなChromeOptionsを取得
     *
     * @return セキュリティが強化されたChromeOptions
     */
    public static ChromeOptions getSecureChromeOptions() {
        ChromeOptions options = new ChromeOptions();

        // セキュリティ引数を追加
        options.addArguments(SECURE_CHROME_ARGS);

        // ヘッドレスモード（必要に応じて）
        // options.addArguments("--headless");

        // 実験的オプション
        Map<String, Object> prefs = new HashMap<>();
        prefs.put("profile.default_content_setting_values.notifications", 2); // 通知無効
        prefs.put("profile.default_content_settings.popups", 0); // ポップアップ無効
        prefs.put("profile.default_content_setting_values.geolocation", 2); // 位置情報無効

        options.setExperimentalOption("prefs", prefs);
        options.setExperimentalOption("useAutomationExtension", false);
        options.setExperimentalOption("excludeSwitches", Arrays.asList("enable-automation"));

        return options;
    }

    /**
     * パフォーマンス重視のChromeOptionsを取得（画像やJavaScript有効）
     *
     * @return パフォーマンスを考慮したChromeOptions
     */
    public static ChromeOptions getPerformanceChromeOptions() {
        ChromeOptions options = new ChromeOptions();

        // 基本的なセキュリティオプション（JavaScript有効）
        String[] performanceArgs = {
                "--incognito",
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-extensions",
                "--disable-plugins",
                "--disable-gpu",
                "--disable-software-rasterizer",
                "--disable-blink-features=AutomationControlled",
                "--no-first-run",
                "--no-service-autorun",
                "--disable-component-update",
                "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        };

        options.addArguments(performanceArgs);

        Map<String, Object> prefs = new HashMap<>();
        prefs.put("profile.default_content_setting_values.notifications", 2);
        prefs.put("profile.default_content_settings.popups", 0);

        options.setExperimentalOption("prefs", prefs);
        options.setExperimentalOption("useAutomationExtension", false);
        options.setExperimentalOption("excludeSwitches", Arrays.asList("enable-automation"));

        return options;
    }

    /**
     * セキュアなFirefoxOptionsを取得
     *
     * @return セキュリティが強化されたFirefoxOptions
     */
    public static FirefoxOptions getSecureFirefoxOptions() {
        FirefoxOptions options = new FirefoxOptions();
        FirefoxProfile profile = new FirefoxProfile();

        // セキュリティ設定を適用
        for (Map.Entry<String, Object> pref : SECURE_FIREFOX_PREFS.entrySet()) {
            if (pref.getValue() instanceof Boolean) {
                profile.setPreference(pref.getKey(), (Boolean) pref.getValue());
            } else if (pref.getValue() instanceof Integer) {
                profile.setPreference(pref.getKey(), (Integer) pref.getValue());
            } else if (pref.getValue() instanceof String) {
                profile.setPreference(pref.getKey(), (String) pref.getValue());
            }
        }

        options.setProfile(profile);

        // ヘッドレスモード（必要に応じて）
        // options.addArguments("--headless");

        return options;
    }

    /**
     * プロキシ設定付きのChromeOptionsを取得
     *
     * @param proxyHost プロキシホスト
     * @param proxyPort プロキシポート
     * @return プロキシ設定されたChromeOptions
     */
    public static ChromeOptions getProxyChromeOptions(String proxyHost, int proxyPort) {
        ChromeOptions options = getSecureChromeOptions();
        options.addArguments("--proxy-server=http://" + proxyHost + ":" + proxyPort);
        return options;
    }

    /**
     * カスタムUser-Agent設定付きのChromeOptionsを取得
     *
     * @param userAgent カスタムUser-Agent文字列
     * @return カスタムUA設定されたChromeOptions
     */
    public static ChromeOptions getCustomUserAgentChromeOptions(String userAgent) {
        ChromeOptions options = getSecureChromeOptions();
        options.addArguments("--user-agent=" + userAgent);
        return options;
    }

    /**
     * ウィンドウサイズを指定したChromeOptionsを取得
     *
     * @param width  ウィンドウ幅
     * @param height ウィンドウ高さ
     * @return サイズ指定されたChromeOptions
     */
    public static ChromeOptions getWindowSizeChromeOptions(int width, int height) {
        ChromeOptions options = getSecureChromeOptions();
        options.addArguments("--window-size=" + width + "," + height);
        return options;
    }

    /**
     * 完全ヘッドレスモードのChromeOptionsを取得
     *
     * @return ヘッドレス設定されたChromeOptions
     */
    public static ChromeOptions getHeadlessChromeOptions() {
        ChromeOptions options = getSecureChromeOptions();
        options.addArguments("--headless");
        options.addArguments("--window-size=1920,1080");
        return options;
    }
}