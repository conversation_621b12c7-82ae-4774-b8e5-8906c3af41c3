package com.mrcresearch.security;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * 入力検証とサニタイズのためのユーティリティクラス
 * SQLインジェクション、XSS、ディレクトリトラバーサル攻撃を防ぐための包括的な入力検証を提供
 */
public class InputValidator {

    // 正規表現パターン
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
    );

    private static final Pattern URL_PATTERN = Pattern.compile(
            "^(https?|ftp)://[^\\s/$.?#].[^\\s]*$", Pattern.CASE_INSENSITIVE
    );

    private static final Pattern ALPHANUMERIC_PATTERN = Pattern.compile("^[a-zA-Z0-9]+$");

    private static final Pattern FILENAME_PATTERN = Pattern.compile("^[a-zA-Z0-9._-]+$");

    // 危険な文字のパターン
    private static final Pattern SQL_INJECTION_PATTERN = Pattern.compile(
            ".*('|(\\-\\-)|(;)|(\\|)|(\\*)|(%)|(<)|(>)|(\\{)|(\\})|(\\[)|(\\])).*",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern XSS_PATTERN = Pattern.compile(
            ".*(<script|javascript:|vbscript:|onload|onerror|onclick).*",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern DIRECTORY_TRAVERSAL_PATTERN = Pattern.compile(
            ".*(\\.\\.[\\\\/]|\\.\\.[\\\\]).*"
    );

    /**
     * メールアドレスの検証
     *
     * @param email 検証するメールアドレス
     * @return 有効な場合true
     */
    public static boolean isValidEmail(String email) {
        return email != null && EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * URLの検証
     *
     * @param url 検証するURL
     * @return 有効な場合true
     */
    public static boolean isValidUrl(String url) {
        return url != null && URL_PATTERN.matcher(url).matches();
    }

    /**
     * 英数字のみかチェック
     *
     * @param input 検証する文字列
     * @return 英数字のみの場合true
     */
    public static boolean isAlphanumeric(String input) {
        return input != null && ALPHANUMERIC_PATTERN.matcher(input).matches();
    }

    /**
     * ファイル名の検証（安全な文字のみ）
     *
     * @param filename 検証するファイル名
     * @return 安全な場合true
     */
    public static boolean isValidFilename(String filename) {
        return filename != null &&
                !filename.trim().isEmpty() &&
                FILENAME_PATTERN.matcher(filename).matches() &&
                filename.length() <= 255;
    }

    /**
     * SQLインジェクションの可能性をチェック
     *
     * @param input 検証する文字列
     * @return 安全な場合true
     */
    public static boolean isSafeSqlInput(String input) {
        return input == null || !SQL_INJECTION_PATTERN.matcher(input).matches();
    }

    /**
     * XSS攻撃の可能性をチェック
     *
     * @param input 検証する文字列
     * @return 安全な場合true
     */
    public static boolean isSafeXssInput(String input) {
        return input == null || !XSS_PATTERN.matcher(input).matches();
    }

    /**
     * ディレクトリトラバーサル攻撃の可能性をチェック
     *
     * @param path 検証するパス
     * @return 安全な場合true
     */
    public static boolean isSafePath(String path) {
        return path == null || !DIRECTORY_TRAVERSAL_PATTERN.matcher(path).matches();
    }

    /**
     * 文字列のサニタイズ（危険な文字の削除/エスケープ）
     *
     * @param input サニタイズする文字列
     * @return サニタイズされた文字列
     */
    public static String sanitizeString(String input) {
        if (input == null) return null;

        // 危険な文字を削除
        String sanitized = input
                .replaceAll("[<>\"'&]", "")  // HTML特殊文字
                .replaceAll("[;\\-'\"\\\\]", "")  // SQL関連文字
                .replaceAll("\\.\\.[\\\\/]", "")  // ディレクトリトラバーサル
                .trim();

        // 長さ制限
        if (sanitized.length() > 255) {
            sanitized = sanitized.substring(0, 255);
        }

        return sanitized;
    }

    /**
     * HTMLエスケープ
     *
     * @param input エスケープする文字列
     * @return HTMLエスケープされた文字列
     */
    public static String escapeHtml(String input) {
        if (input == null) return null;

        return input
                .replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&#x27;");
    }

    /**
     * 包括的な入力検証
     *
     * @param input      検証する文字列
     * @param maxLength  最大長
     * @param allowEmpty 空文字を許可するか
     * @return 安全な場合true
     */
    public static boolean isValidInput(String input, int maxLength, boolean allowEmpty) {
        if (input == null) return allowEmpty;
        if (input.trim().isEmpty()) return allowEmpty;
        if (input.length() > maxLength) return false;

        return isSafeSqlInput(input) &&
                isSafeXssInput(input) &&
                isSafePath(input);
    }

    /**
     * 数値文字列の検証
     *
     * @param input 検証する文字列
     * @return 数値として有効な場合true
     */
    public static boolean isValidNumber(String input) {
        if (StringUtils.isEmpty(input)) return false;
        try {
            Double.parseDouble(input);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 整数文字列の検証
     *
     * @param input 検証する文字列
     * @param min   最小値
     * @param max   最大値
     * @return 有効な範囲内の整数の場合true
     */
    public static boolean isValidInteger(String input, int min, int max) {
        if (StringUtils.isEmpty(input)) return false;
        try {
            int value = Integer.parseInt(input);
            return value >= min && value <= max;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}