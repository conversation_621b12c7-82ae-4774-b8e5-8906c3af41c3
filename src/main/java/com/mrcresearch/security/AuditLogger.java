package com.mrcresearch.security;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * セキュリティ監査ログを記録するためのユーティリティクラス
 * 認証、認可、アクセス制御などのセキュリティイベントを構造化ログとして記録
 */
public class AuditLogger {

    private static final Logger SYSTEM_LOGGER = Logger.getLogger(AuditLogger.class.getName());
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    // ログファイルのパス
    private static final String LOG_DIR = "logs";
    private static final String AUDIT_LOG_FILE = "security-audit.log";

    // シングルトンインスタンス
    private static final AuditLogger INSTANCE = new AuditLogger();

    private AuditLogger() {
        // プライベートコンストラクタ
        initializeLogDirectory();
    }

    /**
     * シングルトンインスタンスを取得
     *
     * @return AuditLoggerのインスタンス
     */
    public static AuditLogger getInstance() {
        return INSTANCE;
    }

    /**
     * ログディレクトリを初期化
     */
    private void initializeLogDirectory() {
        try {
            Path logDir = Paths.get(LOG_DIR);
            if (!Files.exists(logDir)) {
                Files.createDirectories(logDir);
            }
        } catch (IOException e) {
            SYSTEM_LOGGER.log(Level.WARNING, "監査ログディレクトリの作成に失敗", e);
        }
    }

    /**
     * ログイン成功を記録
     *
     * @param username  ユーザー名
     * @param ipAddress IPアドレス
     * @param userAgent ユーザーエージェント
     */
    public void logLoginSuccess(String username, String ipAddress, String userAgent) {
        Map<String, Object> details = new HashMap<>();
        details.put("username", username);
        details.put("ipAddress", ipAddress);
        details.put("userAgent", userAgent);

        logAuditEvent(AuditEventType.LOGIN_SUCCESS, "ログイン成功", details);
    }

    /**
     * ログイン失敗を記録
     *
     * @param username  ユーザー名
     * @param ipAddress IPアドレス
     * @param reason    失敗理由
     */
    public void logLoginFailure(String username, String ipAddress, String reason) {
        Map<String, Object> details = new HashMap<>();
        details.put("username", username);
        details.put("ipAddress", ipAddress);
        details.put("reason", reason);

        logAuditEvent(AuditEventType.LOGIN_FAILURE, "ログイン失敗", details);
    }

    /**
     * アカウントロックアウトを記録
     *
     * @param username        ユーザー名
     * @param ipAddress       IPアドレス
     * @param lockoutDuration ロックアウト時間（分）
     */
    public void logAccountLockout(String username, String ipAddress, long lockoutDuration) {
        Map<String, Object> details = new HashMap<>();
        details.put("username", username);
        details.put("ipAddress", ipAddress);
        details.put("lockoutDurationMinutes", lockoutDuration);

        logAuditEvent(AuditEventType.ACCOUNT_LOCKED, "アカウントロックアウト", details);
    }

    /**
     * 不正なアクセス試行を記録
     *
     * @param username  ユーザー名
     * @param ipAddress IPアドレス
     * @param resource  アクセス対象リソース
     * @param reason    拒否理由
     */
    public void logUnauthorizedAccess(String username, String ipAddress, String resource, String reason) {
        Map<String, Object> details = new HashMap<>();
        details.put("username", username);
        details.put("ipAddress", ipAddress);
        details.put("resource", resource);
        details.put("reason", reason);

        logAuditEvent(AuditEventType.UNAUTHORIZED_ACCESS, "不正アクセス試行", details);
    }

    /**
     * 権限変更を記録
     *
     * @param adminUser  実行者
     * @param targetUser 対象ユーザー
     * @param oldRole    変更前の役割
     * @param newRole    変更後の役割
     */
    public void logPermissionChange(String adminUser, String targetUser, String oldRole, String newRole) {
        Map<String, Object> details = new HashMap<>();
        details.put("adminUser", adminUser);
        details.put("targetUser", targetUser);
        details.put("oldRole", oldRole);
        details.put("newRole", newRole);

        logAuditEvent(AuditEventType.PERMISSION_CHANGE, "権限変更", details);
    }

    /**
     * データアクセスを記録
     *
     * @param username    ユーザー名
     * @param operation   操作（CREATE/READ/UPDATE/DELETE）
     * @param resource    リソース名
     * @param recordCount 処理レコード数
     */
    public void logDataAccess(String username, String operation, String resource, int recordCount) {
        Map<String, Object> details = new HashMap<>();
        details.put("username", username);
        details.put("operation", operation);
        details.put("resource", resource);
        details.put("recordCount", recordCount);

        logAuditEvent(AuditEventType.DATA_ACCESS, "データアクセス", details);
    }

    /**
     * システムエラーを記録
     *
     * @param errorType    エラータイプ
     * @param errorMessage エラーメッセージ
     * @param username     関連ユーザー名（可能な場合）
     */
    public void logSystemError(String errorType, String errorMessage, String username) {
        Map<String, Object> details = new HashMap<>();
        details.put("errorType", errorType);
        details.put("errorMessage", errorMessage);
        if (username != null) {
            details.put("username", username);
        }

        logAuditEvent(AuditEventType.SYSTEM_ERROR, "システムエラー", details);
    }

    /**
     * セキュリティ設定変更を記録
     *
     * @param adminUser 実行者
     * @param setting   設定項目
     * @param oldValue  変更前の値
     * @param newValue  変更後の値
     */
    public void logSecuritySettingChange(String adminUser, String setting, String oldValue, String newValue) {
        Map<String, Object> details = new HashMap<>();
        details.put("adminUser", adminUser);
        details.put("setting", setting);
        details.put("oldValue", oldValue);
        details.put("newValue", newValue);

        logAuditEvent(AuditEventType.SECURITY_SETTING_CHANGE, "セキュリティ設定変更", details);
    }

    /**
     * 監査イベントを記録する内部メソッド
     *
     * @param eventType   イベントタイプ
     * @param description 説明
     * @param details     詳細情報
     */
    private void logAuditEvent(AuditEventType eventType, String description, Map<String, Object> details) {
        AuditLogEntry logEntry = new AuditLogEntry(
                LocalDateTime.now(),
                eventType,
                description,
                details
        );

        // 非同期でログを書き込み（パフォーマンス向上）
        CompletableFuture.runAsync(() -> writeLogEntry(logEntry))
                .exceptionally(throwable -> {
                    SYSTEM_LOGGER.log(Level.WARNING, "監査ログの書き込みに失敗", throwable);
                    return null;
                });
    }

    /**
     * ログエントリをファイルに書き込み
     *
     * @param logEntry ログエントリ
     */
    private void writeLogEntry(AuditLogEntry logEntry) {
        try {
            String jsonLog = OBJECT_MAPPER.writeValueAsString(logEntry) + System.lineSeparator();
            Path logFile = Paths.get(LOG_DIR, AUDIT_LOG_FILE);

            Files.write(logFile, jsonLog.getBytes(),
                    StandardOpenOption.CREATE, StandardOpenOption.APPEND);

        } catch (JsonProcessingException e) {
            SYSTEM_LOGGER.log(Level.WARNING, "監査ログのJSON変換に失敗", e);
        } catch (IOException e) {
            SYSTEM_LOGGER.log(Level.WARNING, "監査ログファイルの書き込みに失敗", e);
        }
    }

    /**
     * 監査イベントタイプの列挙型
     */
    public enum AuditEventType {
        LOGIN_SUCCESS,
        LOGIN_FAILURE,
        ACCOUNT_LOCKED,
        UNAUTHORIZED_ACCESS,
        PERMISSION_CHANGE,
        DATA_ACCESS,
        SYSTEM_ERROR,
        SECURITY_SETTING_CHANGE
    }

    /**
     * 監査ログエントリのデータクラス
     */
    public static class AuditLogEntry {
        private final String timestamp;
        private final AuditEventType eventType;
        private final String description;
        private final Map<String, Object> details;

        public AuditLogEntry(LocalDateTime timestamp, AuditEventType eventType,
                             String description, Map<String, Object> details) {
            this.timestamp = timestamp.format(DATE_FORMATTER);
            this.eventType = eventType;
            this.description = description;
            this.details = details != null ? new HashMap<>(details) : new HashMap<>();
        }

        public String getTimestamp() {
            return timestamp;
        }

        public AuditEventType getEventType() {
            return eventType;
        }

        public String getDescription() {
            return description;
        }

        public Map<String, Object> getDetails() {
            return details;
        }
    }
}