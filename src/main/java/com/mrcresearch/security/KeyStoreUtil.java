package com.mrcresearch.security;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.security.KeyStore;
import java.util.Objects;

/**
 * PKCS12 KeyStore utility to persist an AES SecretKey.
 * Password must be provided via environment variable SCRAPINGMERCARI_KS_PASS.
 */
public class KeyStoreUtil {
    private static final String KS_TYPE = "PKCS12"; // portable across platforms
    private static final String KS_FILE_NAME = "app-keystore.p12";
    private static final String KEY_ALIAS = "app-aes-key";

    public static KeyStore loadOrCreateKeyStore(Path dir) {
        try {
            String password = Objects.requireNonNull(System.getenv("SCRAPINGMERCARI_KS_PASS"),
                    "環境変数 SCRAPINGMERCARI_KS_PASS が未設定です");
            if (!Files.exists(dir)) {
                Files.createDirectories(dir);
            }
            Path ks = dir.resolve(KS_FILE_NAME);

            KeyStore keyStore = KeyStore.getInstance(KS_TYPE);
            if (Files.exists(ks)) {
                try (FileInputStream fis = new FileInputStream(ks.toFile())) {
                    keyStore.load(fis, password.toCharArray());
                }
            } else {
                keyStore.load(null, null);
                try (FileOutputStream fos = new FileOutputStream(ks.toFile())) {
                    keyStore.store(fos, password.toCharArray());
                }
            }
            return keyStore;
        } catch (Exception e) {
            throw new RuntimeException("KeyStore load/create failed", e);
        }
    }

    public static SecretKey loadOrCreateSecretKey(KeyStore ks, Path ksPathDir) {
        try {
            String password = Objects.requireNonNull(System.getenv("SCRAPINGMERCARI_KS_PASS"),
                    "環境変数 SCRAPINGMERCARI_KS_PASS が未設定です");
            KeyStore.ProtectionParameter prot = new KeyStore.PasswordProtection(password.toCharArray());

            KeyStore.Entry entry = ks.getEntry(KEY_ALIAS, prot);
            if (entry instanceof KeyStore.SecretKeyEntry ske) {
                return ske.getSecretKey();
            }
            // Create new AES-256 key
            KeyGenerator kg = KeyGenerator.getInstance("AES");
            kg.init(256);
            SecretKey key = kg.generateKey();

            ks.setEntry(KEY_ALIAS, new KeyStore.SecretKeyEntry(key), prot);
            try (FileOutputStream fos = new FileOutputStream(ksPathDir.resolve(KS_FILE_NAME).toFile())) {
                ks.store(fos, password.toCharArray());
            }
            return key;
        } catch (Exception e) {
            throw new RuntimeException("SecretKey load/create failed", e);
        }
    }
}
