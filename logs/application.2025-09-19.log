2025-09-19 00:00:01.975 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:00:03.703 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(11)回目
2025-09-19 00:00:06.717 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:00:08.488 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(12)回目
2025-09-19 00:00:11.511 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:00:12.598 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:00:13.323 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(13)回目
2025-09-19 00:00:16.339 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 12
2025-09-19 00:00:18.100 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(14)回目
2025-09-19 00:00:21.103 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 42
2025-09-19 00:00:22.843 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(15)回目
2025-09-19 00:00:25.860 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 71
2025-09-19 00:00:25.860 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 15 回クリックしました
2025-09-19 00:00:27.601 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:00:32.012 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Found 480 items for seller: 176310729
2025-09-19 00:00:32.013 [SearchQueueProcessor] INFO  c.m.u.d.SellerSearchRepository - セラー検索をID: 160 で正常に保存しました
2025-09-19 00:00:32.013 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved seller search record with ID: 160
2025-09-19 00:00:32.016 [SearchQueueProcessor] INFO  c.m.util.SellerSearchDataConverter - 480 件の GetItemModel オブジェクトを Item オブジェクトに変換しました
2025-09-19 00:00:32.016 [SearchQueueProcessor] INFO  c.m.u.database.SearchItemRepository - Attempting to save/update 480 items.
2025-09-19 00:00:32.201 [SearchQueueProcessor] INFO  c.m.u.database.SearchItemRepository - Successfully saved/updated 480 items to search_items table.
2025-09-19 00:00:32.203 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved 480 items to search_items table
2025-09-19 00:00:32.493 [SearchQueueProcessor] INFO  c.m.u.d.SellerSearchItemRepository - seller_search_items テーブルに 480 件のアイテム参照を正常に保存しました
2025-09-19 00:00:32.493 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved 480 item references to seller_search_items table
2025-09-19 00:00:32.493 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Successfully completed seller search data persistence for seller: 176310729
2025-09-19 00:00:32.493 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 176310729 のセラー検索が正常に完了しました
2025-09-19 00:00:32.495 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラーを正常に更新しました: 176310729
2025-09-19 00:00:32.496 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 176310729 のリサーチステータスを 完了 に正常に更新しました
2025-09-19 00:00:32.496 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 176310729 のセラーのステータスを '完了' に正常に更新しました
2025-09-19 00:00:35.214 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの実行が完了しました: セラーリサーチ: 176310729
2025-09-19 00:00:35.214 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - セラーリサーチ完了後にテーブルを更新しました: 176310729
2025-09-19 00:00:35.214 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクが完全に完了しました: セラーリサーチ: 176310729
2025-09-19 00:00:35.214 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: セラーリサーチ: 250026963 (残り: 4)
2025-09-19 00:00:35.217 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 250026963 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-19 00:00:35.217 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 250026963 のセラーのステータスを 'リサーチ中' に正常に更新しました
2025-09-19 00:00:42.610 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:00:57.616 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:01:06.649 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 250026963 のセラー検索実行を開始します
2025-09-19 00:01:12.618 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:01:15.939 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(1)回目
2025-09-19 00:01:18.943 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:01:20.578 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(2)回目
2025-09-19 00:01:23.594 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:01:25.413 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(3)回目
2025-09-19 00:01:27.620 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:01:28.417 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:01:30.085 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(4)回目
2025-09-19 00:01:33.094 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:01:34.792 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(5)回目
2025-09-19 00:01:37.796 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:01:39.481 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(6)回目
2025-09-19 00:01:42.488 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:01:42.633 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:01:44.152 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(7)回目
2025-09-19 00:01:47.161 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:01:48.874 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(8)回目
2025-09-19 00:01:51.890 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:01:53.623 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(9)回目
2025-09-19 00:01:56.636 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:01:57.640 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:01:58.375 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(10)回目
2025-09-19 00:02:01.391 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:02:03.161 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(11)回目
2025-09-19 00:02:06.178 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:02:07.884 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(12)回目
2025-09-19 00:02:10.896 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:02:12.643 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(13)回目
2025-09-19 00:02:12.645 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:02:15.661 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:02:18.257 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(14)回目
2025-09-19 00:02:21.270 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:02:23.022 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(15)回目
2025-09-19 00:02:26.034 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:02:27.650 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:02:27.975 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(16)回目
2025-09-19 00:02:30.982 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:02:32.817 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(17)回目
2025-09-19 00:02:35.834 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:02:37.615 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(18)回目
2025-09-19 00:02:40.618 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:02:42.395 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(19)回目
2025-09-19 00:02:42.656 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:02:45.398 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:02:47.189 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(20)回目
2025-09-19 00:02:50.202 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:02:52.008 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(21)回目
2025-09-19 00:02:55.020 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:02:57.229 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(22)回目
2025-09-19 00:02:57.671 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:03:00.241 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 1
2025-09-19 00:03:02.097 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(23)回目
2025-09-19 00:03:05.100 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 4
2025-09-19 00:03:07.636 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(24)回目
2025-09-19 00:03:10.644 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 26
2025-09-19 00:03:12.488 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(25)回目
2025-09-19 00:03:12.678 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:03:15.492 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 52
2025-09-19 00:03:17.727 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(26)回目
2025-09-19 00:03:20.732 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 81
2025-09-19 00:03:20.732 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 26 回クリックしました
2025-09-19 00:03:20.737 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 250026963 のリサーチステータスを エラー に正常に更新しました
2025-09-19 00:03:20.737 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - エラーのため、セラーID: 250026963 のセラーのステータスを 'エラー' に更新しました
2025-09-19 00:03:20.738 [SearchQueueProcessor] ERROR c.m.s.analyze.service.SearchBySeller - Error during seller search execution: Index -1 out of bounds for length 0
java.lang.IndexOutOfBoundsException: Index -1 out of bounds for length 0
	at java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
	at java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
	at java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
	at java.base/java.util.Objects.checkIndex(Objects.java:385)
	at java.base/java.util.ArrayList.get(ArrayList.java:427)
	at com.mrcresearch.service.analyze.model.GetItemModel.<init>(GetItemModel.java:83)
	at com.mrcresearch.service.tools.DriverTool.getItemListFromHAR(DriverTool.java:371)
	at com.mrcresearch.service.analyze.service.SellerListAnalyze.fromProfile(SellerListAnalyze.java:129)
	at com.mrcresearch.service.analyze.service.SellerListAnalyze.execute(SellerListAnalyze.java:105)
	at com.mrcresearch.service.analyze.service.SearchBySeller.execute(SearchBySeller.java:48)
	at com.mrcresearch.screen.component.SalesAggregationPanel$5.execute(SalesAggregationPanel.java:790)
	at com.mrcresearch.util.SearchQueueManager.lambda$startProcessingThread$5(SearchQueueManager.java:182)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:03:23.222 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - セラーリサーチエラー後にテーブルを更新しました: 250026963
2025-09-19 00:03:23.222 [SearchQueueProcessor] ERROR c.m.util.SearchQueueManager - Task failed: セラーリサーチ: 250026963 - Index -1 out of bounds for length 0
java.lang.IndexOutOfBoundsException: Index -1 out of bounds for length 0
	at java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
	at java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
	at java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
	at java.base/java.util.Objects.checkIndex(Objects.java:385)
	at java.base/java.util.ArrayList.get(ArrayList.java:427)
	at com.mrcresearch.service.analyze.model.GetItemModel.<init>(GetItemModel.java:83)
	at com.mrcresearch.service.tools.DriverTool.getItemListFromHAR(DriverTool.java:371)
	at com.mrcresearch.service.analyze.service.SellerListAnalyze.fromProfile(SellerListAnalyze.java:129)
	at com.mrcresearch.service.analyze.service.SellerListAnalyze.execute(SellerListAnalyze.java:105)
	at com.mrcresearch.service.analyze.service.SearchBySeller.execute(SearchBySeller.java:48)
	at com.mrcresearch.screen.component.SalesAggregationPanel$5.execute(SalesAggregationPanel.java:790)
	at com.mrcresearch.util.SearchQueueManager.lambda$startProcessingThread$5(SearchQueueManager.java:182)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:03:23.223 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクが完全に完了しました: セラーリサーチ: 250026963
2025-09-19 00:03:23.223 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: セラーリサーチ: 232194206 (残り: 3)
2025-09-19 00:03:23.223 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 232194206 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-19 00:03:23.223 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 232194206 のセラーのステータスを 'リサーチ中' に正常に更新しました
2025-09-19 00:03:27.681 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:03:42.696 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:03:55.590 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 232194206 のセラー検索実行を開始します
2025-09-19 00:03:57.705 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:04:05.713 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(1)回目
2025-09-19 00:04:08.724 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:04:10.363 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(2)回目
2025-09-19 00:04:12.720 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:04:13.367 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:04:15.013 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(3)回目
2025-09-19 00:04:18.026 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:04:19.682 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(4)回目
2025-09-19 00:04:22.698 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:04:24.391 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(5)回目
2025-09-19 00:04:27.403 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:04:27.734 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:04:29.064 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(6)回目
2025-09-19 00:04:32.077 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:04:33.761 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(7)回目
2025-09-19 00:04:36.766 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:04:38.436 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(8)回目
2025-09-19 00:04:41.451 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:04:42.745 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:04:43.172 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(9)回目
2025-09-19 00:04:46.175 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:04:47.845 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(10)回目
2025-09-19 00:04:50.859 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:04:52.563 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(11)回目
2025-09-19 00:04:55.566 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:04:57.268 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(12)回目
2025-09-19 00:04:57.750 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:05:00.278 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:05:02.023 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(13)回目
2025-09-19 00:05:05.026 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 14
2025-09-19 00:05:06.755 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(14)回目
2025-09-19 00:05:09.760 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 33
2025-09-19 00:05:11.499 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(15)回目
2025-09-19 00:05:12.751 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:05:14.506 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 50
2025-09-19 00:05:16.281 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(16)回目
2025-09-19 00:05:19.296 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 60
2025-09-19 00:05:19.296 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 16 回クリックしました
2025-09-19 00:05:24.281 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Found 510 items for seller: 232194206
2025-09-19 00:05:24.283 [SearchQueueProcessor] INFO  c.m.u.d.SellerSearchRepository - セラー検索をID: 161 で正常に保存しました
2025-09-19 00:05:24.283 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved seller search record with ID: 161
2025-09-19 00:05:24.285 [SearchQueueProcessor] INFO  c.m.util.SellerSearchDataConverter - 510 件の GetItemModel オブジェクトを Item オブジェクトに変換しました
2025-09-19 00:05:24.285 [SearchQueueProcessor] INFO  c.m.u.database.SearchItemRepository - Attempting to save/update 510 items.
2025-09-19 00:05:24.433 [SearchQueueProcessor] INFO  c.m.u.database.SearchItemRepository - Successfully saved/updated 510 items to search_items table.
2025-09-19 00:05:24.433 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved 510 items to search_items table
2025-09-19 00:05:24.736 [SearchQueueProcessor] INFO  c.m.u.d.SellerSearchItemRepository - seller_search_items テーブルに 510 件のアイテム参照を正常に保存しました
2025-09-19 00:05:24.736 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved 510 item references to seller_search_items table
2025-09-19 00:05:24.736 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Successfully completed seller search data persistence for seller: 232194206
2025-09-19 00:05:24.736 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 232194206 のセラー検索が正常に完了しました
2025-09-19 00:05:24.737 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラーを正常に更新しました: 232194206
2025-09-19 00:05:24.737 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 232194206 のリサーチステータスを 完了 に正常に更新しました
2025-09-19 00:05:24.737 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 232194206 のセラーのステータスを '完了' に正常に更新しました
2025-09-19 00:05:27.426 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの実行が完了しました: セラーリサーチ: 232194206
2025-09-19 00:05:27.427 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - セラーリサーチ完了後にテーブルを更新しました: 232194206
2025-09-19 00:05:27.427 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクが完全に完了しました: セラーリサーチ: 232194206
2025-09-19 00:05:27.427 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: セラーリサーチ: 349539819 (残り: 2)
2025-09-19 00:05:27.428 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 349539819 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-19 00:05:27.428 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 349539819 のセラーのステータスを 'リサーチ中' に正常に更新しました
2025-09-19 00:05:27.765 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:05:42.776 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:05:57.778 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:05:58.939 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 349539819 のセラー検索実行を開始します
2025-09-19 00:06:08.119 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(1)回目
2025-09-19 00:06:11.121 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:06:12.750 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(2)回目
2025-09-19 00:06:12.779 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:06:15.760 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:06:17.419 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(3)回目
2025-09-19 00:06:20.421 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:06:22.075 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(4)回目
2025-09-19 00:06:25.087 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 1
2025-09-19 00:06:26.771 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(5)回目
2025-09-19 00:06:27.784 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:06:29.785 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 15
2025-09-19 00:06:31.452 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(6)回目
2025-09-19 00:06:34.464 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 41
2025-09-19 00:06:36.173 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(7)回目
2025-09-19 00:06:39.179 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 69
2025-09-19 00:06:39.179 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 7 回クリックしました
2025-09-19 00:06:41.364 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Found 240 items for seller: 349539819
2025-09-19 00:06:41.364 [SearchQueueProcessor] INFO  c.m.u.d.SellerSearchRepository - セラー検索をID: 162 で正常に保存しました
2025-09-19 00:06:41.364 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved seller search record with ID: 162
2025-09-19 00:06:41.365 [SearchQueueProcessor] INFO  c.m.util.SellerSearchDataConverter - 240 件の GetItemModel オブジェクトを Item オブジェクトに変換しました
2025-09-19 00:06:41.365 [SearchQueueProcessor] INFO  c.m.u.database.SearchItemRepository - Attempting to save/update 240 items.
2025-09-19 00:06:41.373 [SearchQueueProcessor] INFO  c.m.u.database.SearchItemRepository - Successfully saved/updated 240 items to search_items table.
2025-09-19 00:06:41.373 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved 240 items to search_items table
2025-09-19 00:06:41.511 [SearchQueueProcessor] INFO  c.m.u.d.SellerSearchItemRepository - seller_search_items テーブルに 240 件のアイテム参照を正常に保存しました
2025-09-19 00:06:41.511 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved 240 item references to seller_search_items table
2025-09-19 00:06:41.511 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Successfully completed seller search data persistence for seller: 349539819
2025-09-19 00:06:41.511 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 349539819 のセラー検索が正常に完了しました
2025-09-19 00:06:41.511 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラーを正常に更新しました: 349539819
2025-09-19 00:06:41.513 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 349539819 のリサーチステータスを 完了 に正常に更新しました
2025-09-19 00:06:41.513 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 349539819 のセラーのステータスを '完了' に正常に更新しました
2025-09-19 00:06:42.794 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:06:44.171 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの実行が完了しました: セラーリサーチ: 349539819
2025-09-19 00:06:44.171 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - セラーリサーチ完了後にテーブルを更新しました: 349539819
2025-09-19 00:06:44.171 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクが完全に完了しました: セラーリサーチ: 349539819
2025-09-19 00:06:44.171 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: セラーリサーチ: 716407612 (残り: 1)
2025-09-19 00:06:44.173 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 716407612 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-19 00:06:44.173 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 716407612 のセラーのステータスを 'リサーチ中' に正常に更新しました
2025-09-19 00:06:45.752 [LittleProxy-9-ClientToProxyWorker-3] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x82ce9d7f, L:/192.168.0.143:8888 ! R:/192.168.0.143:54827]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:06:45.778 [LittleProxy-9-ClientToProxyWorker-4] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x14fa59fb, L:/192.168.0.143:8888 ! R:/192.168.0.143:54829]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:06:57.803 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:07:12.812 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:07:14.986 [LittleProxy-9-ClientToProxyWorker-5] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x64f1f0f1, L:/192.168.0.143:8888 ! R:/192.168.0.143:54844]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.034 [LittleProxy-9-ClientToProxyWorker-6] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xfc3956b0, L:/192.168.0.143:8888 ! R:/192.168.0.143:54846]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.122 [LittleProxy-9-ClientToProxyWorker-7] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x05a42d13, L:/192.168.0.143:8888 ! R:/192.168.0.143:54848]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.151 [LittleProxy-9-ClientToProxyWorker-0] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x43a80322, L:/192.168.0.143:8888 ! R:/192.168.0.143:54850]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.180 [LittleProxy-9-ClientToProxyWorker-1] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x9ffa40b4, L:/192.168.0.143:8888 ! R:/192.168.0.143:54852]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.256 [LittleProxy-9-ClientToProxyWorker-2] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x18152993, L:/192.168.0.143:8888 ! R:/192.168.0.143:54854]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.301 [LittleProxy-9-ClientToProxyWorker-3] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x0d9380db, L:/192.168.0.143:8888 ! R:/192.168.0.143:54856]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.338 [LittleProxy-9-ClientToProxyWorker-4] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xfebc45e1, L:/192.168.0.143:8888 ! R:/192.168.0.143:54858]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.364 [LittleProxy-9-ClientToProxyWorker-5] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x38d2f0c9, L:/192.168.0.143:8888 ! R:/192.168.0.143:54860]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.405 [LittleProxy-9-ClientToProxyWorker-6] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x579d6694, L:/192.168.0.143:8888 ! R:/192.168.0.143:54862]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.442 [LittleProxy-9-ClientToProxyWorker-7] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x611c5f29, L:/192.168.0.143:8888 ! R:/192.168.0.143:54864]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.466 [LittleProxy-9-ClientToProxyWorker-0] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x6f15c576, L:/192.168.0.143:8888 ! R:/192.168.0.143:54866]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.531 [LittleProxy-9-ClientToProxyWorker-1] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x8f1fbcdf, L:/192.168.0.143:8888 ! R:/192.168.0.143:54868]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.555 [LittleProxy-9-ClientToProxyWorker-2] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xe8adec76, L:/192.168.0.143:8888 ! R:/192.168.0.143:54870]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.590 [LittleProxy-9-ClientToProxyWorker-3] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xfb423509, L:/192.168.0.143:8888 ! R:/192.168.0.143:54872]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.625 [LittleProxy-9-ClientToProxyWorker-4] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xf31a03c6, L:/192.168.0.143:8888 ! R:/192.168.0.143:54874]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.708 [LittleProxy-9-ClientToProxyWorker-5] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x7da94bf5, L:/192.168.0.143:8888 ! R:/192.168.0.143:54876]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.733 [LittleProxy-9-ClientToProxyWorker-6] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xdb4cc4a9, L:/192.168.0.143:8888 ! R:/192.168.0.143:54878]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.741 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 716407612 のセラー検索実行を開始します
2025-09-19 00:07:15.771 [LittleProxy-9-ClientToProxyWorker-7] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x37a0689b, L:/192.168.0.143:8888 ! R:/192.168.0.143:54881]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.798 [LittleProxy-9-ClientToProxyWorker-0] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x944ca69a, L:/192.168.0.143:8888 ! R:/192.168.0.143:54883]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.868 [LittleProxy-9-ClientToProxyWorker-1] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xc37570ef, L:/192.168.0.143:8888 ! R:/192.168.0.143:54885]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.910 [LittleProxy-9-ClientToProxyWorker-2] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xee791a92, L:/192.168.0.143:8888 ! R:/192.168.0.143:54887]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.948 [LittleProxy-9-ClientToProxyWorker-3] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xb7ea0f94, L:/192.168.0.143:8888 ! R:/192.168.0.143:54889]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.974 [LittleProxy-9-ClientToProxyWorker-4] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xcf10c440, L:/192.168.0.143:8888 ! R:/192.168.0.143:54891]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:15.998 [LittleProxy-9-ClientToProxyWorker-5] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x037b5be6, L:/192.168.0.143:8888 ! R:/192.168.0.143:54893]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:16.032 [LittleProxy-9-ClientToProxyWorker-6] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x17416ab6, L:/192.168.0.143:8888 ! R:/192.168.0.143:54895]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:07:24.951 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(1)回目
2025-09-19 00:07:27.826 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:07:27.966 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:07:29.611 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(2)回目
2025-09-19 00:07:32.626 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:07:34.271 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(3)回目
2025-09-19 00:07:37.281 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:07:38.969 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(4)回目
2025-09-19 00:07:41.974 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:07:42.841 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:07:43.677 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(5)回目
2025-09-19 00:07:46.690 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:07:48.348 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(6)回目
2025-09-19 00:07:51.356 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:07:53.011 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(7)回目
2025-09-19 00:07:56.013 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:07:57.704 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(8)回目
2025-09-19 00:07:57.843 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:08:00.715 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:08:02.433 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(9)回目
2025-09-19 00:08:05.437 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:08:07.152 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(10)回目
2025-09-19 00:08:10.160 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:08:11.830 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(11)回目
2025-09-19 00:08:12.849 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:08:14.842 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:08:16.545 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(12)回目
2025-09-19 00:08:19.561 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:08:21.269 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(13)回目
2025-09-19 00:08:24.285 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:08:25.978 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(14)回目
2025-09-19 00:08:27.863 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:08:28.992 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 2
2025-09-19 00:08:30.739 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(15)回目
2025-09-19 00:08:33.753 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 17
2025-09-19 00:08:35.486 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(16)回目
2025-09-19 00:08:38.490 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 38
2025-09-19 00:08:40.253 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(17)回目
2025-09-19 00:08:42.869 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-19 00:08:43.259 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 67
2025-09-19 00:08:43.259 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 17 回クリックしました
2025-09-19 00:08:48.328 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Found 540 items for seller: 716407612
2025-09-19 00:08:48.329 [SearchQueueProcessor] INFO  c.m.u.d.SellerSearchRepository - セラー検索をID: 163 で正常に保存しました
2025-09-19 00:08:48.329 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved seller search record with ID: 163
2025-09-19 00:08:48.331 [SearchQueueProcessor] INFO  c.m.util.SellerSearchDataConverter - 540 件の GetItemModel オブジェクトを Item オブジェクトに変換しました
2025-09-19 00:08:48.331 [SearchQueueProcessor] INFO  c.m.u.database.SearchItemRepository - Attempting to save/update 540 items.
2025-09-19 00:08:48.519 [SearchQueueProcessor] INFO  c.m.u.database.SearchItemRepository - Successfully saved/updated 540 items to search_items table.
2025-09-19 00:08:48.519 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved 540 items to search_items table
2025-09-19 00:08:48.847 [SearchQueueProcessor] INFO  c.m.u.d.SellerSearchItemRepository - seller_search_items テーブルに 540 件のアイテム参照を正常に保存しました
2025-09-19 00:08:48.847 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved 540 item references to seller_search_items table
2025-09-19 00:08:48.847 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Successfully completed seller search data persistence for seller: 716407612
2025-09-19 00:08:48.847 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 716407612 のセラー検索が正常に完了しました
2025-09-19 00:08:48.848 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラーを正常に更新しました: 716407612
2025-09-19 00:08:48.848 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 716407612 のリサーチステータスを 完了 に正常に更新しました
2025-09-19 00:08:48.848 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 716407612 のセラーのステータスを '完了' に正常に更新しました
2025-09-19 00:08:51.526 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの実行が完了しました: セラーリサーチ: 716407612
2025-09-19 00:08:51.527 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - セラーリサーチ完了後にテーブルを更新しました: 716407612
2025-09-19 00:08:51.527 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクが完全に完了しました: セラーリサーチ: 716407612
2025-09-19 00:08:51.527 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: セラーリサーチ: 837669355 (残り: 0)
2025-09-19 00:08:51.528 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 837669355 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-19 00:08:51.528 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 837669355 のセラーのステータスを 'リサーチ中' に正常に更新しました
2025-09-19 00:08:52.269 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 自動更新タイマーを停止しました - 進行中のリサーチはありません
2025-09-19 00:08:53.305 [LittleProxy-10-ClientToProxyWorker-3] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x34eff6ca, L:/192.168.0.143:8888 ! R:/192.168.0.143:55229]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:23.194 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 837669355 のセラー検索実行を開始します
2025-09-19 00:09:23.216 [LittleProxy-10-ClientToProxyWorker-5] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x0a0527e6, L:/192.168.0.143:8888 ! R:/192.168.0.143:55249]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:23.272 [LittleProxy-10-ClientToProxyWorker-6] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x902506b5, L:/192.168.0.143:8888 ! R:/192.168.0.143:55251]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:23.375 [LittleProxy-10-ClientToProxyWorker-7] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xab25a40d, L:/192.168.0.143:8888 ! R:/192.168.0.143:55253]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:23.408 [LittleProxy-10-ClientToProxyWorker-0] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x7714f027, L:/192.168.0.143:8888 ! R:/192.168.0.143:55255]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:23.450 [LittleProxy-10-ClientToProxyWorker-1] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xf034be11, L:/192.168.0.143:8888 ! R:/192.168.0.143:55257]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:23.538 [LittleProxy-10-ClientToProxyWorker-2] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xde987ea1, L:/192.168.0.143:8888 ! R:/192.168.0.143:55259]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:23.590 [LittleProxy-10-ClientToProxyWorker-3] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xebaa468f, L:/192.168.0.143:8888 ! R:/192.168.0.143:55261]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:23.631 [LittleProxy-10-ClientToProxyWorker-4] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x945bcd46, L:/192.168.0.143:8888 ! R:/192.168.0.143:55263]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:23.659 [LittleProxy-10-ClientToProxyWorker-5] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xd2178bdc, L:/192.168.0.143:8888 ! R:/192.168.0.143:55265]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:23.703 [LittleProxy-10-ClientToProxyWorker-6] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x899d4e24, L:/192.168.0.143:8888 ! R:/192.168.0.143:55267]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:23.743 [LittleProxy-10-ClientToProxyWorker-7] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xad308fc2, L:/192.168.0.143:8888 ! R:/192.168.0.143:55269]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:23.768 [LittleProxy-10-ClientToProxyWorker-0] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xba40f5d3, L:/192.168.0.143:8888 ! R:/192.168.0.143:55271]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:23.831 [LittleProxy-10-ClientToProxyWorker-1] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x3ab3fc09, L:/192.168.0.143:8888 ! R:/192.168.0.143:55273]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:23.860 [LittleProxy-10-ClientToProxyWorker-2] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x799e7d7e, L:/192.168.0.143:8888 ! R:/192.168.0.143:55275]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:23.897 [LittleProxy-10-ClientToProxyWorker-3] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x8a2b7514, L:/192.168.0.143:8888 ! R:/192.168.0.143:55277]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:23.932 [LittleProxy-10-ClientToProxyWorker-4] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xffdb562a, L:/192.168.0.143:8888 ! R:/192.168.0.143:55279]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:24.012 [LittleProxy-10-ClientToProxyWorker-5] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xf3163f4c, L:/192.168.0.143:8888 ! R:/192.168.0.143:55281]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:24.040 [LittleProxy-10-ClientToProxyWorker-6] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x91b65a73, L:/192.168.0.143:8888 ! R:/192.168.0.143:55283]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:24.064 [LittleProxy-10-ClientToProxyWorker-7] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xdea5a49f, L:/192.168.0.143:8888 ! R:/192.168.0.143:55285]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:24.091 [LittleProxy-10-ClientToProxyWorker-0] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xb8bcbaed, L:/192.168.0.143:8888 ! R:/192.168.0.143:55287]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:24.132 [LittleProxy-10-ClientToProxyWorker-1] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x450b44e0, L:/192.168.0.143:8888 ! R:/192.168.0.143:55289]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:24.173 [LittleProxy-10-ClientToProxyWorker-2] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xf1873a05, L:/192.168.0.143:8888 ! R:/192.168.0.143:55292]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:24.211 [LittleProxy-10-ClientToProxyWorker-3] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xd978c2e2, L:/192.168.0.143:8888 ! R:/192.168.0.143:55294]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:24.237 [LittleProxy-10-ClientToProxyWorker-4] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x56208ecb, L:/192.168.0.143:8888 ! R:/192.168.0.143:55296]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:24.261 [LittleProxy-10-ClientToProxyWorker-5] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xb6e4c991, L:/192.168.0.143:8888 ! R:/192.168.0.143:55298]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:24.297 [LittleProxy-10-ClientToProxyWorker-6] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x25f2e94c, L:/192.168.0.143:8888 ! R:/192.168.0.143:55300]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-19 00:09:32.273 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(1)回目
2025-09-19 00:09:35.274 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:09:36.909 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(2)回目
2025-09-19 00:09:39.919 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:09:41.564 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(3)回目
2025-09-19 00:09:44.570 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:09:46.224 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(4)回目
2025-09-19 00:09:49.235 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:09:50.923 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(5)回目
2025-09-19 00:09:53.937 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:09:55.616 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(6)回目
2025-09-19 00:09:58.621 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:10:00.279 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(7)回目
2025-09-19 00:10:03.294 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:10:04.994 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(8)回目
2025-09-19 00:10:07.996 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-19 00:10:09.660 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(9)回目
2025-09-19 00:10:12.674 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 3
2025-09-19 00:10:14.218 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 9 回クリックしました
2025-09-19 00:10:16.680 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Found 283 items for seller: 837669355
2025-09-19 00:10:16.683 [SearchQueueProcessor] INFO  c.m.u.d.SellerSearchRepository - セラー検索をID: 164 で正常に保存しました
2025-09-19 00:10:16.683 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved seller search record with ID: 164
2025-09-19 00:10:16.685 [SearchQueueProcessor] INFO  c.m.util.SellerSearchDataConverter - 283 件の GetItemModel オブジェクトを Item オブジェクトに変換しました
2025-09-19 00:10:16.685 [SearchQueueProcessor] INFO  c.m.u.database.SearchItemRepository - Attempting to save/update 283 items.
2025-09-19 00:10:16.693 [SearchQueueProcessor] INFO  c.m.u.database.SearchItemRepository - Successfully saved/updated 283 items to search_items table.
2025-09-19 00:10:16.693 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved 283 items to search_items table
2025-09-19 00:10:16.953 [SearchQueueProcessor] INFO  c.m.u.d.SellerSearchItemRepository - seller_search_items テーブルに 283 件のアイテム参照を正常に保存しました
2025-09-19 00:10:16.953 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved 283 item references to seller_search_items table
2025-09-19 00:10:16.953 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Successfully completed seller search data persistence for seller: 837669355
2025-09-19 00:10:16.953 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 837669355 のセラー検索が正常に完了しました
2025-09-19 00:10:16.955 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラーを正常に更新しました: 837669355
2025-09-19 00:10:16.955 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 837669355 のリサーチステータスを 完了 に正常に更新しました
2025-09-19 00:10:16.955 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 837669355 のセラーのステータスを '完了' に正常に更新しました
2025-09-19 00:10:19.643 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - セラーリサーチ完了後にテーブルを更新しました: 837669355
2025-09-19 00:10:19.643 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの実行が完了しました: セラーリサーチ: 837669355
2025-09-19 00:10:19.643 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクが完全に完了しました: セラーリサーチ: 837669355
2025-09-19 00:11:02.108 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: キーワード検索: 海外 (残り: 0)
2025-09-19 00:11:02.108 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: キーワード検索: 海外 (キューサイズ: 1)
2025-09-19 00:11:02.113 [AWT-EventQueue-0] ERROR c.mrcresearch.screen.keyword.Keyword - Error getting default page value: class java.lang.String cannot be cast to class java.lang.Integer (java.lang.String and java.lang.Integer are in module java.base of loader 'bootstrap')
2025-09-19 00:11:02.114 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの実行が完了しました: キーワード検索: 海外
2025-09-19 00:11:02.114 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクに非同期操作があります。完了を待機中...
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - レコードID: 83 のキーワード検索実行を開始します
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - キーワード検索結果を取得しました: KeywordSearchResultModel{id=83, keyword='海外', categories='', addedDate=Fri Sep 19 00:11:02 JST 2025, updatedDate=Fri Sep 19 00:11:02 JST 2025, status='待機中', researchStatus='リサーチ中', minPrice=300, maxPrice=9999999, pages=99, sortByNew=true, ignoreShops=true, salesStatus='販売済み', itemConditionIds='1', excludeKeyword='', colorIds='null', categoryId=''}
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - IDが以下で始まる ProgressItem を探しています: keyword_83_
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - アクティブな進捗アイテム数: 11
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: seller_research_1758203239295
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: seller_research_1758203579037
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: seller_research_1758205651012
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: seller_research_1758207446772
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: seller_research_1758207455653
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: seller_research_1758207458861
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: seller_research_1758207470426
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: seller_research_1758207474226
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: seller_research_1758207485695
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: seller_research_1758207503474
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: keyword_83_1758208262113
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - 一致する ProgressItem が見つかりました: keyword_83_1758208262113
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchResultModel を KeywordSearchModel に変換中...
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchResultModel を KeywordSearchModel に変換中...
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - 設定された単語: 海外
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - maxMore を設定: 98 (ページ数: 99)
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - キーワード: 海外 の検索URLを構築中
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - キーワードパラメータを追加しました: keyword=%E6%B5%B7%E5%A4%96
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - ソートパラメータを追加しました: sort=created_time&order=desc
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - アイテムタイプパラメータを追加しました: item_types=1
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - 販売ステータスパラメータを追加しました: status=sold_out
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - 最終URLパラメータ: keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - 構築された検索URL: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - baseUrl を設定: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - saveName を設定: 海外_1758208262114
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchModel の変換が正常に完了しました
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - 変換が完了しました。検索URL: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - ナビゲートする最大ページ数: 98
2025-09-19 00:11:02.114 [Thread-48] INFO  c.m.s.a.service.SearchByKeyWord - 実際のキーワード検索実行を開始します...
2025-09-19 00:19:53.283 [Thread-0] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Shutdown initiated...
2025-09-19 00:19:53.287 [Thread-0] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Shutdown completed.
2025-09-19 00:19:56.784 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Starting...
2025-09-19 00:19:56.969 [main] INFO  com.zaxxer.hikari.pool.HikariPool - MercariResearchDB-Pool - Added connection org.sqlite.jdbc4.JDBC4Connection@2a798d51
2025-09-19 00:19:56.970 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Start completed.
2025-09-19 00:19:56.997 [main] INFO  c.m.u.database.DatabaseSchemaManager - バージョン18への移行を開始します: keyword_search_results テーブルに不足しているカラムを追加中
2025-09-19 00:19:57.001 [main] INFO  c.m.u.database.DatabaseSchemaManager - バージョン18への移行が正常に完了しました
2025-09-19 00:19:57.140 [main] INFO  c.m.u.database.SellerMigrationUtil - セラーデータ移行は不要です。
2025-09-19 00:19:57.724 [AWT-EventQueue-0] INFO  c.m.screen.main.Application - TLSバージョンの設定が完了しました
2025-09-19 00:19:57.991 [AWT-EventQueue-0] INFO  com.mrcresearch.screen.main.Main - メインパネルを初期化しています
2025-09-19 00:20:00.971 [AWT-EventQueue-0] INFO  com.mrcresearch.util.ImageUtil - WebPデコーダが正常に登録されました
2025-09-19 00:20:02.227 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 自動更新タイマーを停止しました - 進行中のリサーチはありません
2025-09-19 00:20:03.128 [SwingWorker-pool-1-thread-4] INFO  c.mrcresearch.service.common.Version - 現在のバージョン：4.0.24
2025-09-19 00:20:25.320 [Thread-0] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Shutdown initiated...
2025-09-19 00:20:25.322 [Thread-0] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Shutdown completed.
2025-09-19 00:20:32.972 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Starting...
2025-09-19 00:20:33.143 [main] INFO  com.zaxxer.hikari.pool.HikariPool - MercariResearchDB-Pool - Added connection org.sqlite.jdbc4.JDBC4Connection@2a798d51
2025-09-19 00:20:33.144 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Start completed.
2025-09-19 00:20:33.284 [main] INFO  c.m.u.database.SellerMigrationUtil - セラーデータ移行は不要です。
2025-09-19 00:20:33.838 [AWT-EventQueue-0] INFO  c.m.screen.main.Application - TLSバージョンの設定が完了しました
2025-09-19 00:20:33.981 [AWT-EventQueue-0] INFO  com.mrcresearch.screen.main.Main - メインパネルを初期化しています
2025-09-19 00:20:36.647 [AWT-EventQueue-0] INFO  com.mrcresearch.util.ImageUtil - WebPデコーダが正常に登録されました
2025-09-19 00:20:36.926 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 自動更新タイマーを停止しました - 進行中のリサーチはありません
2025-09-19 00:20:38.118 [SwingWorker-pool-1-thread-4] INFO  c.mrcresearch.service.common.Version - 現在のバージョン：4.0.24
2025-09-19 00:20:41.059 [Thread-0] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Shutdown initiated...
2025-09-19 00:20:41.064 [Thread-0] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Shutdown completed.
2025-09-19 00:21:49.538 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Starting...
2025-09-19 00:21:49.721 [main] INFO  com.zaxxer.hikari.pool.HikariPool - MercariResearchDB-Pool - Added connection org.sqlite.jdbc4.JDBC4Connection@2a798d51
2025-09-19 00:21:49.723 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Start completed.
2025-09-19 00:21:49.864 [main] INFO  c.m.u.database.SellerMigrationUtil - セラーデータ移行は不要です。
2025-09-19 00:21:50.441 [AWT-EventQueue-0] INFO  c.m.screen.main.Application - TLSバージョンの設定が完了しました
2025-09-19 00:21:50.598 [AWT-EventQueue-0] INFO  com.mrcresearch.screen.main.Main - メインパネルを初期化しています
2025-09-19 00:21:53.304 [AWT-EventQueue-0] INFO  com.mrcresearch.util.ImageUtil - WebPデコーダが正常に登録されました
2025-09-19 00:21:53.582 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 自動更新タイマーを停止しました - 進行中のリサーチはありません
2025-09-19 00:21:55.275 [SwingWorker-pool-1-thread-4] INFO  c.mrcresearch.service.common.Version - 現在のバージョン：4.0.24
2025-09-19 00:21:59.076 [Thread-0] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Shutdown initiated...
2025-09-19 00:21:59.077 [Thread-0] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Shutdown completed.
2025-09-19 00:30:05.835 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Starting...
2025-09-19 00:30:06.013 [main] INFO  com.zaxxer.hikari.pool.HikariPool - MercariResearchDB-Pool - Added connection org.sqlite.jdbc4.JDBC4Connection@2a798d51
2025-09-19 00:30:06.015 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Start completed.
2025-09-19 00:30:06.150 [main] INFO  c.m.u.database.SellerMigrationUtil - セラーデータ移行は不要です。
2025-09-19 00:30:06.710 [AWT-EventQueue-0] INFO  c.m.screen.main.Application - TLSバージョンの設定が完了しました
2025-09-19 00:30:06.866 [AWT-EventQueue-0] INFO  com.mrcresearch.screen.main.Main - メインパネルを初期化しています
2025-09-19 00:30:09.618 [AWT-EventQueue-0] INFO  com.mrcresearch.util.ImageUtil - WebPデコーダが正常に登録されました
2025-09-19 00:30:11.111 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 自動更新タイマーを停止しました - 進行中のリサーチはありません
2025-09-19 00:30:11.978 [SwingWorker-pool-1-thread-4] INFO  c.mrcresearch.service.common.Version - 現在のバージョン：4.0.24
2025-09-19 00:35:33.193 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: キーワード検索: 海外 (キューサイズ: 1)
2025-09-19 00:35:33.193 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: キーワード検索: 海外 (残り: 0)
2025-09-19 00:35:33.203 [AWT-EventQueue-0] ERROR c.mrcresearch.screen.keyword.Keyword - Error getting default page value: class java.lang.String cannot be cast to class java.lang.Integer (java.lang.String and java.lang.Integer are in module java.base of loader 'bootstrap')
2025-09-19 00:35:33.209 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの実行が完了しました: キーワード検索: 海外
2025-09-19 00:35:33.209 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクに非同期操作があります。完了を待機中...
2025-09-19 00:35:33.230 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - レコードID: 84 のキーワード検索実行を開始します
2025-09-19 00:35:33.237 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - キーワード検索結果を取得しました: KeywordSearchResultModel{id=84, keyword='海外', categories='', addedDate=Fri Sep 19 00:35:33 JST 2025, updatedDate=Fri Sep 19 00:35:33 JST 2025, status='待機中', researchStatus='リサーチ中', minPrice=300, maxPrice=9999999, pages=99, sortByNew=true, ignoreShops=true, salesStatus='販売済み', itemConditionIds='1', excludeKeyword='', colorIds='null', categoryId=''}
2025-09-19 00:35:33.237 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - IDが以下で始まる ProgressItem を探しています: keyword_84_
2025-09-19 00:35:33.237 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - アクティブな進捗アイテム数: 1
2025-09-19 00:35:33.239 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: keyword_84_1758209733204
2025-09-19 00:35:33.239 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - 一致する ProgressItem が見つかりました: keyword_84_1758209733204
2025-09-19 00:35:33.239 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchResultModel を KeywordSearchModel に変換中...
2025-09-19 00:35:33.239 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchResultModel を KeywordSearchModel に変換中...
2025-09-19 00:35:33.239 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - 設定された単語: 海外
2025-09-19 00:35:33.239 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - maxMore を設定: 98 (ページ数: 99)
2025-09-19 00:35:33.239 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - キーワード: 海外 の検索URLを構築中
2025-09-19 00:35:33.239 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - キーワードパラメータを追加しました: keyword=%E6%B5%B7%E5%A4%96
2025-09-19 00:35:33.239 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - ソートパラメータを追加しました: sort=created_time&order=desc
2025-09-19 00:35:33.239 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - アイテムタイプパラメータを追加しました: item_types=1
2025-09-19 00:35:33.239 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - 販売ステータスパラメータを追加しました: status=sold_out
2025-09-19 00:35:33.239 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - 最終URLパラメータ: keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:35:33.239 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - 構築された検索URL: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:35:33.239 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - baseUrl を設定: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:35:33.240 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - saveName を設定: 海外_1758209733239
2025-09-19 00:35:33.240 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchModel の変換が正常に完了しました
2025-09-19 00:35:33.240 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - 変換が完了しました。検索URL: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:35:33.240 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - ナビゲートする最大ページ数: 98
2025-09-19 00:35:33.240 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - 実際のキーワード検索実行を開始します...
2025-09-19 00:36:10.936 [LittleProxy-0-ClientToProxyWorker-1] ERROR io.netty.util.ResourceLeakDetector - LEAK: ByteBuf.release() was not called before it's garbage-collected. See https://netty.io/wiki/reference-counted-objects.html for more information.
Recent access records: 
Created at:
	io.netty.buffer.PooledByteBufAllocator.newDirectBuffer(PooledByteBufAllocator.java:363)
	io.netty.buffer.AbstractByteBufAllocator.directBuffer(AbstractByteBufAllocator.java:187)
	io.netty.buffer.AbstractByteBufAllocator.directBuffer(AbstractByteBufAllocator.java:178)
	io.netty.buffer.CompositeByteBuf.allocBuffer(CompositeByteBuf.java:1858)
	io.netty.buffer.CompositeByteBuf.copy(CompositeByteBuf.java:1510)
	io.netty.buffer.AbstractByteBuf.copy(AbstractByteBuf.java:1195)
	io.netty.handler.codec.http.HttpObjectAggregator$AggregatedFullHttpRequest.copy(HttpObjectAggregator.java:405)
	org.littleshoot.proxy.impl.ClientToProxyConnection.copy(ClientToProxyConnection.java:1044)
	org.littleshoot.proxy.impl.ClientToProxyConnection.doReadHTTPInitial(ClientToProxyConnection.java:211)
	org.littleshoot.proxy.impl.ClientToProxyConnection.readHTTPInitial(ClientToProxyConnection.java:187)
	org.littleshoot.proxy.impl.ClientToProxyConnection.readHTTPInitial(ClientToProxyConnection.java:55)
	org.littleshoot.proxy.impl.ProxyConnection.readHTTP(ProxyConnection.java:144)
	org.littleshoot.proxy.impl.ProxyConnection.read(ProxyConnection.java:122)
	org.littleshoot.proxy.impl.ProxyConnection.channelRead0(ProxyConnection.java:582)
	io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.handler.timeout.IdleStateHandler.channelRead(IdleStateHandler.java:286)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.channel.ChannelInboundHandlerAdapter.channelRead(ChannelInboundHandlerAdapter.java:93)
	org.littleshoot.proxy.impl.ProxyConnection$RequestReadMonitor.channelRead(ProxyConnection.java:709)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:103)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:103)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:324)
	io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:296)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.channel.ChannelInboundHandlerAdapter.channelRead(ChannelInboundHandlerAdapter.java:93)
	org.littleshoot.proxy.impl.ProxyConnection$BytesReadMonitor.channelRead(ProxyConnection.java:686)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:42:29.447 [LittleProxy-0-ProxyToServerWorker-7] ERROR io.netty.util.ResourceLeakDetector - LEAK: ByteBuf.release() was not called before it's garbage-collected. See https://netty.io/wiki/reference-counted-objects.html for more information.
Recent access records: 
Created at:
	io.netty.buffer.AbstractByteBufAllocator.compositeDirectBuffer(AbstractByteBufAllocator.java:223)
	io.netty.buffer.AbstractByteBufAllocator.compositeBuffer(AbstractByteBufAllocator.java:201)
	io.netty.handler.codec.MessageAggregator.decode(MessageAggregator.java:271)
	io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:88)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:103)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:324)
	io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:296)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.channel.ChannelInboundHandlerAdapter.channelRead(ChannelInboundHandlerAdapter.java:93)
	org.littleshoot.proxy.impl.ProxyConnection$BytesReadMonitor.channelRead(ProxyConnection.java:686)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1518)
	io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:42:58.593 [Thread-0] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Shutdown initiated...
2025-09-19 00:42:58.599 [Thread-0] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Shutdown completed.
2025-09-19 00:43:02.529 [main] INFO  c.m.util.NettyResourceManager - Nettyリソース管理が初期化されました (リーク検出レベル: SIMPLE)
2025-09-19 00:43:02.558 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Starting...
2025-09-19 00:43:02.723 [main] INFO  com.zaxxer.hikari.pool.HikariPool - MercariResearchDB-Pool - Added connection org.sqlite.jdbc4.JDBC4Connection@6d763516
2025-09-19 00:43:02.725 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Start completed.
2025-09-19 00:43:02.877 [main] INFO  c.m.u.database.SellerMigrationUtil - セラーデータ移行は不要です。
2025-09-19 00:43:03.595 [AWT-EventQueue-0] INFO  c.m.screen.main.Application - TLSバージョンの設定が完了しました
2025-09-19 00:43:03.776 [AWT-EventQueue-0] INFO  com.mrcresearch.screen.main.Main - メインパネルを初期化しています
2025-09-19 00:43:06.813 [AWT-EventQueue-0] INFO  com.mrcresearch.util.ImageUtil - WebPデコーダが正常に登録されました
2025-09-19 00:43:08.344 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 自動更新タイマーを停止しました - 進行中のリサーチはありません
2025-09-19 00:43:08.618 [SwingWorker-pool-1-thread-3] WARN  i.n.util.internal.SystemPropertyUtil - Unable to parse the long integer system property 'io.netty.maxDirectMemory':128m - using the default value: -1
2025-09-19 00:43:09.264 [SwingWorker-pool-1-thread-4] INFO  c.mrcresearch.service.common.Version - 現在のバージョン：4.0.24
2025-09-19 00:43:15.141 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: キーワード検索: 海外 (キューサイズ: 1)
2025-09-19 00:43:15.141 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: キーワード検索: 海外 (残り: 0)
2025-09-19 00:43:15.149 [AWT-EventQueue-0] ERROR c.mrcresearch.screen.keyword.Keyword - Error getting default page value: class java.lang.String cannot be cast to class java.lang.Integer (java.lang.String and java.lang.Integer are in module java.base of loader 'bootstrap')
2025-09-19 00:43:15.153 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの実行が完了しました: キーワード検索: 海外
2025-09-19 00:43:15.153 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクに非同期操作があります。完了を待機中...
2025-09-19 00:43:15.186 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - レコードID: 85 のキーワード検索実行を開始します
2025-09-19 00:43:15.195 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - キーワード検索結果を取得しました: KeywordSearchResultModel{id=85, keyword='海外', categories='', addedDate=Fri Sep 19 00:43:15 JST 2025, updatedDate=Fri Sep 19 00:43:15 JST 2025, status='待機中', researchStatus='リサーチ中', minPrice=300, maxPrice=9999999, pages=99, sortByNew=true, ignoreShops=true, salesStatus='販売済み', itemConditionIds='1', excludeKeyword='', colorIds='null', categoryId=''}
2025-09-19 00:43:15.195 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - IDが以下で始まる ProgressItem を探しています: keyword_85_
2025-09-19 00:43:15.195 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - アクティブな進捗アイテム数: 1
2025-09-19 00:43:15.195 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: keyword_85_1758210195151
2025-09-19 00:43:15.195 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 一致する ProgressItem が見つかりました: keyword_85_1758210195151
2025-09-19 00:43:15.197 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchResultModel を KeywordSearchModel に変換中...
2025-09-19 00:43:15.197 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchResultModel を KeywordSearchModel に変換中...
2025-09-19 00:43:15.197 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 設定された単語: 海外
2025-09-19 00:43:15.197 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - maxMore を設定: 98 (ページ数: 99)
2025-09-19 00:43:15.197 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - キーワード: 海外 の検索URLを構築中
2025-09-19 00:43:15.197 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - キーワードパラメータを追加しました: keyword=%E6%B5%B7%E5%A4%96
2025-09-19 00:43:15.197 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - ソートパラメータを追加しました: sort=created_time&order=desc
2025-09-19 00:43:15.197 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - アイテムタイプパラメータを追加しました: item_types=1
2025-09-19 00:43:15.197 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 販売ステータスパラメータを追加しました: status=sold_out
2025-09-19 00:43:15.197 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 最終URLパラメータ: keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:43:15.197 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 構築された検索URL: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:43:15.197 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - baseUrl を設定: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:43:15.199 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - saveName を設定: 海外_1758210195197
2025-09-19 00:43:15.199 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchModel の変換が正常に完了しました
2025-09-19 00:43:15.200 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 変換が完了しました。検索URL: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:43:15.200 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - ナビゲートする最大ページ数: 98
2025-09-19 00:43:15.200 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 実際のキーワード検索実行を開始します...
2025-09-19 00:44:20.337 [Thread-3] ERROR c.m.s.a.service.SearchByKeyWord - ボタンをクリックできませんでした。
2025-09-19 00:44:20.675 [Thread-3] INFO  c.m.util.KeywordSearchDataConverter - データベース保存用に 351 件のキーワード検索アイテムオブジェクトを変換しました
2025-09-19 00:44:23.445 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - キーワード検索実行が正常に完了しました。
2025-09-19 00:44:23.445 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 351 件の検索結果アイテムをデータベースに保存中...
2025-09-19 00:44:23.445 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - キーワード検索結果ID: 85 のアイテム 351 件の保存を開始します
2025-09-19 00:44:23.454 [Thread-3] INFO  c.m.u.database.SearchItemRepository - Attempting to save/update 351 items.
2025-09-19 00:44:23.459 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m38411352091_1.jpg?1758086575'. Using substring fallback.
2025-09-19 00:44:23.459 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m54751580008_1.jpg?1724149998'. Using substring fallback.
2025-09-19 00:44:23.459 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m38444444581_1.jpg?1758209521'. Using substring fallback.
2025-09-19 00:44:23.459 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m36952441061_1.jpg?1758209521'. Using substring fallback.
2025-09-19 00:44:23.459 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m91717235691_1.jpg?1758209502'. Using substring fallback.
2025-09-19 00:44:23.459 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m88144805914_1.jpg?1758209435'. Using substring fallback.
2025-09-19 00:44:23.459 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m67872815936_1.jpg?1757758790'. Using substring fallback.
2025-09-19 00:44:23.459 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m29895533545_1.jpg?1758209146'. Using substring fallback.
2025-09-19 00:44:23.459 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m71540473704_1.jpg?1758208973'. Using substring fallback.
2025-09-19 00:44:23.459 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m66743714696_1.jpg?1758208724'. Using substring fallback.
2025-09-19 00:44:23.459 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m89750541763_1.jpg?1758208699'. Using substring fallback.
2025-09-19 00:44:23.459 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m41802173469_1.jpg?1758208642'. Using substring fallback.
2025-09-19 00:44:23.459 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m32132709202_1.jpg?1743585281'. Using substring fallback.
2025-09-19 00:44:23.459 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m20958580746_1.jpg?1758208529'. Using substring fallback.
2025-09-19 00:44:23.459 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m37089923259_1.jpg?1758208416'. Using substring fallback.
2025-09-19 00:44:23.460 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m81522109730_1.jpg?1758208411'. Using substring fallback.
2025-09-19 00:44:23.460 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m73688393806_1.jpg?1756702111'. Using substring fallback.
2025-09-19 00:44:23.460 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m96277674621_1.jpg?1757972670'. Using substring fallback.
2025-09-19 00:44:23.460 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m92759365566_1.jpg?1758198674'. Using substring fallback.
2025-09-19 00:44:23.460 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m99984729740_1.jpg?1758207933'. Using substring fallback.
2025-09-19 00:44:23.460 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m76364740857_1.jpg?1724676909'. Using substring fallback.
2025-09-19 00:44:23.460 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m12559840217_1.jpg?1757589377'. Using substring fallback.
2025-09-19 00:44:23.460 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m40921971461_1.jpg?1758207697'. Using substring fallback.
2025-09-19 00:44:23.460 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m48271389870_1.jpg?1758207585'. Using substring fallback.
2025-09-19 00:44:23.460 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m42911450982_1.jpg?1757772503'. Using substring fallback.
2025-09-19 00:44:23.460 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m38496881024_1.jpg?1758207367'. Using substring fallback.
2025-09-19 00:44:23.460 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m49778089058_1.jpg?1758207257'. Using substring fallback.
2025-09-19 00:44:23.460 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m61598883536_1.jpg?1758207067'. Using substring fallback.
2025-09-19 00:44:23.460 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m69385596142_1.jpg?1758206947'. Using substring fallback.
2025-09-19 00:44:23.460 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m72148765713_1.jpg?1758206911'. Using substring fallback.
2025-09-19 00:44:23.460 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m57364219702_1.jpg?1758206896'. Using substring fallback.
2025-09-19 00:44:23.460 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m25449991448_1.jpg?1758206825'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m89163067103_1.jpg?1758206768'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m35653734944_1.jpg?1758206646'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m71635777956_1.jpg?1758206636'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m25622800931_1.jpg?1757818020'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m54346344096_1.jpg?1758169128'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m37286434816_1.jpg?1758206101'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m81875804781_1.jpg?1758134760'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m38290796994_1.jpg?1758206007'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m88814979051_1.jpg?1758205990'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m94115847562_1.jpg?1757745383'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m80808234008_1.jpg?1758205947'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m42502655746_1.jpg?1758185565'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m78102073259_1.jpg?1758205723'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m20042278233_1.jpg?1758100276'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m41688709047_1.jpg?1758205467'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m50825021033_1.jpg?1758205440'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m28879548731_1.jpg?1758205359'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m16225963315_1.jpg?1757903423'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m94385365527_1.jpg?1758205075'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m51536325736_1.jpg?1754972835'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m42169916933_1.jpg?1758205013'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m76838163246_1.jpg?1758197650'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m68971012314_1.jpg?1758204930'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m19963680369_1.jpg?1758204887'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m46266342720_1.jpg?1758204874'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m92638652472_1.jpg?1758204865'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m85442161985_1.jpg?1758204676'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m94708460183_1.jpg?1758204628'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m48537894819_1.jpg?1756030441'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m37817117560_1.jpg?1758204573'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m99103885478_1.jpg?1758204460'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m83871927437_1.jpg?1756074500'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m68816832434_1.jpg?1758202580'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m33084046543_1.jpg?1758204229'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m11969418259_1.jpg?1758204143'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m79820356422_1.jpg?1758131593'. Using substring fallback.
2025-09-19 00:44:23.461 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m78724794160_1.jpg?1758204127'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m89581216575_1.jpg?1757162104'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m70557697923_1.jpg?1758204061'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m91231394070_1.jpg?1758204035'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m84449550447_1.jpg?1757774378'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m35219813973_1.jpg?1758107609'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m97065488544_1.jpg?1757241687'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m49943660723_1.jpg?1758203972'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m52264667506_1.jpg?1758203944'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m31517560982_1.jpg?1758203722'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m27870350818_1.jpg?1758203914'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m27703837494_1.jpg?1758203906'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m71262920334_1.jpg?1758203892'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m91717211310_1.jpg?1758203871'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m47079210210_1.jpg?1758203865'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m45559789357_1.jpg?1758155456'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m72357102352_1.jpg?1758203798'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m20687375723_1.jpg?1755749007'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m92705507846_1.jpg?1746887341'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m70015278992_1.jpg?1758203703'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m95959594887_1.jpg?1758203695'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m13555299665_1.jpg?1758203579'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m44051293341_1.jpg?1758203561'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m94144766648_1.jpg?1758203551'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m71862277001_1.jpg?1758097728'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m32725942416_1.jpg?1758203534'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m71322610423_1.jpg?1758203516'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m28779613980_1.jpg?1756815559'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m65604242102_1.jpg?1758203493'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m19470518035_1.jpg?1758203492'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m77427271255_1.jpg?1758203478'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m12928361637_1.jpg?1758203439'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m81770212161_1.jpg?1758203416'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m62707462839_1.jpg?1758203402'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m53426433264_1.jpg?1758203364'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m62879754415_1.jpg?1758203336'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m29065340241_1.jpg?1758203277'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m74861905327_1.jpg?1758197292'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m67811584179_1.jpg?1758203266'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m39203186312_1.jpg?1758203262'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m43348866168_1.jpg?1758203197'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m16821869406_1.jpg?1758203189'. Using substring fallback.
2025-09-19 00:44:23.462 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m46747032928_1.jpg?1758203188'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m56819104273_1.jpg?1758203146'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m55065252831_1.jpg?1758203099'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m56232106432_1.jpg?1758203093'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m36463723304_1.jpg?1758203047'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m27935865004_1.jpg?1704328398'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m50207501667_1.jpg?1758202946'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m98536340462_1.jpg?1758202943'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m39835325778_1.jpg?1758197299'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m17950992317_1.jpg?1758202856'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m57977569701_1.jpg?1758202840'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m44387234271_1.jpg?1757752662'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m78232904847_1.jpg?1758202813'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m24240561291_1.jpg?1758202804'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m96278918322_1.jpg?1758202789'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m73685184179_1.jpg?1758202762'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m78606747639_1.jpg?1758202710'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m90114616834_1.jpg?1758202640'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m74167231103_1.jpg?1758202634'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m51310499603_1.jpg?1757558307'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m52190692618_1.jpg?1758202508'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m84909076927_1.jpg?1758198755'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m86416777269_1.jpg?1758202438'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m60727160223_1.jpg?1758080884'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m89578181568_1.jpg?1758202364'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m20869819999_1.jpg?1752714494'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m22102324992_1.jpg?1758202307'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m80686153927_1.jpg?1758092117'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m46235253885_1.jpg?1666001203'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m88772856060_1.jpg?1758202243'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m82979581340_1.jpg?1758202271'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m52416388744_1.jpg?1758195218'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m75668378270_1.jpg?1758205661'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m23360655089_1.jpg?1758202209'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m13168234410_1.jpg?1758202199'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m17286379823_1.jpg?1758189344'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m89376432521_1.jpg?1726392589'. Using substring fallback.
2025-09-19 00:44:23.463 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m95386675608_1.jpg?1758202144'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m55752065093_1.jpg?1758203344'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m24314010795_1.jpg?1758202142'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m85683479446_1.jpg?1755824691'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m21705470121_1.jpg?1758202089'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m59820067749_1.jpg?1758201777'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m71406279483_1.jpg?1758202057'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m83418491705_1.jpg?1758202062'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m64668696848_1.jpg?1758202053'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m12798114484_1.jpg?1758202047'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m29012293072_1.jpg?1757585903'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m10110871747_1.jpg?1758202009'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m62658343430_1.jpg?1758202002'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m93661803259_1.jpg?1758201980'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m84343918368_1.jpg?1758201934'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m89493279966_1.jpg?1758201907'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m19456628968_1.jpg?1758201905'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m68813962845_1.jpg?1758029705'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m67663973977_1.jpg?1758201850'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m10737862357_1.jpg?1758200021'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m39878438424_1.jpg?1758201819'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m32602534545_1.jpg?1758201809'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m99284833129_1.jpg?1758201794'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m18961747879_1.jpg?1758201787'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m23976049469_1.jpg?1758201778'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m53163103633_1.jpg?1758201775'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m66612354262_1.jpg?1758201773'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m54282813733_1.jpg?1758201760'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m73079017585_1.jpg?1755603594'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m52517172504_1.jpg?1758201792'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m53344749793_1.jpg?1758201734'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m25468326160_1.jpg?1758201718'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m90945636893_1.jpg?1758201685'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m57403027644_1.jpg?1758201693'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m67383373492_1.jpg?1758201629'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m90713637211_1.jpg?1758112367'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m71726364036_1.jpg?1758201628'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m97225829871_1.jpg?1758201570'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m70459226918_1.jpg?1758201569'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m99606648779_1.jpg?1758197934'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m81714327711_1.jpg?1758200536'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m63152626214_1.jpg?1754727808'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m69653852924_1.jpg?1756302520'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m18812264149_1.jpg?1758201430'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m53691821216_1.jpg?1758201415'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m45230464604_1.jpg?1758201360'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m65619561449_1.jpg?1757074702'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m91896807488_1.jpg?1758201297'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m29956282201_1.jpg?1758201291'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m35816319740_1.jpg?1758201263'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m20678515554_1.jpg?1758202218'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m79041609340_1.jpg?1758201234'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m51007076194_1.jpg?1758201233'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m65629189472_1.jpg?1758201232'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m85842851309_1.jpg?1758201229'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m52233300345_1.jpg?1758201209'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m14624146423_1.jpg?1755863792'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m30122918832_1.jpg?1758201168'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m74349355384_1.jpg?1758201134'. Using substring fallback.
2025-09-19 00:44:23.464 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m76390881009_1.jpg?1758201113'. Using substring fallback.
2025-09-19 00:44:23.465 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m73190429920_1.jpg?1758201099'. Using substring fallback.
2025-09-19 00:44:23.465 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m82603061236_1.jpg?1758201087'. Using substring fallback.
2025-09-19 00:44:23.465 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m61634441554_1.jpg?1758201068'. Using substring fallback.
2025-09-19 00:44:23.465 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m66442645445_1.jpg?1758201044'. Using substring fallback.
2025-09-19 00:44:23.465 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m92540062211_1.jpg?1758201010'. Using substring fallback.
2025-09-19 00:44:23.465 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m78127278172_1.jpg?1756645743'. Using substring fallback.
2025-09-19 00:44:23.465 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m39926221267_1.jpg?1758200985'. Using substring fallback.
2025-09-19 00:44:23.465 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m94766649936_1.jpg?1758200981'. Using substring fallback.
2025-09-19 00:44:23.465 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m93183118760_1.jpg?1758200916'. Using substring fallback.
2025-09-19 00:44:23.465 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m14511837564_1.jpg?1758200883'. Using substring fallback.
2025-09-19 00:44:23.465 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m98104008300_1.jpg?1757326150'. Using substring fallback.
2025-09-19 00:44:23.465 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m82684429600_1.jpg?1758198184'. Using substring fallback.
2025-09-19 00:44:23.465 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m23863499924_1.jpg?1758200808'. Using substring fallback.
2025-09-19 00:44:23.465 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m92403417566_1.jpg?1758186320'. Using substring fallback.
2025-09-19 00:44:23.465 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m92049734754_1.jpg?1758200794'. Using substring fallback.
2025-09-19 00:44:23.465 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m17886750568_1.jpg?1758200779'. Using substring fallback.
2025-09-19 00:44:23.465 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m23784178378_1.jpg?1758200773'. Using substring fallback.
2025-09-19 00:44:23.465 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m72784090434_1.jpg?1758194571'. Using substring fallback.
2025-09-19 00:44:23.465 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m37663880938_1.jpg?1758200742'. Using substring fallback.
2025-09-19 00:44:23.465 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m59474040811_1.jpg?1758200672'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m84159257247_1.jpg?1755243745'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m27496934754_1.jpg?1758200626'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m25451759329_1.jpg?1758200605'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m88875672381_1.jpg?1758200595'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m68011950714_1.jpg?1758200580'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m56803201086_1.jpg?1758200571'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m77192968766_1.jpg?1758200554'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m73195184184_1.jpg?1758200501'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m70875468036_1.jpg?1758200492'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m38737594165_1.jpg?1758200482'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m84150896486_1.jpg?1758200473'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m70734877714_1.jpg?1758200457'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m73990019771_1.jpg?1757084008'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m44269305733_1.jpg?1758200449'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m96892661020_1.jpg?1758200362'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m66332662626_1.jpg?1757501604'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m16939813995_1.jpg?1758200384'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m31552041190_1.jpg?1758200373'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m18780470337_1.jpg?1757743421'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m40269337526_1.jpg?1758198508'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m92170059462_1.jpg?1757757749'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m43060722991_1.jpg?1758200289'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m45599701758_1.jpg?1758200285'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m76429478424_1.jpg?1758200018'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m93067616411_1.jpg?1758200252'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m80376339719_1.jpg?1758200236'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m64572687196_1.jpg?1758198813'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m28691521322_1.jpg?1758200219'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m26700066153_1.jpg?1758015719'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m85698447265_1.jpg?1758200160'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m24028816398_1.jpg?1758193045'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m74375408533_1.jpg?1757949597'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m72131929084_1.jpg?1758200085'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m54576580598_1.jpg?1758200072'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m10243539129_1.jpg?1758200067'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m70822328776_1.jpg?1758200062'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m79626335969_1.jpg?1758200044'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m31489888268_1.jpg?1758200043'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m28653766887_1.jpg?1758199994'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m32391237353_1.jpg?1758199990'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m85794365174_1.jpg?1758199989'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m95315280729_1.jpg?1758199988'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m35271262661_1.jpg?1758199983'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m81862480042_1.jpg?1758199970'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m80191147360_1.jpg?1758199332'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m37722578325_1.jpg?1758199960'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m22075883336_1.jpg?1758199901'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m63437838836_1.jpg?1758199875'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m23824685958_1.jpg?1758199832'. Using substring fallback.
2025-09-19 00:44:23.466 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m27228708180_1.jpg?1758199830'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m42999473174_1.jpg?1758199773'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m49970164413_1.jpg?1758199758'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m55580649485_1.jpg?1758199743'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m92009885482_1.jpg?1758199742'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m27720769111_1.jpg?1758199725'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m35464243035_1.jpg?1758199460'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m73875464138_1.jpg?1758199715'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m46590918914_1.jpg?1758199682'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m74795318020_1.jpg?1758199643'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m70958970962_1.jpg?1758199634'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m56578325779_1.jpg?1758199617'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m91935815888_1.jpg?1758199615'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m56785994520_1.jpg?1758199594'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m80830546255_1.jpg?1758199566'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m34892168112_1.jpg?1757736783'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m98744990797_1.jpg?1758039419'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m75616386751_1.jpg?1758183103'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m88499373743_1.jpg?1758203502'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m21414661602_1.jpg?1758199533'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m93224111760_1.jpg?1758199508'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m30602788600_1.jpg?1758199502'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m22564006141_1.jpg?1758199495'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m14113178841_1.jpg?1758199481'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m73638888744_1.jpg?1757982689'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m81115013427_1.jpg?1758199474'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m41646180456_1.jpg?1758199421'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m18516636451_1.jpg?1758096390'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m96312501662_1.jpg?1758199419'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m63141914095_1.jpg?1758199411'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m74015487006_1.jpg?1758199393'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m70929400702_1.jpg?1758199340'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m30079886051_1.jpg?1757939834'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m31974205198_1.jpg?1758199283'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m32920770737_1.jpg?1758201443'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m40100430103_1.jpg?1758199208'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m49089637509_1.jpg?1758199151'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m38880295579_1.jpg?1758199150'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m54895718726_1.jpg?1758199125'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m38537513464_1.jpg?1757075076'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m11489061027_1.jpg?1758199121'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m44727316150_1.jpg?1758199117'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m94095081506_1.jpg?1758199092'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m19844371154_1.jpg?1758199090'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m63359014635_1.jpg?1758199084'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m17165670464_1.jpg?1758199070'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m23494706164_1.jpg?1758199061'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m87547450594_1.jpg?1758199051'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m66173124596_1.jpg?1758199029'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m62578613959_1.jpg?1758199013'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m51639467319_1.jpg?1758198984'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m44238887513_1.jpg?1758198982'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m85202688518_1.jpg?1758021834'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m72108450599_1.jpg?1758118949'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m78121121510_1.jpg?1727996670'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m97059482049_1.jpg?1758198932'. Using substring fallback.
2025-09-19 00:44:23.467 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m63845232663_1.jpg?1758198905'. Using substring fallback.
2025-09-19 00:44:23.468 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m57527370367_1.jpg?1758198865'. Using substring fallback.
2025-09-19 00:44:23.468 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m24671676795_1.jpg?1758198857'. Using substring fallback.
2025-09-19 00:44:23.468 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m19295122823_1.jpg?1758010704'. Using substring fallback.
2025-09-19 00:44:23.468 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m84324344001_1.jpg?1758198846'. Using substring fallback.
2025-09-19 00:44:23.468 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m70274554776_1.jpg?1758198810'. Using substring fallback.
2025-09-19 00:44:23.468 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m58059289899_1.jpg?1757301016'. Using substring fallback.
2025-09-19 00:44:23.468 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m67497284679_1.jpg?1758198773'. Using substring fallback.
2025-09-19 00:44:23.468 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m68632014617_1.jpg?1758198769'. Using substring fallback.
2025-09-19 00:44:23.468 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m49324233992_1.jpg?1758063983'. Using substring fallback.
2025-09-19 00:44:23.468 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m64805806362_1.jpg?1758198719'. Using substring fallback.
2025-09-19 00:44:23.468 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m21378203153_1.jpg?1758194274'. Using substring fallback.
2025-09-19 00:44:23.468 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m58032355121_1.jpg?1758198678'. Using substring fallback.
2025-09-19 00:44:23.468 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m97572122672_1.jpg?1758198645'. Using substring fallback.
2025-09-19 00:44:23.468 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m78951076420_1.jpg?1752934597'. Using substring fallback.
2025-09-19 00:44:23.468 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m31317972212_1.jpg?1758198638'. Using substring fallback.
2025-09-19 00:44:23.468 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m73622508526_1.jpg?1758198625'. Using substring fallback.
2025-09-19 00:44:23.468 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m10944050091_1.jpg?1758198621'. Using substring fallback.
2025-09-19 00:44:23.468 [Thread-3] WARN  c.m.u.database.SearchItemRepository - Could not parse thumbnail path: 'https://static.mercdn.net/thumb/item/webp/m69912073662_1.jpg?1758198619'. Using substring fallback.
2025-09-19 00:44:23.487 [Thread-3] INFO  c.m.u.database.SearchItemRepository - Successfully saved/updated 351 items to search_items table.
2025-09-19 00:44:23.796 [Thread-3] INFO  c.m.u.d.KeywordSearchItemRepository - keyword_search_items テーブルに 351 件のアイテム参照を正常に保存しました
2025-09-19 00:44:23.796 [Thread-3] INFO  c.m.u.d.KeywordSearchRepository - キーワード検索結果ID: 85 の search_items テーブルに 351 件のアイテム、keyword_search_items テーブルに 351 件の参照を正常に保存しました
2025-09-19 00:44:23.796 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - キーワード検索結果ID: 85 のデータベースに 351 件のアイテムを正常に保存しました
2025-09-19 00:44:23.796 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - キーワード検索結果ID: 85 のデータベースに 351 件のアイテムを保存しました
2025-09-19 00:44:23.796 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - レコードID: 85 のキーワード検索が正常に完了しました
2025-09-19 00:44:23.798 [Thread-3] INFO  c.m.util.SearchQueueManager - タスク完了通知を受信しました
2025-09-19 00:44:23.798 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの非同期操作が完了しました: キーワード検索: 海外
2025-09-19 00:44:23.798 [SearchQueueProcessor] INFO  c.mrcresearch.screen.keyword.Keyword - キーワード検索タスクが完了しました: 海外
2025-09-19 00:44:23.798 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクが完全に完了しました: キーワード検索: 海外
2025-09-19 00:46:55.402 [AWT-EventQueue-0] WARN  com.mrcresearch.util.ImageUtil - Image file exists but is empty: m35464243035.jpg
2025-09-19 00:46:55.405 [AWT-EventQueue-0] WARN  com.mrcresearch.util.ImageUtil - Image file exists but is empty: m52517172504.jpg
2025-09-19 00:46:55.409 [AWT-EventQueue-0] WARN  com.mrcresearch.util.ImageUtil - Image file exists but is empty: m35464243035.jpg
2025-09-19 00:46:55.409 [AWT-EventQueue-0] WARN  com.mrcresearch.util.ImageUtil - Image file exists but is empty: m52517172504.jpg
2025-09-19 00:46:55.412 [AWT-EventQueue-0] WARN  com.mrcresearch.util.ImageUtil - Image file exists but is empty: m35464243035.jpg
2025-09-19 00:46:55.412 [AWT-EventQueue-0] WARN  com.mrcresearch.util.ImageUtil - Image file exists but is empty: m52517172504.jpg
2025-09-19 00:47:02.072 [AWT-EventQueue-0] WARN  com.mrcresearch.util.ImageUtil - Image file exists but is empty: m35464243035.jpg
2025-09-19 00:47:02.072 [AWT-EventQueue-0] WARN  com.mrcresearch.util.ImageUtil - Image file exists but is empty: m52517172504.jpg
2025-09-19 00:47:13.600 [AWT-EventQueue-0] WARN  com.mrcresearch.util.ImageUtil - Image file exists but is empty: m35464243035.jpg
2025-09-19 00:47:13.600 [AWT-EventQueue-0] WARN  com.mrcresearch.util.ImageUtil - Image file exists but is empty: m52517172504.jpg
2025-09-19 00:47:16.866 [AWT-EventQueue-0] WARN  com.mrcresearch.util.ImageUtil - Image file exists but is empty: m35464243035.jpg
2025-09-19 00:47:16.866 [AWT-EventQueue-0] WARN  com.mrcresearch.util.ImageUtil - Image file exists but is empty: m52517172504.jpg
2025-09-19 00:47:22.612 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: キーワード検索: 海外 (キューサイズ: 1)
2025-09-19 00:47:22.612 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: キーワード検索: 海外 (残り: 0)
2025-09-19 00:47:22.615 [AWT-EventQueue-0] ERROR c.mrcresearch.screen.keyword.Keyword - Error getting default page value: class java.lang.String cannot be cast to class java.lang.Integer (java.lang.String and java.lang.Integer are in module java.base of loader 'bootstrap')
2025-09-19 00:47:22.616 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの実行が完了しました: キーワード検索: 海外
2025-09-19 00:47:22.616 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクに非同期操作があります。完了を待機中...
2025-09-19 00:47:22.616 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - レコードID: 86 のキーワード検索実行を開始します
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - キーワード検索結果を取得しました: KeywordSearchResultModel{id=86, keyword='海外', categories='', addedDate=Fri Sep 19 00:47:22 JST 2025, updatedDate=Fri Sep 19 00:47:22 JST 2025, status='待機中', researchStatus='リサーチ中', minPrice=300, maxPrice=9999999, pages=99, sortByNew=true, ignoreShops=true, salesStatus='販売済み', itemConditionIds='1', excludeKeyword='', colorIds='null', categoryId=''}
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - IDが以下で始まる ProgressItem を探しています: keyword_86_
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - アクティブな進捗アイテム数: 1
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: keyword_86_1758210442616
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - 一致する ProgressItem が見つかりました: keyword_86_1758210442616
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchResultModel を KeywordSearchModel に変換中...
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchResultModel を KeywordSearchModel に変換中...
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - 設定された単語: 海外
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - maxMore を設定: 98 (ページ数: 99)
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - キーワード: 海外 の検索URLを構築中
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - キーワードパラメータを追加しました: keyword=%E6%B5%B7%E5%A4%96
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - ソートパラメータを追加しました: sort=created_time&order=desc
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - アイテムタイプパラメータを追加しました: item_types=1
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - 販売ステータスパラメータを追加しました: status=sold_out
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - 最終URLパラメータ: keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - 構築された検索URL: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - baseUrl を設定: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - saveName を設定: 海外_1758210442617
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchModel の変換が正常に完了しました
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - 変換が完了しました。検索URL: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - ナビゲートする最大ページ数: 98
2025-09-19 00:47:22.617 [Thread-9] INFO  c.m.s.a.service.SearchByKeyWord - 実際のキーワード検索実行を開始します...
2025-09-19 00:56:58.418 [Thread-1] INFO  c.m.screen.main.Application - アプリケーションをシャットダウンしています...
2025-09-19 00:56:58.418 [Thread-1] INFO  c.m.util.NettyResourceManager - Nettyリソース管理をシャットダウンしています...
2025-09-19 00:56:58.418 [Thread-0] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Shutdown initiated...
2025-09-19 00:56:58.493 [Thread-0] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Shutdown completed.
2025-09-19 00:56:58.495 [LittleProxy-1-ClientToProxyWorker-4] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.disconnected(ClientToProxyConnection.java:528)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.channelInactive(ClientToProxyConnection.java:55)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.codec.MessageAggregator.channelInactive(MessageAggregator.java:438)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.codec.http.HttpContentDecoder.channelInactive(HttpContentDecoder.java:225)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:56:58.495 [LittleProxy-1-ProxyToServerWorker-7] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.serverDisconnected(ClientToProxyConnection.java:651)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.disconnected(ProxyToServerConnection.java:446)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.channelInactive(ProxyToServerConnection.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:56:58.495 [LittleProxy-1-ClientToProxyWorker-3] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.disconnected(ClientToProxyConnection.java:528)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.channelInactive(ClientToProxyConnection.java:55)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.codec.MessageAggregator.channelInactive(MessageAggregator.java:438)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.codec.http.HttpContentDecoder.channelInactive(HttpContentDecoder.java:225)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:56:58.495 [LittleProxy-1-ClientToProxyWorker-4] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.disconnected(ClientToProxyConnection.java:528)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.channelInactive(ClientToProxyConnection.java:55)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.codec.MessageAggregator.channelInactive(MessageAggregator.java:438)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.codec.http.HttpContentDecoder.channelInactive(HttpContentDecoder.java:225)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:56:58.495 [LittleProxy-1-ProxyToServerWorker-7] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.serverDisconnected(ClientToProxyConnection.java:651)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.disconnected(ProxyToServerConnection.java:446)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.channelInactive(ProxyToServerConnection.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:56:58.496 [LittleProxy-1-ClientToProxyWorker-3] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.disconnected(ClientToProxyConnection.java:528)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.channelInactive(ClientToProxyConnection.java:55)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.codec.MessageAggregator.channelInactive(MessageAggregator.java:438)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.codec.http.HttpContentDecoder.channelInactive(HttpContentDecoder.java:225)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:56:58.496 [LittleProxy-1-ClientToProxyWorker-4] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.disconnected(ClientToProxyConnection.java:528)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.channelInactive(ClientToProxyConnection.java:55)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.codec.MessageAggregator.channelInactive(MessageAggregator.java:438)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.codec.http.HttpContentDecoder.channelInactive(HttpContentDecoder.java:225)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:56:58.496 [LittleProxy-1-ProxyToServerWorker-7] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.serverDisconnected(ClientToProxyConnection.java:651)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.disconnected(ProxyToServerConnection.java:446)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.channelInactive(ProxyToServerConnection.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:56:58.499 [LittleProxy-1-ProxyToServerWorker-7] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.serverDisconnected(ClientToProxyConnection.java:651)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.disconnected(ProxyToServerConnection.java:446)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.channelInactive(ProxyToServerConnection.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:56:58.499 [LittleProxy-1-ProxyToServerWorker-7] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.serverDisconnected(ClientToProxyConnection.java:651)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.disconnected(ProxyToServerConnection.java:446)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.channelInactive(ProxyToServerConnection.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:56:58.500 [LittleProxy-1-ProxyToServerWorker-7] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.serverDisconnected(ClientToProxyConnection.java:651)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.disconnected(ProxyToServerConnection.java:446)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.channelInactive(ProxyToServerConnection.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:56:58.500 [LittleProxy-1-ProxyToServerWorker-7] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.serverDisconnected(ClientToProxyConnection.java:651)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.disconnected(ProxyToServerConnection.java:446)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.channelInactive(ProxyToServerConnection.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:56:58.500 [LittleProxy-1-ProxyToServerWorker-7] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.serverDisconnected(ClientToProxyConnection.java:651)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.disconnected(ProxyToServerConnection.java:446)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.channelInactive(ProxyToServerConnection.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:56:58.500 [LittleProxy-1-ProxyToServerWorker-7] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.serverDisconnected(ClientToProxyConnection.java:651)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.disconnected(ProxyToServerConnection.java:446)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.channelInactive(ProxyToServerConnection.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:56:58.500 [LittleProxy-1-ProxyToServerWorker-7] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.serverDisconnected(ClientToProxyConnection.java:651)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.disconnected(ProxyToServerConnection.java:446)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.channelInactive(ProxyToServerConnection.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:56:58.501 [LittleProxy-1-ProxyToServerWorker-7] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.serverDisconnected(ClientToProxyConnection.java:651)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.disconnected(ProxyToServerConnection.java:446)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.channelInactive(ProxyToServerConnection.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:56:58.502 [LittleProxy-1-ProxyToServerWorker-7] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.serverDisconnected(ClientToProxyConnection.java:651)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.disconnected(ProxyToServerConnection.java:446)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.channelInactive(ProxyToServerConnection.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:56:58.502 [LittleProxy-1-ProxyToServerWorker-7] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.serverDisconnected(ClientToProxyConnection.java:651)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.disconnected(ProxyToServerConnection.java:446)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.channelInactive(ProxyToServerConnection.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 00:56:58.651 [Thread-1] INFO  c.m.util.NettyResourceManager - Nettyリソース管理のシャットダウンが完了しました
2025-09-19 00:57:02.332 [main] INFO  c.m.util.NettyResourceManager - Nettyリソース管理が初期化されました (リーク検出レベル: SIMPLE)
2025-09-19 00:57:02.361 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Starting...
2025-09-19 00:57:02.526 [main] INFO  com.zaxxer.hikari.pool.HikariPool - MercariResearchDB-Pool - Added connection org.sqlite.jdbc4.JDBC4Connection@6d763516
2025-09-19 00:57:02.527 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Start completed.
2025-09-19 00:57:02.664 [main] INFO  c.m.u.database.SellerMigrationUtil - セラーデータ移行は不要です。
2025-09-19 00:57:03.422 [AWT-EventQueue-0] INFO  c.m.screen.main.Application - TLSバージョンの設定が完了しました
2025-09-19 00:57:03.598 [AWT-EventQueue-0] INFO  com.mrcresearch.screen.main.Main - メインパネルを初期化しています
2025-09-19 00:57:06.648 [AWT-EventQueue-0] INFO  com.mrcresearch.util.ImageUtil - WebPデコーダが正常に登録されました
2025-09-19 00:57:08.137 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 自動更新タイマーを停止しました - 進行中のリサーチはありません
2025-09-19 00:57:08.407 [SwingWorker-pool-1-thread-3] WARN  i.n.util.internal.SystemPropertyUtil - Unable to parse the long integer system property 'io.netty.maxDirectMemory':128m - using the default value: -1
2025-09-19 00:57:09.034 [SwingWorker-pool-1-thread-4] INFO  c.mrcresearch.service.common.Version - 現在のバージョン：4.0.24
2025-09-19 00:57:13.470 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: キーワード検索: 海外 (残り: 0)
2025-09-19 00:57:13.470 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: キーワード検索: 海外 (キューサイズ: 1)
2025-09-19 00:57:13.479 [AWT-EventQueue-0] ERROR c.mrcresearch.screen.keyword.Keyword - Error getting default page value: class java.lang.String cannot be cast to class java.lang.Integer (java.lang.String and java.lang.Integer are in module java.base of loader 'bootstrap')
2025-09-19 00:57:13.483 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの実行が完了しました: キーワード検索: 海外
2025-09-19 00:57:13.483 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクに非同期操作があります。完了を待機中...
2025-09-19 00:57:13.500 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - レコードID: 87 のキーワード検索実行を開始します
2025-09-19 00:57:13.511 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - キーワード検索結果を取得しました: KeywordSearchResultModel{id=87, keyword='海外', categories='', addedDate=Fri Sep 19 00:57:13 JST 2025, updatedDate=Fri Sep 19 00:57:13 JST 2025, status='待機中', researchStatus='リサーチ中', minPrice=300, maxPrice=9999999, pages=99, sortByNew=true, ignoreShops=true, salesStatus='販売済み', itemConditionIds='1', excludeKeyword='', colorIds='null', categoryId=''}
2025-09-19 00:57:13.511 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - IDが以下で始まる ProgressItem を探しています: keyword_87_
2025-09-19 00:57:13.511 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - アクティブな進捗アイテム数: 1
2025-09-19 00:57:13.511 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: keyword_87_1758211033480
2025-09-19 00:57:13.511 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 一致する ProgressItem が見つかりました: keyword_87_1758211033480
2025-09-19 00:57:13.511 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchResultModel を KeywordSearchModel に変換中...
2025-09-19 00:57:13.511 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchResultModel を KeywordSearchModel に変換中...
2025-09-19 00:57:13.511 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 設定された単語: 海外
2025-09-19 00:57:13.511 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - maxMore を設定: 98 (ページ数: 99)
2025-09-19 00:57:13.511 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - キーワード: 海外 の検索URLを構築中
2025-09-19 00:57:13.512 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - キーワードパラメータを追加しました: keyword=%E6%B5%B7%E5%A4%96
2025-09-19 00:57:13.512 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - ソートパラメータを追加しました: sort=created_time&order=desc
2025-09-19 00:57:13.512 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - アイテムタイプパラメータを追加しました: item_types=1
2025-09-19 00:57:13.512 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 販売ステータスパラメータを追加しました: status=sold_out
2025-09-19 00:57:13.512 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 最終URLパラメータ: keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:57:13.512 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 構築された検索URL: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:57:13.512 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - baseUrl を設定: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:57:13.514 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - saveName を設定: 海外_1758211033512
2025-09-19 00:57:13.515 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchModel の変換が正常に完了しました
2025-09-19 00:57:13.515 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 変換が完了しました。検索URL: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:57:13.515 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - ナビゲートする最大ページ数: 98
2025-09-19 00:57:13.515 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 実際のキーワード検索実行を開始します...
2025-09-19 00:59:14.462 [Thread-1] INFO  c.m.screen.main.Application - アプリケーションをシャットダウンしています...
2025-09-19 00:59:14.462 [Thread-1] INFO  c.m.util.NettyResourceManager - Nettyリソース管理をシャットダウンしています...
2025-09-19 00:59:14.496 [Thread-0] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Shutdown initiated...
2025-09-19 00:59:14.504 [Thread-0] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Shutdown completed.
2025-09-19 00:59:14.633 [Thread-1] INFO  c.m.util.NettyResourceManager - Nettyリソース管理のシャットダウンが完了しました
2025-09-19 00:59:17.958 [main] INFO  c.m.util.NettyResourceManager - Nettyリソース管理が初期化されました (リーク検出レベル: SIMPLE)
2025-09-19 00:59:17.987 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Starting...
2025-09-19 00:59:18.157 [main] INFO  com.zaxxer.hikari.pool.HikariPool - MercariResearchDB-Pool - Added connection org.sqlite.jdbc4.JDBC4Connection@6d763516
2025-09-19 00:59:18.158 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Start completed.
2025-09-19 00:59:18.295 [main] INFO  c.m.u.database.SellerMigrationUtil - セラーデータ移行は不要です。
2025-09-19 00:59:18.845 [AWT-EventQueue-0] INFO  c.m.screen.main.Application - TLSバージョンの設定が完了しました
2025-09-19 00:59:18.996 [AWT-EventQueue-0] INFO  com.mrcresearch.screen.main.Main - メインパネルを初期化しています
2025-09-19 00:59:21.858 [AWT-EventQueue-0] INFO  com.mrcresearch.util.ImageUtil - WebPデコーダが正常に登録されました
2025-09-19 00:59:22.213 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 自動更新タイマーを停止しました - 進行中のリサーチはありません
2025-09-19 00:59:22.573 [SwingWorker-pool-1-thread-3] WARN  i.n.util.internal.SystemPropertyUtil - Unable to parse the long integer system property 'io.netty.maxDirectMemory':128m - using the default value: -1
2025-09-19 00:59:23.202 [SwingWorker-pool-1-thread-4] INFO  c.mrcresearch.service.common.Version - 現在のバージョン：4.0.24
2025-09-19 00:59:33.529 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: キーワード検索: 海外 (残り: 0)
2025-09-19 00:59:33.529 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: キーワード検索: 海外 (キューサイズ: 1)
2025-09-19 00:59:33.541 [AWT-EventQueue-0] ERROR c.mrcresearch.screen.keyword.Keyword - Error getting default page value: class java.lang.String cannot be cast to class java.lang.Integer (java.lang.String and java.lang.Integer are in module java.base of loader 'bootstrap')
2025-09-19 00:59:33.546 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの実行が完了しました: キーワード検索: 海外
2025-09-19 00:59:33.546 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクに非同期操作があります。完了を待機中...
2025-09-19 00:59:33.562 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - レコードID: 88 のキーワード検索実行を開始します
2025-09-19 00:59:33.572 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - キーワード検索結果を取得しました: KeywordSearchResultModel{id=88, keyword='海外', categories='', addedDate=Fri Sep 19 00:59:33 JST 2025, updatedDate=Fri Sep 19 00:59:33 JST 2025, status='待機中', researchStatus='リサーチ中', minPrice=300, maxPrice=9999999, pages=99, sortByNew=true, ignoreShops=true, salesStatus='販売済み', itemConditionIds='1', excludeKeyword='', colorIds='null', categoryId=''}
2025-09-19 00:59:33.572 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - IDが以下で始まる ProgressItem を探しています: keyword_88_
2025-09-19 00:59:33.572 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - アクティブな進捗アイテム数: 1
2025-09-19 00:59:33.573 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: keyword_88_1758211173543
2025-09-19 00:59:33.573 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 一致する ProgressItem が見つかりました: keyword_88_1758211173543
2025-09-19 00:59:33.573 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchResultModel を KeywordSearchModel に変換中...
2025-09-19 00:59:33.573 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchResultModel を KeywordSearchModel に変換中...
2025-09-19 00:59:33.573 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 設定された単語: 海外
2025-09-19 00:59:33.573 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - maxMore を設定: 98 (ページ数: 99)
2025-09-19 00:59:33.573 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - キーワード: 海外 の検索URLを構築中
2025-09-19 00:59:33.573 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - キーワードパラメータを追加しました: keyword=%E6%B5%B7%E5%A4%96
2025-09-19 00:59:33.574 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - ソートパラメータを追加しました: sort=created_time&order=desc
2025-09-19 00:59:33.574 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - アイテムタイプパラメータを追加しました: item_types=1
2025-09-19 00:59:33.574 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 販売ステータスパラメータを追加しました: status=sold_out
2025-09-19 00:59:33.574 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 最終URLパラメータ: keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:59:33.574 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 構築された検索URL: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:59:33.574 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - baseUrl を設定: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:59:33.576 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - saveName を設定: 海外_1758211173574
2025-09-19 00:59:33.576 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchModel の変換が正常に完了しました
2025-09-19 00:59:33.576 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 変換が完了しました。検索URL: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 00:59:33.576 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - ナビゲートする最大ページ数: 98
2025-09-19 00:59:33.576 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 実際のキーワード検索実行を開始します...
2025-09-19 01:00:06.189 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - キーワード検索を開始します: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 01:03:14.041 [Thread-1] INFO  c.m.screen.main.Application - アプリケーションをシャットダウンしています...
2025-09-19 01:03:14.041 [Thread-1] INFO  c.m.util.NettyResourceManager - Nettyリソース管理をシャットダウンしています...
2025-09-19 01:03:14.085 [Thread-0] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Shutdown initiated...
2025-09-19 01:03:14.096 [Thread-0] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Shutdown completed.
2025-09-19 01:03:14.103 [LittleProxy-0-ProxyToServerWorker-6] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.serverDisconnected(ClientToProxyConnection.java:651)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.disconnected(ProxyToServerConnection.java:446)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.channelInactive(ProxyToServerConnection.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 01:03:14.103 [LittleProxy-0-ProxyToServerWorker-6] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.serverDisconnected(ClientToProxyConnection.java:651)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.disconnected(ProxyToServerConnection.java:446)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.channelInactive(ProxyToServerConnection.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 01:03:14.103 [LittleProxy-0-ProxyToServerWorker-6] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.serverDisconnected(ClientToProxyConnection.java:651)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.disconnected(ProxyToServerConnection.java:446)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.channelInactive(ProxyToServerConnection.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 01:03:14.105 [LittleProxy-0-ProxyToServerWorker-6] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.serverDisconnected(ClientToProxyConnection.java:651)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.disconnected(ProxyToServerConnection.java:446)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.channelInactive(ProxyToServerConnection.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 01:03:14.109 [LittleProxy-0-ProxyToServerWorker-6] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.serverDisconnected(ClientToProxyConnection.java:651)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.disconnected(ProxyToServerConnection.java:446)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.channelInactive(ProxyToServerConnection.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 01:03:14.110 [LittleProxy-0-ProxyToServerWorker-6] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.serverDisconnected(ClientToProxyConnection.java:651)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.disconnected(ProxyToServerConnection.java:446)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.channelInactive(ProxyToServerConnection.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 01:03:14.110 [LittleProxy-0-ProxyToServerWorker-6] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:926)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:828)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:818)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:498)
	at io.netty.util.concurrent.DefaultPromise.addListener(DefaultPromise.java:183)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:95)
	at io.netty.channel.DefaultChannelPromise.addListener(DefaultChannelPromise.java:30)
	at org.littleshoot.proxy.impl.ProxyConnection.disconnect(ProxyConnection.java:473)
	at org.littleshoot.proxy.impl.ClientToProxyConnection.serverDisconnected(ClientToProxyConnection.java:651)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.disconnected(ProxyToServerConnection.java:446)
	at org.littleshoot.proxy.impl.ProxyConnection.channelInactive(ProxyConnection.java:616)
	at org.littleshoot.proxy.impl.ProxyToServerConnection.channelInactive(ProxyToServerConnection.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.timeout.IdleStateHandler.channelInactive(IdleStateHandler.java:277)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:389)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:354)
	at io.netty.handler.ssl.SslHandler.channelInactive(SslHandler.java:1106)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:818)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 01:03:14.229 [Thread-1] INFO  c.m.util.NettyResourceManager - Nettyリソース管理のシャットダウンが完了しました
2025-09-19 01:03:17.928 [main] INFO  c.m.util.NettyResourceManager - Nettyリソース管理が初期化されました (リーク検出レベル: SIMPLE)
2025-09-19 01:03:17.960 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Starting...
2025-09-19 01:03:18.160 [main] INFO  com.zaxxer.hikari.pool.HikariPool - MercariResearchDB-Pool - Added connection org.sqlite.jdbc4.JDBC4Connection@6d763516
2025-09-19 01:03:18.162 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Start completed.
2025-09-19 01:03:18.308 [main] INFO  c.m.u.database.SellerMigrationUtil - セラーデータ移行は不要です。
2025-09-19 01:03:18.944 [AWT-EventQueue-0] INFO  c.m.screen.main.Application - TLSバージョンの設定が完了しました
2025-09-19 01:03:19.104 [AWT-EventQueue-0] INFO  com.mrcresearch.screen.main.Main - メインパネルを初期化しています
2025-09-19 01:03:22.250 [AWT-EventQueue-0] INFO  com.mrcresearch.util.ImageUtil - WebPデコーダが正常に登録されました
2025-09-19 01:03:23.717 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 自動更新タイマーを停止しました - 進行中のリサーチはありません
2025-09-19 01:03:23.920 [SwingWorker-pool-1-thread-3] WARN  i.n.util.internal.SystemPropertyUtil - Unable to parse the long integer system property 'io.netty.maxDirectMemory':128m - using the default value: -1
2025-09-19 01:03:24.907 [SwingWorker-pool-1-thread-4] INFO  c.mrcresearch.service.common.Version - 現在のバージョン：4.0.24
2025-09-19 01:04:08.990 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: キーワード検索: 海外 (キューサイズ: 1)
2025-09-19 01:04:08.990 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: キーワード検索: 海外 (残り: 0)
2025-09-19 01:04:09.000 [AWT-EventQueue-0] ERROR c.mrcresearch.screen.keyword.Keyword - Error getting default page value: class java.lang.String cannot be cast to class java.lang.Integer (java.lang.String and java.lang.Integer are in module java.base of loader 'bootstrap')
2025-09-19 01:04:09.006 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの実行が完了しました: キーワード検索: 海外
2025-09-19 01:04:09.006 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクに非同期操作があります。完了を待機中...
2025-09-19 01:04:09.041 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - レコードID: 89 のキーワード検索実行を開始します
2025-09-19 01:04:09.051 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - キーワード検索結果を取得しました: KeywordSearchResultModel{id=89, keyword='海外', categories='', addedDate=Fri Sep 19 01:04:08 JST 2025, updatedDate=Fri Sep 19 01:04:08 JST 2025, status='待機中', researchStatus='リサーチ中', minPrice=300, maxPrice=9999999, pages=99, sortByNew=true, ignoreShops=true, salesStatus='販売済み', itemConditionIds='1', excludeKeyword='', colorIds='null', categoryId=''}
2025-09-19 01:04:09.051 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - IDが以下で始まる ProgressItem を探しています: keyword_89_
2025-09-19 01:04:09.052 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - アクティブな進捗アイテム数: 1
2025-09-19 01:04:09.052 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: keyword_89_1758211449003
2025-09-19 01:04:09.052 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 一致する ProgressItem が見つかりました: keyword_89_1758211449003
2025-09-19 01:04:09.054 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchResultModel を KeywordSearchModel に変換中...
2025-09-19 01:04:09.056 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchResultModel を KeywordSearchModel に変換中...
2025-09-19 01:04:09.056 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 設定された単語: 海外
2025-09-19 01:04:09.056 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - maxMore を設定: 98 (ページ数: 99)
2025-09-19 01:04:09.056 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - キーワード: 海外 の検索URLを構築中
2025-09-19 01:04:09.056 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - キーワードパラメータを追加しました: keyword=%E6%B5%B7%E5%A4%96
2025-09-19 01:04:09.056 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - ソートパラメータを追加しました: sort=created_time&order=desc
2025-09-19 01:04:09.056 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - アイテムタイプパラメータを追加しました: item_types=1
2025-09-19 01:04:09.056 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 販売ステータスパラメータを追加しました: status=sold_out
2025-09-19 01:04:09.056 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 最終URLパラメータ: keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 01:04:09.056 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 構築された検索URL: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 01:04:09.056 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - baseUrl を設定: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 01:04:09.058 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - saveName を設定: 海外_1758211449056
2025-09-19 01:04:09.058 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchModel の変換が正常に完了しました
2025-09-19 01:04:09.058 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 変換が完了しました。検索URL: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 01:04:09.058 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - ナビゲートする最大ページ数: 98
2025-09-19 01:04:09.058 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 実際のキーワード検索実行を開始します...
2025-09-19 01:04:39.787 [Thread-1] INFO  c.m.screen.main.Application - アプリケーションをシャットダウンしています...
2025-09-19 01:04:39.787 [Thread-1] INFO  c.m.util.NettyResourceManager - Nettyリソース管理をシャットダウンしています...
2025-09-19 01:04:39.804 [Thread-0] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Shutdown initiated...
2025-09-19 01:04:39.813 [Thread-0] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Shutdown completed.
2025-09-19 01:04:39.933 [Thread-1] INFO  c.m.util.NettyResourceManager - Nettyリソース管理のシャットダウンが完了しました
2025-09-19 23:59:01.417 [main] INFO  c.m.util.NettyResourceManager - Nettyリソース管理が初期化されました (リーク検出レベル: SIMPLE)
2025-09-19 23:59:01.488 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Starting...
2025-09-19 23:59:01.733 [main] INFO  com.zaxxer.hikari.pool.HikariPool - MercariResearchDB-Pool - Added connection org.sqlite.jdbc4.JDBC4Connection@52bf72b5
2025-09-19 23:59:01.735 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Start completed.
2025-09-19 23:59:01.880 [main] INFO  c.m.u.database.SellerMigrationUtil - セラーデータ移行は不要です。
2025-09-19 23:59:02.639 [AWT-EventQueue-0] INFO  c.m.screen.main.Application - TLSバージョンの設定が完了しました
2025-09-19 23:59:02.925 [AWT-EventQueue-0] INFO  com.mrcresearch.screen.main.Main - メインパネルを初期化しています
2025-09-19 23:59:05.898 [AWT-EventQueue-0] INFO  com.mrcresearch.util.ImageUtil - WebPデコーダが正常に登録されました
2025-09-19 23:59:08.148 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 自動更新タイマーを停止しました - 進行中のリサーチはありません
2025-09-19 23:59:08.404 [SwingWorker-pool-1-thread-3] WARN  i.n.util.internal.SystemPropertyUtil - Unable to parse the long integer system property 'io.netty.maxDirectMemory':128m - using the default value: -1
2025-09-19 23:59:09.049 [SwingWorker-pool-1-thread-4] INFO  c.mrcresearch.service.common.Version - 現在のバージョン：4.0.24
2025-09-19 23:59:17.965 [AWT-EventQueue-0] INFO  c.m.screen.settings.Settings - Updated keyword screen with new default settings
2025-09-19 23:59:19.606 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: キーワード検索: 海外 (残り: 0)
2025-09-19 23:59:19.606 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: キーワード検索: 海外 (キューサイズ: 1)
2025-09-19 23:59:19.617 [AWT-EventQueue-0] ERROR c.mrcresearch.screen.keyword.Keyword - Error getting default page value: class java.lang.String cannot be cast to class java.lang.Integer (java.lang.String and java.lang.Integer are in module java.base of loader 'bootstrap')
2025-09-19 23:59:19.626 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの実行が完了しました: キーワード検索: 海外
2025-09-19 23:59:19.626 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクに非同期操作があります。完了を待機中...
2025-09-19 23:59:19.665 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - レコードID: 90 のキーワード検索実行を開始します
2025-09-19 23:59:19.672 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - キーワード検索結果を取得しました: KeywordSearchResultModel{id=90, keyword='海外', categories='', addedDate=Fri Sep 19 23:59:19 JST 2025, updatedDate=Fri Sep 19 23:59:19 JST 2025, status='待機中', researchStatus='リサーチ中', minPrice=300, maxPrice=9999999, pages=10, sortByNew=true, ignoreShops=true, salesStatus='販売済み', itemConditionIds='1', excludeKeyword='', colorIds='null', categoryId=''}
2025-09-19 23:59:19.672 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - IDが以下で始まる ProgressItem を探しています: keyword_90_
2025-09-19 23:59:19.672 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - アクティブな進捗アイテム数: 1
2025-09-19 23:59:19.673 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: keyword_90_1758293959620
2025-09-19 23:59:19.673 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 一致する ProgressItem が見つかりました: keyword_90_1758293959620
2025-09-19 23:59:19.673 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchResultModel を KeywordSearchModel に変換中...
2025-09-19 23:59:19.673 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchResultModel を KeywordSearchModel に変換中...
2025-09-19 23:59:19.673 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 設定された単語: 海外
2025-09-19 23:59:19.673 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - maxMore を設定: 9 (ページ数: 10)
2025-09-19 23:59:19.673 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - キーワード: 海外 の検索URLを構築中
2025-09-19 23:59:19.673 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - キーワードパラメータを追加しました: keyword=%E6%B5%B7%E5%A4%96
2025-09-19 23:59:19.673 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - ソートパラメータを追加しました: sort=created_time&order=desc
2025-09-19 23:59:19.673 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - アイテムタイプパラメータを追加しました: item_types=1
2025-09-19 23:59:19.673 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 販売ステータスパラメータを追加しました: status=sold_out
2025-09-19 23:59:19.673 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 最終URLパラメータ: keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 23:59:19.675 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 構築された検索URL: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 23:59:19.675 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - baseUrl を設定: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 23:59:19.675 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - saveName を設定: 海外_1758293959675
2025-09-19 23:59:19.675 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchModel の変換が正常に完了しました
2025-09-19 23:59:19.675 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 変換が完了しました。検索URL: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-19 23:59:19.675 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - ナビゲートする最大ページ数: 9
2025-09-19 23:59:19.676 [Thread-3] INFO  c.m.s.a.service.SearchByKeyWord - 実際のキーワード検索実行を開始します...
2025-09-19 23:59:53.627 [Thread-3] ERROR c.m.s.a.service.SearchByKeyWord - General error executing keyword search for record ID 90: Cannot invoke "org.openqa.selenium.WebDriver.manage()" because "this.driver" is null
java.lang.NullPointerException: Cannot invoke "org.openqa.selenium.WebDriver.manage()" because "this.driver" is null
	at com.mrcresearch.service.tools.DriverTool.startDriver(DriverTool.java:124)
	at com.mrcresearch.service.analyze.service.SearchByKeyWord.execute(SearchByKeyWord.java:61)
	at com.mrcresearch.service.analyze.service.SearchByKeyWord.execute(SearchByKeyWord.java:257)
	at com.mrcresearch.screen.keyword.Keyword.lambda$startAsyncStatusUpdateWithProgressAndCompletion$10(Keyword.java:673)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-19 23:59:53.628 [Thread-3] ERROR c.m.s.a.service.SearchByKeyWord - Error class: NullPointerException
2025-09-19 23:59:53.632 [Thread-3] INFO  c.m.util.SearchQueueManager - タスク完了通知を受信しました
2025-09-19 23:59:53.633 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの非同期操作が完了しました: キーワード検索: 海外
2025-09-19 23:59:53.633 [SearchQueueProcessor] INFO  c.mrcresearch.screen.keyword.Keyword - キーワード検索タスクが完了しました: 海外
2025-09-19 23:59:53.633 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクが完全に完了しました: キーワード検索: 海外
