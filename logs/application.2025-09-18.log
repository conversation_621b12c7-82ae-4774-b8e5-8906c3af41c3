2025-09-18 21:53:51.646 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Starting...
2025-09-18 21:53:51.769 [main] INFO  com.zaxxer.hikari.pool.HikariPool - MercariResearchDB-Pool - Added connection org.sqlite.jdbc4.JDBC4Connection@6973bf95
2025-09-18 21:53:51.771 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Start completed.
2025-09-18 21:53:51.918 [main] INFO  c.m.u.database.SellerMigrationUtil - セラーデータ移行は不要です。
2025-09-18 21:53:52.636 [AWT-EventQueue-0] INFO  c.m.screen.main.Application - TLSバージョンの設定が完了しました
2025-09-18 21:53:52.971 [AWT-EventQueue-0] INFO  com.mrcresearch.screen.main.Main - メインパネルを初期化しています
2025-09-18 21:53:55.645 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 自動更新タイマーを停止しました - 進行中のリサーチはありません
2025-09-18 21:55:50.538 [SwingWorker-pool-2-thread-4] INFO  c.mrcresearch.service.common.Version - 現在のバージョン：4.0.24
2025-09-18 21:56:00.680 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: キーワード検索: 海外製 (キューサイズ: 1)
2025-09-18 21:56:00.680 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: キーワード検索: 海外製 (残り: 0)
2025-09-18 21:56:00.706 [AWT-EventQueue-0] ERROR c.mrcresearch.screen.keyword.Keyword - Error getting default page value: class java.lang.String cannot be cast to class java.lang.Integer (java.lang.String and java.lang.Integer are in module java.base of loader 'bootstrap')
2025-09-18 21:56:00.711 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの実行が完了しました: キーワード検索: 海外製
2025-09-18 21:56:00.711 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクに非同期操作があります。完了を待機中...
2025-09-18 21:56:00.754 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - レコードID: 81 のキーワード検索実行を開始します
2025-09-18 21:56:00.765 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - キーワード検索結果を取得しました: KeywordSearchResultModel{id=81, keyword='海外製', categories='', addedDate=Thu Sep 18 21:56:00 JST 2025, updatedDate=Thu Sep 18 21:56:00 JST 2025, status='待機中', researchStatus='リサーチ中', minPrice=300, maxPrice=9999999, pages=10, sortByNew=true, ignoreShops=true, salesStatus='販売済み', itemConditionIds='1', excludeKeyword='', colorIds='null', categoryId=''}
2025-09-18 21:56:00.765 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - IDが以下で始まる ProgressItem を探しています: keyword_81_
2025-09-18 21:56:00.765 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - アクティブな進捗アイテム数: 1
2025-09-18 21:56:00.765 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: keyword_81_1758200160707
2025-09-18 21:56:00.765 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - 一致する ProgressItem が見つかりました: keyword_81_1758200160707
2025-09-18 21:56:00.766 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchResultModel を KeywordSearchModel に変換中...
2025-09-18 21:56:00.766 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchResultModel を KeywordSearchModel に変換中...
2025-09-18 21:56:00.766 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - 設定された単語: 海外製
2025-09-18 21:56:00.766 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - maxMore を設定: 9 (ページ数: 10)
2025-09-18 21:56:00.766 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - キーワード: 海外製 の検索URLを構築中
2025-09-18 21:56:00.766 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - キーワードパラメータを追加しました: keyword=%E6%B5%B7%E5%A4%96%E8%A3%BD
2025-09-18 21:56:00.766 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - ソートパラメータを追加しました: sort=created_time&order=desc
2025-09-18 21:56:00.768 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - アイテムタイプパラメータを追加しました: item_types=1
2025-09-18 21:56:00.768 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - 販売ステータスパラメータを追加しました: status=sold_out
2025-09-18 21:56:00.768 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - 最終URLパラメータ: keyword=%E6%B5%B7%E5%A4%96%E8%A3%BD&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-18 21:56:00.768 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - 構築された検索URL: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96%E8%A3%BD&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-18 21:56:00.768 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - baseUrl を設定: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96%E8%A3%BD&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-18 21:56:00.768 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - saveName を設定: 海外製_1758200160768
2025-09-18 21:56:00.768 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchModel の変換が正常に完了しました
2025-09-18 21:56:00.768 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - 変換が完了しました。検索URL: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96%E8%A3%BD&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-18 21:56:00.768 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - ナビゲートする最大ページ数: 9
2025-09-18 21:56:00.768 [Thread-2] INFO  c.m.s.a.service.SearchByKeyWord - 実際のキーワード検索実行を開始します...
2025-09-18 21:56:04.524 [AWT-EventQueue-0] INFO  com.mrcresearch.util.ImageUtil - WebPデコーダが正常に登録されました
2025-09-18 21:56:12.307 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのため自動更新タイマーを開始しました
2025-09-18 21:56:24.213 [AWT-EventQueue-0] INFO  c.m.util.database.SellerRepository - セラー: 239833043 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-18 21:56:24.219 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: セラーリサーチ: 239833043 (キューサイズ: 1)
2025-09-18 21:56:27.314 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 21:56:34.663 [Thread-2] ERROR c.m.s.a.service.SearchByKeyWord - General error executing keyword search for record ID 81: Cannot invoke "org.openqa.selenium.WebDriver.manage()" because "this.driver" is null
java.lang.NullPointerException: Cannot invoke "org.openqa.selenium.WebDriver.manage()" because "this.driver" is null
	at com.mrcresearch.service.tools.DriverTool.startDriver(DriverTool.java:115)
	at com.mrcresearch.service.analyze.service.SearchByKeyWord.execute(SearchByKeyWord.java:61)
	at com.mrcresearch.service.analyze.service.SearchByKeyWord.execute(SearchByKeyWord.java:240)
	at com.mrcresearch.screen.keyword.Keyword.lambda$startAsyncStatusUpdateWithProgressAndCompletion$10(Keyword.java:673)
	at java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-18 21:56:34.663 [Thread-2] ERROR c.m.s.a.service.SearchByKeyWord - Error class: NullPointerException
2025-09-18 21:56:34.664 [Thread-2] INFO  c.m.util.SearchQueueManager - タスク完了通知を受信しました
2025-09-18 21:56:34.665 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの非同期操作が完了しました: キーワード検索: 海外製
2025-09-18 21:56:34.666 [SearchQueueProcessor] INFO  c.mrcresearch.screen.keyword.Keyword - キーワード検索タスクが完了しました: 海外製
2025-09-18 21:56:34.666 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクが完全に完了しました: キーワード検索: 海外製
2025-09-18 21:56:34.666 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: セラーリサーチ: 239833043 (残り: 0)
2025-09-18 21:56:34.667 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 239833043 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-18 21:56:34.668 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 239833043 のセラーのステータスを 'リサーチ中' に正常に更新しました
2025-09-18 21:56:42.320 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 21:56:57.328 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 21:57:07.122 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 239833043 のセラー検索実行を開始します
2025-09-18 21:57:12.329 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 21:57:19.697 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:57:24.284 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:57:27.343 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 21:57:28.845 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:57:33.429 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:57:37.992 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:57:42.351 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 21:57:42.566 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:57:47.145 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:57:51.727 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:57:56.301 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:57:57.356 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 21:58:00.876 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:58:05.444 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:58:09.998 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:58:12.366 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 21:58:14.594 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:58:19.171 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:58:23.737 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:58:27.373 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 21:58:28.291 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:58:32.856 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:58:37.421 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:58:42.006 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:58:42.376 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 21:58:46.565 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:58:51.126 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:58:55.684 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:58:57.390 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 21:59:00.251 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:59:04.811 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:59:09.370 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:59:12.392 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 21:59:13.934 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:59:18.492 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:59:23.043 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:59:27.401 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 21:59:27.587 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:59:32.144 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:59:36.708 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:59:41.272 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:59:42.412 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 21:59:45.838 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:59:50.401 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:59:54.950 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 21:59:57.414 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 21:59:59.507 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:00:04.060 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:00:08.613 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:00:12.425 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:00:13.176 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:00:17.743 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:00:22.302 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:00:26.860 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:00:27.432 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:00:31.426 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:00:35.981 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:00:40.542 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:00:42.433 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:00:45.100 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:00:49.667 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:00:54.227 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:00:57.446 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:00:58.792 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:01:03.344 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:01:07.898 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:01:12.457 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:01:12.458 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:01:17.008 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:01:21.570 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:01:26.123 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:01:27.458 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:01:30.686 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:01:35.246 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:01:39.796 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:01:42.466 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:01:44.345 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:01:48.916 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:01:53.493 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:01:57.467 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:01:58.061 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:02:02.625 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:02:07.192 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:02:11.752 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:02:12.475 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:02:16.311 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:02:20.861 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:02:25.420 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:02:27.485 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:02:29.971 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:02:34.519 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:02:39.070 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:02:42.487 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:02:43.628 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:02:48.174 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:02:52.726 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:02:57.292 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:02:57.491 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:03:01.857 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:03:06.419 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:03:10.978 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:03:12.503 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:03:15.535 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:03:20.101 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:03:24.661 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:03:27.509 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:03:29.208 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:03:33.772 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:03:38.332 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:03:42.513 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:03:42.899 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:03:47.474 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:03:52.036 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:03:56.590 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:03:57.520 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:04:01.160 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:04:05.707 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:04:10.269 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:04:12.523 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:04:14.816 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:04:19.383 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:04:23.954 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:04:27.526 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:04:28.498 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:04:33.064 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:04:37.619 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:04:42.174 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:04:42.527 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:04:46.730 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:04:51.296 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:04:55.861 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:04:57.527 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:05:00.426 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:05:04.991 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:05:09.540 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:05:12.541 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:05:14.092 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:05:18.656 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:05:23.208 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:05:27.542 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:05:27.756 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:05:32.317 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:05:36.864 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:05:41.426 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:05:42.555 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:05:45.993 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:05:50.549 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:05:55.096 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:05:57.559 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:05:59.655 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:06:04.206 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:06:08.766 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:06:12.574 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:06:13.327 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:06:17.890 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:06:22.451 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:06:27.009 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:06:27.577 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:06:31.556 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:06:36.103 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:06:40.664 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:06:42.589 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:06:45.209 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:06:49.776 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:06:54.348 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:06:57.592 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:06:58.902 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:07:03.477 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:07:08.043 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:07:12.605 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:07:12.606 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:07:17.169 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:07:21.749 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:07:26.316 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:07:27.620 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:07:30.881 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:07:35.431 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:07:39.995 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:07:42.632 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:07:44.556 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:07:49.110 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:07:53.669 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:07:57.645 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:07:58.231 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:08:02.777 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:08:07.342 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:08:11.893 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:08:12.646 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:08:16.455 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:08:21.013 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:08:25.572 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:08:27.652 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:08:30.121 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:08:34.686 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:08:39.228 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:08:42.666 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:08:43.782 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:08:48.329 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:08:52.897 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:08:57.473 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:08:57.673 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:09:02.018 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:09:06.572 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:09:11.122 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:09:12.690 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:09:15.685 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:09:20.248 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:09:24.813 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:09:27.700 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:09:29.381 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:09:33.944 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:09:38.498 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:09:42.701 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:09:43.054 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:09:47.621 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:09:52.167 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:09:56.721 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:09:57.709 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:10:01.270 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:10:05.825 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:10:10.382 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:10:12.714 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:10:14.938 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:10:19.500 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:10:24.065 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:10:27.717 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:10:28.635 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:10:33.190 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:10:37.741 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:10:42.307 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:10:42.723 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:10:46.865 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:10:51.422 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:10:55.983 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:10:57.736 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:11:00.533 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:11:05.103 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:11:09.667 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:11:12.748 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:11:14.223 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:11:18.776 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:11:23.347 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:11:27.751 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:11:27.905 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:11:32.459 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:11:37.007 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:11:41.560 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:11:42.764 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:11:46.136 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:11:50.688 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:11:55.248 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:11:57.769 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:11:59.810 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:12:04.361 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:12:08.911 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:12:12.771 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:12:13.466 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:12:18.019 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:12:22.578 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:12:27.117 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:12:27.781 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:12:31.666 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:12:36.236 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:12:40.787 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:12:42.787 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:12:45.341 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:12:49.898 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:12:54.454 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:12:57.794 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:12:59.009 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:13:03.561 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:13:08.115 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:13:12.670 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:13:12.809 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:13:17.232 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:13:21.782 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:13:26.339 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:13:27.820 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:13:30.902 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:13:35.455 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:13:40.017 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:13:42.828 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:13:44.564 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:13:49.107 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:13:53.653 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:13:57.837 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:13:58.204 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:14:02.753 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:14:07.318 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:14:11.860 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:14:12.841 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:14:16.418 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:14:20.974 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:14:25.520 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:14:27.857 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:14:30.073 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:14:34.632 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:14:39.181 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:14:42.861 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:14:43.742 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:14:48.291 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:14:52.841 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:14:57.397 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:14:57.874 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:15:01.946 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:15:06.499 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:15:11.053 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:15:12.874 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:15:15.602 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:15:20.154 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:15:24.706 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:15:27.889 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:15:29.259 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:15:33.816 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:15:38.366 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:15:42.900 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:15:42.931 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:15:47.483 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:15:52.045 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:15:56.599 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:15:57.906 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:16:01.149 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:16:05.721 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:16:10.279 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:16:12.921 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:16:14.840 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:16:19.388 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:16:23.927 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:16:27.927 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:16:28.483 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:16:33.041 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:16:37.596 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:16:42.151 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:16:42.931 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:16:46.723 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:16:51.338 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:16:55.893 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:16:57.933 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:17:00.452 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:17:05.007 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:17:09.570 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:17:12.946 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:17:14.131 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:17:18.690 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:17:23.256 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:17:27.817 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:17:27.955 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:17:32.366 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:17:36.924 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:17:41.461 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:17:42.967 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:17:46.009 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:17:50.573 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:17:55.122 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:17:57.971 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:17:59.684 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:18:04.236 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:18:08.791 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:18:12.986 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:18:13.343 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:18:17.905 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:18:22.444 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:18:27.001 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:18:27.995 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:18:31.555 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:18:36.123 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:18:40.670 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:18:43.007 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:18:45.221 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:18:49.775 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:18:54.325 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:18:58.012 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:18:58.886 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:19:03.441 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:19:07.995 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:19:12.550 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:19:13.023 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:19:17.105 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:19:21.663 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:19:26.215 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:19:28.034 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:19:30.763 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:19:35.323 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:19:39.864 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:19:43.048 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:19:44.424 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:19:48.978 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:19:53.531 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:19:58.051 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:19:58.083 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:20:02.638 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:20:07.198 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:20:11.757 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:20:13.054 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:20:16.327 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:20:20.885 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:20:25.455 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:20:28.056 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:20:30.003 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:20:34.543 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:20:39.096 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:20:43.064 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:20:43.650 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:20:48.207 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:20:52.759 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:20:57.301 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:20:58.071 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:21:01.848 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:21:06.401 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:21:10.960 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:21:13.073 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:21:15.519 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:21:20.078 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:21:24.636 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:21:28.088 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:21:29.193 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:21:33.742 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:21:38.306 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:21:42.861 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:21:43.088 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:21:47.413 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:21:51.972 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:21:56.529 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:21:58.101 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:22:01.083 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:22:05.645 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:22:10.205 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:22:13.113 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:22:14.769 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:22:19.324 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:22:23.877 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:22:28.123 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:22:28.433 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:22:32.981 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:22:37.525 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:22:42.071 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:22:43.133 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:22:46.626 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:22:51.189 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:22:55.729 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:22:58.145 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:23:00.281 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:23:04.832 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:23:09.386 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:23:13.147 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:23:13.946 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:23:18.498 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:23:23.053 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:23:27.608 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:23:28.149 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:23:32.157 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:23:36.711 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:23:41.288 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:23:43.157 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:23:45.850 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:23:50.413 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:23:54.968 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:23:58.170 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:23:59.513 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:24:04.062 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:24:08.620 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:24:13.182 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:24:13.183 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:24:17.741 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:24:22.295 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:24:26.851 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:24:28.194 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:24:31.422 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:24:35.968 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:24:40.509 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:24:43.200 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:24:45.064 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:24:49.626 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:24:54.169 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:24:58.206 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:24:58.717 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:25:03.275 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:25:07.827 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:25:12.370 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:25:13.217 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:25:16.917 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:25:21.472 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:25:26.017 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:25:28.219 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:25:30.574 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:25:35.131 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:25:39.684 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:25:43.225 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:25:44.230 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:25:48.771 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:25:53.321 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:25:57.874 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:25:58.229 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:26:02.425 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:26:06.986 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:26:11.533 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:26:13.234 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:26:16.081 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:26:20.637 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:26:25.187 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:26:28.247 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:26:29.743 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:26:34.291 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:26:38.839 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:26:43.250 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:26:43.391 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:26:47.955 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:26:52.508 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:26:57.073 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:26:58.257 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:27:01.638 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:27:06.189 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:27:10.750 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:27:13.268 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:27:15.301 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:27:19.865 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:27:24.427 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:27:28.281 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:27:28.987 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:27:33.539 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:27:38.099 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:27:42.662 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:27:43.295 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:27:47.214 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:27:51.760 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:27:56.323 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:27:58.298 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:28:00.877 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:28:05.422 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:28:09.973 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:28:13.311 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:28:14.531 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:28:19.092 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:28:23.640 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:28:28.202 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:28:28.324 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:28:32.753 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:28:37.306 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:28:41.858 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:28:43.335 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:28:46.412 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:28:50.956 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:28:55.510 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:28:58.339 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:29:00.059 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:29:04.618 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:29:09.154 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:29:13.346 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:29:13.702 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:29:18.266 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:29:22.827 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:29:27.370 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:29:28.355 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:29:31.927 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:29:36.473 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:29:41.021 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:29:43.357 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:29:45.559 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:29:50.109 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:29:54.667 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:29:58.365 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:29:59.212 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:30:03.764 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:30:08.314 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:30:12.865 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:30:13.370 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:30:17.406 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:30:21.946 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:30:26.499 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:30:28.377 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:30:31.056 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:30:35.617 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:30:40.196 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:30:43.388 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:30:44.756 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:30:49.328 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:30:53.891 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:30:58.397 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:30:58.459 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:31:03.020 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:31:07.573 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:31:12.122 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:31:13.403 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:31:16.692 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:31:21.272 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:31:25.820 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:31:28.412 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:31:30.365 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:31:34.916 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:31:39.474 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:31:43.413 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:31:44.027 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:31:48.591 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:31:52.490 [Thread-0] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Shutdown initiated...
2025-09-18 22:31:52.499 [Thread-0] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Shutdown completed.
2025-09-18 22:46:43.980 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Starting...
2025-09-18 22:46:44.153 [main] INFO  com.zaxxer.hikari.pool.HikariPool - MercariResearchDB-Pool - Added connection org.sqlite.jdbc4.JDBC4Connection@2a798d51
2025-09-18 22:46:44.155 [main] INFO  com.zaxxer.hikari.HikariDataSource - MercariResearchDB-Pool - Start completed.
2025-09-18 22:46:44.301 [main] INFO  c.m.u.database.SellerMigrationUtil - セラーデータ移行は不要です。
2025-09-18 22:46:44.864 [AWT-EventQueue-0] INFO  c.m.screen.main.Application - TLSバージョンの設定が完了しました
2025-09-18 22:46:45.026 [AWT-EventQueue-0] INFO  com.mrcresearch.screen.main.Main - メインパネルを初期化しています
2025-09-18 22:46:47.697 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 自動更新タイマーを停止しました - 進行中のリサーチはありません
2025-09-18 22:46:50.027 [SwingWorker-pool-1-thread-4] INFO  c.mrcresearch.service.common.Version - 現在のバージョン：4.0.24
2025-09-18 22:46:53.094 [AWT-EventQueue-0] INFO  com.mrcresearch.util.ImageUtil - WebPデコーダが正常に登録されました
2025-09-18 22:47:15.427 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのため自動更新タイマーを開始しました
2025-09-18 22:47:19.299 [AWT-EventQueue-0] INFO  c.m.util.database.SellerRepository - セラー: 239833043 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-18 22:47:19.300 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: セラーリサーチ: 239833043 (キューサイズ: 1)
2025-09-18 22:47:19.300 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: セラーリサーチ: 239833043 (残り: 0)
2025-09-18 22:47:19.314 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 239833043 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-18 22:47:19.314 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 239833043 のセラーのステータスを 'リサーチ中' に正常に更新しました
2025-09-18 22:47:30.437 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:47:45.827 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:47:51.902 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 239833043 のセラー検索実行を開始します
2025-09-18 22:47:56.951 [LittleProxy-0-ProxyToServerWorker-4] ERROR io.netty.util.ResourceLeakDetector - LEAK: ByteBuf.release() was not called before it's garbage-collected. See https://netty.io/wiki/reference-counted-objects.html for more information.
Recent access records: 
Created at:
	io.netty.buffer.PooledByteBufAllocator.newDirectBuffer(PooledByteBufAllocator.java:363)
	io.netty.buffer.AbstractByteBufAllocator.directBuffer(AbstractByteBufAllocator.java:187)
	io.netty.buffer.AbstractByteBufAllocator.directBuffer(AbstractByteBufAllocator.java:178)
	io.netty.buffer.CompositeByteBuf.allocBuffer(CompositeByteBuf.java:1858)
	io.netty.buffer.CompositeByteBuf.copy(CompositeByteBuf.java:1510)
	io.netty.buffer.AbstractByteBuf.copy(AbstractByteBuf.java:1195)
	io.netty.handler.codec.http.HttpObjectAggregator$AggregatedFullHttpRequest.copy(HttpObjectAggregator.java:405)
	org.littleshoot.proxy.impl.ClientToProxyConnection.copy(ClientToProxyConnection.java:1044)
	org.littleshoot.proxy.impl.ClientToProxyConnection.doReadHTTPInitial(ClientToProxyConnection.java:211)
	org.littleshoot.proxy.impl.ClientToProxyConnection.readHTTPInitial(ClientToProxyConnection.java:187)
	org.littleshoot.proxy.impl.ClientToProxyConnection.readHTTPInitial(ClientToProxyConnection.java:55)
	org.littleshoot.proxy.impl.ProxyConnection.readHTTP(ProxyConnection.java:144)
	org.littleshoot.proxy.impl.ProxyConnection.read(ProxyConnection.java:122)
	org.littleshoot.proxy.impl.ProxyConnection.channelRead0(ProxyConnection.java:582)
	io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.handler.timeout.IdleStateHandler.channelRead(IdleStateHandler.java:286)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.channel.ChannelInboundHandlerAdapter.channelRead(ChannelInboundHandlerAdapter.java:93)
	org.littleshoot.proxy.impl.ProxyConnection$RequestReadMonitor.channelRead(ProxyConnection.java:709)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:103)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:103)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:324)
	io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:296)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.channel.ChannelInboundHandlerAdapter.channelRead(ChannelInboundHandlerAdapter.java:93)
	org.littleshoot.proxy.impl.ProxyConnection$BytesReadMonitor.channelRead(ProxyConnection.java:686)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-18 22:48:00.458 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:48:01.484 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(1)回目
2025-09-18 22:48:04.519 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:48:06.166 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(2)回目
2025-09-18 22:48:09.187 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:48:10.869 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(3)回目
2025-09-18 22:48:13.878 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:48:15.555 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(4)回目
2025-09-18 22:48:15.994 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:48:18.572 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:48:20.184 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 4 回クリックしました
2025-09-18 22:48:22.115 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Found 144 items for seller: 239833043
2025-09-18 22:48:22.118 [SearchQueueProcessor] INFO  c.m.u.d.SellerSearchRepository - セラー検索をID: 156 で正常に保存しました
2025-09-18 22:48:22.118 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved seller search record with ID: 156
2025-09-18 22:48:22.134 [SearchQueueProcessor] INFO  c.m.util.SellerSearchDataConverter - 144 件の GetItemModel オブジェクトを Item オブジェクトに変換しました
2025-09-18 22:48:22.145 [SearchQueueProcessor] INFO  c.m.u.database.SearchItemRepository - Attempting to save/update 144 items.
2025-09-18 22:48:22.170 [SearchQueueProcessor] INFO  c.m.u.database.SearchItemRepository - Successfully saved/updated 144 items to search_items table.
2025-09-18 22:48:22.170 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved 144 items to search_items table
2025-09-18 22:48:22.267 [SearchQueueProcessor] INFO  c.m.u.d.SellerSearchItemRepository - seller_search_items テーブルに 144 件のアイテム参照を正常に保存しました
2025-09-18 22:48:22.267 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved 144 item references to seller_search_items table
2025-09-18 22:48:22.267 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Successfully completed seller search data persistence for seller: 239833043
2025-09-18 22:48:22.267 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 239833043 のセラー検索が正常に完了しました
2025-09-18 22:48:22.268 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラーを正常に更新しました: 239833043
2025-09-18 22:48:22.268 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 239833043 のリサーチステータスを 完了 に正常に更新しました
2025-09-18 22:48:22.268 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 239833043 のセラーのステータスを '完了' に正常に更新しました
2025-09-18 22:48:24.929 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの実行が完了しました: セラーリサーチ: 239833043
2025-09-18 22:48:24.929 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - セラーリサーチ完了後にテーブルを更新しました: 239833043
2025-09-18 22:48:24.929 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクが完全に完了しました: セラーリサーチ: 239833043
2025-09-18 22:48:26.167 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 自動更新タイマーを停止しました - 進行中のリサーチはありません
2025-09-18 22:52:59.039 [AWT-EventQueue-0] INFO  c.m.util.database.SellerRepository - セラーを正常に挿入しました: 562238482
2025-09-18 22:52:59.039 [AWT-EventQueue-0] INFO  c.m.util.database.SellerRepository - セラー: 562238482 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-18 22:52:59.039 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: セラーリサーチ: 562238482 (キューサイズ: 1)
2025-09-18 22:52:59.039 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: セラーリサーチ: 562238482 (残り: 0)
2025-09-18 22:52:59.040 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 562238482 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-18 22:52:59.041 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 562238482 のセラーのステータスを 'リサーチ中' に正常に更新しました
2025-09-18 22:52:59.716 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのため自動更新タイマーを開始しました
2025-09-18 22:53:14.718 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:53:24.343 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: キーワード検索: 海外製 (キューサイズ: 1)
2025-09-18 22:53:29.729 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:53:30.644 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 562238482 のセラー検索実行を開始します
2025-09-18 22:53:40.014 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(1)回目
2025-09-18 22:53:43.032 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:53:44.672 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(2)回目
2025-09-18 22:53:44.730 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:53:47.686 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:53:49.334 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(3)回目
2025-09-18 22:53:52.340 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:53:53.976 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(4)回目
2025-09-18 22:53:56.985 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:53:58.671 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(5)回目
2025-09-18 22:53:59.739 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:54:01.683 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:54:03.355 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(6)回目
2025-09-18 22:54:06.364 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:54:08.065 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(7)回目
2025-09-18 22:54:11.073 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:54:12.746 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(8)回目
2025-09-18 22:54:14.749 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:54:15.754 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:54:17.445 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(9)回目
2025-09-18 22:54:20.450 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:54:22.149 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(10)回目
2025-09-18 22:54:25.158 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:54:26.864 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(11)回目
2025-09-18 22:54:29.761 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 22:54:29.872 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:54:31.579 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(12)回目
2025-09-18 22:54:34.586 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 22:54:36.132 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 12 回クリックしました
2025-09-18 22:54:39.540 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Found 367 items for seller: 562238482
2025-09-18 22:54:39.542 [SearchQueueProcessor] INFO  c.m.u.d.SellerSearchRepository - セラー検索をID: 157 で正常に保存しました
2025-09-18 22:54:39.542 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved seller search record with ID: 157
2025-09-18 22:54:39.544 [SearchQueueProcessor] INFO  c.m.util.SellerSearchDataConverter - 367 件の GetItemModel オブジェクトを Item オブジェクトに変換しました
2025-09-18 22:54:39.546 [SearchQueueProcessor] INFO  c.m.u.database.SearchItemRepository - Attempting to save/update 367 items.
2025-09-18 22:54:39.586 [SearchQueueProcessor] INFO  c.m.u.database.SearchItemRepository - Successfully saved/updated 367 items to search_items table.
2025-09-18 22:54:39.586 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved 367 items to search_items table
2025-09-18 22:54:39.924 [SearchQueueProcessor] INFO  c.m.u.d.SellerSearchItemRepository - seller_search_items テーブルに 367 件のアイテム参照を正常に保存しました
2025-09-18 22:54:39.924 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved 367 item references to seller_search_items table
2025-09-18 22:54:39.924 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Successfully completed seller search data persistence for seller: 562238482
2025-09-18 22:54:39.924 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 562238482 のセラー検索が正常に完了しました
2025-09-18 22:54:39.925 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラーを正常に更新しました: 562238482
2025-09-18 22:54:39.926 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 562238482 のリサーチステータスを 完了 に正常に更新しました
2025-09-18 22:54:39.926 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 562238482 のセラーのステータスを '完了' に正常に更新しました
2025-09-18 22:54:42.717 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの実行が完了しました: セラーリサーチ: 562238482
2025-09-18 22:54:42.717 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - セラーリサーチ完了後にテーブルを更新しました: 562238482
2025-09-18 22:54:42.717 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクが完全に完了しました: セラーリサーチ: 562238482
2025-09-18 22:54:42.717 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: キーワード検索: 海外製 (残り: 0)
2025-09-18 22:54:42.730 [AWT-EventQueue-0] ERROR c.mrcresearch.screen.keyword.Keyword - Error getting default page value: class java.lang.String cannot be cast to class java.lang.Integer (java.lang.String and java.lang.Integer are in module java.base of loader 'bootstrap')
2025-09-18 22:54:42.734 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの実行が完了しました: キーワード検索: 海外製
2025-09-18 22:54:42.734 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクに非同期操作があります。完了を待機中...
2025-09-18 22:54:42.735 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - レコードID: 82 のキーワード検索実行を開始します
2025-09-18 22:54:42.741 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - キーワード検索結果を取得しました: KeywordSearchResultModel{id=82, keyword='海外製', categories='', addedDate=Thu Sep 18 22:54:42 JST 2025, updatedDate=Thu Sep 18 22:54:42 JST 2025, status='待機中', researchStatus='リサーチ中', minPrice=300, maxPrice=9999999, pages=10, sortByNew=true, ignoreShops=true, salesStatus='販売済み', itemConditionIds='1', excludeKeyword='', colorIds='null', categoryId=''}
2025-09-18 22:54:42.741 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - IDが以下で始まる ProgressItem を探しています: keyword_82_
2025-09-18 22:54:42.741 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - アクティブな進捗アイテム数: 3
2025-09-18 22:54:42.741 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: seller_research_1758203239295
2025-09-18 22:54:42.741 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: seller_research_1758203579037
2025-09-18 22:54:42.741 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - ProgressItem ID を確認中: keyword_82_1758203682730
2025-09-18 22:54:42.741 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - 一致する ProgressItem が見つかりました: keyword_82_1758203682730
2025-09-18 22:54:42.743 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchResultModel を KeywordSearchModel に変換中...
2025-09-18 22:54:42.743 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchResultModel を KeywordSearchModel に変換中...
2025-09-18 22:54:42.743 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - 設定された単語: 海外製
2025-09-18 22:54:42.743 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - maxMore を設定: 9 (ページ数: 10)
2025-09-18 22:54:42.743 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - キーワード: 海外製 の検索URLを構築中
2025-09-18 22:54:42.743 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - キーワードパラメータを追加しました: keyword=%E6%B5%B7%E5%A4%96%E8%A3%BD
2025-09-18 22:54:42.743 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - ソートパラメータを追加しました: sort=created_time&order=desc
2025-09-18 22:54:42.743 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - アイテムタイプパラメータを追加しました: item_types=1
2025-09-18 22:54:42.743 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - 販売ステータスパラメータを追加しました: status=sold_out
2025-09-18 22:54:42.743 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - 最終URLパラメータ: keyword=%E6%B5%B7%E5%A4%96%E8%A3%BD&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-18 22:54:42.743 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - 構築された検索URL: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96%E8%A3%BD&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-18 22:54:42.743 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - baseUrl を設定: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96%E8%A3%BD&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-18 22:54:42.744 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - saveName を設定: 海外製_1758203682743
2025-09-18 22:54:42.744 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - KeywordSearchModel の変換が正常に完了しました
2025-09-18 22:54:42.744 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - 変換が完了しました。検索URL: https://jp.mercari.com/search?keyword=%E6%B5%B7%E5%A4%96%E8%A3%BD&sort=created_time&order=desc&item_types=1&status=sold_out&item_condition_id=1&d664efe3-ae5a-4824-b729-e789bf93aba9=B38F1DC9286E0B80812D9B19DB14298C1FF1116CA8332D9EE9061026635C9088
2025-09-18 22:54:42.746 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - ナビゲートする最大ページ数: 9
2025-09-18 22:54:42.746 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - 実際のキーワード検索実行を開始します...
2025-09-18 22:54:43.348 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 自動更新タイマーを停止しました - 進行中のリサーチはありません
2025-09-18 22:56:14.216 [Thread-11] INFO  c.m.util.KeywordSearchDataConverter - データベース保存用に 1120 件のキーワード検索アイテムオブジェクトを変換しました
2025-09-18 22:56:17.008 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - キーワード検索実行が正常に完了しました。
2025-09-18 22:56:17.009 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - 1120 件の検索結果アイテムをデータベースに保存中...
2025-09-18 22:56:17.009 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - キーワード検索結果ID: 82 のアイテム 1120 件の保存を開始します
2025-09-18 22:56:17.009 [Thread-11] INFO  c.m.u.database.SearchItemRepository - Attempting to save/update 1120 items.
2025-09-18 22:56:17.305 [Thread-11] INFO  c.m.u.database.SearchItemRepository - Successfully saved/updated 1120 items to search_items table.
2025-09-18 22:56:17.950 [Thread-11] INFO  c.m.u.d.KeywordSearchItemRepository - keyword_search_items テーブルに 1120 件のアイテム参照を正常に保存しました
2025-09-18 22:56:17.950 [Thread-11] INFO  c.m.u.d.KeywordSearchRepository - キーワード検索結果ID: 82 の search_items テーブルに 1120 件のアイテム、keyword_search_items テーブルに 1120 件の参照を正常に保存しました
2025-09-18 22:56:17.950 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - キーワード検索結果ID: 82 のデータベースに 1120 件のアイテムを正常に保存しました
2025-09-18 22:56:17.950 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - キーワード検索結果ID: 82 のデータベースに 1120 件のアイテムを保存しました
2025-09-18 22:56:17.951 [Thread-11] INFO  c.m.s.a.service.SearchByKeyWord - レコードID: 82 のキーワード検索が正常に完了しました
2025-09-18 22:56:17.951 [Thread-11] INFO  c.m.util.SearchQueueManager - タスク完了通知を受信しました
2025-09-18 22:56:17.951 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの非同期操作が完了しました: キーワード検索: 海外製
2025-09-18 22:56:17.951 [SearchQueueProcessor] INFO  c.mrcresearch.screen.keyword.Keyword - キーワード検索タスクが完了しました: 海外製
2025-09-18 22:56:17.951 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクが完全に完了しました: キーワード検索: 海外製
2025-09-18 23:27:31.013 [AWT-EventQueue-0] INFO  c.m.util.database.SellerRepository - セラーを正常に挿入しました: 895917404
2025-09-18 23:27:31.013 [AWT-EventQueue-0] INFO  c.m.util.database.SellerRepository - セラー: 895917404 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-18 23:27:31.013 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: セラーリサーチ: 895917404 (キューサイズ: 1)
2025-09-18 23:27:31.013 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: セラーリサーチ: 895917404 (残り: 0)
2025-09-18 23:27:31.015 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 895917404 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-18 23:27:31.015 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 895917404 のセラーのステータスを 'リサーチ中' に正常に更新しました
2025-09-18 23:27:31.717 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのため自動更新タイマーを開始しました
2025-09-18 23:27:33.100 [LittleProxy-3-ClientToProxyWorker-5] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xba025891, L:/*************:8888 ! R:/*************:62117]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:27:33.156 [LittleProxy-3-ClientToProxyWorker-6] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x101203bb, L:/*************:8888 ! R:/*************:62119]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:27:46.720 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 23:28:01.735 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 23:28:02.187 [LittleProxy-3-ClientToProxyWorker-7] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xef210ef8, L:/*************:8888 ! R:/*************:62133]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:02.230 [LittleProxy-3-ClientToProxyWorker-0] ERROR io.netty.util.ResourceLeakDetector - LEAK: ByteBuf.release() was not called before it's garbage-collected. See https://netty.io/wiki/reference-counted-objects.html for more information.
Recent access records: 
Created at:
	io.netty.buffer.PooledByteBufAllocator.newDirectBuffer(PooledByteBufAllocator.java:363)
	io.netty.buffer.AbstractByteBufAllocator.directBuffer(AbstractByteBufAllocator.java:187)
	io.netty.buffer.AbstractByteBufAllocator.directBuffer(AbstractByteBufAllocator.java:178)
	io.netty.buffer.CompositeByteBuf.allocBuffer(CompositeByteBuf.java:1858)
	io.netty.buffer.CompositeByteBuf.copy(CompositeByteBuf.java:1510)
	io.netty.buffer.AbstractByteBuf.copy(AbstractByteBuf.java:1195)
	io.netty.handler.codec.http.HttpObjectAggregator$AggregatedFullHttpRequest.copy(HttpObjectAggregator.java:405)
	org.littleshoot.proxy.impl.ClientToProxyConnection.copy(ClientToProxyConnection.java:1044)
	org.littleshoot.proxy.impl.ClientToProxyConnection.doReadHTTPInitial(ClientToProxyConnection.java:211)
	org.littleshoot.proxy.impl.ClientToProxyConnection.readHTTPInitial(ClientToProxyConnection.java:187)
	org.littleshoot.proxy.impl.ClientToProxyConnection.readHTTPInitial(ClientToProxyConnection.java:55)
	org.littleshoot.proxy.impl.ProxyConnection.readHTTP(ProxyConnection.java:144)
	org.littleshoot.proxy.impl.ProxyConnection.read(ProxyConnection.java:122)
	org.littleshoot.proxy.impl.ProxyConnection.channelRead0(ProxyConnection.java:582)
	io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.handler.timeout.IdleStateHandler.channelRead(IdleStateHandler.java:286)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.channel.ChannelInboundHandlerAdapter.channelRead(ChannelInboundHandlerAdapter.java:93)
	org.littleshoot.proxy.impl.ProxyConnection$RequestReadMonitor.channelRead(ProxyConnection.java:709)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:103)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:103)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:324)
	io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:296)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.channel.ChannelInboundHandlerAdapter.channelRead(ChannelInboundHandlerAdapter.java:93)
	org.littleshoot.proxy.impl.ProxyConnection$BytesReadMonitor.channelRead(ProxyConnection.java:686)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1518)
	io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	java.base/java.lang.Thread.run(Thread.java:1623)
2025-09-18 23:28:02.235 [LittleProxy-3-ClientToProxyWorker-0] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x399cacec, L:/*************:8888 ! R:/*************:62135]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:02.324 [LittleProxy-3-ClientToProxyWorker-1] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xe168c6ad, L:/*************:8888 ! R:/*************:62137]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:02.354 [LittleProxy-3-ClientToProxyWorker-2] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x3ad608a2, L:/*************:8888 ! R:/*************:62139]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:02.461 [LittleProxy-3-ClientToProxyWorker-4] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x67e8ad31, L:/*************:8888 ! R:/*************:62143]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:02.509 [LittleProxy-3-ClientToProxyWorker-5] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xef66bbd3, L:/*************:8888 ! R:/*************:62145]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:02.558 [LittleProxy-3-ClientToProxyWorker-6] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x31dfa1ae, L:/*************:8888 ! R:/*************:62147]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:02.584 [LittleProxy-3-ClientToProxyWorker-7] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x122def8f, L:/*************:8888 ! R:/*************:62149]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:02.630 [LittleProxy-3-ClientToProxyWorker-0] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x61417d8a, L:/*************:8888 ! R:/*************:62151]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:02.668 [LittleProxy-3-ClientToProxyWorker-1] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x04b7a5f1, L:/*************:8888 ! R:/*************:62153]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:02.692 [LittleProxy-3-ClientToProxyWorker-2] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xa2f17786, L:/*************:8888 ! R:/*************:62155]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:02.754 [LittleProxy-3-ClientToProxyWorker-3] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xdb198e18, L:/*************:8888 ! R:/*************:62157]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:02.779 [LittleProxy-3-ClientToProxyWorker-4] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x58e24b6f, L:/*************:8888 ! R:/*************:62159]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:02.812 [LittleProxy-3-ClientToProxyWorker-5] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xf1619447, L:/*************:8888 ! R:/*************:62161]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:02.844 [LittleProxy-3-ClientToProxyWorker-6] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xd6c16b5f, L:/*************:8888 ! R:/*************:62163]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:02.898 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 895917404 のセラー検索実行を開始します
2025-09-18 23:28:02.910 [LittleProxy-3-ClientToProxyWorker-7] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xf69518cf, L:/*************:8888 ! R:/*************:62166]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:02.964 [LittleProxy-3-ClientToProxyWorker-1] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xdf2c51db, L:/*************:8888 ! R:/*************:62170]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:03.040 [LittleProxy-3-ClientToProxyWorker-3] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x928b254a, L:/*************:8888 ! R:/*************:62175]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:03.077 [LittleProxy-3-ClientToProxyWorker-4] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x275a733b, L:/*************:8888 ! R:/*************:62177]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:03.111 [LittleProxy-3-ClientToProxyWorker-5] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x22445a7a, L:/*************:8888 ! R:/*************:62179]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:03.147 [LittleProxy-3-ClientToProxyWorker-6] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x9172223c, L:/*************:8888 ! R:/*************:62181]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:03.173 [LittleProxy-3-ClientToProxyWorker-7] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0x9c6a0da4, L:/*************:8888 ! R:/*************:62183]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:03.212 [LittleProxy-3-ClientToProxyWorker-0] ERROR o.l.p.impl.ClientToProxyConnection - (AWAITING_INITIAL) [id: 0xae216754, L:/*************:8888 ! R:/*************:62185]: Caught an exception on ClientToProxyConnection
io.netty.handler.codec.DecoderException: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:471)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base/java.lang.Thread.run(Thread.java:1623)
Caused by: javax.net.ssl.SSLHandshakeException: Received fatal alert: unknown_ca
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:130)
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:117)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:358)
	at java.base/sun.security.ssl.Alert$AlertConsumer.consume(Alert.java:287)
	at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:204)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
	at java.base/sun.security.ssl.SSLEngineImpl.decode(SSLEngineImpl.java:736)
	at java.base/sun.security.ssl.SSLEngineImpl.readRecord(SSLEngineImpl.java:691)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:506)
	at java.base/sun.security.ssl.SSLEngineImpl.unwrap(SSLEngineImpl.java:482)
	at java.base/javax.net.ssl.SSLEngine.unwrap(SSLEngine.java:679)
	at io.netty.handler.ssl.SslHandler$SslEngineType$3.unwrap(SslHandler.java:282)
	at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1372)
	at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1267)
	at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1314)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:501)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:440)
	... 16 common frames omitted
2025-09-18 23:28:11.726 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(1)回目
2025-09-18 23:28:14.744 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 23:28:16.359 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(2)回目
2025-09-18 23:28:16.740 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 23:28:19.364 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 23:28:20.992 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(3)回目
2025-09-18 23:28:23.995 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 23:28:25.642 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(4)回目
2025-09-18 23:28:28.653 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 23:28:30.305 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(5)回目
2025-09-18 23:28:31.746 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 23:28:33.313 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 23:28:34.995 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(6)回目
2025-09-18 23:28:38.012 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 23:28:39.665 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(7)回目
2025-09-18 23:28:42.676 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 23:28:44.355 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(8)回目
2025-09-18 23:28:46.753 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 23:28:47.368 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 23:28:49.034 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(9)回目
2025-09-18 23:28:52.051 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 23:28:53.742 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(10)回目
2025-09-18 23:28:56.754 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 23:28:58.283 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 10 回クリックしました
2025-09-18 23:29:01.445 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Found 324 items for seller: 895917404
2025-09-18 23:29:01.446 [SearchQueueProcessor] INFO  c.m.u.d.SellerSearchRepository - セラー検索をID: 158 で正常に保存しました
2025-09-18 23:29:01.446 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved seller search record with ID: 158
2025-09-18 23:29:01.448 [SearchQueueProcessor] INFO  c.m.util.SellerSearchDataConverter - 324 件の GetItemModel オブジェクトを Item オブジェクトに変換しました
2025-09-18 23:29:01.448 [SearchQueueProcessor] INFO  c.m.u.database.SearchItemRepository - Attempting to save/update 324 items.
2025-09-18 23:29:01.463 [SearchQueueProcessor] INFO  c.m.u.database.SearchItemRepository - Successfully saved/updated 324 items to search_items table.
2025-09-18 23:29:01.463 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved 324 items to search_items table
2025-09-18 23:29:01.735 [SearchQueueProcessor] INFO  c.m.u.d.SellerSearchItemRepository - seller_search_items テーブルに 324 件のアイテム参照を正常に保存しました
2025-09-18 23:29:01.735 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved 324 item references to seller_search_items table
2025-09-18 23:29:01.735 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Successfully completed seller search data persistence for seller: 895917404
2025-09-18 23:29:01.735 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 895917404 のセラー検索が正常に完了しました
2025-09-18 23:29:01.736 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラーを正常に更新しました: 895917404
2025-09-18 23:29:01.736 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 895917404 のリサーチステータスを 完了 に正常に更新しました
2025-09-18 23:29:01.736 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 895917404 のセラーのステータスを '完了' に正常に更新しました
2025-09-18 23:29:01.766 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 23:29:02.410 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 自動更新タイマーを停止しました - 進行中のリサーチはありません
2025-09-18 23:29:04.404 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの実行が完了しました: セラーリサーチ: 895917404
2025-09-18 23:29:04.404 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - セラーリサーチ完了後にテーブルを更新しました: 895917404
2025-09-18 23:29:04.404 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクが完全に完了しました: セラーリサーチ: 895917404
2025-09-18 23:57:00.900 [AWT-EventQueue-0] INFO  c.m.screen.settings.Settings - Updated keyword screen with new default settings
2025-09-18 23:57:01.091 [AWT-EventQueue-0] INFO  c.m.screen.main.Application - フォントサイズが以下に変更されました: MEDIUM
2025-09-18 23:57:06.362 [AWT-EventQueue-0] INFO  c.m.screen.settings.Settings - Updated keyword screen with new default settings
2025-09-18 23:57:06.470 [AWT-EventQueue-0] INFO  c.m.screen.main.Application - フォントサイズが以下に変更されました: SMALL
2025-09-18 23:57:26.772 [AWT-EventQueue-0] INFO  c.m.util.database.SellerRepository - セラー: 163855284 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-18 23:57:26.774 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: セラーリサーチ: 163855284 (キューサイズ: 1)
2025-09-18 23:57:26.774 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: セラーリサーチ: 163855284 (残り: 0)
2025-09-18 23:57:26.775 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 163855284 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-18 23:57:26.775 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 163855284 のセラーのステータスを 'リサーチ中' に正常に更新しました
2025-09-18 23:57:27.485 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのため自動更新タイマーを開始しました
2025-09-18 23:57:35.655 [AWT-EventQueue-0] INFO  c.m.util.database.SellerRepository - セラー: 176310729 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-18 23:57:35.656 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: セラーリサーチ: 176310729 (キューサイズ: 1)
2025-09-18 23:57:38.866 [AWT-EventQueue-0] INFO  c.m.util.database.SellerRepository - セラー: 250026963 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-18 23:57:38.866 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: セラーリサーチ: 250026963 (キューサイズ: 2)
2025-09-18 23:57:42.496 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 23:57:50.427 [AWT-EventQueue-0] INFO  c.m.util.database.SellerRepository - セラーを正常に挿入しました: 232194206
2025-09-18 23:57:50.427 [AWT-EventQueue-0] INFO  c.m.util.database.SellerRepository - セラー: 232194206 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-18 23:57:50.427 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: セラーリサーチ: 232194206 (キューサイズ: 3)
2025-09-18 23:57:54.226 [AWT-EventQueue-0] INFO  c.m.util.database.SellerRepository - セラーを正常に挿入しました: 349539819
2025-09-18 23:57:54.228 [AWT-EventQueue-0] INFO  c.m.util.database.SellerRepository - セラー: 349539819 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-18 23:57:54.228 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: セラーリサーチ: 349539819 (キューサイズ: 4)
2025-09-18 23:57:57.502 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 23:57:58.596 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 163855284 のセラー検索実行を開始します
2025-09-18 23:58:05.697 [AWT-EventQueue-0] INFO  c.m.util.database.SellerRepository - セラーを正常に挿入しました: 716407612
2025-09-18 23:58:05.698 [AWT-EventQueue-0] INFO  c.m.util.database.SellerRepository - セラー: 716407612 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-18 23:58:05.698 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: セラーリサーチ: 716407612 (キューサイズ: 5)
2025-09-18 23:58:07.930 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(1)回目
2025-09-18 23:58:10.941 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 23:58:12.511 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 23:58:12.581 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(2)回目
2025-09-18 23:58:15.582 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 4
2025-09-18 23:58:17.224 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(3)回目
2025-09-18 23:58:20.230 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 21
2025-09-18 23:58:21.921 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(4)回目
2025-09-18 23:58:23.476 [AWT-EventQueue-0] INFO  c.m.util.database.SellerRepository - セラーを正常に挿入しました: 837669355
2025-09-18 23:58:23.477 [AWT-EventQueue-0] INFO  c.m.util.database.SellerRepository - セラー: 837669355 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-18 23:58:23.477 [AWT-EventQueue-0] INFO  c.m.util.SearchQueueManager - タスクをキューに追加しました: セラーリサーチ: 837669355 (キューサイズ: 6)
2025-09-18 23:58:24.926 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 44
2025-09-18 23:58:26.612 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(5)回目
2025-09-18 23:58:27.512 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 23:58:29.621 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 74
2025-09-18 23:58:29.621 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 5 回クリックしました
2025-09-18 23:58:32.309 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Found 180 items for seller: 163855284
2025-09-18 23:58:32.311 [SearchQueueProcessor] INFO  c.m.u.d.SellerSearchRepository - セラー検索をID: 159 で正常に保存しました
2025-09-18 23:58:32.311 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved seller search record with ID: 159
2025-09-18 23:58:32.313 [SearchQueueProcessor] INFO  c.m.util.SellerSearchDataConverter - 180 件の GetItemModel オブジェクトを Item オブジェクトに変換しました
2025-09-18 23:58:32.313 [SearchQueueProcessor] INFO  c.m.u.database.SearchItemRepository - Attempting to save/update 180 items.
2025-09-18 23:58:32.324 [SearchQueueProcessor] INFO  c.m.u.database.SearchItemRepository - Successfully saved/updated 180 items to search_items table.
2025-09-18 23:58:32.324 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved 180 items to search_items table
2025-09-18 23:58:32.441 [SearchQueueProcessor] INFO  c.m.u.d.SellerSearchItemRepository - seller_search_items テーブルに 180 件のアイテム参照を正常に保存しました
2025-09-18 23:58:32.441 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Saved 180 item references to seller_search_items table
2025-09-18 23:58:32.441 [SearchQueueProcessor] INFO  c.m.s.a.service.SellerListAnalyze - Successfully completed seller search data persistence for seller: 163855284
2025-09-18 23:58:32.441 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 163855284 のセラー検索が正常に完了しました
2025-09-18 23:58:32.442 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラーを正常に更新しました: 163855284
2025-09-18 23:58:32.443 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 163855284 のリサーチステータスを 完了 に正常に更新しました
2025-09-18 23:58:32.443 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 163855284 のセラーのステータスを '完了' に正常に更新しました
2025-09-18 23:58:35.258 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクの実行が完了しました: セラーリサーチ: 163855284
2025-09-18 23:58:35.258 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - セラーリサーチ完了後にテーブルを更新しました: 163855284
2025-09-18 23:58:35.258 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクが完全に完了しました: セラーリサーチ: 163855284
2025-09-18 23:58:35.258 [SearchQueueProcessor] INFO  c.m.util.SearchQueueManager - タスクを開始します: セラーリサーチ: 176310729 (残り: 5)
2025-09-18 23:58:35.262 [SearchQueueProcessor] INFO  c.m.util.database.SellerRepository - セラー: 176310729 のリサーチステータスを リサーチ中 に正常に更新しました
2025-09-18 23:58:35.262 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 176310729 のセラーのステータスを 'リサーチ中' に正常に更新しました
2025-09-18 23:58:42.515 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 23:58:57.526 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 23:59:07.574 [SearchQueueProcessor] INFO  c.m.s.analyze.service.SearchBySeller - セラーID: 176310729 のセラー検索実行を開始します
2025-09-18 23:59:12.541 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 23:59:16.679 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(1)回目
2025-09-18 23:59:19.691 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 23:59:21.326 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(2)回目
2025-09-18 23:59:24.337 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 23:59:25.983 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(3)回目
2025-09-18 23:59:27.556 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 23:59:28.988 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 23:59:30.698 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(4)回目
2025-09-18 23:59:33.701 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 23:59:35.390 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(5)回目
2025-09-18 23:59:38.398 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 23:59:40.111 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(6)回目
2025-09-18 23:59:42.570 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 23:59:43.114 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 23:59:44.813 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(7)回目
2025-09-18 23:59:47.819 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 23:59:49.529 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(8)回目
2025-09-18 23:59:52.541 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 23:59:54.244 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(9)回目
2025-09-18 23:59:57.248 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 古いアイテム数: 0
2025-09-18 23:59:57.586 [AWT-EventQueue-0] INFO  c.m.s.c.SalesAggregationPanel - 進行中のリサーチのためテーブルを自動更新しました
2025-09-18 23:59:58.959 [SearchQueueProcessor] INFO  c.m.service.tools.DriverTool - 「もっと見る」をクリックしました(10)回目
