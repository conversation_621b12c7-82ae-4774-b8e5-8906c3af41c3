#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1048576 bytes. Error detail: AllocateHeap
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (allocation.cpp:44), pid=32944, tid=27880
#
# JRE version:  (21.0.5+8) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.5+8-b631.16, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://gitlab.com': 

Host: AMD Ryzen 5 8600G w/ Radeon 760M Graphics      , 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
Time: Sun Mar  2 05:27:20 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.4974) elapsed time: 0.010844 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001a25bc00f30):  JavaThread "Unknown thread" [_thread_in_vm, id=27880, stack(0x0000002ffd200000,0x0000002ffd300000) (1024K)]

Stack: [0x0000002ffd200000,0x0000002ffd300000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e52b9]
V  [jvm.dll+0x8c3633]
V  [jvm.dll+0x8c5b8e]
V  [jvm.dll+0x8c6273]
V  [jvm.dll+0x288f46]
V  [jvm.dll+0xc0e57]
V  [jvm.dll+0x3339c5]
V  [jvm.dll+0x88aa4d]
V  [jvm.dll+0x3ca518]
V  [jvm.dll+0x873b98]
V  [jvm.dll+0x45eede]
V  [jvm.dll+0x460bc1]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffd0f7aa148, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:

=>0x000001a25bc00f30 (exited) JavaThread "Unknown thread"    [_thread_in_vm, id=27880, stack(0x0000002ffd200000,0x0000002ffd300000) (1024K)]
Total: 1

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000000000000, size: 0 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.008 Loaded shared library C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff6da890000 - 0x00007ff6da89a000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\java.exe
0x00007ffe5acd0000 - 0x00007ffe5aee7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffe592e0000 - 0x00007ffe593a4000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffe58250000 - 0x00007ffe58621000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffe58660000 - 0x00007ffe58771000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffe4f910000 - 0x00007ffe4f92b000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\VCRUNTIME140.dll
0x00007ffe16200000 - 0x00007ffe16218000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\jli.dll
0x00007ffe58b60000 - 0x00007ffe58d11000 	C:\WINDOWS\System32\USER32.dll
0x00007ffe58800000 - 0x00007ffe58826000 	C:\WINDOWS\System32\win32u.dll
0x00007ffe36f70000 - 0x00007ffe37202000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80\COMCTL32.dll
0x00007ffe5aa10000 - 0x00007ffe5aa39000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffe58ab0000 - 0x00007ffe58b57000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffe57e90000 - 0x00007ffe57fab000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffe57fb0000 - 0x00007ffe5804a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffe597a0000 - 0x00007ffe597d1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffe4f810000 - 0x00007ffe4f81c000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\vcruntime140_1.dll
0x00007ffd1e560000 - 0x00007ffd1e5ed000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\msvcp140.dll
0x00007ffd0eb50000 - 0x00007ffd0f911000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\server\jvm.dll
0x00007ffe5abd0000 - 0x00007ffe5ac81000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffe5aa50000 - 0x00007ffe5aaf7000 	C:\WINDOWS\System32\sechost.dll
0x00007ffe58630000 - 0x00007ffe58658000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffe595f0000 - 0x00007ffe59704000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffe59010000 - 0x00007ffe59081000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffe56c80000 - 0x00007ffe56ccd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffe505c0000 - 0x00007ffe505f4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffe4f3e0000 - 0x00007ffe4f3ea000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffe56c60000 - 0x00007ffe56c73000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffe56f10000 - 0x00007ffe56f28000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffe4f7a0000 - 0x00007ffe4f7aa000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\jimage.dll
0x00007ffe55670000 - 0x00007ffe558a2000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffe5a1a0000 - 0x00007ffe5a530000 	C:\WINDOWS\System32\combase.dll
0x00007ffe593b0000 - 0x00007ffe59487000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffe4b410000 - 0x00007ffe4b442000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffe57e10000 - 0x00007ffe57e8b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffe4ad10000 - 0x00007ffe4ad30000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80;C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://gitlab.com': 
java_class_path (initial): C:/Users/<USER>/AppData/Local/Programs/IntelliJ IDEA Ultimate/plugins/vcs-git/lib/git4idea-rt.jar;C:/Users/<USER>/AppData/Local/Programs/IntelliJ IDEA Ultimate/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 524288000                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8355053568                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8355053568                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-18.0.2.1
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files (x86)\Razer Chroma SDK\bin;C:\Program Files\Razer Chroma SDK\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\VMware\VMware Player\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files\dotnet;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\platform-tools;C:\TDM-GCC-64\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\Java\jdk-18.0.2\bin;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;%JAVA_OHME%\bin;C:\Program Files (x86)\Paragon Software\APFS for Windows;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\Program Files (x86)\Razer\ChromaBroadcast\bin;C:\Program Files\Razer\ChromaBroadcast\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python310;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Program Files\JetBrains\IntelliJ IDEA 2024.2.1\bin;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python310;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Program Files\JetBrains\IntelliJ IDEA 2024.2.1\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama
USERNAME=chooi
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 117 Stepping 2, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 11592K (0% of 32632560K total physical memory with 3599756K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
OS uptime: 2 days 20:28 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (12 cores per cpu, 2 threads per core) family 25 model 117 stepping 2 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ss, avx512_ifma
Processor Information for all 12 processors :
  Max Mhz: 4351, Current Mhz: 4351, Mhz Limit: 4351

Memory: 4k page, system-wide physical 31867M (3515M free)
TotalPageFile size 43940M (AvailPageFile size 5M)
current process WorkingSet (physical memory assigned to process): 11M, peak: 11M
current process commit charge ("private bytes"): 13M, peak: 14M

vm_info: OpenJDK 64-Bit Server VM (21.0.5+8-b631.16) for windows-amd64 JRE (21.0.5+8-b631.16), built on 2024-11-02 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
