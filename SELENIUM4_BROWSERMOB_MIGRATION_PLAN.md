# Selenium 4 + BrowserMob Proxy 移行計画

## 概要

現在のSelenium WebDriverとBrowserUp Proxyの組み合わせから、**Selenium 4の最新機能を活用しつつBrowserMob Proxyを継続使用**する移行プランです。この計画では、Selenium 4の新機能を導入しながら、既存のプロキシ機能との互換性を保持します。

## 移行戦略の背景

### 現在の課題
- BrowserUp Proxyの非推奨化
- Selenium WebDriverの古いバージョン使用
- 新しいブラウザ機能への対応不足
- WebDriver管理の複雑性

### 選択理由
- **既存コード互換性**: HARデータ処理ロジックをそのまま活用
- **段階的移行**: リスクを最小化した漸進的アップグレード
- **安定性重視**: 実績のあるBrowserMob Proxyを継続使用
- **新機能活用**: Selenium 4の改善された機能を導入

## Phase 1: Selenium 4基盤移行（2-3週間）

### 1.1 依存関係の更新

#### Maven依存関係の変更
```xml
<!-- 現在 -->
<dependency>
    <groupId>org.seleniumhq.selenium</groupId>
    <artifactId>selenium-java</artifactId>
    <version>3.x.x</version>
</dependency>

<!-- 移行後 -->
<dependency>
    <groupId>org.seleniumhq.selenium</groupId>
    <artifactId>selenium-java</artifactId>
    <version>4.26.0</version>
</dependency>

<!-- BrowserMob Proxy (継続使用) -->
<dependency>
    <groupId>net.lightbody.bmp</groupId>
    <artifactId>browsermob-core</artifactId>
    <version>2.1.5</version>
</dependency>
```

#### WebDriverManager の導入
```xml
<dependency>
    <groupId>io.github.bonigarcia</groupId>
    <artifactId>webdrivermanager</artifactId>
    <version>5.6.2</version>
</dependency>
```

### 1.2 WebDriver初期化の現代化

#### 従来の問題点
- 手動でのドライバーパス管理
- 古いCapabilitiesクラス使用
- 非推奨APIの使用

#### 改善点
- WebDriverManagerによる自動ドライバー管理
- 新しいOptionsクラスの活用
- Service クラスによる詳細制御

### 1.3 互換性確保

#### 既存APIの保持
- `DriverTool.startDriver()` メソッドのシグネチャ維持
- `getHarEntries()` メソッドの動作保証
- プロキシ設定の透過的な処理

## Phase 2: Selenium 4新機能の段階的導入（3-4週間）

### 2.1 WebDriverManager統合

#### 自動ドライバー管理
```java
// 従来の手動管理から自動管理へ
public class ModernDriverTool {
    public void initializeDriver(String browserType) {
        switch (browserType.toLowerCase()) {
            case "chrome":
                WebDriverManager.chromedriver().setup();
                break;
            case "firefox":
                WebDriverManager.firefoxdriver().setup();
                break;
            case "edge":
                WebDriverManager.edgedriver().setup();
                break;
        }
    }
}
```

#### メリット
- ドライバーバージョンの自動管理
- クロスプラットフォーム対応の簡素化
- メンテナンス負荷の軽減

### 2.2 新しいOptions APIの活用

#### Chrome Options の現代化
```java
// 改善されたChromeOptions設定
ChromeOptions options = new ChromeOptions();
options.addArguments("--disable-blink-features=AutomationControlled");
options.setExperimentalOption("useAutomationExtension", false);
options.setExperimentalOption("excludeSwitches", Arrays.asList("enable-automation"));

// プロキシ設定（BrowserMob Proxy継続使用）
Proxy seleniumProxy = ClientUtil.createSeleniumProxy(browserMobProxy);
options.setProxy(seleniumProxy);
```

#### Firefox Options の強化
```java
FirefoxOptions options = new FirefoxOptions();
options.addPreference("dom.webdriver.enabled", false);
options.addPreference("useAutomationExtension", false);

// プロキシ設定
options.setProxy(seleniumProxy);
```

### 2.3 Service クラスによる詳細制御

#### ChromeDriverService の活用
```java
ChromeDriverService service = new ChromeDriverService.Builder()
    .usingDriverExecutable(new File(driverPath))
    .usingAnyFreePort()
    .withEnvironment(ImmutableMap.of("DISPLAY", ":1"))
    .withLogLevel(ChromeDriverLogLevel.INFO)
    .withLogFile(new File("chromedriver.log"))
    .build();

WebDriver driver = new ChromeDriver(service, options);
```

## Phase 3: 高度な機能統合（2-3週間）

### 3.1 相対ロケーター（RelativeLocator）の導入

#### 従来の要素検索の改善
```java
// 従来の固定的な要素検索
WebElement element = driver.findElement(By.id("target"));

// 相対ロケーターによる柔軟な検索
WebElement element = driver.findElement(
    RelativeLocator.with(By.tagName("input"))
        .below(By.id("username"))
        .toRightOf(By.id("password-label"))
);
```

#### 適用箇所
- 動的なページレイアウトへの対応
- より堅牢な要素検索ロジック
- メンテナンス性の向上

### 3.2 新しいWait機能の活用

#### FluentWait の強化
```java
Wait<WebDriver> wait = new FluentWait<>(driver)
    .withTimeout(Duration.ofSeconds(30))
    .pollingEvery(Duration.ofSeconds(2))
    .ignoring(NoSuchElementException.class)
    .ignoring(StaleElementReferenceException.class);

WebElement element = wait.until(driver -> {
    return driver.findElement(By.id("dynamic-content"));
});
```

### 3.3 ネットワーク監視の強化

#### BrowserMob Proxy + Selenium 4の組み合わせ
```java
public class EnhancedNetworkMonitoring {
    private BrowserMobProxy proxy;
    private WebDriver driver;
    
    public void startMonitoring() {
        // BrowserMob Proxyでネットワーク監視
        proxy.newHar("page-load");
        
        // Selenium 4の新機能でページ読み込み監視
        driver.manage().timeouts().pageLoadTimeout(Duration.ofSeconds(30));
        
        // 組み合わせた監視
        monitorPageLoadWithHAR();
    }
    
    private void monitorPageLoadWithHAR() {
        long startTime = System.currentTimeMillis();
        driver.get(targetUrl);
        long endTime = System.currentTimeMillis();
        
        // HARデータと読み込み時間の組み合わせ分析
        Har har = proxy.getHar();
        analyzePerformance(har, endTime - startTime);
    }
}
```

## Phase 4: パフォーマンス最適化（1-2週間）

### 4.1 並列実行の改善

#### Selenium Grid 4対応
```java
@Test
@Execution(ExecutionMode.CONCURRENT)
public void parallelTest() {
    // Grid 4の新機能を活用した並列実行
    ChromeOptions options = new ChromeOptions();
    options.setCapability("se:name", "Test Name");
    options.setCapability("se:sampleMetadata", "Sample metadata value");
    
    WebDriver driver = new RemoteWebDriver(gridUrl, options);
    // テスト実行
}
```

### 4.2 リソース管理の最適化

#### 自動リソース管理
```java
public class ResourceManagedDriverTool implements AutoCloseable {
    private WebDriver driver;
    private BrowserMobProxy proxy;
    
    @Override
    public void close() {
        try {
            if (driver != null) {
                driver.quit();
            }
        } finally {
            if (proxy != null) {
                proxy.stop();
            }
        }
    }
}

// 使用例
try (ResourceManagedDriverTool driverTool = new ResourceManagedDriverTool()) {
    driverTool.startDriver();
    // テスト実行
} // 自動的にリソース解放
```

## Phase 5: 監視・ログ強化（1週間）

### 5.1 詳細ログ機能

#### Selenium 4のログ機能活用
```java
LoggingPreferences logPrefs = new LoggingPreferences();
logPrefs.enable(LogType.BROWSER, Level.ALL);
logPrefs.enable(LogType.DRIVER, Level.ALL);
logPrefs.enable(LogType.PERFORMANCE, Level.ALL);

ChromeOptions options = new ChromeOptions();
options.setCapability(CapabilityType.LOGGING_PREFS, logPrefs);
```

### 5.2 統合監視システム

#### BrowserMob Proxy + Selenium 4ログの統合
```java
public class IntegratedMonitoring {
    public void collectAllLogs() {
        // HARデータ収集
        Har har = proxy.getHar();
        
        // ブラウザログ収集
        LogEntries browserLogs = driver.manage().logs().get(LogType.BROWSER);
        
        // パフォーマンスログ収集
        LogEntries perfLogs = driver.manage().logs().get(LogType.PERFORMANCE);
        
        // 統合分析
        analyzeIntegratedLogs(har, browserLogs, perfLogs);
    }
}
```

## 技術的考慮事項

### メリット

#### 1. **既存コード互換性**
- HARデータ処理ロジックの完全保持
- 既存のプロキシ設定の継続使用
- 段階的な移行による低リスク

#### 2. **Selenium 4の新機能活用**
- 相対ロケーターによる堅牢な要素検索
- 改善されたWait機能
- 自動ドライバー管理

#### 3. **開発効率の向上**
- WebDriverManagerによる環境構築の簡素化
- 新しいAPIによるコードの可読性向上
- 並列実行の改善

### デメリット・リスク

#### 1. **BrowserMob Proxyの制限**
- HTTP/2サポートの限界
- 将来的なメンテナンス停止リスク
- 新しいブラウザ機能への対応遅れ

#### 2. **技術的負債**
- 古いプロキシライブラリへの依存継続
- 長期的な移行計画の必要性

#### 3. **パフォーマンス制約**
- プロキシオーバーヘッドの継続
- メモリ使用量の最適化限界

## 移行スケジュール

| Phase | 期間 | 主要タスク | 成果物 |
|-------|------|-----------|--------|
| Phase 1 | 2-3週間 | Selenium 4基盤移行 | 更新されたDriverTool |
| Phase 2 | 3-4週間 | 新機能段階導入 | 現代化されたWebDriver管理 |
| Phase 3 | 2-3週間 | 高度機能統合 | 相対ロケーター・Wait機能 |
| Phase 4 | 1-2週間 | パフォーマンス最適化 | 並列実行・リソース管理 |
| Phase 5 | 1週間 | 監視・ログ強化 | 統合監視システム |

**総期間**: 9-13週間

## 長期的な展望

### 段階的な次世代移行

この移行は、将来的なmitmproxyやSelenium Grid 4への移行の基盤となります：

1. **短期（6ヶ月）**: Selenium 4 + BrowserMob Proxyの安定運用
2. **中期（1年）**: mitmproxyへの段階的移行検討
3. **長期（2年）**: 完全な次世代プロキシソリューション

### 投資対効果

- **開発効率**: 30-40%向上（WebDriverManager、新API活用）
- **安定性**: 既存機能の完全保持
- **将来性**: Selenium 4基盤による次世代移行の準備

## 結論

**Selenium 4 + BrowserMob Proxy移行は、リスクを最小化しつつ現代的な機能を導入する現実的なアプローチです。**

この計画により、既存のHARデータ処理ロジックを完全に保持しながら、Selenium 4の新機能を活用できます。将来的なmitmproxy移行への橋渡しとしても機能し、段階的な技術革新を可能にします。
